#!/bin/bash
#
# Cyclops Scraper Installation Script
#
# This script installs and configures the Cyclops service.
# It handles:
# - System dependencies
# - Python environment setup
# - Service installation
# - Permissions configuration
#
# Usage: ./install.sh
#

set -e

APP_DIR="/home/<USER>/cyclops"
SERVICE_NAME="cyclops"
SCRIPTS_DIR="$APP_DIR/scripts"
SYSTEMD_DIR="$SCRIPTS_DIR/systemd"
START_SCRIPT="$SYSTEMD_DIR/bin/start.sh"

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

handle_error() {
  log "ERROR: $1"
  exit 1
}

# Installation
log "===== Starting Cyclops Installation ====="

# Directory setup
cd $APP_DIR || handle_error "Failed to change directory to $APP_DIR"
log "Changed to directory: $(pwd)"

# Permission check
if [ ! -w "$APP_DIR" ]; then
  handle_error "No write permission in $APP_DIR"
fi

# System dependencies
if dpkg -l | grep -q poppler-utils; then
  log "✓ poppler-utils already installed"
else
  log "Installing system dependencies..."
  sudo apt-get update || handle_error "Failed to update package lists"
  sudo apt-get install -y poppler-utils || handle_error "Failed to install system dependencies"
  log "✓ System dependencies installed" 
fi

# Chrome installation
if command -v google-chrome &>/dev/null; then
  log "✓ Chrome already installed"
else
  log "Installing Chrome..."
  
  # Save current directory and move up one level
  CURRENT_DIR=$(pwd)
  cd .. || handle_error "Failed to change to parent directory"
  
  wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb || \
    handle_error "Failed to download Chrome"
  sudo apt-get install -y ./google-chrome-stable_current_amd64.deb || \
    handle_error "Failed to install Chrome"
  rm google-chrome-stable_current_amd64.deb
  
  # Return to app directory
  cd "$CURRENT_DIR" || handle_error "Failed to return to app directory"
  
  # Verify installation
  if google-chrome --version; then
    log "✓ Chrome installed: $(google-chrome --version)"
  else
    handle_error "Chrome installation verification failed"
  fi
fi

# Redis installation
if command -v redis-cli &>/dev/null; then
  log "✓ Redis already installed"
else
  log "Installing Redis..."
  sudo apt-get install -y redis-server || handle_error "Failed to install Redis"
  
  # Enable and start Redis service
  sudo systemctl enable redis-server || handle_error "Failed to enable Redis service"
  sudo systemctl start redis-server || handle_error "Failed to start Redis service"
  
  # Verify Redis is running
  if redis-cli ping >/dev/null 2>&1; then
    log "✓ Redis installed and running"
  else
    handle_error "Redis installation verification failed"
  fi
fi

# Check Redis service status
if systemctl is-active --quiet redis-server; then
  log "✓ Redis service is active"
else
  log "Warning: Redis service is not running. Starting..."
  sudo systemctl start redis-server || handle_error "Failed to start Redis service"
fi

# Python setup
if ! command -v python3 &>/dev/null; then
  log "Installing Python 3..."
  sudo apt-get install -y python3 || handle_error "Failed to install Python 3"
  log "✓ Python 3 installed"
else
  log "✓ Python 3 already installed"
fi

if ! command -v pip3 &>/dev/null && ! python3 -m pip --version &>/dev/null; then
  log "Installing pip..."
  sudo apt-get install -y python3-pip || handle_error "Failed to install pip"
  log "✓ pip installed"
else
  log "✓ pip already installed"
fi

if ! python3 -m venv --help &>/dev/null; then
  log "Installing python3.12-venv..."
  sudo apt-get install -y python3.12-venv || handle_error "Failed to install python3-venv"
  log "✓ python3-venv installed"
else
  log "✓ python3-venv already installed"
fi

# Environment check
if [ ! -f "$APP_DIR/.env" ]; then
  handle_error ".env file missing. Please configure environment variables"
fi

# Ownership configuration
log "Configuring permissions..."
for dir in "$APP_DIR" "$APP_DIR/venv"; do
  owner=$(stat -c "%U:%G" "$dir" 2>/dev/null || echo "none")
  if [ "$owner" != "ubuntu:users" ]; then
    sudo chown -R ubuntu:users "$dir"
    log "✓ Updated ownership for $dir"
  fi
done

# Service scripts setup
if [ ! -x "$START_SCRIPT" ]; then
  log "Making start script executable"
  sudo chmod +x "$START_SCRIPT"
  log "✓ Start script permissions updated"
fi

log "Installing and starting systemd service..."

NEED_RELOAD=false
SERVICE_FILE="$SYSTEMD_DIR/$SERVICE_NAME.service"
SYSTEM_SERVICE="/etc/systemd/system/$SERVICE_NAME.service"

# Check if service file has changed or doesn't exist
if [ ! -f "$SYSTEM_SERVICE" ] || ! cmp -s "$SERVICE_FILE" "$SYSTEM_SERVICE"; then
  log "Updating systemd service file..."
  sudo cp "$SERVICE_FILE" "$SYSTEM_SERVICE"
  NEED_RELOAD=true
  log "✓ Service file updated"
fi

if $NEED_RELOAD; then
  log "Reloading systemd daemon..."
  sudo systemctl daemon-reload
  log "✓ Systemd daemon reloaded"
fi

# Enable and restart if not already enabled
if ! systemctl is-enabled --quiet "$SERVICE_NAME"; then
  sudo systemctl enable "$SERVICE_NAME"
  log "✓ Service enabled"
fi

sudo systemctl restart "$SERVICE_NAME"

# Installation complete
log "===== Installation Complete ====="
log "Service status: systemctl status $SERVICE_NAME"
log "View logs: journalctl -xeu $SERVICE_NAME"
log "Installation location: $APP_DIR"

exit 0
