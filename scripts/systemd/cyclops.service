[Unit]
Description=Cyclops Scraper API Service
Documentation=https://github.com/drumkitai/cyclops
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/cyclops
# Increase max open file descriptors
LimitNOFILE=8192

# Environment setup
Environment="PATH=/home/<USER>/cyclops/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
EnvironmentFile=/home/<USER>/cyclops/.env

# Execution
ExecStart="/home/<USER>/cyclops/scripts/systemd/bin/start.sh"
ExecStop=/bin/kill -SIGTERM $MAINPID

# Restart configuration
Restart=always
RestartSec=10
TimeoutStartSec=900
TimeoutStopSec=30

# Process management
KillMode=mixed
KillSignal=SIGTERM

# Security hardening
ProtectSystem=full
NoNewPrivileges=true
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictNamespaces=true
RestrictRealtime=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target
