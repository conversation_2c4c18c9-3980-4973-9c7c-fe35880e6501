#!/bin/bash
#
# Cyclops Start Script
#
# This script manages the Python environment and starts Cyclops.
# It handles:
# - Virtual environment setup
# - Dependency installation
# - Application startup
#
# Usage: ./start.sh
#

set -e

APP_DIR="/home/<USER>/cyclops"
VENV_DIR="$APP_DIR/venv"
REQUIREMENTS_FILE="$APP_DIR/requirements.txt"

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

handle_error() {
  log "ERROR: $1"
  exit 1
}

log "===== Starting Cyclops ====="

# Validate environment
[ -d "$APP_DIR" ] || handle_error "Application directory not found"
[ -f "$REQUIREMENTS_FILE" ] || handle_error "Requirements file not found"

# Change to app directory
cd "$APP_DIR" || handle_error "Failed to change to app directory"
log "Working directory: $(pwd)"

# Virtual environment setup
if [ ! -d "$VENV_DIR" ]; then
  log "Creating virtual environment..."
  python3 -m venv "$VENV_DIR" || handle_error "Failed to create virtual environment"
  log "✓ Virtual environment created"
fi

# Activate virtual environment
log "Activating virtual environment..."
# shellcheck source=/dev/null
source "$VENV_DIR/bin/activate" || handle_error "Failed to activate virtual environment"
log "✓ Virtual environment activated"

log "Installing dependencies..."
python3 -m pip install -r "$REQUIREMENTS_FILE" || handle_error "Failed to install dependencies"
log "✓ Dependencies installed"

# Start application
log "Starting Cyclops application..."
exec python3 app.py --host 0.0.0.0
