"""Configuration settings."""

import os
from typing import Dict, Final, Optional

from dotenv import load_dotenv

load_dotenv(override=True)

# Base configuration
APP_ENV: Final[str] = os.environ.get("APP_ENV", "development")

# Sentry configuration
SENTRY_DSN: Final[str] = os.environ.get("SENTRY_DSN")

# Redis configuration
REDIS_HOST: Final[str] = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT: Final[int] = int(os.environ.get("REDIS_PORT", "6379"))
REDIS_PASSWORD: Final[Optional[str]] = os.environ.get("REDIS_PASSWORD")
REDIS_PREFIX: Final[str] = os.environ.get("REDIS_PREFIX", "scraper")

# Logging
LOG_LEVEL: Final[str] = os.environ.get("LOG_LEVEL", "INFO").upper()

# Platform base URLs
# TODO: make this more extensible
PLATFORM_URLS: Final[Dict[str, str]] = {
    "c3reservations": "https://www.c3reservations.com/cswg/app/login",
    "costco": "https://appointments.cwtraffic.com",
    "e2open": "https://na-app.tms.e2open.com",
    "manhattan": "https://ahold-tlm.logistics.com/manh/index.html?i=85",
    "onenetwork": "https://logistics.onenetwork.com",
    "retaillink": "https://retaillink.login.wal-mart.com",
    "retalix": "https://www.ncrpowertraffic.com/default.aspx",
    "yardview": os.environ.get("YARDVIEW_URL", "https://target.yardview.com/"),
}

# Timeouts
DEFAULT_TIMEOUT: Final[int] = 10
PAGE_LOAD_TIMEOUT: Final[int] = 30
SCRIPT_TIMEOUT: Final[int] = 30

# S3
BASE_LOCAL_DIR: Final[str] = os.environ.get("BASE_LOCAL_DIR", "s3_temp")
S3_BUCKET_NAME: Final[str] = os.environ.get(
    "S3_BUCKET_NAME", "drumkit-cyclops-archive"
)
