"""Browser session management functions."""

import queue
import threading
from typing import List

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.webdriver import WebDriver
from webdriver_manager.chrome import ChromeDriverManager

from config import PAGE_LOAD_TIMEOUT, SCRIPT_TIMEOUT
from utils.logging import logger

from models.base import BaseResponse


MAX_POOL_SIZE = 5
_driver_pool: queue.Queue = queue.Queue(maxsize=MAX_POOL_SIZE)
_pool_lock = threading.Lock()
_active_drivers: List[WebDriver] = []


def _create_driver(headless: bool = True) -> WebDriver:
    """Create a new WebDriver instance.

    Args:
        headless: Whether to run in headless mode

    Returns:
        WebDriver instance
    """
    options = Options()

    # if headless:
    #     options.add_argument("--headless")

    # Common options for stability
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--start-maximized")

    # Add user agent to avoid detection
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )

    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)

    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    driver.set_script_timeout(SCRIPT_TIMEOUT)

    driver.implicitly_wait(10)

    return driver


def _is_driver_alive(driver: WebDriver) -> bool:
    try:
        driver.current_url
        return True
    except Exception:
        logger.warning("WebDriver instance is no longer alive")
        return False


def close_driver(driver: WebDriver) -> None:
    """Close a WebDriver instance.

    Args:
        driver: WebDriver to close
    """
    try:
        driver.quit()
        logger.info("Closed browser session")
    except Exception as e:
        logger.error(f"Error closing browser session: {str(e)}")


def get_driver(headless: bool = True) -> WebDriver:
    """Get a WebDriver from the pool or create a new one."""
    global _driver_pool, _active_drivers
    with _pool_lock:
        try:
            driver = _driver_pool.get_nowait()
            if _is_driver_alive(driver):
                logger.info("Reusing existing WebDriver from pool")
                return driver
            else:
                close_driver(driver)
        except queue.Empty:
            if len(_active_drivers) < MAX_POOL_SIZE:
                driver = _create_driver(headless)
                _active_drivers.append(driver)
                return driver
            else:
                close_driver(driver)
                return _create_driver(headless)


def release_driver(driver: WebDriver) -> None:
    """Return a driver to the pool if alive, or close it."""
    global _driver_pool
    with _pool_lock:
        if _is_driver_alive(driver):
            try:
                _driver_pool.put_nowait(driver)
                logger.info("WebDriver returned to pool")
            except queue.Full:
                close_driver(driver)
        else:
            close_driver(driver)


def with_driver(func):
    """Decorator that provides a WebDriver from the pool to a function.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """

    def wrapper(*args, headless=True, **kwargs):
        driver = None
        try:
            driver = get_driver(headless=headless)
            result = func(*args, driver=driver, **kwargs)
            release_driver(driver)
            return result
        except Exception as e:
            logger.error(f"Error in browser session: {str(e)}")
            if driver:
                release_driver(driver)
            return kwargs.get("response_cls", BaseResponse)(
                success=False,
                message=f"Browser session error: {str(e)}",
                errors=[str(e)],
            )

    return wrapper


def add_cookies(driver: WebDriver, cookies: list) -> None:
    """Add cookies to the browser session.

    Args:
        driver: WebDriver instance
        cookies: List of cookies to add
    """
    for cookie in cookies:
        if isinstance(cookie, dict):
            if cookie.get(
                "secure", False
            ) and not driver.current_url.startswith("https://"):
                continue

            cookie_dict = {
                k: v
                for k, v in cookie.items()
                if k
                in [
                    "name",
                    "value",
                    "domain",
                    "path",
                    "expiry",
                    "secure",
                    "httpOnly",
                ]
            }

            try:
                driver.add_cookie(cookie_dict)
            except Exception as e:
                logger.warning(f"Failed to add cookie: {str(e)}")


def get_cookies(driver: WebDriver) -> list:
    """Get cookies from the browser session.

    Args:
        driver: WebDriver instance

    Returns:
        List of cookies
    """
    return driver.get_cookies()


def shutdown():
    """Close all drivers in the pool on app shutdown."""
    global _driver_pool, _active_drivers
    with _pool_lock:
        while not _driver_pool.empty():
            driver = _driver_pool.get_nowait()
            close_driver(driver)
        for driver in _active_drivers:
            close_driver(driver)
        _active_drivers.clear()
        logger.info("All WebDriver instances closed during shutdown")
