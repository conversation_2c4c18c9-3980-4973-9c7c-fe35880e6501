"""App entrypoint."""

import argparse
from contextlib import asynccontextmanager
import sys

from fastapi import FastAPI
import uvicorn

from router import router
from session import shutdown
from utils.logging import logger
from utils.sentry import init_sentry


@asynccontextmanager
async def lifespan(app: FastAPI):
    init_sentry()
    yield
    shutdown
    logger.info("App shutdown complete")


app = FastAPI(title="Scraper API", version="1.0.0", lifespan=lifespan)
app.include_router(router)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run the scheduling scraper server"
    )
    parser.add_argument(
        "--host", help="Server host (default: 127.0.0.1)", default="127.0.0.1"
    )
    parser.add_argument(
        "--port", help="Server port (default: 8000)", type=int, default=8000
    )
    parser.add_argument(
        "--reload",
        help="Enable auto-reload for development",
        action="store_true",
    )
    return parser.parse_args()


def main() -> int:
    """Main entry point for server."""
    args = parse_args()

    try:
        uvicorn.run(
            "app:app", host=args.host, port=args.port, reload=args.reload
        )
        return 0
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
