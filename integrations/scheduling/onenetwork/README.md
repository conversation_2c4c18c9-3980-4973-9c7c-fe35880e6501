#### API Examples

All requests are sent as JSON to the endpoint http://localhost:8000/ using POST method. Here are examples for each supported operation:

[Architecture Diagram](./docs/onenetwork_arc_diag.png)
![architecture.png](docs/onenetwork_arc_diag.png)

Note: Only one of shipNo or refNo should be provided in the request.

##### Data Retrieval

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

```json
{
  "integration": "scheduling",
  "platform": "OneNetwork",
  "action": "GetOpenSlots",
  "userId": "user123",
  "startDate": "2025-06-10T12:00:00.0000Z",
  "endDate": "2025-06-11T12:00:00.0000Z",
  "shipNo": ["123456"],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Success Response:
```json
{
    "success": true,
    "message": "Successfully retrieved open slots",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "notes": "Open slots available for the respective warehouse.",
            "status": "AVAILABLE",
            "warehouse": {
                "city": "Florence",
                "country": "US",
                "name": "Kroger - Mt. Zion (Florence, KY) - 091A",
                "openSlots": [
                    {
                        "duration": 60,
                        "scheduledTime": "2025-06-11T13:30:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-06-11T12:30:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-06-12T13:30:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-06-12T12:30:00"
                    }
                ],
                "state": "KY",
                "stopType": "dropoff",
                "website": "",
                "zipCode": "41042",
                "timezone": "America/New_York"
            }
        }
    ]
}
```

Failure Cases:
- Mandatory parameters missing: startDate, endDate etc. [Invalid request: startDate: Field required]
- Start date cannot be in the past
- End date must be on or after start date
- Start Date and End Date should be in proper format '%Y-%m-%dT%H:%M:%S.%fZ'. [time data '04-23-2025T17:00:00' does not match format '%Y-%m-%dT%H:%M:%S.%fZ']

###### Get Appointment

Get all existing confirmed appointment details related to shared proId.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "OneNetwork",
  "action": "GetAppointment",
  "userId": "user123",
  "shipNo": "123456",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: shipNo etc. [Invalid request: shipNo: Field required]
- Failed to switch to appointment scheduling popup

Success Response:
```json
{
    "success": true,
    "message": "Successfully retrieved appointment details for 33174",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "appointmentId": "857934777",
            "duration": 60,
            "location": "",
            "notes": "Carrier: National Freight, Equipment: Dry Van",
            "reference": "Kroger - Mt. Zion (Florence, KY) - 091A\nFlorence, KY 41042",
            "scheduledTime": "Jun 4, 2025 12:00 PM",
            "status": "Confirmed",
            "warehouse": {
                "city": "Florence",
                "country": "US",
                "name": "Kroger - Mt. Zion (Florence, KY) - 091A",
                "openSlots": [],
                "state": "KY",
                "stopType": "dropoff",
                "website": "",
                "zipCode": "41042"
            },
            "extended": null
        }
    ]
}
```

##### Appointment Management

###### Create Appointment

Creates a new appointment.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "OneNetwork",
  "action": "MakeAppointment",
  "userId": "user123",
  "appointments": [
    {
        "refNo": "",
        "shipNo": ["123456"],
        "scheduledTime": "2025-06-11T12:30:00",
        "duration": 60,
        "notes": "Special handling required",
        "status": "Scheduled",
        "contact": {
            "phone": "1234567890",
            "email": "<EMAIL>"
        },
        "warehouse": {
            "name": "",
            "city": "",
            "state": "",
            "zipCode": "",
            "country": "",
            "stopType": ""
        }
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: scheduledTime, duration etc. [Invalid request: scheduledTime: Field required]
- Failed to switch to appointment scheduling popup

###### Update Appointment

Modifies an existing appointment.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "OneNetwork",
  "action": "MakeAppointment",
  "userId": "user123",
  "appointments": [
    {
        "refNo": "",
        "shipNo": ["123456"],
        "scheduledTime": "2025-06-11T12:30:00",
        "duration": 60,
        "notes": "Special handling required",
        "status": "Scheduled",
        "contact": {
            "phone": "1234567890",
            "email": "<EMAIL>"
        },
        "warehouse": {
            "name": "",
            "city": "",
            "state": "",
            "zipCode": "",
            "country": "",
            "stopType": ""
        }
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: appointmentId, appointment etc. [Invalid request: appointmentId: Field required]
- Failed to switch to appointment scheduling popup

###### Cancel Appointment

Cancels an existing appointment.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "OneNetwork",
  "action": "CancelAppointment",
  "userId": "user123",
  "shipNo": "123456",
  "reason": "Appointment cancelled per customer request, through automated flow.",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: shipNo etc. [Invalid request: shipNo: Field required]
- Failed to switch to appointment scheduling popup

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```
