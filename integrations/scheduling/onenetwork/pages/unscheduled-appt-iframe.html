<!DOCTYPE html>

<html class="neo-light neo-theme-none skip-neo-theme"><head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <style type="text/css">svg:not(:root).svg-inline--fa {
  overflow: visible;
}

.svg-inline--fa {
  display: inline-block;
  font-size: inherit;
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.225em;
}
.svg-inline--fa.fa-w-1 {
  width: 0.0625em;
}
.svg-inline--fa.fa-w-2 {
  width: 0.125em;
}
.svg-inline--fa.fa-w-3 {
  width: 0.1875em;
}
.svg-inline--fa.fa-w-4 {
  width: 0.25em;
}
.svg-inline--fa.fa-w-5 {
  width: 0.3125em;
}
.svg-inline--fa.fa-w-6 {
  width: 0.375em;
}
.svg-inline--fa.fa-w-7 {
  width: 0.4375em;
}
.svg-inline--fa.fa-w-8 {
  width: 0.5em;
}
.svg-inline--fa.fa-w-9 {
  width: 0.5625em;
}
.svg-inline--fa.fa-w-10 {
  width: 0.625em;
}
.svg-inline--fa.fa-w-11 {
  width: 0.6875em;
}
.svg-inline--fa.fa-w-12 {
  width: 0.75em;
}
.svg-inline--fa.fa-w-13 {
  width: 0.8125em;
}
.svg-inline--fa.fa-w-14 {
  width: 0.875em;
}
.svg-inline--fa.fa-w-15 {
  width: 0.9375em;
}
.svg-inline--fa.fa-w-16 {
  width: 1em;
}
.svg-inline--fa.fa-w-17 {
  width: 1.0625em;
}
.svg-inline--fa.fa-w-18 {
  width: 1.125em;
}
.svg-inline--fa.fa-w-19 {
  width: 1.1875em;
}
.svg-inline--fa.fa-w-20 {
  width: 1.25em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: 0.3em;
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: 0.3em;
  width: auto;
}
.svg-inline--fa.fa-border {
  height: 1.5em;
}
.svg-inline--fa.fa-li {
  width: 2em;
}
.svg-inline--fa.fa-fw {
  width: 1.25em;
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-counter {
  background-color: #ff253a;
  border-radius: 1em;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #fff;
  height: 1.5em;
  line-height: 1;
  max-width: 5em;
  min-width: 1.5em;
  overflow: hidden;
  padding: 0.25em;
  right: 0;
  text-overflow: ellipsis;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: 0;
  right: 0;
  top: auto;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: bottom right;
          transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: 0;
  left: 0;
  right: auto;
  top: auto;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}

.fa-layers-top-right {
  right: 0;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-top-left {
  left: 0;
  right: auto;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top left;
          transform-origin: top left;
}

.fa-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
  vertical-align: -0.0667em;
}

.fa-xs {
  font-size: 0.75em;
}

.fa-sm {
  font-size: 0.875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

.fa-border {
  border: solid 0.08em #eee;
  border-radius: 0.1em;
  padding: 0.2em 0.25em 0.15em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: 0.3em;
}
.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: 0.3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1);
}

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-flip-both {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  display: inline-block;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: #fff;
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: 1;
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: 0.4;
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: 0.4;
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: 1;
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}

.fad.fa-inverse {
  color: #fff;
}</style><link rel="stylesheet" type="text/css" href="/oms/css/cc3/OpenSans.css?cachekey=07b835d78bbc82b1aec8fc240f008050">

  <!-- This is used to allow React Dev Tools to work on the page. it HAS to be in a script tag at the beginning -->
  <script> __REACT_DEVTOOLS_GLOBAL_HOOK__ = window.parent.__REACT_DEVTOOLS_GLOBAL_HOOK__ </script>

  <!-- Timestamp for recording load times. -->
  <script>
    window.PLT_PAGE_LOAD_START = new Date();
    window.PLT_UI_LOAD_TIMES = {
      perspective: null,
      dependencies: {},
    };
  </script>

  <!-- Provides necessary configuration to make this page stand alone outside of CC in certain scenarios -->







  <!-- Provides a document ready base function docReady() -->
  <script>
    (function(funcName, baseObj) {
        // The public function name defaults to window.docReady
        // but you can pass in your own object and own function name and those will be used
        // if you want to put them in a different namespace
        funcName = funcName || "docReady";
        baseObj = baseObj || window;
        var readyList = [];
        var readyFired = false;
        var readyEventHandlersInstalled = false;

        // call this when the document is ready
        // this function protects itself against being called more than once
        function ready() {
            if (!readyFired) {
                // this must be set to true before we start calling callbacks
                readyFired = true;
                for (var i = 0; i < readyList.length; i++) {
                    // if a callback here happens to add new ready handlers,
                    // the docReady() function will see that it already fired
                    // and will schedule the callback to run right after
                    // this event loop finishes so all handlers will still execute
                    // in order and no new ones will be added to the readyList
                    // while we are processing the list
                    readyList[i].fn.call(window, readyList[i].ctx);
                }
                // allow any closures held by these functions to free
                readyList = [];
            }
        }

        function readyStateChange() {
            if ( document.readyState === "complete" ) {
                ready();
            }
        }

        // This is the one public interface
        // docReady(fn, context);
        // the context argument is optional - if present, it will be passed
        // as an argument to the callback
        baseObj[funcName] = function(callback, context) {
            // if ready has already fired, then just schedule the callback
            // to fire asynchronously, but right away
            if (readyFired) {
                setTimeout(function() {callback(context);}, 1);
                return;
            } else {
                // add the function and context to the list
                readyList.push({fn: callback, ctx: context});
            }
            // if document already ready to go, schedule the ready function to run
            if (document.readyState === "complete") {
                setTimeout(ready, 1);
            } else if (!readyEventHandlersInstalled) {
                // otherwise if we don't have event handlers installed, install them
                if (document.addEventListener) {
                    // first choice is DOMContentLoaded event
                    document.addEventListener("DOMContentLoaded", ready, false);
                    // backup is window load event
                    window.addEventListener("load", ready, false);
                } else {
                    // must be IE
                    document.attachEvent("onreadystatechange", readyStateChange);
                    window.attachEvent("onload", ready);
                }
                readyEventHandlersInstalled = true;
            }
        }
    })("docReady", window);
</script>
  <!-- This holds the standard ONE way of handling session time outs -->





  <!-- This makes all query params available in JS via urlQueryParams[<param name>] -->


<script>
var urlQueryParams;
(window.onpopstate = function () {
  var match,
    pl     = /\+/g,  // Regex for replacing addition symbol with a space
    search = /([^&=]+)=?([^&]*)/g,
    decode = function (s) { return decodeURIComponent(s.replace(pl, " ")); },
    query  = window.location.search.substring(1);

  // use Object.create(null) to prevent prototype injection .. see https://www.whitesourcesoftware.com/resources/blog/prototype-pollution-vulnerabilities/
  urlQueryParams = Object.create(null);
  while (match = search.exec(query))
    urlQueryParams[decode(match[1])] = decode(match[2]);

  if (window.frameElement && window.frameElement.getPanel) {
    var desktop = window.top.desktop;
    // This handling is needed when you open webaction/panel in a
    // separate browser window (PLT-26540)
    if (!desktop && window.top.opener && window.top.opener.top) {
      desktop = window.top.opener.top.desktop;
    }
    var params = desktop.getIFrameParams(window.frameElement.getPanel());
    for (key in params) {
      urlQueryParams[key] = params[key]
    }
  }

  console.log("QUERY PARAMS:", urlQueryParams);
  window.onpopstate = null;
})();
</script>

  <script src="/oms/scripts/SimulatedToday.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script>




  <link rel="stylesheet" type="text/css" href="/oms/font-awesome/css/font-awesome.css?cachekey=07b835d78bbc82b1aec8fc240f008050">
  <link rel="stylesheet" type="text/css" href="/oms/css/react/OneReactFramework.css?cachekey=07b835d78bbc82b1aec8fc240f008050">

  <!-- global react registry used by modules -->
  <script src="/oms/scripts/GlobalReactRegistry.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script>

  <!-- Workaround for: https://github.com/facebook/react/issues/6822 -->
  <style type="text/css">
    body {
      margin: 0;
      height: 100%;
    }

    input[type=text]::-ms-clear {
      display: none;
    }
  </style>










  <link rel="stylesheet" type="text/css" href="/oms/css/neo/legacy.css?cachekey=07b835d78bbc82b1aec8fc240f008050">


  <script type="text/javascript">
    //GlobalCacheKey for DynamicComponent to avoid cache problems
    window.GlobalCacheKey = 'cachekey=07b835d78bbc82b1aec8fc240f008050';

    window.One = window.One || {};    // Necessary for embedding the theme in this page.

    docReady(function () {
      // We also must listen for theme changes
      var windowTopDispatcher = window.top['One.desktop.GlobalDispatcher'];
      var windowTopEvents = window.top['One.desktop.GlobalEvents'];
      var windowOpenerTopDispatcher = window.opener && window.opener.window.top['One.desktop.GlobalDispatcher'];
      var windowOpenerTopEvents = window.opener && window.opener.window.top['One.desktop.GlobalEvents'];
      var globalDispatcher = windowTopDispatcher || windowOpenerTopDispatcher;
      var globalEvents = windowTopEvents || windowOpenerTopEvents;

      // Explicitly request the theme in case the theme controller failed to load the theme properly
      if (window.document.querySelectorAll(".theme-style-element").length <= 0) {
        if (globalDispatcher && globalEvents) {
          try {
            globalDispatcher.fire(globalEvents.REQUEST_THEME, {
              contentWindow: window
              , themeTemplateName: "ReactBootstrap"
            });
          }
          catch (err) {
            console.log('There was an error trying to retrieve the theme:');
            console.log(err.stack || err.message);
          }
        }
      }

      // We also must listen for theme changes
      globalDispatcher.on(window, globalEvents.SWITCH_THEME, function (e) {
        // Simply re-request the current theme to ensure our theme is up to date
        globalDispatcher.fire(globalEvents.REQUEST_THEME, {
          contentWindow: window
          , themeTemplateName: "ReactBootstrap"
        });
      });
    });
  </script>
<style type="text/css">
:root {
  --scrollbar-width: 0px;
  --scrollbar-height: 0px;
}
</style><style class="theme-style-element modulethemes-DefaultTheme">

.panel-primary > .panel-heading {
  color: ;
  background-color: ;
  border-color: ;
}

.nav-pills > li.active > a,
.nav-pills > li.active > a:focus,
.nav-pills > li.active > a:hover {
  color: ;
  background-color: ;
}


/*CUSTOM CSS*/
/*Custom CSS properties for overriding special needs scenarios*/
#cc3head-bgcontainer {

}

.cc3head-rightTray-header .nav-icon {

}

.cc3head-icon-tray-a {

}

.cc3head-icon-tray-a .fa-refresh:hover {

}

.cc3head-icon-tray-li > a:hover,
.cc3head-icon-tray-li > a:focus {

}

.x-panel-header {

}

.x-grid3-header {

}

.x-tab-strip-top .x-tab-strip-active .x-tab-right,
.x-tab-strip-top .x-tab-strip-active .x-tab-left,
.x-tab-strip-top .x-tab-strip-active .x-tab-strip-inner {

}

.x-tab-strip-top .x-tab-right,
.x-tab-strip-top .x-tab-left,
.x-tab-strip-top .x-tab-strip-inner {

}

.x-toolbar .x-btn-tl,
.x-toolbar .x-btn-tc,
.x-toolbar .x-btn-tr,
.x-toolbar .x-btn-ml,
.x-toolbar .x-btn-mc,
.x-toolbar .x-btn-mr,
.x-toolbar .x-btn-bl,
.x-toolbar .x-btn-bc,
.x-toolbar .x-btn-br {

}

.x-grid3-hd-row td {

}

.x-grid3-hd-inner {

}

ul.x-tab-strip li {

}

</style><link href="/oms/apps/Scheduling/react-modules/SiteCalendar/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/apps/Scheduling/react-modules/SiteCalendar/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/public/react-modules/ReactWidgetComponents/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/public/react-modules/ReactWidgetComponents/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/public/react-modules/Flexmonster/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/public/react-modules/Flexmonster/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><style type="text/css">.vis-time-axis {
  position: relative;
  overflow: hidden;
}

.vis-time-axis.vis-foreground {
  top: 0;
  left: 0;
  width: 100%;
}

.vis-time-axis.vis-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.vis-time-axis .vis-text {
  position: absolute;
  color: #4d4d4d;
  padding: 3px;
  overflow: hidden;
  box-sizing: border-box;

  white-space: nowrap;
}

.vis-time-axis .vis-text.vis-measure {
  position: absolute;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  visibility: hidden;
}

.vis-time-axis .vis-grid.vis-vertical {
  position: absolute;
  border-left: 1px solid;
}

.vis-time-axis .vis-grid.vis-vertical-rtl {
  position: absolute;
  border-right: 1px solid;
}

.vis-time-axis .vis-grid.vis-minor {
  border-color: #e5e5e5;
}

.vis-time-axis .vis-grid.vis-major {
  border-color: #bfbfbf;
}
</style><style type="text/css">.vis .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  /* Must be displayed above for example selected Timeline items */
  z-index: 10;
}

.vis-active {
  box-shadow: 0 0 10px #86d5f8;
}
</style><style type="text/css">.vis-custom-time {
  background-color: #6E94FF;
  width: 2px;
  cursor: move;
  z-index: 1;
}

.vis-custom-time > .vis-custom-time-marker {
  background-color: inherit;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  padding: 3px 5px;
  top: 0px;
  cursor: initial;
  z-index: inherit;
}</style><style type="text/css">.vis-timeline {
  /*
  -webkit-transition: height .4s ease-in-out;
  transition:         height .4s ease-in-out;
  */
}

.vis-panel {
  /*
  -webkit-transition: height .4s ease-in-out, top .4s ease-in-out;
  transition:         height .4s ease-in-out, top .4s ease-in-out;
  */
}

.vis-axis {
  /*
  -webkit-transition: top .4s ease-in-out;
  transition:         top .4s ease-in-out;
  */
}

/* TODO: get animation working nicely

.vis-item {
  -webkit-transition: top .4s ease-in-out;
  transition:         top .4s ease-in-out;
}

.vis-item.line {
  -webkit-transition: height .4s ease-in-out, top .4s ease-in-out;
  transition:         height .4s ease-in-out, top .4s ease-in-out;
}
/**/</style><style type="text/css">.vis-current-time {
  background-color: #FF7F6E;
  width: 2px;
  z-index: 1;
  pointer-events: none;
}

.vis-rolling-mode-btn {
  height: 40px;
  width: 40px;
  position: absolute;
  top: 7px;
  right: 20px;
  border-radius: 50%;
  font-size: 28px;
  cursor: pointer;
  opacity: 0.8;
  color: white;
  font-weight: bold;
  text-align: center;
  background: #3876c2;
}
.vis-rolling-mode-btn:before {
  content: "\26F6";
}

.vis-rolling-mode-btn:hover {
  opacity: 1;
}</style><style type="text/css">.vis-panel {
  position: absolute;

  padding: 0;
  margin: 0;

  box-sizing: border-box;
}

.vis-panel.vis-center,
.vis-panel.vis-left,
.vis-panel.vis-right,
.vis-panel.vis-top,
.vis-panel.vis-bottom {
  border: 1px #bfbfbf;
}

.vis-panel.vis-center,
.vis-panel.vis-left,
.vis-panel.vis-right {
  border-top-style: solid;
  border-bottom-style: solid;
  overflow: hidden;
}

.vis-left.vis-panel.vis-vertical-scroll, .vis-right.vis-panel.vis-vertical-scroll {
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
}

.vis-left.vis-panel.vis-vertical-scroll {
  direction: rtl;
}

.vis-left.vis-panel.vis-vertical-scroll .vis-content {
  direction: ltr;
}

.vis-right.vis-panel.vis-vertical-scroll {
  direction: ltr;
}

.vis-right.vis-panel.vis-vertical-scroll .vis-content {
  direction: rtl;
}

.vis-panel.vis-center,
.vis-panel.vis-top,
.vis-panel.vis-bottom {
  border-left-style: solid;
  border-right-style: solid;
}

.vis-background {
  overflow: hidden;
}

.vis-panel > .vis-content {
  position: relative;
}

.vis-panel .vis-shadow {
  position: absolute;
  width: 100%;
  height: 1px;
  box-shadow: 0 0 10px rgba(0,0,0,0.8);
  /* TODO: find a nice way to ensure vis-shadows are drawn on top of items
  z-index: 1;
  */
}

.vis-panel .vis-shadow.vis-top {
  top: -1px;
  left: 0;
}

.vis-panel .vis-shadow.vis-bottom {
  bottom: -1px;
  left: 0;
}</style><style type="text/css">.vis-graph-group0 {
    fill:#4f81bd;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #4f81bd;
}

.vis-graph-group1 {
    fill:#f79646;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #f79646;
}

.vis-graph-group2 {
    fill: #8c51cf;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #8c51cf;
}

.vis-graph-group3 {
    fill: #75c841;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #75c841;
}

.vis-graph-group4 {
    fill: #ff0100;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #ff0100;
}

.vis-graph-group5 {
    fill: #37d8e6;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #37d8e6;
}

.vis-graph-group6 {
    fill: #042662;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #042662;
}

.vis-graph-group7 {
    fill:#00ff26;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #00ff26;
}

.vis-graph-group8 {
    fill:#ff00ff;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #ff00ff;
}

.vis-graph-group9 {
    fill: #8f3938;
    fill-opacity:0;
    stroke-width:2px;
    stroke: #8f3938;
}

.vis-timeline .vis-fill {
    fill-opacity:0.1;
    stroke: none;
}


.vis-timeline .vis-bar {
    fill-opacity:0.5;
    stroke-width:1px;
}

.vis-timeline .vis-point {
    stroke-width:2px;
    fill-opacity:1.0;
}


.vis-timeline .vis-legend-background {
    stroke-width:1px;
    fill-opacity:0.9;
    fill: #ffffff;
    stroke: #c2c2c2;
}


.vis-timeline .vis-outline {
    stroke-width:1px;
    fill-opacity:1;
    fill: #ffffff;
    stroke: #e5e5e5;
}

.vis-timeline .vis-icon-fill {
    fill-opacity:0.3;
    stroke: none;
}
</style><style type="text/css">
.vis-timeline {
  position: relative;
  border: 1px solid #bfbfbf;
  overflow: hidden;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.vis-loading-screen {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}</style><style type="text/css">/* override some bootstrap styles screwing up the timelines css */

.vis [class*="span"] {
  min-height: 0;
  width: auto;
}
</style><style type="text/css">
.vis-item {
  position: absolute;
  color: #1A1A1A;
  border-color: #97B0F8;
  border-width: 1px;
  background-color: #D5DDF6;
  display: inline-block;
  z-index: 1;
  /*overflow: hidden;*/
}

.vis-item.vis-selected {
  border-color: #FFC200;
  background-color: #FFF785;

  /* z-index must be higher than the z-index of custom time bar and current time bar */
  z-index: 2;
}

.vis-editable.vis-selected {
  cursor: move;
}

.vis-item.vis-point.vis-selected {
  background-color: #FFF785;
}

.vis-item.vis-box {
  text-align: center;
  border-style: solid;
  border-radius: 2px;
}

.vis-item.vis-point {
  background: none;
}

.vis-item.vis-dot {
  position: absolute;
  padding: 0;
  border-width: 4px;
  border-style: solid;
  border-radius: 4px;
}

.vis-item.vis-range {
  border-style: solid;
  border-radius: 2px;
  box-sizing: border-box;
}

.vis-item.vis-background {
  border: none;
  background-color: rgba(213, 221, 246, 0.4);
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

.vis-item .vis-item-overflow {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.vis-item-visible-frame {
  white-space: nowrap;
}

.vis-item.vis-range .vis-item-content {
  position: relative;
  display: inline-block;
}

.vis-item.vis-background .vis-item-content {
  position: absolute;
  display: inline-block;
}

.vis-item.vis-line {
  padding: 0;
  position: absolute;
  width: 0;
  border-left-width: 1px;
  border-left-style: solid;
}

.vis-item .vis-item-content {
  white-space: nowrap;
  box-sizing: border-box;
  padding: 5px;
}

.vis-item .vis-onUpdateTime-tooltip {
  position: absolute;
  background: #4f81bd;
  color: white;
  width: 200px;
  text-align: center;
  white-space: nowrap;
  padding: 5px;
  border-radius: 1px;
  transition: 0.4s;
  -o-transition: 0.4s;
  -moz-transition: 0.4s;
  -webkit-transition: 0.4s;
}

.vis-item .vis-delete, .vis-item .vis-delete-rtl {
  position: absolute;
  top: 0px;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
  padding: 0px 5px;
  cursor: pointer;

  -webkit-transition: background 0.2s linear;
  -moz-transition: background 0.2s linear;
  -ms-transition: background 0.2s linear;
  -o-transition: background 0.2s linear;
  transition: background 0.2s linear;
}

.vis-item .vis-delete {
  right: -24px;
}

.vis-item .vis-delete-rtl {
  left: -24px;
}

.vis-item .vis-delete:after, .vis-item .vis-delete-rtl:after {
  content: "\00D7"; /* MULTIPLICATION SIGN */
  color: red;
  font-family: arial, sans-serif;
  font-size: 22px;
  font-weight: bold;

  -webkit-transition: color 0.2s linear;
  -moz-transition: color 0.2s linear;
  -ms-transition: color 0.2s linear;
  -o-transition: color 0.2s linear;
  transition: color 0.2s linear;
}

.vis-item .vis-delete:hover, .vis-item .vis-delete-rtl:hover {
  background: red;
}

.vis-item .vis-delete:hover:after, .vis-item .vis-delete-rtl:hover:after {
  color: white;
}

.vis-item .vis-drag-center {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0px;
  cursor: move;
}

.vis-item.vis-range .vis-drag-left {
  position: absolute;
  width: 24px;
  max-width: 20%;
  min-width: 2px;
  height: 100%;
  top: 0;
  left: -4px;

  cursor: w-resize;
}

.vis-item.vis-range .vis-drag-right {
  position: absolute;
  width: 24px;
  max-width: 20%;
  min-width: 2px;
  height: 100%;
  top: 0;
  right: -4px;

  cursor: e-resize;
}

.vis-range.vis-item.vis-readonly .vis-drag-left,
.vis-range.vis-item.vis-readonly .vis-drag-right {
  cursor: auto;
}

.vis-item.vis-cluster {
  vertical-align: center;
  text-align: center;
  border-style: solid;
  border-radius: 2px;
}

.vis-item.vis-cluster-line {
  padding: 0;
  position: absolute;
  width: 0;
  border-left-width: 1px;
  border-left-style: solid;
}

.vis-item.vis-cluster-dot {
  position: absolute;
  padding: 0;
  border-width: 4px;
  border-style: solid;
  border-radius: 4px;
}</style><style type="text/css">div.vis-tooltip {
  position: absolute;
  visibility: hidden;
  padding: 5px;
  white-space: nowrap;

  font-family: verdana;
  font-size:14px;
  color:#000000;
  background-color: #f5f4ed;

  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  border: 1px solid #808074;

  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.2);
  pointer-events: none;

  z-index: 5;
}
</style><style type="text/css">
.vis-itemset {
  position: relative;
  padding: 0;
  margin: 0;

  box-sizing: border-box;
}

.vis-itemset .vis-background,
.vis-itemset .vis-foreground {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.vis-axis {
  position: absolute;
  width: 100%;
  height: 0;
  left: 0;
  z-index: 1;
}

.vis-foreground .vis-group {
  position: relative;
  box-sizing: border-box;
  border-bottom: 1px solid #bfbfbf;
}

.vis-foreground .vis-group:last-child {
  border-bottom: none;
}

.vis-nesting-group {
  cursor: pointer;
}

.vis-label.vis-nested-group.vis-group-level-unknown-but-gte1 {
  background: #f5f5f5;
}
.vis-label.vis-nested-group.vis-group-level-0 {
  background-color: #ffffff;
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-0 .vis-inner {
  padding-left: 0;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-0 .vis-inner {
  padding-right: 0;
}
.vis-label.vis-nested-group.vis-group-level-1 {
  background-color: rgba(0, 0, 0, 0.05);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-1 .vis-inner {
  padding-left: 15px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-1 .vis-inner {
  padding-right: 15px;
}
.vis-label.vis-nested-group.vis-group-level-2 {
  background-color: rgba(0, 0, 0, 0.1);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-2 .vis-inner {
  padding-left: 30px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-2 .vis-inner {
  padding-right: 30px;
}
.vis-label.vis-nested-group.vis-group-level-3 {
  background-color: rgba(0, 0, 0, 0.15);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-3 .vis-inner {
  padding-left: 45px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-3 .vis-inner {
  padding-right: 45px;
}
.vis-label.vis-nested-group.vis-group-level-4 {
  background-color: rgba(0, 0, 0, 0.2);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-4 .vis-inner {
  padding-left: 60px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-4 .vis-inner {
  padding-right: 60px;
}
.vis-label.vis-nested-group.vis-group-level-5 {
  background-color: rgba(0, 0, 0, 0.25);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-5 .vis-inner {
  padding-left: 75px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-5 .vis-inner {
  padding-right: 75px;
}
.vis-label.vis-nested-group.vis-group-level-6 {
  background-color: rgba(0, 0, 0, 0.3);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-6 .vis-inner {
  padding-left: 90px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-6 .vis-inner {
  padding-right: 90px;
}
.vis-label.vis-nested-group.vis-group-level-7 {
  background-color: rgba(0, 0, 0, 0.35);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-7 .vis-inner {
  padding-left: 105px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-7 .vis-inner {
  padding-right: 105px;
}
.vis-label.vis-nested-group.vis-group-level-8 {
  background-color: rgba(0, 0, 0, 0.4);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-8 .vis-inner {
  padding-left: 120px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-8 .vis-inner {
  padding-right: 120px;
}
.vis-label.vis-nested-group.vis-group-level-9 {
  background-color: rgba(0, 0, 0, 0.45);
}
.vis-ltr .vis-label.vis-nested-group.vis-group-level-9 .vis-inner {
  padding-left: 135px;
}
.vis-rtl .vis-label.vis-nested-group.vis-group-level-9 .vis-inner {
  padding-right: 135px;
}
/* default takes over beginning with level-10 (thats why we add .vis-nested-group
  to the selectors above, to have higher specifity than these rules for the defaults) */
.vis-label.vis-nested-group {
  background-color: rgba(0, 0, 0, 0.5);
}
.vis-ltr .vis-label.vis-nested-group .vis-inner {
  padding-left: 150px;
}
.vis-rtl .vis-label.vis-nested-group .vis-inner {
  padding-right: 150px;
}

.vis-group-level-unknown-but-gte1 {
  border: 1px solid red;
}

/* expanded/collapsed indicators */
.vis-label.vis-nesting-group:before,
.vis-label.vis-nesting-group:before {
  display: inline-block;
  width: 15px;
}
.vis-label.vis-nesting-group.expanded:before {
  content: "\25BC";
}
.vis-label.vis-nesting-group.collapsed:before {
  content: "\25B6";
}
.vis-rtl .vis-label.vis-nesting-group.collapsed:before {
  content: "\25C0";
}
/* compensate missing expanded/collapsed indicator, but only at levels > 0 */
.vis-ltr .vis-label:not(.vis-nesting-group):not(.vis-group-level-0) {
  padding-left: 15px;
}
.vis-rtl .vis-label:not(.vis-nesting-group):not(.vis-group-level-0) {
  padding-right: 15px;
}

.vis-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}</style><style type="text/css">
.vis-labelset {
  position: relative;

  overflow: hidden;

  box-sizing: border-box;
}

.vis-labelset .vis-label {
  position: relative;
  left: 0;
  top: 0;
  width: 100%;
  color: #4d4d4d;

  box-sizing: border-box;
}

.vis-labelset .vis-label {
  border-bottom: 1px solid #bfbfbf;
}

.vis-labelset .vis-label.draggable {
  cursor: pointer;
}

.vis-group-is-dragging {
  background: rgba(0, 0, 0, .1);
}

.vis-labelset .vis-label:last-child {
  border-bottom: none;
}

.vis-labelset .vis-label .vis-inner {
  display: inline-block;
  padding: 5px;
}

.vis-labelset .vis-label .vis-inner.vis-hidden {
  padding: 0;
}
</style><style type="text/css">div.vis-configuration {
    position:relative;
    display:block;
    float:left;
    font-size:12px;
}

div.vis-configuration-wrapper {
    display:block;
    width:700px;
}

div.vis-configuration-wrapper::after {
  clear: both;
  content: "";
  display: block;
}

div.vis-configuration.vis-config-option-container{
    display:block;
    width:495px;
    background-color: #ffffff;
    border:2px solid #f7f8fa;
    border-radius:4px;
    margin-top:20px;
    left:10px;
    padding-left:5px;
}

div.vis-configuration.vis-config-button{
    display:block;
    width:495px;
    height:25px;
    vertical-align: middle;
    line-height:25px;
    background-color: #f7f8fa;
    border:2px solid #ceced0;
    border-radius:4px;
    margin-top:20px;
    left:10px;
    padding-left:5px;
    cursor: pointer;
    margin-bottom:30px;
}

div.vis-configuration.vis-config-button.hover{
    background-color: #4588e6;
    border:2px solid #214373;
    color:#ffffff;
}

div.vis-configuration.vis-config-item{
    display:block;
    float:left;
    width:495px;
    height:25px;
    vertical-align: middle;
    line-height:25px;
}


div.vis-configuration.vis-config-item.vis-config-s2{
    left:10px;
    background-color: #f7f8fa;
    padding-left:5px;
    border-radius:3px;
}
div.vis-configuration.vis-config-item.vis-config-s3{
    left:20px;
    background-color: #e4e9f0;
    padding-left:5px;
    border-radius:3px;
}
div.vis-configuration.vis-config-item.vis-config-s4{
    left:30px;
    background-color: #cfd8e6;
    padding-left:5px;
    border-radius:3px;
}

div.vis-configuration.vis-config-header{
    font-size:18px;
    font-weight: bold;
}

div.vis-configuration.vis-config-label{
    width:120px;
    height:25px;
    line-height: 25px;
}

div.vis-configuration.vis-config-label.vis-config-s3{
    width:110px;
}
div.vis-configuration.vis-config-label.vis-config-s4{
    width:100px;
}

div.vis-configuration.vis-config-colorBlock{
    top:1px;
    width:30px;
    height:19px;
    border:1px solid #444444;
    border-radius:2px;
    padding:0px;
    margin:0px;
    cursor:pointer;
}

input.vis-configuration.vis-config-checkbox {
    left:-5px;
}


input.vis-configuration.vis-config-rangeinput{
    position:relative;
    top:-5px;
    width:60px;
    /*height:13px;*/
    padding:1px;
    margin:0;
    pointer-events:none;
}

input.vis-configuration.vis-config-range{
    /*removes default webkit styles*/
    -webkit-appearance: none;

    /*fix for FF unable to apply focus style bug */
    border: 0px solid white;
    background-color:rgba(0,0,0,0);

    /*required for proper track sizing in FF*/
    width: 300px;
    height:20px;
}
input.vis-configuration.vis-config-range::-webkit-slider-runnable-track {
    width: 300px;
    height: 5px;
    background: #dedede; /* Old browsers */
    background: -moz-linear-gradient(top,  #dedede 0%, #c8c8c8 99%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#dedede), color-stop(99%,#c8c8c8)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #dedede 0%,#c8c8c8 99%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #dedede 0%,#c8c8c8 99%); /* IE10+ */
    background: linear-gradient(to bottom,  #dedede 0%,#c8c8c8 99%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dedede', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */

    border: 1px solid #999999;
    box-shadow: #aaaaaa 0px 0px 3px 0px;
    border-radius: 3px;
}
input.vis-configuration.vis-config-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    border: 1px solid #14334b;
    height: 17px;
    width: 17px;
    border-radius: 50%;
    background: #3876c2; /* Old browsers */
    background: -moz-linear-gradient(top,  #3876c2 0%, #385380 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#3876c2), color-stop(100%,#385380)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #3876c2 0%,#385380 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #3876c2 0%,#385380 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #3876c2 0%,#385380 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #3876c2 0%,#385380 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3876c2', endColorstr='#385380',GradientType=0 ); /* IE6-9 */
    box-shadow: #111927 0px 0px 1px 0px;
    margin-top: -7px;
}
input.vis-configuration.vis-config-range:focus {
    outline: none;
}
input.vis-configuration.vis-config-range:focus::-webkit-slider-runnable-track {
    background: #9d9d9d; /* Old browsers */
    background: -moz-linear-gradient(top, #9d9d9d 0%, #c8c8c8 99%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#9d9d9d), color-stop(99%,#c8c8c8)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #9d9d9d 0%,#c8c8c8 99%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #9d9d9d 0%,#c8c8c8 99%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #9d9d9d 0%,#c8c8c8 99%); /* IE10+ */
    background: linear-gradient(to bottom,  #9d9d9d 0%,#c8c8c8 99%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#9d9d9d', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */
}

input.vis-configuration.vis-config-range::-moz-range-track {
    width: 300px;
    height: 10px;
    background: #dedede; /* Old browsers */
    background: -moz-linear-gradient(top,  #dedede 0%, #c8c8c8 99%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#dedede), color-stop(99%,#c8c8c8)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #dedede 0%,#c8c8c8 99%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #dedede 0%,#c8c8c8 99%); /* IE10+ */
    background: linear-gradient(to bottom,  #dedede 0%,#c8c8c8 99%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dedede', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */

    border: 1px solid #999999;
    box-shadow: #aaaaaa 0px 0px 3px 0px;
    border-radius: 3px;
}
input.vis-configuration.vis-config-range::-moz-range-thumb {
    border: none;
    height: 16px;
    width: 16px;

    border-radius: 50%;
    background:  #385380;
}

/*hide the outline behind the border*/
input.vis-configuration.vis-config-range:-moz-focusring{
    outline: 1px solid white;
    outline-offset: -1px;
}

input.vis-configuration.vis-config-range::-ms-track {
    width: 300px;
    height: 5px;

    /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */
    background: transparent;

    /*leave room for the larger thumb to overflow with a transparent border */
    border-color: transparent;
    border-width: 6px 0;

    /*remove default tick marks*/
    color: transparent;
}
input.vis-configuration.vis-config-range::-ms-fill-lower {
    background: #777;
    border-radius: 10px;
}
input.vis-configuration.vis-config-range::-ms-fill-upper {
    background: #ddd;
    border-radius: 10px;
}
input.vis-configuration.vis-config-range::-ms-thumb {
    border: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background:  #385380;
}
input.vis-configuration.vis-config-range:focus::-ms-fill-lower {
    background: #888;
}
input.vis-configuration.vis-config-range:focus::-ms-fill-upper {
    background: #ccc;
}

.vis-configuration-popup {
    position: absolute;
    background: rgba(57, 76, 89, 0.85);
    border: 2px solid #f2faff;
    line-height:30px;
    height:30px;
    width:150px;
    text-align:center;
    color: #ffffff;
    font-size:14px;
    border-radius:4px;
    -webkit-transition: opacity 0.3s ease-in-out;
    -moz-transition: opacity 0.3s ease-in-out;
    transition: opacity 0.3s ease-in-out;
}
.vis-configuration-popup:after, .vis-configuration-popup:before {
    left: 100%;
    top: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.vis-configuration-popup:after {
    border-color: rgba(136, 183, 213, 0);
    border-left-color: rgba(57, 76, 89, 0.85);
    border-width: 8px;
    margin-top: -8px;
}
.vis-configuration-popup:before {
    border-color: rgba(194, 225, 245, 0);
    border-left-color: #f2faff;
    border-width: 12px;
    margin-top: -12px;
}</style><style type="text/css">
.vis-panel.vis-background.vis-horizontal .vis-grid.vis-horizontal {
  position: absolute;
  width: 100%;
  height: 0;
  border-bottom: 1px solid;
}

.vis-panel.vis-background.vis-horizontal .vis-grid.vis-minor {
  border-color: #e5e5e5;
}

.vis-panel.vis-background.vis-horizontal .vis-grid.vis-major {
  border-color: #bfbfbf;
}


.vis-data-axis .vis-y-axis.vis-major {
  width: 100%;
  position: absolute;
  color: #4d4d4d;
  white-space: nowrap;
}

.vis-data-axis .vis-y-axis.vis-major.vis-measure {
  padding: 0;
  margin: 0;
  border: 0;
  visibility: hidden;
  width: auto;
}


.vis-data-axis .vis-y-axis.vis-minor {
  position: absolute;
  width: 100%;
  color: #bebebe;
  white-space: nowrap;
}

.vis-data-axis .vis-y-axis.vis-minor.vis-measure {
  padding: 0;
  margin: 0;
  border: 0;
  visibility: hidden;
  width: auto;
}

.vis-data-axis .vis-y-axis.vis-title {
  position: absolute;
  color: #4d4d4d;
  white-space: nowrap;
  bottom: 20px;
  text-align: center;
}

.vis-data-axis .vis-y-axis.vis-title.vis-measure {
  padding: 0;
  margin: 0;
  visibility: hidden;
  width: auto;
}

.vis-data-axis .vis-y-axis.vis-title.vis-left {
  bottom: 0;
  -webkit-transform-origin: left top;
  -moz-transform-origin: left top;
  -ms-transform-origin: left top;
  -o-transform-origin: left top;
  transform-origin: left bottom;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

.vis-data-axis .vis-y-axis.vis-title.vis-right {
  bottom: 0;
  -webkit-transform-origin: right bottom;
  -moz-transform-origin: right bottom;
  -ms-transform-origin: right bottom;
  -o-transform-origin: right bottom;
  transform-origin: right bottom;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.vis-legend {
  background-color: rgba(247, 252, 255, 0.65);
  padding: 5px;
  border: 1px solid #b3b3b3;
  box-shadow: 2px 2px 10px rgba(154, 154, 154, 0.55);
}

.vis-legend-text {
  /*font-size: 10px;*/
  white-space: nowrap;
  display: inline-block
}</style><link href="/oms/public/react-modules/ReactAdvancedComponents/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/public/react-modules/ReactAdvancedComponents/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/public/react-modules/CustomInteractions/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/public/react-modules/CustomInteractions/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/public/react-modules/ReactLayout/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/public/react-modules/ReactLayout/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/apps/Scheduling/react-modules/Appointment/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/apps/Scheduling/react-modules/Appointment/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/apps/Transportation/react-modules/BaseComponents/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/apps/Transportation/react-modules/BaseComponents/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/apps/Transportation/react-modules/Components/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/apps/Transportation/react-modules/Components/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script><link href="/oms/apps/Transportation/react-modules/Plugins/Appointment/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050" rel="stylesheet"><script src="/oms/apps/Transportation/react-modules/Plugins/Appointment/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script></head>

<body class="cc">
  <main>


  <!-- load the core bundle -->
  <link rel="stylesheet" type="text/css" href="/oms/public/react-modules/ReactCore/1/bundle.css?cachekey=07b835d78bbc82b1aec8fc240f008050">
  <script src="/oms/public/react-modules/ReactCore/1/bundle.js?cachekey=07b835d78bbc82b1aec8fc240f008050"></script>

  <script>
    docReady(function () {
      OneGlobalDeps['@onenetwork-plt/react-core'].getPackage('PLT/ReactCore/' + 1);
    });
  </script>
  </main><div class="CustomIcon__iconSet___Q6buz"><span>0</span></div>
<script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;95132a9dccf75506&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;51081231df53418795f5eaecd4ddd4f3&quot;}" crossorigin="anonymous"></script>


<div></div><div id="main"><div class="NeoFrame__main___KmZZy override"><div class="NeoFrame__body____wq1h override"><div class="ResizeObserver__mainContainerStyle___lsJ7K"></div><div class="Appointment__loadingMaskContainer___OdCX_"></div><div class="Appointment__container___lXv1b"><div class="Tabs__container___XoQCU Appointment__tabPanel___SotJ0 override"><div class="Tabs__tabHeader___ac2Kj" role="tablist" tabindex="-1"><div role="tab" tabindex="0" class="Tabs__tab___tjKDV Tabs__selected____npn_ Tabs__focused___Jdkdy override" datadisabled="false"><div class="Text__text___zTZ7W override"><span class="Text__hidden___upunY override">Reservation</span><span class="Text__visible___lBJtU override">Reservation</span></div></div><div role="tab" tabindex="0" class="Tabs__tab___tjKDV override" datadisabled="false"><div class="Text__text___zTZ7W override"><span class="Text__hidden___upunY override">Shipments</span><span class="Text__visible___lBJtU override">Shipments</span></div></div><div class="ResizeObserver__mainContainerStyle___lsJ7K"></div></div><div class="Tabs__tabBody___xR1lr Appointment__tabBody___F3JoN override"><div class="ReservationPanel__reservationPanel___EEbsL"><div class="ReservationForm__reservationForm___GlFUD ReservationPanel__formPanel___P2qnG override"><div class="CollapsiblePanel__collapsiblePanel___MsBMr"><h3 class="CollapsiblePanel__header___ZwhlK"><div class="CollapsiblePanel__icon___fctfs"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-w-14 CollapsiblePanel__chevronIcon___CpqIg override" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"></path></svg></div>Appointment Specification</h3><div class="CollapsiblePanel__collapsiblePanelContent___TWYTx override"><div class="FormLayout__container___Tv0n1 FormLayout__legacyMargins___NY18T undefined override"><div class="form-layout-top FormLayout__gridContainer___sLexx override" style="grid-template-columns: 1fr 1fr 1fr;"><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Shipment</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="ShipmentsField__shipmentsField___r5S3Z override"><button type="button" class="neoHoverable neoPrimaryIcon Button__medium___sbsIZ Button__tertiary___qW7Rf Button__button___gg2hY undefined Button__notDisabled___u003Q ShipmentsField__shipmentLink___s1JfA override" tabindex="0" aria-label="Button" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">00109134682</span></button><div class="ShipmentsField__buttonContainer___K6T90"><div class="MenuSelect__container___dYDi4 MenuSelect__right___SRXCu override"><div><button type="button" class="neoHoverable Button__small___T8Rd6 Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q MenuSelect__menuButton___hbyjp override override" tabindex="0" aria-label="Options Menu" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="plus" class="svg-inline--fa fa-plus fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg></span></button><div class="MenuSelect__textContainer___VEB9z"></div></div></div></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Site</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="CompositeField__container___bTTI5 CompositeField__fullWidth___RANce override"><div class="CompositeField__componentPartFullWidth___vz5qu override ModelLinkField__container___0_9EU ModelLinkField__fullWidth___mkFeq override"><div class="AutocompleteField__container___kWBmJ AutocompleteField__fullWidth___A_kw9 AutocompleteField__disabled___rLHKc CompositeField__componentPartFullWidth___vz5qu override override"><div><span class="AutocompleteField__selectedItems___cH3AX override"><span tabindex="0" role="link" class="AutocompleteField__selectedItem___xiloR AutocompleteField__disabled___rLHKc AutocompleteField__clickable____fypE override" focused="false"><span class="AutocompleteField__selectedItemDisplayValue___kBbuW"><span title="Kroger - Mt. Zion (Florence, KY) - 091A">Kroger - Mt. Zion (Florence, KY) - 091A</span></span></span></span></div></div></div><div class="override"><button type="button" class="Button__small___T8Rd6 Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="calendar-alt" class="svg-inline--fa fa-calendar-alt fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"></path></svg></span></button></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Reservation ID</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="StringField__container___n823p StringField__fullWidth___vDn4R override"><input type="text" class="StringField__textField___JEkir StringField__disabled___g7AaQ StringField__fullWidth___vDn4R override" name="ReservationGrpId" disabled="" aria-label="Reservation ID" value=""></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Target</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="CompositeField__container___bTTI5 CompositeField__fullWidth___RANce override"><div class="DateTimeField__container___lZYHy DateTimeField__notDisabled___EC3wL CompositeField__componentPartFullWidth___vz5qu override override"><div class="DateTimeField__relativeTooltipWrapper___gWXfO"><input type="text" aria-label="Jun 26, 2025 10:00 AM" class="DateTimeField__input___Os_0_ override" value="Jun 26, 2025 10:00 AM"></div><div class="DateTimeField__trigger___EEG5_ override" role="button" tabindex="0" aria-label="Open date picker"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="calendar-alt" class="svg-inline--fa fa-calendar-alt fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"></path></svg></div><div class="DateTimeField__trigger___EEG5_ override" tabindex="0" role="button" aria-label="Open time picker"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="clock" class="svg-inline--fa fa-clock fa-w-16 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"></path></svg></div></div><div class="override"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="Undo" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="undo" class="svg-inline--fa fa-undo fa-w-16 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M212.333 224.333H12c-6.627 0-12-5.373-12-12V12C0 5.373 5.373 0 12 0h48c6.627 0 12 5.373 12 12v78.112C117.773 39.279 184.26 7.47 258.175 8.007c136.906.994 246.448 111.623 246.157 248.532C504.041 393.258 393.12 504 256.333 504c-64.089 0-122.496-24.313-166.51-64.215-5.099-4.622-5.334-12.554-.467-17.42l33.967-33.967c4.474-4.474 11.662-4.717 16.401-.525C170.76 415.336 211.58 432 256.333 432c97.268 0 176-78.716 176-176 0-97.267-78.716-176-176-176-58.496 0-110.28 28.476-142.274 72.333h98.274c6.627 0 12 5.373 12 12v48c0 6.627-5.373 12-12 12z"></path></svg></span></button></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Appointment Type</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="ReservationStateField__reservationStateField___QM0iE"><div class="ReservationStateField__state___trJwm"></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Contact</div><span class="FieldLabel__required___Y_fFK override">*</span></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="ContactField__contactField___FCGIz"><button type="button" class="neoHoverable neoPrimaryIcon Button__medium___sbsIZ Button__tertiary___qW7Rf Button__button___gg2hY undefined Button__notDisabled___u003Q ContactField__smallLinkButton___W3CoV override" tabindex="0" aria-label="Edit" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Edit</span></button></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Max Candidates</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="ComboField__container___wCx8t ComboField__fullWidth___MEMMP ComboField__notDisabled___IZ5PE ComboField__editable___jwNFj override"><div class="ComboField__innerContainer___LUEfq"><div class="ComboField__inputContainer___DaVGs override"><input type="text" name="disabled-autocomplete-input-1750176355371" tabindex="0" class="ComboField__input___dNH_j override" autocomplete="new-password" aria-label="Max Candidates" value="5"></div><div role="button" tabindex="0" class="ComboField__trigger___B61kB override" aria-label="Max Candidates"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="angle-down" class="svg-inline--fa fa-angle-down fa-w-10 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z"></path></svg></div></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Supplier Partners</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="ModelLinkField__container___0_9EU ModelLinkField__fullWidth___mkFeq override"><div class="AutocompleteField__container___kWBmJ AutocompleteField__disabled___rLHKc override"><div><span class="AutocompleteField__selectedItems___cH3AX override"><span tabindex="0" role="link" class="AutocompleteField__selectedItem___xiloR AutocompleteField__disabled___rLHKc AutocompleteField__clickable____fypE override" focused="false"><span class="AutocompleteField__selectedItemDisplayValue___kBbuW"><span title="Chelten House">Chelten House</span></span></span></span></div></div></div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Load</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="TotalLoadField__totalLoad___zgb6O">22 Pallet, 3,638 Case / 40,407.9 Pound / 950.434 Cuft</div></div></div></div><div class="FormLayout__gridItem___xuEAA override" style="grid-column: auto / span 1;"><div class="undefined override"><div class="FieldLabel__labelTop___WWvLg override"><div class="FieldLabel__labelText___hizyN override">Notes</div></div><div class="FieldLabel__fieldOuterContainer___wCf6L override"><div class="NotesField__notesField___lO9Z5"><button type="button" class="neoHoverable neoPrimaryIcon Button__medium___sbsIZ Button__tertiary___qW7Rf Button__button___gg2hY undefined Button__notDisabled___u003Q NotesField__smallLinkButton___n7Bm5 override" tabindex="0" aria-label="Add Notes" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Add Notes</span></button></div></div></div></div></div></div></div></div></div><div class="ReservationPanel__centerPanel___xPSCV ReservationPanel__vertical___HY1pg override"><div class="ReservationPanel__centerContent___CeFGp"><div class="CollapsiblePanel__collapsiblePanel___MsBMr"><h3 class="CollapsiblePanel__header___ZwhlK"><div class="CollapsiblePanel__icon___fctfs"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" class="svg-inline--fa fa-chevron-down fa-w-14 CollapsiblePanel__chevronIcon___CpqIg override" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"></path></svg></div><div class="ReservationCandidatesPanel__collapseHeader___WujLj"><span>Reservation Candidates</span><div><button type="button" class="neoHoverable neoPrimaryIcon Button__medium___sbsIZ Button__icon___iR11S Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="Refresh" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="sync" class="svg-inline--fa fa-sync fa-w-16 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M440.65 12.57l4 82.77A247.16 247.16 0 0 0 255.83 8C134.73 8 33.91 94.92 12.29 209.82A12 12 0 0 0 24.09 224h49.05a12 12 0 0 0 11.67-9.26 175.91 175.91 0 0 1 317-56.94l-101.46-4.86a12 12 0 0 0-12.57 12v47.41a12 12 0 0 0 12 12H500a12 12 0 0 0 12-12V12a12 12 0 0 0-12-12h-47.37a12 12 0 0 0-11.98 12.57zM255.83 432a175.61 175.61 0 0 1-146-77.8l101.8 4.87a12 12 0 0 0 12.57-12v-47.4a12 12 0 0 0-12-12H12a12 12 0 0 0-12 12V500a12 12 0 0 0 12 12h47.35a12 12 0 0 0 12-12.6l-4.15-82.57A247.17 247.17 0 0 0 255.83 504c121.11 0 221.93-86.92 243.55-201.82a12 12 0 0 0-11.8-14.18h-49.05a12 12 0 0 0-11.67 9.26A175.86 175.86 0 0 1 255.83 432z"></path></svg></span></button><button type="button" class="neoHoverable neoPrimaryIcon Button__medium___sbsIZ Button__icon___iR11S Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="Get Next Candidates" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="forward" class="svg-inline--fa fa-forward fa-w-16 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z"></path></svg></span></button></div></div></h3><div class="CollapsiblePanel__collapsiblePanelContent___TWYTx override"><div class="ReservationPanel__candidatesPanel___F0qEq override"><div class="ReservationCandidatesTable__candidatesTable___W1Ll4"><div class="ReservationCandidatesTable__group___u6qgV override"><div class="Table__container___LX6dQ Table__highContrast___T5mif ReservationCandidatesTable__reservationCandidatesTable___gMhmM header-undefined override" role="presentation"><div class="Table__editorContainer___KrYHP"></div><div class="ResizeObserver__mainContainerStyle___lsJ7K"></div><div data-instance="tabulator-1750169806991-7754637" class="tabulator" role="grid" tabulator-layout="fitColumns" style="height: 100%;"><div class="tabulator-header" style="padding-right: 0px;"><div class="tabulator-headers" role="row" style="margin-left: 50px;"><div class="tabulator-col tabulator-frozen" role="columnheader" aria-sort="none" tabulator-field="__error" title="" style="display: none; min-width: 40px; width: 40px; position: absolute; left: 0px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col tabulator-frozen tabulator-frozen-left" role="columnheader" aria-sort="none" tabulator-field="__selection-mode" title="" style="min-width: 40px; width: 50px; position: absolute; left: 0px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title">&nbsp;</div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="startTime" title="" style="min-width: 40px; width: 430px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><span class="fa-layers fa-fw fa-lg HeaderCell__editable___t6AJ4"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle" class="svg-inline--fa fa-circle fa-w-16 HeaderCell__circle___pPuVU" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8z"></path></svg><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pen" class="svg-inline--fa fa-pen fa-w-16 HeaderCell__pen___VXuq0" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.5, 0.5)  rotate(0 0 0)"><path fill="currentColor" d="M290.74 93.24l128.02 128.02-277.99 277.99-114.14 12.6C11.35 513.54-1.56 500.62.14 485.34l12.7-114.22 277.9-277.88zm207.2-19.06l-60.11-60.11c-18.75-18.75-49.16-18.75-67.91 0l-56.55 56.55 128.02 128.02 56.55-56.55c18.75-18.76 18.75-49.16 0-67.91z" transform="translate(-256 -256)"></path></g></g></svg></span><span class="HeaderCell__asterisk___xkTc7">*</span><div class="HeaderCell__title___xEdME">Start Time</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="endTime" title="" style="display: none; min-width: 40px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><span class="fa-layers fa-fw fa-lg HeaderCell__editable___t6AJ4"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle" class="svg-inline--fa fa-circle fa-w-16 HeaderCell__circle___pPuVU" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8z"></path></svg><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pen" class="svg-inline--fa fa-pen fa-w-16 HeaderCell__pen___VXuq0" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.5, 0.5)  rotate(0 0 0)"><path fill="currentColor" d="M290.74 93.24l128.02 128.02-277.99 277.99-114.14 12.6C11.35 513.54-1.56 500.62.14 485.34l12.7-114.22 277.9-277.88zm207.2-19.06l-60.11-60.11c-18.75-18.75-49.16-18.75-67.91 0l-56.55 56.55 128.02 128.02 56.55-56.55c18.75-18.76 18.75-49.16 0-67.91z" transform="translate(-256 -256)"></path></g></g></svg></span><span class="HeaderCell__asterisk___xkTc7">*</span><div class="HeaderCell__title___xEdME">End Time</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="resourceName" title="" style="display: none; min-width: 40px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><span class="fa-layers fa-fw fa-lg HeaderCell__editable___t6AJ4"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle" class="svg-inline--fa fa-circle fa-w-16 HeaderCell__circle___pPuVU" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8z"></path></svg><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pen" class="svg-inline--fa fa-pen fa-w-16 HeaderCell__pen___VXuq0" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="transform-origin: 0.5em 0.5em;"><g transform="translate(256 256)"><g transform="translate(0, 0)  scale(0.5, 0.5)  rotate(0 0 0)"><path fill="currentColor" d="M290.74 93.24l128.02 128.02-277.99 277.99-114.14 12.6C11.35 513.54-1.56 500.62.14 485.34l12.7-114.22 277.9-277.88zm207.2-19.06l-60.11-60.11c-18.75-18.75-49.16-18.75-67.91 0l-56.55 56.55 128.02 128.02 56.55-56.55c18.75-18.76 18.75-49.16 0-67.91z" transform="translate(-256 -256)"></path></g></g></svg></span><span class="HeaderCell__asterisk___xkTc7">*</span><div class="HeaderCell__title___xEdME">Dock Door</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="duration" title="" style="display: none; min-width: 40px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><div class="HeaderCell__title___xEdME">Duration</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="slotType" title="" style="min-width: 40px; width: 430px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><div class="HeaderCell__title___xEdME">Slot Type</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="__bulk-edit" title="" style="display: none; min-width: 40px; width: 100px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm override"><div>Bulk Edit</div><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-frozen-rows-holder"></div></div><div class="tabulator-tableHolder" style="min-height: calc(100% - 45px); height: calc(100% - 45px); max-height: calc(100% - 45px);"><div class="tabulator-table" style="margin-right: 0px; padding-top: 0px; padding-bottom: 0px;"><div class="tabulator-row tabulator-row-odd" role="row" style="padding-left: 50px;"><div class="tabulator-cell tabulator-frozen" role="gridcell" tabulator-field="__error" title="" style="width: 40px; text-align: center; display: none; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell tabulator-frozen tabulator-frozen-left" role="gridcell" tabulator-field="__selection-mode" title="" style="width: 50px; text-align: center; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div role="radio" tabindex="0" aria-checked="false" class="RadioButton__radioContainer___Z0Yj_ override" aria-label="Radio" value="0"><div class="RadioButton__radio___yQmqk override"><div class="RadioButton__radioInner___Pl_wz override"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="startTime" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 10:30 AM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="endTime" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 11:30 AM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="resourceName" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><div class="Text__text___zTZ7W FormattedTypes__default___mHvIR override override"><span class="Text__hidden___upunY override">Live 005</span><span class="Text__visible___lBJtU override">Live 005</span></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="duration" title="" style="display: none; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">1H</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="slotType" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">Regular (Live)</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="__bulk-edit" title="" style="width: 100px; display: none; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="" aria-disabled="false"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"></path></svg><span class="Button__labelContainer___RL7XX override"></span></button><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-row tabulator-row-even" role="row" style="padding-left: 50px;"><div class="tabulator-cell tabulator-frozen" role="gridcell" tabulator-field="__error" title="" style="width: 40px; text-align: center; display: none; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell tabulator-frozen tabulator-frozen-left" role="gridcell" tabulator-field="__selection-mode" title="" style="width: 50px; text-align: center; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div role="radio" tabindex="0" aria-checked="false" class="RadioButton__radioContainer___Z0Yj_ override" aria-label="Radio" value="1"><div class="RadioButton__radio___yQmqk override"><div class="RadioButton__radioInner___Pl_wz override"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="startTime" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 11:30 AM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="endTime" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 12:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="resourceName" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><div class="Text__text___zTZ7W FormattedTypes__default___mHvIR override override"><span class="Text__hidden___upunY override">Live 006</span><span class="Text__visible___lBJtU override">Live 006</span></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="duration" title="" style="display: none; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">1H</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="slotType" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">Regular (Live)</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="__bulk-edit" title="" style="width: 100px; display: none; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="" aria-disabled="false"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"></path></svg><span class="Button__labelContainer___RL7XX override"></span></button><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-row tabulator-row-odd" role="row" style="padding-left: 50px;"><div class="tabulator-cell tabulator-frozen" role="gridcell" tabulator-field="__error" title="" style="width: 40px; text-align: center; display: none; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell tabulator-frozen tabulator-frozen-left" role="gridcell" tabulator-field="__selection-mode" title="" style="width: 50px; text-align: center; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div role="radio" tabindex="0" aria-checked="false" class="RadioButton__radioContainer___Z0Yj_ override" aria-label="Radio" value="2"><div class="RadioButton__radio___yQmqk override"><div class="RadioButton__radioInner___Pl_wz override"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="startTime" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 1:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="endTime" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 2:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="resourceName" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><div class="Text__text___zTZ7W FormattedTypes__default___mHvIR override override"><span class="Text__hidden___upunY override">Live 006</span><span class="Text__visible___lBJtU override">Live 006</span></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="duration" title="" style="display: none; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">1H</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="slotType" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">Regular (Live)</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="__bulk-edit" title="" style="width: 100px; display: none; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="" aria-disabled="false"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"></path></svg><span class="Button__labelContainer___RL7XX override"></span></button><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-row tabulator-row-even" role="row" style="padding-left: 50px;"><div class="tabulator-cell tabulator-frozen" role="gridcell" tabulator-field="__error" title="" style="width: 40px; text-align: center; display: none; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell tabulator-frozen tabulator-frozen-left" role="gridcell" tabulator-field="__selection-mode" title="" style="width: 50px; text-align: center; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div role="radio" tabindex="0" aria-checked="false" class="RadioButton__radioContainer___Z0Yj_ override" aria-label="Radio" value="3"><div class="RadioButton__radio___yQmqk override"><div class="RadioButton__radioInner___Pl_wz override"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="startTime" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 12:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="endTime" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 26, 2025 1:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="resourceName" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><div class="Text__text___zTZ7W FormattedTypes__default___mHvIR override override"><span class="Text__hidden___upunY override">Live 009</span><span class="Text__visible___lBJtU override">Live 009</span></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="duration" title="" style="display: none; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">1H</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="slotType" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">Regular (Live)</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="__bulk-edit" title="" style="width: 100px; display: none; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="" aria-disabled="false"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"></path></svg><span class="Button__labelContainer___RL7XX override"></span></button><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-row tabulator-row-odd" role="row" style="padding-left: 50px;"><div class="tabulator-cell tabulator-frozen" role="gridcell" tabulator-field="__error" title="" style="width: 40px; text-align: center; display: none; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell tabulator-frozen tabulator-frozen-left" role="gridcell" tabulator-field="__selection-mode" title="" style="width: 50px; text-align: center; position: absolute; left: 0px; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div role="radio" tabindex="0" aria-checked="false" class="RadioButton__radioContainer___Z0Yj_ override" aria-label="Radio" value="4"><div class="RadioButton__radio___yQmqk override"><div class="RadioButton__radioInner___Pl_wz override"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="startTime" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 25, 2025 11:30 AM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="endTime" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><span class="FormattedTypes__default___mHvIR override override">Jun 25, 2025 12:30 PM</span></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="resourceName" title="" style="display: none; height: 40px;"><div class="table-cell-formatter"><div class="Text__text___zTZ7W FormattedTypes__default___mHvIR override override"><span class="Text__hidden___upunY override">Live 004</span><span class="Text__visible___lBJtU override">Live 004</span></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="duration" title="" style="display: none; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">1H</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="slotType" title="" style="width: 430px; height: 40px;"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context">Regular (Live)</div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-cell" role="gridcell" tabulator-field="__bulk-edit" title="" style="width: 100px; display: none; height: 40px;"><div class="table-cell-formatter table-cell-nowrap">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm"><button type="button" class="neoHoverable Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q override" tabindex="0" aria-label="" aria-disabled="false"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"></path></svg><span class="Button__labelContainer___RL7XX override"></span></button><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="NeoPrescriptionsPanel__neoPrescriptionsPanel___SHNxe"><div class="NeoSwitcher__neoSwitcherContainer___IHTBR NeoSwitcher__horizontal___qJo8M override"><div class="NeoSwitcher__switcherContainer___xt0ZT"><div class="NeoSwitcher__neoImageContainer___xD0h_"><img src="/oms/img/neo/neo_circle_light.png" alt="NEO Logo"><div class="NeoSwitcher__prescription___TY6is"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="prescription" class="svg-inline--fa fa-prescription fa-w-12 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M301.26 352l78.06-78.06c6.25-6.25 6.25-16.38 0-22.63l-22.63-22.63c-6.25-6.25-16.38-6.25-22.63 0L256 306.74l-83.96-83.96C219.31 216.8 256 176.89 256 128c0-53.02-42.98-96-96-96H16C7.16 32 0 39.16 0 48v256c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16v-80h18.75l128 128-78.06 78.06c-6.25 6.25-6.25 16.38 0 22.63l22.63 22.63c6.25 6.25 16.38 6.25 22.63 0L256 397.25l78.06 78.06c6.25 6.25 16.38 6.25 22.63 0l22.63-22.63c6.25-6.25 6.25-16.38 0-22.63L301.26 352zM64 96h96c17.64 0 32 14.36 32 32s-14.36 32-32 32H64V96z"></path></svg></div></div><div class="NeoSwitcher__navigationContainer___IT9ph NeoSwitcher__onlyText___JwEXR override"><div><span class="NeoSwitcher__textNeo___zubLb">NEO</span><span class="NeoSwitcher__textOperation___gtf9e">Prescriptions</span></div></div></div><div class="NeoSwitcher__neoBoxesContainer____wVN5 NeoSwitcher__horizontal___qJo8M undefined override"><div class="Box__container___z18qK Box__large___SNZwz override NeoSwitcher__boxContainer___LNmmd override"><div class="Box__contentContainer___tIkG6"><div class="Box__content___FFQIF Box__large___SNZwz Box__border___GUcmM override NeoItBox__content___E25O1 override"><div class="NeoItBox__container___z8sce"><div class="NeoItBox__content___E25O1"><div class="NeoItBox__titleContainer___yf1oi"><div class="NeoItBox__circle___WGsBO"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle" class="svg-inline--fa fa-circle fa-w-16 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8z"></path></svg></div><div class="NeoItBox__title___GLlfo">Added Shipment Recommendations</div></div><div>Recommended shipments to be added to appointment.</div></div></div></div></div><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q Box__neoItButton___iYkQc Box__large___SNZwz override override" tabindex="0" aria-label="Get Shipments" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Get Shipments</span></button></div></div></div></div></div></div></div></div><div class="BottomBar__bottomBar___Wmo6y"><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Create Appointment Request" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Create Appointment Request</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Reschedule Appointment" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Reschedule Appointment</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Cancel Appointment" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Cancel Appointment</span></button></div><div class="Stash__stash___i7F_a override"><button type="button" class="neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__disabled___jbF7x override" tabindex="-1" aria-label="Schedule Confirmed Appointment" aria-disabled="true"><span class="Button__labelContainer___RL7XX override">Schedule Confirmed Appointment</span></button></div><div class="Stash__stash___i7F_a override"><button type="button" class="neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__disabled___jbF7x override" tabindex="-1" aria-label="Schedule Soft Appointment" aria-disabled="true"><span class="Button__labelContainer___RL7XX override">Schedule Soft Appointment</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Save" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Save</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Cancel" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Cancel</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Add Complimentary Appointment" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Add Complimentary Appointment</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Reschedule Live Appointment" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Reschedule Live Appointment</span></button></div><div class="Stash__stash___i7F_a Stash__hidden___RJfpz override"><div class="BottomBar__actionsDivider___V8f0s"></div><div class="MenuSelect__container___dYDi4 MenuSelect__left___YFv46 override"><div><div class="MenuSelect__textContainer___VEB9z"><span class="MenuSelect__value___hNVoH override">Actions</span></div><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q MenuSelect__menuButton___hbyjp override override" tabindex="0" aria-label="Options Menu" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg></span></button></div></div></div></div></div></div></div></div><div class="MultitaskWindow__rootEl___bFWIN"></div><section id="highcharts-tooltip-section"><svg xmlns="http://www.w3.org/2000/svg" version="1.1"><defs xmlns="http://www.w3.org/2000/svg"><filter id="h-tooltip-dropshadow" height="130%"><feGaussianBlur in="SourceAlpha" stdDeviation="3"></feGaussianBlur><feOffset dx="2" dy="2" result="offsetblur"></feOffset><feComponentTransfer><feFuncA type="linear" slope="0.75"></feFuncA></feComponentTransfer><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs></svg></section><div class="one-react-ui one-react-ui-portal"><div class="PopupContainer__preventOverflow___bUb02"><div class="PopupContainer__outerContainer___RpuNh skip-neo-theme override" data-placement="bottom-start" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(0px, 5px, 0px);"><div class="PopupContainer__container___CKGm6 override"><div tabindex="-1" class="PopupContainer__content___CvcLs MenuSelect__popup___K8cTX override"><div></div></div></div></div></div></div><div class="one-react-ui one-react-ui-portal"><div class="PopupContainer__preventOverflow___bUb02"><div class="PopupContainer__outerContainer___RpuNh skip-neo-theme override" data-placement="bottom-start" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(148px, 5px, 0px);"><div class="PopupContainer__container___CKGm6 override"><div tabindex="-1" class="PopupContainer__content___CvcLs MenuSelect__popup___K8cTX override"><div></div></div></div></div></div></div><div class="one-react-ui one-react-ui-portal"><div class="skip-neo-theme override"><div tabindex="-1" class="Slideout__slider___Hmu8C PerspectiveLocalSlideout__container___UdoQP override override" style="transform: translateX(5px); right: -427px; opacity: 0;"><div class="PerspectiveLocalSlideout__shadowContainer___DTAqO override"></div><div class="PerspectiveLocalSlideout__contentContainer___dahUH override"><div class="NeoItBox__headerContainer___JSGRK"><img src="/oms/img/neo/neoswitcher/neo_prescription_30_40_dark.png" alt="NEO Prescriptions"><div class="NeoItBox__slideoutTitle___yw7Cn">Added Shipment Recommendations</div><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY Button__noLabel___stGUL Button__notDisabled___u003Q NeoItBox__closeBtn___lHHXj override" tabindex="0" aria-label="Close" aria-disabled="false"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="times" class="svg-inline--fa fa-times fa-w-11 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="currentColor" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></span></button></div><div></div><div class="PerspectiveLocalSlideout__body___nOEWx override"><div class="NeoItSlideout__container___lstRn"><div></div><div class="RecommendedShipmentsSlideoutContent__recommendedShipmentsSlideoutContentContainer___LD5iy"><div class="Table__container___LX6dQ Table__highContrast___T5mif RecommendedShipmentsSlideoutContent__table___GWTdR header-undefined override" role="presentation"><div class="Table__editorContainer___KrYHP"></div><div class="ResizeObserver__mainContainerStyle___lsJ7K"></div><div data-instance="tabulator-1750169807003-1493105" class="tabulator" role="grid" tabulator-layout="fitColumns" style="height: 100%;"><div class="tabulator-header" style="padding-right: 0px;"><div class="tabulator-headers" role="row" style="margin-left: 50px;"><div class="tabulator-col tabulator-frozen" role="columnheader" aria-sort="none" tabulator-field="__error" title="" style="display: none; min-width: 40px; width: 40px; position: absolute; left: 0px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col tabulator-frozen tabulator-frozen-left" role="columnheader" aria-sort="none" tabulator-field="__selection-mode" title="" style="min-width: 40px; width: 50px; position: absolute; left: 0px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="Checkbox__checkboxContainer___zDQLw SelectionHeaderCell__checkboxContainer___cgIvS override override" role="checkbox" tabindex="0" aria-checked="false" aria-label="Checkbox"><div class="Checkbox__checkbox____wrea"><div class="Checkbox__checkboxInner___WBse0"></div></div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="ShipmentML" title="" style="min-width: 40px; width: 287px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-header-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="HeaderCell__container___OGgRL override" tabindex="-1"><div class="HeaderCell__title___xEdME">Shipment</div></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div><div class="tabulator-col" role="columnheader" aria-sort="none" tabulator-field="__bulk-edit" title="" style="display: none; min-width: 40px; width: 100px; height: 45px;"><div class="tabulator-col-content"><div class="tabulator-col-title-holder"><div class="tabulator-col-title"><div class="table-cell-formatter">
        <span class="error-container"><div><span></span></div></span>
        <div class="table-cell-react-context"><div class="BulkEditCell__container___ERbSm override"><div>Bulk Edit</div><span class="BulkEditCell__pasteButton___mFQF9"><button type="button" class="Button__medium___sbsIZ Button__iconAlt___wsJ9W Button__button___gg2hY Button__noLabel___stGUL Button__disabled___jbF7x override" tabindex="-1" aria-label="" aria-disabled="true"><span class="Button__labelContainer___RL7XX override"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="paste" class="svg-inline--fa fa-paste fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128 184c0-30.879 25.122-56 56-56h136V56c0-13.255-10.745-24-24-24h-80.61C204.306 12.89 183.637 0 160 0s-44.306 12.89-55.39 32H24C10.745 32 0 42.745 0 56v336c0 13.255 10.745 24 24 24h104V184zm32-144c13.255 0 24 10.745 24 24s-10.745 24-24 24-24-10.745-24-24 10.745-24 24-24zm184 248h104v200c0 13.255-10.745 24-24 24H184c-13.255 0-24-10.745-24-24V184c0-13.255 10.745-24 24-24h136v104c0 13.2 10.8 24 24 24zm104-38.059V256h-96v-96h6.059a24 24 0 0 1 16.97 7.029l65.941 65.941a24.002 24.002 0 0 1 7.03 16.971z"></path></svg></span></button></span></div></div></div></div></div></div><div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div></div><div class="tabulator-frozen-rows-holder"></div></div><div class="tabulator-tableHolder" style="min-height: calc(100% - 45px); height: calc(100% - 45px); max-height: calc(100% - 45px);"><div class="tabulator-table" style="margin-right: 0px; display: none;"></div><div class="tabulator-placeholder" tabulator-render-mode="virtual" style="width: 337px;"><span>No Data</span></div></div></div></div></div><div class="NeoItSlideout__buttonBar___V1Xt6"><button type="button" class="neoHoverable neoAltIcon Button__medium___sbsIZ Button__primary___FSeua Button__button___gg2hY undefined Button__notDisabled___u003Q override" tabindex="0" aria-label="Add selected" aria-disabled="false"><span class="Button__labelContainer___RL7XX override">Add selected</span></button></div></div></div></div><div class="PerspectiveLocalSlideout__button___PcI66 PerspectiveLocalSlideout__disablePointer___JNAp8 override" role="button" tabindex="0" aria-label="Slide Out"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="angle-double-left" class="svg-inline--fa fa-angle-double-left fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M223.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L319.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L393.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34zm-192 34l136 136c9.4 9.4 24.6 9.4 33.9 0l22.6-22.6c9.4-9.4 9.4-24.6 0-33.9L127.9 256l96.4-96.4c9.4-9.4 9.4-24.6 0-33.9L201.7 103c-9.4-9.4-24.6-9.4-33.9 0l-136 136c-9.5 9.4-9.5 24.6-.1 34z"></path></svg></div><div class="ResizeObserver__mainContainerStyle___lsJ7K"></div></div></div></div><div class="one-react-ui one-react-ui-portal"></div><div class="one-react-ui one-react-ui-portal"></div></body></html>