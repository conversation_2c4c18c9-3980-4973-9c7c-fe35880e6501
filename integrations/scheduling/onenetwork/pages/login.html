<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en" class="TridactylThemeMidnight"><head>
  <title>One Network Enterprises | Logon</title>
  
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="Content-Script-Type" content="text/javascript">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta http-equiv="imagetoolbar" content="no">
  
  <link rel="icon" href="https://logon.onenetwork.com/sso/img/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="https://logon.onenetwork.com/sso/img/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="login_files/bootstrap.min.css">
  <link rel="stylesheet" href="login_files/font-awesome.min.css">
  <link href="login_files/css.css" rel="stylesheet">
  <link href="login_files/sso.css" rel="stylesheet" type="text/css">
  <style>
    #login-ct, #logout-ct, #session-expired-ct, #pki-login-ct, #saml-login-ct, #login-disabled-ct { display: none; }
  </style>
  	<script>SSO_CSRF = {enabled: true};</script>
	<script src="login_files/csrfProtection.js"></script>
<script>window.top.SSO_CSRF.addTokens(["YpIeeaz49AZR2L3DNYu3MEg","AJNMDHLYr30jjrRtbDHyQNs","AOJ1xoZu3Rzdfv5hOglYXzE"]);</script>
  <script type="text/javascript">
/**
 * File: util/Cookie.js
 * Simple functions to get and set cookie information. This is copied directly into logon.jsp
 * because the utility is only being used in this file and we want to reduce the number of
 * requests being made in SSO pages.
 *
 * Copyright: 
 *  2004 <NAME_EMAIL> All rights reserved
 */

var COOKIE_VERSION = "0.1";
function getCookie(name) 
{
    var start = document.cookie.indexOf(name+"=");
    var len = start+name.length+1;
    
    if ((!start) && (name != document.cookie.substring(0,name.length))) 
        return null;
        
  if (start == -1) 
    return null;
  
  var end = document.cookie.indexOf(";",len);
    
  if (end == -1) end = document.cookie.length;
    
    return unescape(document.cookie.substring(len,end));
}
function setCookie(name, value, expires, path, domain, secure) 
{
  setRawCookie(
    name + "=" +escape(value) +
    ( (expires) ? ";expires=" + expires.toGMTString() : "") +
    ( (path) ? ";path=" + path : "") + 
    ( (domain) ? ";domain=" + domain : "") +
    ( (secure) ? ";secure" : "")
  );
}
function removeCookie(name, path, domain, secure)
{
  var thepast = new Date();
  thepast.setYear(thepast.getYear() - 2);
  
  setCookie(name, "", thepast, path, domain, secure);
}
function setPermCookie(name, value, path, domain, secure)
{
  var thefuture = new Date();
  var year = (thefuture.getYear() < 1000) ? thefuture.getYear() + 1900 : thefuture.getYear();
  thefuture.setYear(year + 5);
  setCookie(name, value, thefuture, path, domain, secure);
}
function setRawCookie(cookie)
{
  document.cookie = cookie;
}
  </script>
  <script type="text/javascript">
    // framebuster
    if (top != self) { top.location.replace(self.location.href); }
    
    function onBodyLoadAlt() { 
      var lang = getCookie('lgxlang');
      var select = document.forms[0].language;
      if (lang != null && select != null && select.options != null) {
        for (var i = 0; i < select.options.length; i++) {
          if (select.options[i].value == lang) {
            select.options.selectedIndex = i;
            break;
          }
        }
      }

      // The logout.jsp and sessionExpired.jsp pages redirect to this page with an attribute set,
      // and this page displays the appropriate message with a link for the user to re-login.
    
        document.getElementById('login-ct').style.display = 'block';
    

      focusUsernameField();
      populateErrorMessages();
	  populateVideoPreferences();
    }
    
    function showLoginForm() {
      document.getElementById('logout-ct').style.display = 'none';
      document.getElementById('session-expired-ct').style.display = 'none';
      document.getElementById('login-ct').style.display = 'block';
      
      focusUsernameField();
	  populateVideoPreferences();
    }
    
    function onLanguageChange() {
      setLangCookie();
      window.location.reload();
      return true;
    }
    
    function setLangCookie() {
      var langField = document.forms[0].language;
      var lang = (langField.options == null) ? langField.value : langField.options[langField.selectedIndex].value;
      document.domain.search(/(\w+.\w+$)/);
      var domain = RegExp.$1;
      setPermCookie("lgxlang", lang, '/', domain);
    }
    
    /**
     * function to handle forgot passwod link in message.
     */
    function forgotPasswordLink() {
      location.href="/sso/requestPassword.sso";
    }

    function reloadCaptcha(){
        var d = new Date();
        document.getElementById('confirmationCode').value = '';
        $("#captcha_image").attr("src", "/sso/simpleCaptchaImg?" + d.getTime());
    }

  </script>
  <script type="text/javascript">
  function onBodyLoad() {
  try {
    var errorsMsg = document.getElementById('errors-list').innerHTML;

    var messagesEl = document.getElementById('messages-list');
    if (messagesEl.children[0].nodeName.toLowerCase() == 'li') {
      messagesEl.parentNode.parentNode.style.display = 'block';
    }
  }
  catch(e) {}

  var submitBtnEl = document.getElementById('submitButton');
  if(submitBtnEl) {
    submitBtnEl.onmouseover = function () {
      submitBtnEl.className = 'loginButton loginButtonOver';
    };
    submitBtnEl.onmouseout = function() {
      submitBtnEl.className = 'loginButton';
    };
    if(document.forms.length > 0) {
      document.forms[0].onsubmit = function (e) {
        submitBtnEl.setAttribute('disabled', 'true');
        submitBtnEl.className = 'loginButton loginButtonDisabled';
      }
    }
  }

  var showCapture = document.getElementById('showCapture');
  if(showCapture) {
    if(showCapture.value == 'true'){
      document.getElementById('confirmation').style.display = 'block';
      document.getElementById('confirmationCode').type = 'text';
    }else{
      document.getElementById('confirmation').style.display = 'none';
      document.getElementById('confirmationCode').type = 'hidden';
    }
  }
  
  if (errorsMsg && typeof populateErrorMessages === 'function') {
    populateErrorMessages(); 
  }

}
</script>
<style type="text/css">@media print {
        .TridactylStatusIndicator {
            display: none !important;
        }
    }</style></head><iframe class="cleanslate hidden" src="login_files/commandline.html" id="cmdline_iframe" loading="lazy" style="height: 0px !important;"></iframe>

<body onload="onBodyLoad(); onBodyLoadAlt();">
  <main>
  <div id="main-ct-wrapper">
    <div id="main-ct" class="container-fluid">
      <form id="logonForm" method="POST" action="/sso/signon.sso;jsessionid=C7980DDDD3CC4A2553E4368F30DC276D" target="_top">
      
      
      <div class="row login-container">
        <div class="col-xs-12">  
          <div id="username-errorCt"> 
          </div>
          <div id="password-errorCt"> 
          </div>
          <div class="login-panel">
            <div style="padding-top: 30px"> </div>
            <div class="login-header">
              <img src="login_files/vertical-with-text-light.webp" class="one-logo" alt="One Network Enterprises">
            </div>
            <input type="hidden" name="appId" value="">
            <input type="hidden" name="showMfaQRCode" value="false">
            <input type="hidden" name="_origReqURL" value="">
            <div class="login-form">

              <div class="token-help-section" style="display: none">
                <div class="token-help-header"> 
                  Token Error
                </div>
                <div class="token-help-text"> 
                  The token is invalid or expired. Please click the password link to reset.
                </div>
              </div>
              
              <div class="captcha-help-section" style="display: none">
                <div class="captcha-help-header"> 
                  Too Many Failed Attempts
                </div>
                <div class="captcha-help-text"> 
                  <a href="https://logon.onenetwork.com/sso/requestPassword.sso">Click here</a> if you forgot your password.
                </div>
              </div>

              <div id="login-ct" style="display: block;">
                 
                
                <div id="messages" style="display: none">
                  <div class="msg">
                    Messages:
                    <ul id="messages-list"></ul>
                  </div>
                  <div class="arrow"></div>
                </div>
                
                
                
                

                
                <br> 
                
                  <span id="errors-list"></span>
                  <span class="username-text">
                    <img src="login_files/error-icon.webp" id="invalid-username-icon" alt="Invalid Username">
                    <span id="invalid-username"> 
                      Username is not valid. <br> <br>
                      Please try again.
                    </span>
                    Username
                  </span> <br>
                  <input type="text" name="userName" maxlength="100" size="20" id="input-username" class="input-user" aria-label="Username">
                  <span class="password-text">
                    <img src="login_files/error-icon.webp" id="invalid-password-icon" alt="Invalid Password">
                    <span id="invalid-password"> 
                      Password is not valid. <br> <br>
                      Please try again.
                    </span>
                    Password
                  </span> <br>
                  <input type="password" class="input-password" id="input-password" name="password" autocomplete="off" title="Password">
                
                
                

                <div id="confirmation" style="display: none">
                  <div style="padding-bottom: 10px;">
                  <div style="width: 70%; float: left;">
                
                  </div>                
                  <div class="banners" style="display: flex">
                    <div title="The security check is to protect against automated attacks." style="margin-right: 5px">                  
                      <img src="login_files/info.webp" aria-label="The security check is to protect against automated attacks." style="cursor: pointer;">       
                    </div>
                    <div>
                      <img src="login_files/refresh.webp" aria-label="Reload Captcha" style="cursor: pointer;" onclick="reloadCaptcha()">
                  </div>
                  </div>
                  </div>
                  <br>
                  <div class="captcha">
                    <span class="confirmation-text">
                      Enter Captcha Code
                    </span>
                    <input type="hidden" size="20" maxlength="6" name="confirmationCode" class="input-password" style="padding-right: 0px;" id="confirmationCode" autocomplete="off" aria-label="Enter Captcha Code" required="">
                  </div>
                </div>
                
                  <input type="submit" name="loginBtn" value="Log In" id="submitButton" class="loginButton">

                <div class="forgot-credentials">
                  Forgot <a href="https://logon.onenetwork.com/sso/requestUsername.sso">Username</a> or 
                  <a href="https://logon.onenetwork.com/sso/requestPassword.sso">Password</a>?
                </div>
                
                <div class="social-icons">
                  










<a href="mailto:<EMAIL>" class="icon-tooltip" rel="noopener noreferrer" aria-label="Join One Network through Email">
  <div class="mail social-icon"></div>
  <span class="email-icon">
    <p class="social-icon-text"> Email Customer Support </p>
  </span>
</a>
<a href="https://www.facebook.com/OneNetworkEnterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Facebook">
  <div class="facebook social-icon"></div>
  <span class="facebook-icon">
    <p class="social-icon-text"> Join One Network on Facebook </p>
  </span>
</a>
<a href="https://twitter.com/onenetwork" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Twitter">
  <div class="twitter social-icon"></div>
  <span class="twitter-icon">
    <p class="social-icon-text"> Join One Network on Twitter </p>
  </span>
</a>
<a href="https://www.linkedin.com/company/one-network-enterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on LinkedIn">
  <div class="linkedin social-icon"></div>
  <span class="linkedin-icon">
    <p class="social-icon-text"> Join One Network on LinkedIn </p>
  </span>
</a>
<a href="https://www.youtube.com/user/onemediareach/feed" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on YouTube">
  <div class="youtube social-icon"></div>
  <span class="youtube-icon">
    <p class="social-icon-text"> Join One Network on YouTube </p>
  </span>
</a>
               </div>

                <footer class="footer-text">
                  <p class="copyright-text">© 2025 <a href="https://blueyonder.com/" class="one_link" target="_blank" rel="noopener noreferrer">Blue Yonder Group, Inc.</a><br> All rights reserved.</p>
                </footer>
                
                
                
                
                  <input type="hidden" name="language" value="en">
                
              </div>
              
              <div id="logout-ct" style="height: 200px">
                <div class="logout-text">
                  Logged Out
                </div>
                <div class="logout-subtext">
                  
                  You have successfully logged out. <br>
                  Thank you for using One Network.
                   
                </div>
                
                <div class="relogin btn btn-primary" onclick="showLoginForm()" style="padding: 0">
                  
                    <a href="javascript:;" onclick="showLoginForm()">Return to Login</a>  
                  
                </div>

                <div class="social-icons footer-icons">
                  










<a href="mailto:<EMAIL>" class="icon-tooltip" rel="noopener noreferrer" aria-label="Join One Network through Email">
  <div class="mail social-icon"></div>
  <span class="email-icon">
    <p class="social-icon-text"> Email Customer Support </p>
  </span>
</a>
<a href="https://www.facebook.com/OneNetworkEnterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Facebook">
  <div class="facebook social-icon"></div>
  <span class="facebook-icon">
    <p class="social-icon-text"> Join One Network on Facebook </p>
  </span>
</a>
<a href="https://twitter.com/onenetwork" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Twitter">
  <div class="twitter social-icon"></div>
  <span class="twitter-icon">
    <p class="social-icon-text"> Join One Network on Twitter </p>
  </span>
</a>
<a href="https://www.linkedin.com/company/one-network-enterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on LinkedIn">
  <div class="linkedin social-icon"></div>
  <span class="linkedin-icon">
    <p class="social-icon-text"> Join One Network on LinkedIn </p>
  </span>
</a>
<a href="https://www.youtube.com/user/onemediareach/feed" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on YouTube">
  <div class="youtube social-icon"></div>
  <span class="youtube-icon">
    <p class="social-icon-text"> Join One Network on YouTube </p>
  </span>
</a>
              </div>
              
                <footer class="footer-section footer-text">
                  <p class="copyright-text">© 2025 <a href="https://blueyonder.com/" class="one_link" target="_blank" rel="noopener noreferrer">Blue Yonder Group, Inc.</a><br> All rights reserved.</p>
                </footer>
              </div>
              
              <div id="saml-login-ct">
                <div class="instructions">
                  
                  A system error occurred. You may try to logon again. 
Please contact your ONE Network administrator if this error persists.
                  
                </div>
                
              </div>

              <div id="login-disabled-ct">
                <div class="instructions">
                  To login, please visit your enterprise sign-on URL for ONE.
                </div>
                <div class="support">
                  <a href="mailto:<EMAIL>">Email Customer Support</a><br><br>
                </div>
              </div>
              
              <div id="session-expired-ct">
                <div class="session-expired-text">
                  Session Expired
                </div>
                <div class="session-expired-subtext">
                  Your session has expired due to inactivity. <br> You must logon again to use the system.
                </div>
                
                <div class="relogin btn btn-primary">
                  
                    <a href="javascript:;" onclick="showLoginForm()">Return to Login</a>  
                  
                </div>
                <div class="social-icons footer-icons">
                  










<a href="mailto:<EMAIL>" class="icon-tooltip" rel="noopener noreferrer" aria-label="Join One Network through Email">
  <div class="mail social-icon"></div>
  <span class="email-icon">
    <p class="social-icon-text"> Email Customer Support </p>
  </span>
</a>
<a href="https://www.facebook.com/OneNetworkEnterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Facebook">
  <div class="facebook social-icon"></div>
  <span class="facebook-icon">
    <p class="social-icon-text"> Join One Network on Facebook </p>
  </span>
</a>
<a href="https://twitter.com/onenetwork" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Twitter">
  <div class="twitter social-icon"></div>
  <span class="twitter-icon">
    <p class="social-icon-text"> Join One Network on Twitter </p>
  </span>
</a>
<a href="https://www.linkedin.com/company/one-network-enterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on LinkedIn">
  <div class="linkedin social-icon"></div>
  <span class="linkedin-icon">
    <p class="social-icon-text"> Join One Network on LinkedIn </p>
  </span>
</a>
<a href="https://www.youtube.com/user/onemediareach/feed" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on YouTube">
  <div class="youtube social-icon"></div>
  <span class="youtube-icon">
    <p class="social-icon-text"> Join One Network on YouTube </p>
  </span>
</a>
              </div>

                <footer class="footer-section footer-text">
                  <p class="copyright-text">© 2025 <a href="https://blueyonder.com/" class="one_link" target="_blank" rel="noopener noreferrer">Blue Yonder Group, Inc.</a><br> All rights reserved.</p>
                </footer>
              </div>
               
              <div id="pki-login-ct">
                <div id="pki-errors" style="display: none">
                  <div class="msg">
                    Errors:
                    <ul id="pki-errors-list"></ul>
                  </div>
                  <div class="arrow"></div>
                </div>
                
                <div class="relogin btn btn-primary">
                  <a href="https://logon.onenetwork.com/sso/logon.sso">Return to Login</a>  
                </div>
                <div class="social-icons footer-icons">
                  










<a href="mailto:<EMAIL>" class="icon-tooltip" rel="noopener noreferrer" aria-label="Join One Network through Email">
  <div class="mail social-icon"></div>
  <span class="email-icon">
    <p class="social-icon-text"> Email Customer Support </p>
  </span>
</a>
<a href="https://www.facebook.com/OneNetworkEnterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Facebook">
  <div class="facebook social-icon"></div>
  <span class="facebook-icon">
    <p class="social-icon-text"> Join One Network on Facebook </p>
  </span>
</a>
<a href="https://twitter.com/onenetwork" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on Twitter">
  <div class="twitter social-icon"></div>
  <span class="twitter-icon">
    <p class="social-icon-text"> Join One Network on Twitter </p>
  </span>
</a>
<a href="https://www.linkedin.com/company/one-network-enterprises" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on LinkedIn">
  <div class="linkedin social-icon"></div>
  <span class="linkedin-icon">
    <p class="social-icon-text"> Join One Network on LinkedIn </p>
  </span>
</a>
<a href="https://www.youtube.com/user/onemediareach/feed" class="icon-tooltip" target="_blank" rel="noopener noreferrer" aria-label="Join One Network on YouTube">
  <div class="youtube social-icon"></div>
  <span class="youtube-icon">
    <p class="social-icon-text"> Join One Network on YouTube </p>
  </span>
</a>
                </div>              

                <footer class="footer-section footer-text">
                  <p class="copyright-text">© 2025 <a href="https://blueyonder.com/" class="one_link" target="_blank" rel="noopener noreferrer">Blue Yonder Group, Inc.</a><br> All rights reserved.</p>
                </footer>          
              </div>            
            
          </div>
        </div>
        











<div id="media-player">
  <button id="video-button" class="active" onclick="toggleVideo(event)" aria-label="Toggle Video">
    <div id="video-icon"></div>
  </button>
  <video id="banner-video" class="neo-video" playsinline="" autoplay="autoplay" loop="" muted="muted" style="display: block;">
    <source src="login_files/by_logo_reveal.mp4" type="video/mp4">
    Your browser does not support the video tag.
  </video>
  <img id="banner-image" class="neo-image" src="login_files/by-banner.jpg" style="display: none;">
</div>
      
      </div>
   </div></form>
   <script type="text/javascript">
var video = document.querySelector('video');

var videoButton = document.getElementById('video-button');
var videoIcon = document.getElementById('video-icon');

var neoVideo = document.getElementById('banner-video');
var neoImage = document.getElementById('banner-image');

if (localStorage.getItem('videoPreference') === 'disabled') {
  neoVideo.style.display = 'none';
}

function toggleVideo(e) {
  if (videoButton.className === 'active') {
    videoButton.className = 'disabled';
    videoIcon.src = '/sso/img/video-off.png';
    neoVideo.style.display = 'none';
    neoImage.style.display = 'inline';
    video.muted = true;
  } else {
    videoButton.className = 'active';
    videoIcon.src = '/sso/img/video.png';
    neoVideo.style.display = 'block';
    neoImage.style.display = 'none';
    
    video.play();
  }
  
  localStorage.setItem('videoPreference', videoButton.className);
  localStorage.setItem('videoIcon', videoIcon.src);
  e.stopPropagation();
  e.preventDefault();
}

function populateVideoPreferences() {
  if (localStorage.getItem('videoPreference') && localStorage.getItem('videoIcon')) {
    document.getElementById('video-button').className = localStorage.getItem('videoPreference');
    document.getElementById('video-icon').src = localStorage.getItem('videoIcon');
    
    if (localStorage.getItem('videoPreference') === 'active') {
      neoImage.style.display = 'none';
      neoVideo.style.display = 'block';
      video.play();
    } else {
      neoVideo.style.display = 'none';
      neoImage.style.display = 'inline';
    }
  } else {
    //user has not toggled yet/cleared local storage so we show default display with video
    neoImage.style.display = 'none';
    neoVideo.style.display = 'block';
  }
}

</script>
  
  <script src="login_files/jquery.min.js"></script>
  <script src="login_files/bootstrap.min.js"></script>
  
  <script type="text/javascript">
    var focusUsernameField = function() {
      var userNameField = document.forms["logonForm"].elements["userName"];
	    userNameField.setAttribute('aria-label', 'Username');
      if (userNameField.type != "hidden") {
          //remove focus from IE input fields
          if(/MSIE \d|Trident.*rv:/.test(navigator.userAgent)){
          userNameField.blur();
          } else {
          //retain focus on other browsers
          userNameField.focus();
        }
      }
    }
    
    var populateErrorMessages = function() {
      var errorMsg = document.getElementById('errors-list').innerHTML;
      var captchaDialog = document.querySelector('#confirmation');
      
      if (!errorMsg) {
        return;
      }
      var username = document.getElementById('input-username').value;
      var password = document.getElementById('input-password').value;
      var errorCt = document.getElementById('error-container');
      var errorUseCases = ['deactivated', 'locked', 'inactive', 'permission', 'reserved', 'invalid', 'expired', 'initial password', 'granted access', 'activity'];

      if (errorMsg.includes('name')) {
        document.getElementById('invalid-username-icon').style.display = 'inline';
        document.querySelector('.username-text').className += ' enabled';
      }

      if (errorMsg.toLowerCase().includes('password')) {
        if (!errorMsg.toLowerCase().includes('password link')) {
          document.getElementById('invalid-password-icon').style.display = 'inline';
          document.querySelector('.password-text').className += ' enabled';
        }
      }
  	  
  	  if (!errorUseCases.some(error => errorMsg.includes(error))) {
  	    if (username.length > 0) {
	      if (errorMsg.includes('name')) {
	        document.getElementById('invalid-username').innerHTML = 'Username is not valid.';
	        document.getElementById('username-errorCt').style.display = 'flex';
	        document.getElementById('username-errorCt').innerHTML = '<i class="fa fa-exclamation error-exclamation"></i> <span class="error-title"> Error : </span> <span class="error-message"> Username is not valid. </span> <i class="fa fa-times-circle error-close" onclick="hideUsernameError()"></i>';
	      }
	    } else if(!errorMsg.includes('token')) {
	      document.getElementById('invalid-username').innerHTML = 'Username is required';
	      document.getElementById('username-errorCt').style.display = 'flex';
	      document.getElementById('username-errorCt').innerHTML = '<i class="fa fa-exclamation error-exclamation"></i> <span class="error-title"> Error : </span> <span class="error-message"> Username is required </span> <i class="fa fa-times-circle error-close" onclick="hideUsernameError()"></i>';
	    }
  	  }
      
      if (password.length > 0) {
        if (errorMsg.toLowerCase().includes('password')) {
          document.getElementById('invalid-password').innerHTML = 'Password is not valid.';
          document.getElementById('password-errorCt').style.display = 'flex';
          document.getElementById('password-errorCt').innerHTML = '<i class="fa fa-exclamation error-exclamation"></i> <span class="error-title"> Error : </span> <span class="error-message"> Password is not valid. </span> <i class="fa fa-times-circle error-close" onclick="hidePasswordError()"></i>';
        }
      } else if (errorUseCases.some(error => errorMsg.includes(error))) {
        document.getElementById('password-errorCt').style.display = 'flex';
        document.getElementById('password-errorCt').style.width = 'auto';
        document.getElementById('password-errorCt').innerHTML = '<i class="fa fa-exclamation error-exclamation"></i> <span class="error-title"> Error : </span> <span class="error-message">' + errorMsg.replace(/(<|&lt;)br\s*\/*(>|&gt;)/g,' ') + '</span> <i class="fa fa-times-circle error-close" onclick="hidePasswordError()"></i>';
      } else if(!(errorMsg.includes('single-sign') || errorMsg.includes('token'))) {
        document.getElementById('invalid-password').innerHTML = 'Password is required';
        document.getElementById('password-errorCt').style.display = 'flex';
        document.getElementById('password-errorCt').innerHTML = '<i class="fa fa-exclamation error-exclamation"></i> <span class="error-title"> Error : </span> <span class="error-message"> Password is required </span> <i class="fa fa-times-circle error-close" onclick="hidePasswordError()"></i>';
      }
      
      if (errorMsg.toLowerCase().includes('token')) {
        document.querySelector('.token-help-section').style.display = 'block';
        document.querySelector('.social-icons').style.top = 0;
      }
      
      //show errors for both fields
      if ((errorMsg.includes('name') && errorMsg.toLowerCase().includes('password') && !errorMsg.includes('requiredPassword'))
        || (errorMsg.toLowerCase().includes('account')) || (errorMsg.includes('code'))) {
        
        document.getElementById('invalid-password-icon').style.display = 'inline';
        document.querySelector('.password-text').className += ' enabled';
        
        if (captchaDialog && captchaDialog.style.display === 'block') {
          if (errorMsg.includes('code')) {
            document.querySelector('.captcha-help-header').innerHTML = errorMsg;
          }
          
          document.querySelector('.captcha-help-section').style.display = 'block';
          
          document.getElementById('invalid-username-icon').style.display = 'none';
          document.querySelector('.social-icons').style.display = 'none';
          document.querySelector('.footer-text').style.display = 'none';
          document.querySelector('.forgot-credentials').style.display = 'none';
        } else {
          document.getElementById('invalid-password').innerHTML = errorMsg;
          
          document.getElementById('invalid-username-icon').style.display = 'inline';
          document.querySelector('.username-text').className += ' enabled';
          document.getElementById('invalid-username').innerHTML = errorMsg;
          }
        }
      
      }
      
      var hideUsernameError = function(field) {
        document.getElementById('username-errorCt').style.display = 'none';
      }
      
      var hidePasswordError = function(field) {
        document.getElementById('password-errorCt').style.display = 'none';
      }
  </script>
</div></div></main>
<script defer="defer" src="login_files/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;92a8f85b4eaedbf7&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.3.0&quot;,&quot;token&quot;:&quot;51081231df53418795f5eaecd4ddd4f3&quot;}" crossorigin="anonymous"></script>


<span class="cleanslate TridactylStatusIndicator TridactylModenormal">normal</span></body></html>