<!DOCTYPE html>
<html lang=en >
   <head>
      <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
      <meta http-equiv="Content-Script-Type" content="text/javascript"/>
      <link rel="SHORTCUT ICON" type="image/x-icon" href="/oms/favicon.ico"/>
      <link rel="stylesheet" type="text/css" href="/oms/css/cc3/OpenSans.css?cachekey=6beffd669779ee639b9bc6ad8ec837b0">
      <!-- This is used to allow React Dev Tools to work on the page. it HAS to be in a script tag at the beginning -->
      <script> __REACT_DEVTOOLS_GLOBAL_HOOK__ = window.parent.__REACT_DEVTOOLS_GLOBAL_HOOK__ </script>
      <!-- Timestamp for recording load times. -->
      <script>
         window.PLT_PAGE_LOAD_START = new Date();
         window.PLT_UI_LOAD_TIMES = {
           perspective: null,
           dependencies: {},
         };
      </script>
      <!-- Default ONE require js configuration (sets base url and non-node related libraries) -->
      <script>
         var EPTConfig = {
           isEPTIncluded: false
           ,eptName: ''
         };

         var require = {
           baseUrl: '/oms/rest/js/163438/en',
           waitSeconds: 60,
           urlArgs: 'cachekey=6beffd669779ee639b9bc6ad8ec837b0',
           patches: {
         "DPX/Events/EventReportsContextMenuPlugin": ["RTL/Patch/EventsContextMenuPatch"],
         "FIN/reports/InvoiceApprovePlugin": ["TMS/reports/InvoiceApprovePluginPatch"],
         "MFG/reports/WorkOrderReportPlugin": ["SCPT/report/patch/WorkOrderReportPluginPatch"],
         "OMS/admin/VendorPolicies": ["TMS/vendor/VendorDetailsPatch"],
         "OMS/enhancedASN/ASNController": ["SCPT/ASNControllerPatch"],
         "OMS/enhancedASN/ASNUtil": ["SCPT/ASNUtilPatch"],
         "OMS/enhancedOrder/OrderController": ["SCPT/OrderControllerPatch"],
         "OMS/enhancedOrder/ProblemsGrid": ["SCPT/OrderProblemsGridPatch"],
         "OMS/enhancedOrder/report/ReceiptsLinksReportPlugin": ["SCPT/report/patch/ReceiptsLinksReportPluginPatch"],
         "OMS/enhancedOrder/report/dashboard/OrderScheduleDashboard": ["SCPT/OrderScheduleDashboardPatch"],
         "OMS/reports/ShipmentOrderHeaderReportPlugin": ["SCPT/report/patch/ShipmentOrderHeaderReportPluginPatch"],
         "One/AlertPortlet": ["OMS/portlets/AlertPortletPatch"],
         "One/CurrencyConverterPanel": ["TMS/contract/CreateCurrencyConverterPanel"],
         "One/ext/alert/AlertDetail": [
           "MFG/AlertDetailPatch",
           "OMS/alert/AlertDetailPatch",
           "TMS/failedStaticRoute/AlertDetail",
           "RISK/alert/AlertDetailPatch"
         ],
         "One/ext/issue/IssueSubscription": [
           "SCC/common/HoldIssueSubscriptionPatch",
           "FISE/alert/IssueSubscriptionPatch",
           "WMS/issueSubscription/IssueSubscriptionPatch",
           "OMS/issueSubscription/IssueSubscriptionPatch",
           "TMS/issueSubscription/IssueSubscriptionPatch"
         ],
         "One/ext/issue/plugin/IssueSubscriptionReportPlugin": ["TMS/issueSubscription/IssueSubscriptionReportPluginPatch"],
         "One/ext/page/PageFlowDetail": ["DPX/AttachRate/AttachRateUdpPlugin"],
         "One/ext/plugin/CurrencyConverterReportPlugin": ["TMS/reports/CurrencyConverterReportPlugin"],
         "One/ext/plugin/SearchSiteReportInjectPlugin": ["TMS/reports/SearchSiteReportInjectPlugin"],
         "One/ext/report/plugin/WBProblemAnalyticalReportPlugin": ["TMS/WBProblemAnalyticalReportPluginPatch"],
         "One/ext/report/plugin/WBProblemAnalyticalViewPlugin": ["TMS/workbench/WBProblemAnalyticalViewPluginPatch"],
         "SCC/admin/AllServiceLevelsViewPlugin": ["TMS/serviceLevel/AllServiceLevelsViewPluginPatch"],
         "SCC/admin/BillOfMaterialsDetail": ["SNOP/BillOfMaterials/BillOfMaterialsDetailInjection"],
         "SCC/admin/BufferDetail": [
           "MFG/BufferDetailInjection",
           "MFG/BufferDetailPatch",
           "RPL/buffer/BufferDetailInjection",
           "IVP/buffer/BufferDetailInjection",
           "RDTS/BufferDetailInjection",
           "OMS/admin/BufferDetailInjection",
           "OMS/admin/BufferDetailPatch",
           "SCPT/patch/form/BufferDetailPatch"
         ],
         "SCC/admin/BufferLaneDetail": ["RPL/bufferLane/BufferLaneDetailInjection"],
         "SCC/admin/EnterpriseDetail": ["OMS/admin/EnterpriseDetailPatch"],
         "SCC/admin/EquipmentSettingsPanel": ["TMS/equipmentSettings/EquipmentSettingsPanelPatch"],
         "SCC/admin/HoldDetail": ["TMS/SCC/Hold/HoldDetail"],
         "SCC/admin/ItemDetail": [
           "DPX/Item/ItemDetailInjection",
           "MFG/admin/ItemDetailMfg",
           "RPL/item/ItemDetailInjection",
           "IVP/item/ItemDetailInjection",
           "OMS/admin/ItemDetailPatch",
           "TMS/admin/ItemDetailPatch",
           "RPTI/item/ItemDetailInjection"
         ],
         "SCC/admin/LandedCost": ["TMS/landedCost/LandedCost"],
         "SCC/admin/LeadTimeRefDetail": [
           "SCC/test/LeadTimeRefReg",
           "MFG/LeadTimeRefReg",
           "TMS/LeadTimeRef/LeadTimeRefReg",
           "AMS/LeadTimeRefReg"
         ],
         "SCC/admin/LocationDetailsPanel": ["WMS/patch/SCCLocationDetailsPatch"],
         "SCC/admin/OrganizationDetail": [
           "OMS/admin/OrganizationDetailPatch",
           "TMS/admin/OrganizationDetailPatch"
         ],
         "SCC/admin/PartnerDetail": [
           "OMS/admin/policy/PartnerDetailsPatch",
           "TMS/admin/PartnerDetailPatch",
           "SCPT/PartnerDetailPatch"
         ],
         "SCC/admin/PartnerGroupDetail": ["OMS/admin/PartnerGroupDetailPatch"],
         "SCC/admin/SingletonDetail": [
           "WMS/patch/SCCSingletonDetailsPatch",
           "WMS/singleton/SingletonDetailPatch",
           "TMS/singleton/SingletonDetailsPatch",
           "AMS/SingletonInjectionPatch"
         ],
         "SCC/admin/SiteDetail": [
           "DPX/Site/SiteDetailInjection",
           "MFG/admin/SiteDetailMfg",
           "RPL/site/SiteDetailInjection",
           "IVP/site/SiteDetailInjection",
           "OMS/admin/SiteDetailPatch",
           "TMS/site/AdministeredSiteDetails",
           "TMS/site/FreeStorageDaysSiteDetails",
           "TMS/site/SiteDetailPatch",
           "TMS/site/TmsSiteTransitTimeSpeed",
           "SCPT/SiteDetailPatch"
         ],
         "SCC/admin/SiteLaneDetail": ["RPL/siteLane/SiteLaneDetails"],
         "SCC/admin/SiteLaneMode": ["OMS/admin/SiteLaneModePatch"],
         "SCC/admin/StaticRouteDetailsPanel": ["TMS/staticRoute/StaticRouteDetailsPanelPatch"],
         "SCC/admin/StoreBufferDetail": [
           "MFG/admin/StoreBufferDetailMfg",
           "RPL/buffer/StoreBufferDetailInjection"
         ],
         "SCC/admin/TrackingEvent": ["TMS/trackingEventDetails/SCCTrackingEventDetailsPatch"],
         "SCC/admin/TrackingEventReportPlugin": ["TMS/admin/TrackingEventReportPluginPatch"],
         "SCC/admin/UserDetail": ["TMS/SCC/User/UserPanel"],
         "SCC/admin/milestone/MilestoneTypeConfigPanel": ["OMS/orderMilestone/MilestoneTypeConfigPanelPatch"],
         "SCC/admin/milestone/MilestoneTypeDetailPanel": [
           "SCC/test/MilestoneTypeReg",
           "MFG/reports/MilestoneTypeReg",
           "OMS/reports/MilestoneTypeReg",
           "TMS/milestone/MilestoneTypeReg",
           "AMS/MilestoneTypeReg",
           "RPTI/milestone/MilestoneTypeDetailPatch"
         ],
         "SCC/admin/milestone/MilestoneTypesReportPanel": ["OMS/purchaseRequisition/milestones/MilestoneTypesReportPanelPatch"],
         "SCC/admin/policy/PolicyReportPanel": [
           "OMS/admin/OrderPolicies",
           "AMS/workorders/WorkOrderPolicies",
           "SMRO/workorders/SMROWorkOrderPolicies"
         ],
         "SCC/admin/policy/PolicyValueReportsPlugin": ["OMS/admin/policy/PolicyValueDetailsPatch"],
         "SCC/common/BufferDetailBOHReportPlugin": [
           "OMS/reports/BufferDetailBOHReportPluginPatch",
           "SCPT/BufferDetailBOHReportPluginPatch"
         ],
         "SCC/common/BufferDetailReportPlugin": ["OMS/reports/BufferDetailReportPluginPatch"],
         "SCC/common/HoldPluginRegistry": [
           "OMS/enhancedOrder/report/OrderHoldPlugin",
           "TMS/onHoldPolicies/HoldListPlugin"
         ],
         "SCC/common/PartnerPlugin": ["OMS/common/PartnerReportPlugin"],
         "SCC/packingResource/PackingRequirementWindow": [
           "OMS/util/PackingRequirementsWindowPatch",
           "TMS/packingRequirements/PackingRequirementWindowPatch"
         ],
         "SCC/reports/AuthorizationActionListViewPlugin": ["OMS/purchaseRequisition/AuthorizationActionListViewPluginPatch"],
         "SCC/reports/BOMComponentReportsPlugin": ["QCAD/Reports/BOMComponentReportsPluginPatch"],
         "SCC/reports/BillOfMaterialsReportPlugin": ["AMS/SCCBillOfMaterialsReportPluginPatch"],
         "SCC/reports/ContactMappingPlugin": ["TMS/ContactMapping/ContactMappingPluginPatch"],
         "SCC/reports/SearchPartnerGroupPlugin": ["OMS/reports/SearchPartnerGroupPluginPatch"],
         "SCC/reports/SingletonReportPlugin": ["TMS/singleton/SingletonReportPluginPatch"],
         "SCC/reports/StaticRouteReportPlugin": ["TMS/reports/StaticRoutesReportInjectPlugin"],
         "SCC/reports/UpdateStaticRouteViewPlugin": ["TMS/reports/UpdateStaticRouteViewPluginPatch"],
         "SCH/appt/SchedulingWidget": ["TMS/SCH/appt/SchedulingWidget"],
         "TMS/shipmentDetails/ShipmentDetails": [
           "OMS/enhancedASN/BaseShipmentDetailsPatch",
           "SCPT/BaseShipmentDetailsPatch"
         ],
         "TMS/shipmentDetails/ShipmentDetails-Tracking": ["RPTI/ShipmentDetails-TrackingPatch"],
         "TMS/shipmentDetails/ShipmentLinesPopup": ["OMS/enhancedASN/ShipmentLinesPopupPatch"],
         "TMS/shipmentMilestone/UpdateShipmentMilestonesWindow": ["RPTI/milestone/UpdateShipmentMilestonesWindowPatch"],
         "WMS/ReceiptList/CrossDockComponent": ["SCPT/CrossDockComponentPatch"],
         "WMS/ReceiptList/ReceiptPanel": ["SCPT/ReceiptDetailPatch"],
         "WMS/issueSubscription/IssueSubscriptionConfigs": ["SCPT/issueSubscription/IssueSubscriptionFilterConfigs"],
         "WMS/reports/ReceiptListsReportPlugin": [
           "TMS/reports/ReceiptListsReportPluginPatch",
           "SCPT/ReceiptListsReportPluginPatch"
         ],
         "WMS/reports/WarehouseWorkloadSummaryReportPlugin": ["SCPT/WarehouseWorkloadSummaryReportPluginPatch"]
         },


           paths: {
             select2: '/oms/pixeladmin/libs/select2-3.4.5/select2.min'
             ,bootstrap: '/oms/bootstrap/js/bootstrap.min'
             ,'bootstrap-respond': '/oms/bootstrap/js/respond.min'
             ,'jquery-ui': '/oms/scripts/jquery-plugins/jquery-ui'
             ,'jquery-transform': '/oms/scripts/jquery-plugins/jquery-transform'

             ,'antlr': '/oms/scripts/antlr/antlr'
             ,'PlatformEL_JSLexer': '/oms/scripts/antlr/PlatformEL_JSLexer'
             ,'PlatformEL_JSParser': '/oms/scripts/antlr/PlatformEL_JSParser'

             ,'atmosphere': '/oms/scripts/atmosphere/atmosphere'
             ,'leaflet': '/oms/scripts/leaflet/leaflet'
             ,'zebra': '/oms/scripts/zebra/BrowserPrint'
             ,'osrm': '/oms/scripts/osrm/osrm'
             ,'osrm-text-instructions': '/oms/scripts/osrm-text-instructions/bundle'
             // This entry WILL BE REMOVED as it's not technically thrid-party
             ,'push': '/oms/scripts/push'

             // Set up ext js as a module to be loaded
             ,'extbasejs': '/oms/scripts/ext/adapter/ext/ext-base'
             ,'extjs': '/oms/scripts/ext/ext-all'

             // Set up instance js as a module to be requested

             ,'instancejs': '/oms/scripts/instance'

           },


           shim: {
             'select2': ['jquery']
             ,'bootstrap': ['jquery']
             ,'jquery-ui': ['jquery']
             ,'jquery-plugins/atmosphere': ['jquery']
             ,'jquery-plugins/bigtext': ['jquery']
             ,'jquery-plugins/easing': ['jquery']
             ,'jquery-plugins/jquery.file.upload': ['jquery']
             ,'jquery-plugins/jquery.transit': ['jquery']
             ,'jquery-plugins/jquery.ui.widget': ['jquery']
             ,'jquery-plugins/jquery.slimscroll': ['jquery']
             ,'bootstrap-respond': ['bootstrap']
             ,'atmosphere': ['jquery']
             // This entry WILL BE REMOVED as it's not technically third-party
             ,'push': ['atmosphere']
             ,'leaflet': ['jquery']
             ,'zebra': ['jquery']
             ,'osrm': ['jquery']

             // Set up instance js as a module to be requested
             // If there is an ept then instance js loads the ept with a dependency on the EPT
             // Otherwise, just load instance js with no dependencies

             ,'instancejs': ['extjs']

             ,'extjs': ['extbasejs']
           }
         }
      </script>
      <script src="../scripts/util/DateFormatConverter.js"></script>
      <script>
         var PlatformELFunctions = [

           {
             name: 'currentDate',
             fn: function(values) {
         if(values && values.length > 0){
           throw new Error('CurrentDate function doesn\'t support any arguments');
         }
         return new Date();
         }
           },

           {
             name: 'formatDate',
             fn: function(values) {
         if (values.length != 2) {
           throw new Error('Must supply exactly 2 arguments');
         }
         if (!['string', 'number'].includes(typeof values[0])) {
           throw new Error('The first argument must be a string or a number.')
         }
         if (typeof values[1] !== 'string') {
           throw new Error('The second argument must be a string.')
         }

         const outputDateFormat = new this.String(values[1]);

         // We're assuming that 'this' is the window calling this function
         // and relying on whatever is available to format the date.

         if (this.OneGlobalDeps && this.OneGlobalDeps.moment) {
           const convertedDateFormat = dateFormatConverter.javaToMoment(outputDateFormat);
           return this.OneGlobalDeps.moment(values[0]).format(convertedDateFormat);
         }

         let inputDate;
         if (typeof values[0] === 'number') {
           inputDate = new this.Date(values[0]);
         }
         else {
           // Parse the date string using ISO-8601 format
           const dateString = new this.String(values[0]);
           inputDate = this.Date.parseDate(dateString, 'c');
         }

         if (!inputDate) {
           throw new Error("The date '" + dateString + "' doesn't match the ISO-8601 format (ex: '2012-03-29T10:05:45-0600')");
         }

         // Generate a new date string using the output format
         const convertedDateFormat = dateFormatConverter.javaToExt(outputDateFormat);
         return inputDate.format(convertedDateFormat);
         }

           },

           {
             name: 'parseDate',
             fn: function(values) {
         if (values.length != 2) {
           throw new Error('Must supply exactly 2 arguments');
         }
         if ((typeof values[0]) != 'string') {
           throw new Error('The first argument must be a string.')
         }
         if ((typeof values[1]) != 'string') {
           throw new Error('The second argument must be a string.')
         }

         // We're assuming that 'this' is the window calling this function
         // and relying on whatever is available to parse the date.

         const dateString = new this.String(values[0]);
         const inputDateFormat = new this.String(values[1]);

         if (this.OneGlobalDeps && this.OneGlobalDeps.moment) {
           const convertedDateFormat = dateFormatConverter.javaToMoment(outputDateFormat);
           return this.OneGlobalDeps.moment(dateString, convertedDateFormat).format();
         }

         const convertedDateFormat = dateFormatConverter.javaToExt(inputDateFormat);

         // Parse the date string using the input format
         let inputDate = this.Date.parseDate(dateString, convertedDateFormat);
         if (!inputDate) {
           throw new Error("The date '" + dateString + "' doesn't match the format '" + convertedDateFormat + "' (converted from '" + inputDateFormat + "')");
         }

         // Default the year to 1970 if it's not present in the parsed format to conform
         // to how the server implementation behaves.
         if (convertedDateFormat.indexOf('y') < 0 && convertedDateFormat.indexOf('Y') < 0) {
           inputDate.setYear(1970);
         }

         // Generate a new date string using ISO-8601 format
         return inputDate.format('Y-m-d\\TH:i:sO');
         }

           },

           {
             name: 'and',
             fn: function(values) {
         if(!values || values.length < 2) {
           throw new Error('Requires two or more arguments');
         }

         for(var i = 0; i < values.length; i++) {
           if(typeof values[i] !== 'boolean') {
             throw new Error('All arguments to \'and\' must be true or false');
           }

           if (!values[i]) {
             return false;
           }
         }

         return true;
         }
           },

           {
             name: 'hasValue',
             fn: function(values) {
         if(!values || (values.length != 1)){
           throw new Error('Must supply exactly one argument');
         }

         var val = values[0];

         if (val != null && typeof val === 'string') {
           return ![ '$null', '$NULL$' ].includes(val) && val.trim().length > 0;
         }

         return val != null;
         }

           },

           {
             name: 'if',
             fn: function(values) {
         if(!values || (values.length != 3)){
           throw new Error('Must supply exactly three arguments');
         }
         var condition = values[0];
         if((typeof condition) == 'boolean'){
           return condition ? values[1] : values[2];
         }
         else{
           throw new Error('First argument must evaulate to \'true\' or \'false\'');
         }
         }
           },

           {
             name: 'in',
             fn: function(values) {
         if(!values || values.length < 2) {
           throw new Error('Requires two or more arguments');
         }
         var subject = values[0];
         var others = [];
         for(var i = 1; i < values.length; i++){
           others.push(values[i]);
         }
         return !(others.indexOf(subject) == -1);
         }
           },

           {
             name: 'inSet',
             fn: function(values) {
         if (values.length < 2 || values.length> 3) {
           throw new IllegalArgumentException("Requires 2 or 3 arguments");
         }

         let delimiter = '~';
         if(values.length === 3) {
           delimiter = values[2];
         }

         const valueToCheck = values[0];
         const setList = values[1];

         return setList.split(delimiter).includes(valueToCheck);
         }
           },

           {
             name: 'not',
             fn: function(values) {
         if(!values || values.length != 1) {
           throw new Error('Must supply exactly one argument');
         }
         var val = values[0];
         if(typeof val !== 'boolean') {
           throw new Error('Argument must evaulate to \'true\' or \'false\'');
         }
         return !val;
         }
           },

           {
             name: 'or',
             fn: function(values) {
         if(!values || values.length < 2) {
           throw new Error('Requires two or more arguments');
         }

         for(var i = 0; i < values.length; i++) {
           if(typeof values[i] !== 'boolean') {
             throw new Error('All arguments to \'or\' must be true or false');
           }

           if (values[i]) {
             return true;
           }
         }

         return false;
         }
           },

           {
             name: 'isAlpha',
             fn: function(values) {
         if (values.length < 1) {
           throw new Error("Require 1 argument");
         }

         const str = values[0];
         return /^[A-z]*$/.test(str);
         }

           },

           {
             name: 'isDigit',
             fn: function(values) {
         if (values.length < 1) {
           throw new Error("Require 1 argument");
         }

         const str = values[0];
         return /^[0-9]*$/.test(str);
         }

           },

           {
             name: 'isAlphaNum',
             fn: function(values) {
         if (values.length < 1) {
           throw new Error("Require 1 argument");
         }

         const str = values[0];
         return /^[A-z0-9]*$/.test(str);
         }

           },

           {
             name: 'any',
             fn: function(values) {
         for(var i = 0; i < values.length; i++) {
           if(typeof values[i] !== 'boolean') {
             throw new Error('All arguments to \'any\' must be true or false');
           }

           if (values[i]) {
             return true;
           }
         }

         return false;
         }

           },

           {
             name: 'all',
             fn: function(values) {
         if(values.length == 0) {
           return false;
         }

         for(var i = 0; i < values.length; i++) {
           if(typeof values[i] !== 'boolean') {
             throw new Error('All arguments to \'all\' must be true or false');
           }

           if (!values[i]) {
             return false;
           }
         }

         return true;
         }

           },

           {
             name: 'abs',
             fn: function(values) {
         if(values && (values.length > 1 || values.length < 1)){
           throw new Error('Must supply only one argument to absolute');
         }
         if(!values[0]){
           return null;
         }
         return Math.abs.apply(Math.abs, values);
         }
           },

           {
             name: 'max',
             fn: function(values) {
         if(!values || values.length < 1){
           throw new Error('Must supply at least one argument');
         }

         values = values.filter(function(value) {
           return value != null;
         });

         if (values.length === 0) {
           return null;
         }

         return Math.max.apply(Math.max, values);
         }
           },

           {
             name: 'min',
             fn: function(values) {
         if(!values || values.length < 1){
           throw new Error('Must supply at least one argument');
         }

         values = values.filter(function(value) {
           return value != null;
         });

         if (values.length === 0) {
           return null;
         }

         return Math.min.apply(Math.min, values);
         }
           },

           {
             name: 'round',
             fn: function(values) {
         if(!values || values.length != 1) {
           throw new Error('Must supply exactly one argument');
         }
         if(values[0] == null) {
           return null;
         }
         return Math.round.apply(Math.round, values);
         }
           },

           {
             name: 'ceil',
             fn: function(values) {
         if(!values || (values.length > 1 || values.length < 1)){
           throw new Error('Must supply only one argument to ceil function');
         }
         if(!values[0]){
           return null;
         }
         return Math.ceil.apply(Math.ceil, values);
         }
           },

           {
             name: 'floor',
             fn: function(values) {
         if(!values || (values.length > 1 || values.length < 1)){
           throw new Error('Must supply only one argument to floor function');
         }
         if(!values[0]){
           return null;
         }
         return Math.floor.apply(Math.floor, values);
         }
           },

           {
             name: 'sum',
             fn: function(values) {
         if(!values || values.length < 1) {
           throw new Error('Requires one or more arguments');
         }

         var sum = 0;
         for(var i = 0; i < values.length; i++) {
           if(typeof values[i] !== 'number') {
             throw new Error('All arguments to \'sum\' must be numeric');
           }

           sum += values[i];
         }

         return sum;
         }

           },

           {
             name: 'count',
             fn: function(values) {
         if(!values) {
           throw new Error('Requires a valid collection as input');
         }

         return values.length;
         }

           },

           {
             name: 'concat',
             fn: function(values) {
         if(!values || (values.length < 2)){
           throw new Error('Must supply at least two argument');
         }
         var result = '';
         for(var i = 0; i < values.length; i++) {
           result = result.concat(values[i]);
         }
         return result;
         }
           },

           {
             name: 'substring',
             fn: function(values) {
         if(!values || values.length !== 3) {
           throw new Error('Must supply exact three arguments.');
         }

         if(typeof values[0] !== 'string') {
           throw new Error('First argument must be of type String.');
         }

         if(typeof values[1] !== 'number' || typeof values[2] !== 'number') {
           throw new Error('Second and third argument must be numbers.');
         }

         const inputString = values[0];
         const beginIndex = values[1];
         const length = values[2];
         const endIndex = beginIndex + length;

         return inputString.substring(beginIndex - 1, Math.min(inputString.length, endIndex - 1));
         }
           },

           {
             name: 'to_number',
             fn: function(values) {
         var obj = values[0];
         var defaultValue = 0;
         if(values.length > 1) {
           var defaultVal = values[1];
           if((typeof defaultVal) != 'number'){
             throw new Error('Default value should be a number');
           }
           defaultValue = defaultVal;
         }
         if((typeof obj) == 'number') {
           return obj;
         }
         if(isNaN(obj)) {
           return defaultValue;
         }
         return Number.parseInt(obj);
         }
           },

           {
             name: 'indexOf',
             fn: function(values) {
         if(!values || values.length < 2) {
           throw new Error('Requires two or more arguments');
         }

         var stringToSearchFor = values[0].toUpperCase();
         var stringToSearchIn = values[1].toUpperCase();

         var idx = stringToSearchIn.indexOf(stringToSearchFor);

         return (idx == -1) ? -1 : idx + 1;
         }

           },

           {
             name: 'switch',
             fn: function(values) {
         if (values.length < 4) {
           throw new Error("Requires 4 or more arguments");
         }

         var answer = values[values.length - 1];
         for(var i = 1; i < values.length; i += 2) {
           if(values[0] === values[i]) {
             answer = values[i + 1];
             break;
           }
         }
         return answer;
         }
           },

           {
             name: 'len',
             fn: function(values) {
         if (values.length < 1) {
           throw new Error("Requires 1 argument");
         }
         return values[0].length;
         }
           },

         ];

         var PlatformELContextFunctions = {

           'TMS.ShipmentEvt': [

           {
             name: 'withinTimeBounds',
             fn: function(values) {
         if(!values || (values.length != 3)){
           throw new Error('Must supply exactly three arguments');
         }

         var val = values[0];

         if (val != null && typeof val === 'string') {
           return ![ '$null', '$NULL$' ].includes(val) && val.trim().length > 0;
         }

         return val != null;
         }

           },

           ],

         };

         function getPlatformELFunctionsInScopeForContext(context) {
           var contextFuncs = PlatformELContextFunctions[context];
           contextFuncs = contextFuncs || [];
           return PlatformELFunctions.concat(contextFuncs);
         }
      </script>
      <!-- Modifies the requirejs configuration to set up node libraries. -->
      <script>
         /*
          * DO NOT EDIT
          *
          * This file is automatically generated by web/nodelibs/src/buildlibs.js.
          */
         !function(){
           var r = require;
           r.paths['leaflet'] = '/oms/nodelibs/leaflet';
           r.paths['babel-polyfill'] = '/oms/nodelibs/babel-polyfill';
           r.paths['chalk'] = '/oms/nodelibs/chalk';
           r.paths['classnames'] = '/oms/nodelibs/classnames';
           r.paths['color'] = '/oms/nodelibs/color';
           r.paths['core-decorators'] = '/oms/nodelibs/core-decorators';
           r.paths['expect'] = '/oms/nodelibs/expect';
           r.paths['expect-jsx'] = '/oms/nodelibs/expect-jsx';
           r.paths['mobx'] = '/oms/nodelibs/mobx';
           r.paths['mobx-react'] = '/oms/nodelibs/mobx-react';
           r.paths['material-ui'] = '/oms/nodelibs/material-ui';
           r.paths['prismjs'] = '/oms/nodelibs/prismjs';
           r.paths['promise-polyfill'] = '/oms/nodelibs/promise-polyfill';
           r.paths['prop-types'] = '/oms/nodelibs/prop-types';
           r.paths['react'] = '/oms/nodelibs/react';
           r.paths['react-barcode'] = '/oms/nodelibs/react-barcode';
           r.paths['react-bootstrap'] = '/oms/nodelibs/react-bootstrap';
           r.paths['react-dimensions'] = '/oms/nodelibs/react-dimensions';
           r.paths['react-dnd'] = '/oms/nodelibs/react-dnd';
           r.paths['react-dnd-html5-backend'] = '/oms/nodelibs/react-dnd-html5-backend';
           r.paths['react-dnd-touch-backend'] = '/oms/nodelibs/react-dnd-touch-backend';
           r.paths['react-leaflet'] = '/oms/nodelibs/react-leaflet';
           r.paths['react-leaflet-marker-layer'] = '/oms/nodelibs/react-leaflet-marker-layer';
           r.paths['react-scrollbar'] = '/oms/nodelibs/react-scrollbar';
           r.paths['recharts'] = '/oms/nodelibs/recharts';
           r.paths['whatwg-fetch'] = '/oms/nodelibs/whatwg-fetch';
           r.paths['js-string-escape'] = '/oms/nodelibs/js-string-escape';

           r.shim['expect-jsx'] = ["react","expect"];
           r.shim['mobx-react'] = ["mobx","react"];
           r.shim['material-ui'] = ["react","prop-types"];
           r.shim['react-barcode'] = ["react"];
           r.shim['react-bootstrap'] = ["classnames","react","prop-types"];
           r.shim['react-dimensions'] = ["react"];
           r.shim['react-dnd'] = ["react"];
           r.shim['react-leaflet'] = ["leaflet","react"];
           r.shim['react-leaflet-marker-layer'] = ["react","leaflet","react-leaflet"];
           r.shim['react-scrollbar'] = ["react"];
           r.shim['recharts'] = ["react","classnames"];
         }()
      </script>
      <!-- Include require js itself -->
      <script src="/oms/scripts/requirejs/require.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <script src="/oms/scripts/SimulatedToday.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <title>NEO Desktop</title>
      <!-- Provides a document ready base function docReady() -->
      <script>
         (function(funcName, baseObj) {
             // The public function name defaults to window.docReady
             // but you can pass in your own object and own function name and those will be used
             // if you want to put them in a different namespace
             funcName = funcName || "docReady";
             baseObj = baseObj || window;
             var readyList = [];
             var readyFired = false;
             var readyEventHandlersInstalled = false;

             // call this when the document is ready
             // this function protects itself against being called more than once
             function ready() {
                 if (!readyFired) {
                     // this must be set to true before we start calling callbacks
                     readyFired = true;
                     for (var i = 0; i < readyList.length; i++) {
                         // if a callback here happens to add new ready handlers,
                         // the docReady() function will see that it already fired
                         // and will schedule the callback to run right after
                         // this event loop finishes so all handlers will still execute
                         // in order and no new ones will be added to the readyList
                         // while we are processing the list
                         readyList[i].fn.call(window, readyList[i].ctx);
                     }
                     // allow any closures held by these functions to free
                     readyList = [];
                 }
             }

             function readyStateChange() {
                 if ( document.readyState === "complete" ) {
                     ready();
                 }
             }

             // This is the one public interface
             // docReady(fn, context);
             // the context argument is optional - if present, it will be passed
             // as an argument to the callback
             baseObj[funcName] = function(callback, context) {
                 // if ready has already fired, then just schedule the callback
                 // to fire asynchronously, but right away
                 if (readyFired) {
                     setTimeout(function() {callback(context);}, 1);
                     return;
                 } else {
                     // add the function and context to the list
                     readyList.push({fn: callback, ctx: context});
                 }
                 // if document already ready to go, schedule the ready function to run
                 if (document.readyState === "complete") {
                     setTimeout(ready, 1);
                 } else if (!readyEventHandlersInstalled) {
                     // otherwise if we don't have event handlers installed, install them
                     if (document.addEventListener) {
                         // first choice is DOMContentLoaded event
                         document.addEventListener("DOMContentLoaded", ready, false);
                         // backup is window load event
                         window.addEventListener("load", ready, false);
                     } else {
                         // must be IE
                         document.attachEvent("onreadystatechange", readyStateChange);
                         window.attachEvent("onload", ready);
                     }
                     readyEventHandlersInstalled = true;
                 }
             }
         })("docReady", window);
      </script>
      <!-- This holds the standard ONE way of handling session time outs -->
      <!-- This makes all query params available in JS via urlQueryParams[<param name>] -->
      <script>
         var urlQueryParams;
         (window.onpopstate = function () {
           var match,
             pl     = /\+/g,  // Regex for replacing addition symbol with a space
             search = /([^&=]+)=?([^&]*)/g,
             decode = function (s) { return decodeURIComponent(s.replace(pl, " ")); },
             query  = window.location.search.substring(1);

           // use Object.create(null) to prevent prototype injection .. see https://www.whitesourcesoftware.com/resources/blog/prototype-pollution-vulnerabilities/
           urlQueryParams = Object.create(null);
           while (match = search.exec(query))
             urlQueryParams[decode(match[1])] = decode(match[2]);

           if (window.frameElement && window.frameElement.getPanel) {
             var desktop = window.top.desktop;
             // This handling is needed when you open webaction/panel in a
             // separate browser window (PLT-26540)
             if (!desktop && window.top.opener && window.top.opener.top) {
               desktop = window.top.opener.top.desktop;
             }
             var params = desktop.getIFrameParams(window.frameElement.getPanel());
             for (key in params) {
               urlQueryParams[key] = params[key]
             }
           }

           console.log("QUERY PARAMS:", urlQueryParams);
           window.onpopstate = null;
         })();
      </script>
      <script>
         /**
          * A simple utility that allows toggling of options which help developers
          * quickly debug the UI without having to set breakpoints.
          */
         window.top.Debug = {
           // ---------------------------------------------------------------------
           //
           //   Debug options
           //
           // ---------------------------------------------------------------------

           logModuleLoading: {
             enabled: false,
             description: "Enables logging for React UIs and widgets that have been dynamically loaded (this includes React pages opened from the Neo desktop menus)."
           },

           keepPopupsOpen: {
             enabled: false,
             description: "Prevents a popup (such as a Tooltip, Menu, or a field's dropdown list) from being automatically hidden."
           },


           // ---------------------------------------------------------------------
           //
           //   Helper functions
           //
           // ---------------------------------------------------------------------

           toggle: function(opt) {
             if (!this.isValidOption(opt)) {
               throw new Error("Debug option '" + opt + "' isn't valid or doesn't exist");
             }

             this[opt].enabled = !this[opt].enabled;
             console.info("Debug option '" + opt + "' set to: " + (this[opt].enabled ? 'enabled' : 'disabled'));
           },

           help: function() {
             console.info('window.top.Debug:');
             console.info('------------------------------------');
             console.info('Helper functions:');
             console.info(' ');
             console.info('  help(): Prints this help message.');
             console.info('  toggle(optionName): Toggles the provided option from true to false or vice versa.');
             console.info('------------------------------------');
             console.info('Available options:');
             for (var opt in this) {
               if (!this.isValidOption(opt)) {
                 continue;
               }

               console.info(' ');
               console.info('  ' + opt + (this[opt].enabled ? ' - enabled' : ''));
               console.info('  ' + this[opt].description);
             }
             console.info('------------------------------------');
           },

           isEnabled: function(opt) {
             return this.isValidOption(opt) && this[opt].enabled;
           },

           isValidOption: function(opt) {
             return this.hasOwnProperty(opt) && Object.prototype.toString.call(this[opt]) === '[object Object]' && this[opt].hasOwnProperty('enabled');
           }
         };
      </script>
      <!-- Including OneCore -->
      <script src="/oms/scripts/OneCore.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <script src="/oms/scripts/util/Url.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <script src="/oms/scripts/util/SimpleMetrics.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <script src="/oms/scripts/mods/MinimalDateMods.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <script src="/oms/scripts/framework/DialogManager.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <link rel="stylesheet" type="text/css" href="/oms/font-awesome/css/font-awesome.css?cachekey=6beffd669779ee639b9bc6ad8ec837b0">
      <link rel="stylesheet" type="text/css" href="/oms/css/react/OneReactFramework.css?cachekey=6beffd669779ee639b9bc6ad8ec837b0">
      <!-- global react registry used by modules -->
      <script src="/oms/scripts/GlobalReactRegistry.js?cachekey=6beffd669779ee639b9bc6ad8ec837b0"></script>
      <!-- ================== LOADING GlobalLabels and Resources ============================== -->
      <script type="text/javascript">
         GlobalLabels = undefined;
         Resources = undefined;
         GlobalInitPromise = undefined;

         (() => {
          GlobalInitPromise = Promise.all([
            fetch('/oms/rest/i18n/labels/163438/en?md5=f7110189031979ed94fefc13e49b581f')
              .then(r => r.json())
              .then(labels => GlobalLabels = labels),
            fetch('/oms/rest/date/transition-info/en?md5=8b75314e94f27bef4b138b515f5c8141')
              .then(r => r.json())
              .then(resources => Resources = resources),
          ]);
         })();
      </script>
      <!-- ================== DONE LOADING GlobalLabels and Resources ========================= -->
      <script type="text/javascript">
         //GlobalCacheKey for DynamicComponent to avoid cache problems
         window.GlobalCacheKey = 'cachekey=6beffd669779ee639b9bc6ad8ec837b0';
         window.One = window.One || {}; //Some scripts store various global properties in this object

         // ================== LOADING Desktop ===================================


         Desktop = {
           version: 2,
           sessionCheckInterval: 300000,

           platformVersion: "3.10.1262813",
           ESG: {
             enabled: true,
             url: 'https://esg.onenetwork.com',
             widgetVersion: '1.14',
         embedded: false
           },
           inDemoMode: false,
           userAvatar: true,
           maxCustomDashboardCount: 5,
           ccmCreationEnabled: false,
         disableAutoRefreshOnWidgets: false,
          disableAutoRefreshOnWidgets: false,
         disableAutoRefreshOnWidgets: false,
         defaultOrEntTimezonePrefs: false,
           isNeo: true,
           perspectiveBrowserHistoryEnabled: true,
           suppressBrowserMessage: false,
           stage : 'Prod',
           isIdPEnabled: false,
           reactRootVersion: 1,
           plugins: ["PLT/Plugins/InteractiveSimulatorPlugin","SCC/Plugins/NeoDesktop","DPX/NeoDesktop/DashboardFilterContextPlugin","RPL/Plugins/NeoDesktopPlugin","FISE/NeoDesktop/JobStatusPlugin","FISE/NeoDesktop/DashboardFilterContextPlugin","SNOP/NeoDesktop/DashboardFilterContextPlugin","OMS/Plugins/NeoDesktop","TMS/Plugins/NeoDesktop","TMS/NeoDesktop/GlobalSearchPlugin","SCPT/NeoDesktop/GlobalSearchPlugin"],
           hasNeoUserBotPerm: false,
           factsAvailableAgainstSubscribedFeatures: false,
           plasmaEnabled: true,
           neoAssistantLlmType: 'None',
           contextAutoReloadInterval: 60,
           keepAlivePollInterval: 60

           ,JasperServer: {
               url: "https://jrs9001.onenetwork.com/one"
               ,logoutURI: "/exituser.html"
           }

         };


         GlobalAttachmentsWhitelists = '';
         CurrencyCodeForUI = 'USD';
         SetDefaultCurrencyCodeForUI = false;
         IgnorePreferredCountryForUI = false;
         // ================== DONE LOADING Desktop ==============================

         <!-- ================== LOADING GlobalKeys ====================================================== -->
          top.GOOGLE_MAPS_JS_API_KEY = '';
          top.FLEXMONSTER_API_KEY = 'Z7WT-XICC1C-2S210F-284R0E-6M5Y06-16583A-70673I-336537-2E5O07-2L3J1Z-2D0G';
          top.PCMILER_MAPS_JS_API_KEY = 'null';
         <!-- ================== DONE LOADING GlobalKeys ================================================= -->


         // ================== LOADING MapServiceConfig ==========================

         MapServiceConfig = {
           tileServer: 'https://api.maptiler.com/maps/basic/256/{z}/{x}/{y}.png?key=TbDRGPq0GrAx6iVgAHBi',
           geocodingServer: ''
         };
         PerspectiveManagerConfig = {
           frequentlyVisitedThreshold: '20',
           frequentlyVisitedMaxList: '10'
         }

         // ================== DONE LOADING MapServiceConfig =====================

         CSRF = {
           enabled: false
         };

             top['chatPollingSequence'] = '5,10,15,30,60'.split(",");

           top['legacyReportDownloadEnabled'] = false
           top['notificationIntervalInSec'] = 30

         // ================== LOADING OnePushServer =============================

         var isPushServerEnabledGlobally = false;
         OnePushServer = {
           enabled: isPushServerEnabledGlobally,
           url: 'https://push.onenetwork.com:9030/push-server',
           configParams: {}
         };
         // ================== DONE LOADING OnePushServer ========================

         // ================== LOADING OneSsoServer ==============================

         OneSsoServer = {
           url: 'https://logon.onenetwork.com:443/sso'
         };
         // ================== DONE LOADING OneSsoServer =========================

         // ================== LOADING GlobalAddressCache ========================


         GlobalAddressCache = {"metadata":{"isUSZipValidationDisabled":true},"CN":{"country":"CN","addressComponentDef":{"ZIP":{"optional":true},"LONGITUDE":{"optional":true},"SUB_CITY":{"optional":true},"STATE":{"optional":false},"STREET1":{"optional":false},"ADDRESS_ENT":{"optional":true},"STREET2":{"optional":true},"ADDRESS_SITE":{"optional":true},"STREET3":{"optional":true},"TIME_ZONE":{"optional":true},"CITY":{"optional":false},"ADDRESS_ORG":{"optional":true},"LATITUDE":{"optional":true}},"addressDisplayDef":{"addressDisplayLines":[[{"type":"ADDRESS_ENT","suffix":", "},{"type":"ADDRESS_ORG","suffix":", "},{"type":"ADDRESS_SITE"}],[{"type":"STREET1"}],[{"type":"STREET2"}],[{"type":"STREET3"}],[{"type":"CITY","suffix":", "},{"type":"SUB_CITY"}],[{"type":"STATE","suffix":"  "},{"type":"ZIP"}],[{"type":"COUNTRY"}],[{"type":"LATITUDE","suffix":"lat"},{"type":"LONGITUDE","suffix":" lon"}],[{"type":"TIME_ZONE"}]]}},"US":{"country":"US","addressComponentDef":{"ADDRESS_SITE":{"optional":true},"STREET3":{"optional":true},"ZIP":{"optional":false},"TIME_ZONE":{"optional":true},"CITY":{"optional":false},"ADDRESS_ORG":{"optional":true},"LONGITUDE":{"optional":true},"STATE":{"optional":false},"LATITUDE":{"optional":true},"STREET1":{"optional":false},"ADDRESS_ENT":{"optional":true},"STREET2":{"optional":true}},"addressDisplayDef":{"addressDisplayLines":[[{"type":"ADDRESS_ENT","suffix":", "},{"type":"ADDRESS_ORG","suffix":", "},{"type":"ADDRESS_SITE"}],[{"type":"STREET1"}],[{"type":"STREET2"}],[{"type":"STREET3"}],[{"type":"CITY","suffix":", "},{"type":"STATE","suffix":"  "},{"type":"ZIP"}],[{"type":"COUNTRY"}],[{"type":"LATITUDE","suffix":"lat"},{"type":"LONGITUDE","suffix":" lon"}],[{"type":"TIME_ZONE"}]]}},"CA":{"country":"CA","addressComponentDef":{"ADDRESS_SITE":{"optional":true},"STREET3":{"optional":true},"ZIP":{"optional":false},"TIME_ZONE":{"optional":true},"CITY":{"optional":false},"ADDRESS_ORG":{"optional":true},"LONGITUDE":{"optional":true},"STATE":{"optional":false},"LATITUDE":{"optional":true},"STREET1":{"optional":false},"ADDRESS_ENT":{"optional":true},"STREET2":{"optional":true}},"addressDisplayDef":{"addressDisplayLines":[[{"type":"ADDRESS_ENT","suffix":", "},{"type":"ADDRESS_ORG","suffix":", "},{"type":"ADDRESS_SITE"}],[{"type":"STREET1"}],[{"type":"STREET2"}],[{"type":"STREET3"}],[{"type":"CITY","suffix":", "},{"type":"STATE","suffix":"  "},{"type":"ZIP"}],[{"type":"COUNTRY"}],[{"type":"LATITUDE","suffix":"lat"},{"type":"LONGITUDE","suffix":" lon"}],[{"type":"TIME_ZONE"}]]}}};

         // ================== DONE LOADING GlobalAddressCache ===================

         // ================== LOADING GlobalEnumCache ===========================

         GlobalEnumCache = {"OMS.ContractType":{"enumId":"OMS.ContractType","success":true,"items":[{"display":"Blanket","hidingEnts":[],"value":"Blanket","hidingOrgs":[]},{"display":"Sales Contract","hidingEnts":[],"value":"Sales Contract","hidingOrgs":[]},{"display":"Standard","hidingEnts":[],"value":"Standard","hidingOrgs":[]}]},"CurrencyCode":{"enumId":"CurrencyCode","success":true,"items":[{"display":"USD","hidingEnts":[],"value":"USD","hidingOrgs":[]},{"display":"AED","hidingEnts":[],"value":"AED","hidingOrgs":[]},{"display":"AFN","hidingEnts":[],"value":"AFN","hidingOrgs":[]},{"display":"ALL","hidingEnts":[],"value":"ALL","hidingOrgs":[]},{"display":"AMD","hidingEnts":[],"value":"AMD","hidingOrgs":[]},{"display":"ANG","hidingEnts":[],"value":"ANG","hidingOrgs":[]},{"display":"AOA","hidingEnts":[],"value":"AOA","hidingOrgs":[]},{"display":"ARS","hidingEnts":[],"value":"ARS","hidingOrgs":[]},{"display":"AUD","hidingEnts":[],"value":"AUD","hidingOrgs":[]},{"display":"AWG","hidingEnts":[],"value":"AWG","hidingOrgs":[]},{"display":"AZM","hidingEnts":[],"value":"AZM","hidingOrgs":[]},{"display":"AZN","hidingEnts":[],"value":"AZN","hidingOrgs":[]},{"display":"BAM","hidingEnts":[],"value":"BAM","hidingOrgs":[]},{"display":"BBD","hidingEnts":[],"value":"BBD","hidingOrgs":[]},{"display":"BDT","hidingEnts":[],"value":"BDT","hidingOrgs":[]},{"display":"BGN","hidingEnts":[],"value":"BGN","hidingOrgs":[]},{"display":"BHD","hidingEnts":[],"value":"BHD","hidingOrgs":[]},{"display":"BIF","hidingEnts":[],"value":"BIF","hidingOrgs":[]},{"display":"BMD","hidingEnts":[],"value":"BMD","hidingOrgs":[]},{"display":"BND","hidingEnts":[],"value":"BND","hidingOrgs":[]},{"display":"BOB","hidingEnts":[],"value":"BOB","hidingOrgs":[]},{"display":"BOV","hidingEnts":[],"value":"BOV","hidingOrgs":[]},{"display":"BRL","hidingEnts":[],"value":"BRL","hidingOrgs":[]},{"display":"BSD","hidingEnts":[],"value":"BSD","hidingOrgs":[]},{"display":"BTN","hidingEnts":[],"value":"BTN","hidingOrgs":[]},{"display":"BWP","hidingEnts":[],"value":"BWP","hidingOrgs":[]},{"display":"BYN","hidingEnts":[],"value":"BYN","hidingOrgs":[]},{"display":"BYR","hidingEnts":[],"value":"BYR","hidingOrgs":[]},{"display":"BZD","hidingEnts":[],"value":"BZD","hidingOrgs":[]},{"display":"CAD","hidingEnts":[],"value":"CAD","hidingOrgs":[]},{"display":"CDF","hidingEnts":[],"value":"CDF","hidingOrgs":[]},{"display":"CHE","hidingEnts":[],"value":"CHE","hidingOrgs":[]},{"display":"CHF","hidingEnts":[],"value":"CHF","hidingOrgs":[]},{"display":"CHW","hidingEnts":[],"value":"CHW","hidingOrgs":[]},{"display":"CLF","hidingEnts":[],"value":"CLF","hidingOrgs":[]},{"display":"CLP","hidingEnts":[],"value":"CLP","hidingOrgs":[]},{"display":"CNY","hidingEnts":[],"value":"CNY","hidingOrgs":[]},{"display":"COP","hidingEnts":[],"value":"COP","hidingOrgs":[]},{"display":"COU","hidingEnts":[],"value":"COU","hidingOrgs":[]},{"display":"CRC","hidingEnts":[],"value":"CRC","hidingOrgs":[]},{"display":"CSD","hidingEnts":[],"value":"CSD","hidingOrgs":[]},{"display":"CUC","hidingEnts":[],"value":"CUC","hidingOrgs":[]},{"display":"CUP","hidingEnts":[],"value":"CUP","hidingOrgs":[]},{"display":"CVE","hidingEnts":[],"value":"CVE","hidingOrgs":[]},{"display":"CYP","hidingEnts":[],"value":"CYP","hidingOrgs":[]},{"display":"CZK","hidingEnts":[],"value":"CZK","hidingOrgs":[]},{"display":"DJF","hidingEnts":[],"value":"DJF","hidingOrgs":[]},{"display":"DKK","hidingEnts":[],"value":"DKK","hidingOrgs":[]},{"display":"DOP","hidingEnts":[],"value":"DOP","hidingOrgs":[]},{"display":"DZD","hidingEnts":[],"value":"DZD","hidingOrgs":[]},{"display":"EEK","hidingEnts":[],"value":"EEK","hidingOrgs":[]},{"display":"EGP","hidingEnts":[],"value":"EGP","hidingOrgs":[]},{"display":"ERN","hidingEnts":[],"value":"ERN","hidingOrgs":[]},{"display":"ETB","hidingEnts":[],"value":"ETB","hidingOrgs":[]},{"display":"EUR","hidingEnts":[],"value":"EUR","hidingOrgs":[]},{"display":"FJD","hidingEnts":[],"value":"FJD","hidingOrgs":[]},{"display":"FKP","hidingEnts":[],"value":"FKP","hidingOrgs":[]},{"display":"GBP","hidingEnts":[],"value":"GBP","hidingOrgs":[]},{"display":"GEL","hidingEnts":[],"value":"GEL","hidingOrgs":[]},{"display":"GHC","hidingEnts":[],"value":"GHC","hidingOrgs":[]},{"display":"GHS","hidingEnts":[],"value":"GHS","hidingOrgs":[]},{"display":"GIP","hidingEnts":[],"value":"GIP","hidingOrgs":[]},{"display":"GMD","hidingEnts":[],"value":"GMD","hidingOrgs":[]},{"display":"GNF","hidingEnts":[],"value":"GNF","hidingOrgs":[]},{"display":"GTQ","hidingEnts":[],"value":"GTQ","hidingOrgs":[]},{"display":"GWP","hidingEnts":[],"value":"GWP","hidingOrgs":[]},{"display":"GYD","hidingEnts":[],"value":"GYD","hidingOrgs":[]},{"display":"HKD","hidingEnts":[],"value":"HKD","hidingOrgs":[]},{"display":"HNL","hidingEnts":[],"value":"HNL","hidingOrgs":[]},{"display":"HRK","hidingEnts":[],"value":"HRK","hidingOrgs":[]},{"display":"HTG","hidingEnts":[],"value":"HTG","hidingOrgs":[]},{"display":"HUF","hidingEnts":[],"value":"HUF","hidingOrgs":[]},{"display":"IDR","hidingEnts":[],"value":"IDR","hidingOrgs":[]},{"display":"ILS","hidingEnts":[],"value":"ILS","hidingOrgs":[]},{"display":"INR","hidingEnts":[],"value":"INR","hidingOrgs":[]},{"display":"IQD","hidingEnts":[],"value":"IQD","hidingOrgs":[]},{"display":"IRR","hidingEnts":[],"value":"IRR","hidingOrgs":[]},{"display":"ISK","hidingEnts":[],"value":"ISK","hidingOrgs":[]},{"display":"JMD","hidingEnts":[],"value":"JMD","hidingOrgs":[]},{"display":"JOD","hidingEnts":[],"value":"JOD","hidingOrgs":[]},{"display":"JPY","hidingEnts":[],"value":"JPY","hidingOrgs":[]},{"display":"KES","hidingEnts":[],"value":"KES","hidingOrgs":[]},{"display":"KGS","hidingEnts":[],"value":"KGS","hidingOrgs":[]},{"display":"KHR","hidingEnts":[],"value":"KHR","hidingOrgs":[]},{"display":"KMF","hidingEnts":[],"value":"KMF","hidingOrgs":[]},{"display":"KPW","hidingEnts":[],"value":"KPW","hidingOrgs":[]},{"display":"KRW","hidingEnts":[],"value":"KRW","hidingOrgs":[]},{"display":"KWD","hidingEnts":[],"value":"KWD","hidingOrgs":[]},{"display":"KYD","hidingEnts":[],"value":"KYD","hidingOrgs":[]},{"display":"KZT","hidingEnts":[],"value":"KZT","hidingOrgs":[]},{"display":"LAK","hidingEnts":[],"value":"LAK","hidingOrgs":[]},{"display":"LBP","hidingEnts":[],"value":"LBP","hidingOrgs":[]},{"display":"LKR","hidingEnts":[],"value":"LKR","hidingOrgs":[]},{"display":"LRD","hidingEnts":[],"value":"LRD","hidingOrgs":[]},{"display":"LSL","hidingEnts":[],"value":"LSL","hidingOrgs":[]},{"display":"LTL","hidingEnts":[],"value":"LTL","hidingOrgs":[]},{"display":"LVL","hidingEnts":[],"value":"LVL","hidingOrgs":[]},{"display":"LYD","hidingEnts":[],"value":"LYD","hidingOrgs":[]},{"display":"MAD","hidingEnts":[],"value":"MAD","hidingOrgs":[]},{"display":"MDL","hidingEnts":[],"value":"MDL","hidingOrgs":[]},{"display":"MGA","hidingEnts":[],"value":"MGA","hidingOrgs":[]},{"display":"MGF","hidingEnts":[],"value":"MGF","hidingOrgs":[]},{"display":"MKD","hidingEnts":[],"value":"MKD","hidingOrgs":[]},{"display":"MMK","hidingEnts":[],"value":"MMK","hidingOrgs":[]},{"display":"MNT","hidingEnts":[],"value":"MNT","hidingOrgs":[]},{"display":"MOP","hidingEnts":[],"value":"MOP","hidingOrgs":[]},{"display":"MRO","hidingEnts":[],"value":"MRO","hidingOrgs":[]},{"display":"MRU","hidingEnts":[],"value":"MRU","hidingOrgs":[]},{"display":"MTL","hidingEnts":[],"value":"MTL","hidingOrgs":[]},{"display":"MUR","hidingEnts":[],"value":"MUR","hidingOrgs":[]},{"display":"MVR","hidingEnts":[],"value":"MVR","hidingOrgs":[]},{"display":"MWK","hidingEnts":[],"value":"MWK","hidingOrgs":[]},{"display":"MXN","hidingEnts":[],"value":"MXN","hidingOrgs":[]},{"display":"MXV","hidingEnts":[],"value":"MXV","hidingOrgs":[]},{"display":"MYR","hidingEnts":[],"value":"MYR","hidingOrgs":[]},{"display":"MZM","hidingEnts":[],"value":"MZM","hidingOrgs":[]},{"display":"MZN","hidingEnts":[],"value":"MZN","hidingOrgs":[]},{"display":"NAD","hidingEnts":[],"value":"NAD","hidingOrgs":[]},{"display":"NGN","hidingEnts":[],"value":"NGN","hidingOrgs":[]},{"display":"NIO","hidingEnts":[],"value":"NIO","hidingOrgs":[]},{"display":"NOK","hidingEnts":[],"value":"NOK","hidingOrgs":[]},{"display":"NPR","hidingEnts":[],"value":"NPR","hidingOrgs":[]},{"display":"NZD","hidingEnts":[],"value":"NZD","hidingOrgs":[]},{"display":"OMR","hidingEnts":[],"value":"OMR","hidingOrgs":[]},{"display":"PAB","hidingEnts":[],"value":"PAB","hidingOrgs":[]},{"display":"PEN","hidingEnts":[],"value":"PEN","hidingOrgs":[]},{"display":"PGK","hidingEnts":[],"value":"PGK","hidingOrgs":[]},{"display":"PHP","hidingEnts":[],"value":"PHP","hidingOrgs":[]},{"display":"PKR","hidingEnts":[],"value":"PKR","hidingOrgs":[]},{"display":"PLN","hidingEnts":[],"value":"PLN","hidingOrgs":[]},{"display":"PYG","hidingEnts":[],"value":"PYG","hidingOrgs":[]},{"display":"QAR","hidingEnts":[],"value":"QAR","hidingOrgs":[]},{"display":"ROL","hidingEnts":[],"value":"ROL","hidingOrgs":[]},{"display":"RON","hidingEnts":[],"value":"RON","hidingOrgs":[]},{"display":"RSD","hidingEnts":[],"value":"RSD","hidingOrgs":[]},{"display":"RUB","hidingEnts":[],"value":"RUB","hidingOrgs":[]},{"display":"RUR","hidingEnts":[],"value":"RUR","hidingOrgs":[]},{"display":"RWF","hidingEnts":[],"value":"RWF","hidingOrgs":[]},{"display":"SAR","hidingEnts":[],"value":"SAR","hidingOrgs":[]},{"display":"SBD","hidingEnts":[],"value":"SBD","hidingOrgs":[]},{"display":"SCR","hidingEnts":[],"value":"SCR","hidingOrgs":[]},{"display":"SDD","hidingEnts":[],"value":"SDD","hidingOrgs":[]},{"display":"SDG","hidingEnts":[],"value":"SDG","hidingOrgs":[]},{"display":"SEK","hidingEnts":[],"value":"SEK","hidingOrgs":[]},{"display":"SGD","hidingEnts":[],"value":"SGD","hidingOrgs":[]},{"display":"SHP","hidingEnts":[],"value":"SHP","hidingOrgs":[]},{"display":"SIT","hidingEnts":[],"value":"SIT","hidingOrgs":[]},{"display":"SKK","hidingEnts":[],"value":"SKK","hidingOrgs":[]},{"display":"SLE","hidingEnts":[],"value":"SLE","hidingOrgs":[]},{"display":"SLL","hidingEnts":[],"value":"SLL","hidingOrgs":[]},{"display":"SOS","hidingEnts":[],"value":"SOS","hidingOrgs":[]},{"display":"SRD","hidingEnts":[],"value":"SRD","hidingOrgs":[]},{"display":"SSP","hidingEnts":[],"value":"SSP","hidingOrgs":[]},{"display":"STD","hidingEnts":[],"value":"STD","hidingOrgs":[]},{"display":"STN","hidingEnts":[],"value":"STN","hidingOrgs":[]},{"display":"SVC","hidingEnts":[],"value":"SVC","hidingOrgs":[]},{"display":"SYP","hidingEnts":[],"value":"SYP","hidingOrgs":[]},{"display":"SZL","hidingEnts":[],"value":"SZL","hidingOrgs":[]},{"display":"THB","hidingEnts":[],"value":"THB","hidingOrgs":[]},{"display":"TJS","hidingEnts":[],"value":"TJS","hidingOrgs":[]},{"display":"TMM","hidingEnts":[],"value":"TMM","hidingOrgs":[]},{"display":"TMT","hidingEnts":[],"value":"TMT","hidingOrgs":[]},{"display":"TND","hidingEnts":[],"value":"TND","hidingOrgs":[]},{"display":"TOP","hidingEnts":[],"value":"TOP","hidingOrgs":[]},{"display":"TRL","hidingEnts":[],"value":"TRL","hidingOrgs":[]},{"display":"TRY","hidingEnts":[],"value":"TRY","hidingOrgs":[]},{"display":"TTD","hidingEnts":[],"value":"TTD","hidingOrgs":[]},{"display":"TWD","hidingEnts":[],"value":"TWD","hidingOrgs":[]},{"display":"TZS","hidingEnts":[],"value":"TZS","hidingOrgs":[]},{"display":"UAH","hidingEnts":[],"value":"UAH","hidingOrgs":[]},{"display":"UGX","hidingEnts":[],"value":"UGX","hidingOrgs":[]},{"display":"USN","hidingEnts":[],"value":"USN","hidingOrgs":[]},{"display":"USS","hidingEnts":[],"value":"USS","hidingOrgs":[]},{"display":"UYI","hidingEnts":[],"value":"UYI","hidingOrgs":[]},{"display":"UYU","hidingEnts":[],"value":"UYU","hidingOrgs":[]},{"display":"UZS","hidingEnts":[],"value":"UZS","hidingOrgs":[]},{"display":"VEB","hidingEnts":[],"value":"VEB","hidingOrgs":[]},{"display":"VEF","hidingEnts":[],"value":"VEF","hidingOrgs":[]},{"display":"VES","hidingEnts":[],"value":"VES","hidingOrgs":[]},{"display":"VND","hidingEnts":[],"value":"VND","hidingOrgs":[]},{"display":"VUV","hidingEnts":[],"value":"VUV","hidingOrgs":[]},{"display":"WST","hidingEnts":[],"value":"WST","hidingOrgs":[]},{"display":"XAF","hidingEnts":[],"value":"XAF","hidingOrgs":[]},{"display":"XAG","hidingEnts":[],"value":"XAG","hidingOrgs":[]},{"display":"XAU","hidingEnts":[],"value":"XAU","hidingOrgs":[]},{"display":"XBA","hidingEnts":[],"value":"XBA","hidingOrgs":[]},{"display":"XBB","hidingEnts":[],"value":"XBB","hidingOrgs":[]},{"display":"XBC","hidingEnts":[],"value":"XBC","hidingOrgs":[]},{"display":"XBD","hidingEnts":[],"value":"XBD","hidingOrgs":[]},{"display":"XCD","hidingEnts":[],"value":"XCD","hidingOrgs":[]},{"display":"XDR","hidingEnts":[],"value":"XDR","hidingOrgs":[]},{"display":"XOF","hidingEnts":[],"value":"XOF","hidingOrgs":[]},{"display":"XPD","hidingEnts":[],"value":"XPD","hidingOrgs":[]},{"display":"XPF","hidingEnts":[],"value":"XPF","hidingOrgs":[]},{"display":"XPT","hidingEnts":[],"value":"XPT","hidingOrgs":[]},{"display":"XSU","hidingEnts":[],"value":"XSU","hidingOrgs":[]},{"display":"XTS","hidingEnts":[],"value":"XTS","hidingOrgs":[]},{"display":"XUA","hidingEnts":[],"value":"XUA","hidingOrgs":[]},{"display":"XXX","hidingEnts":[],"value":"XXX","hidingOrgs":[]},{"display":"YER","hidingEnts":[],"value":"YER","hidingOrgs":[]},{"display":"YUM","hidingEnts":[],"value":"YUM","hidingOrgs":[]},{"display":"ZAR","hidingEnts":[],"value":"ZAR","hidingOrgs":[]},{"display":"ZIG","hidingEnts":[],"value":"ZIG","hidingOrgs":[]},{"display":"ZMK","hidingEnts":[],"value":"ZMK","hidingOrgs":[]},{"display":"ZMW","hidingEnts":[],"value":"ZMW","hidingOrgs":[]},{"display":"ZWD","hidingEnts":[],"value":"ZWD","hidingOrgs":[]},{"display":"ZWL","hidingEnts":[],"value":"ZWL","hidingOrgs":[]}]},"TMS.ShipmentNumberPurpose":{"enumId":"TMS.ShipmentNumberPurpose","success":true,"items":[{"display":"00","hidingEnts":[],"value":"00","hidingOrgs":[]},{"display":"22","hidingEnts":[],"value":"22","hidingOrgs":[]},{"display":"01","hidingEnts":[],"value":"01","hidingOrgs":[]},{"display":"SU","hidingEnts":[],"value":"SU","hidingOrgs":[]},{"display":"14","hidingEnts":[],"value":"14","hidingOrgs":[]},{"display":"04","hidingEnts":[],"value":"04","hidingOrgs":[]},{"display":"05","hidingEnts":[],"value":"05","hidingOrgs":[]},{"display":"53","hidingEnts":[],"value":"53","hidingOrgs":[]},{"display":"54","hidingEnts":[],"value":"54","hidingOrgs":[]}]},"OMS.ScorecardLevels":{"enumId":"OMS.ScorecardLevels","success":true,"items":[{"display":"Site","hidingEnts":[],"value":"Site","hidingOrgs":[]},{"display":"Corporate","hidingEnts":[],"value":"Corporate","hidingOrgs":[]},{"display":"Corporate And Site","hidingEnts":[],"value":"Corporate And Site","hidingOrgs":[]}]},"OMS.InventoryReportingPeriod":{"enumId":"OMS.InventoryReportingPeriod","success":true,"items":[{"display":"OTHER","hidingEnts":[],"value":"OTHER","hidingOrgs":[]},{"display":"WEEKLY","hidingEnts":[],"value":"WEEKLY","hidingOrgs":[]},{"display":"DAILY","hidingEnts":[],"value":"DAILY","hidingOrgs":[]},{"display":"MONTHLY","hidingEnts":[],"value":"MONTHLY","hidingOrgs":[]}]},"OMS.RequisitionUpdateAlertFields":{"enumId":"OMS.RequisitionUpdateAlertFields","success":true,"items":[{"display":"Expiry Date","hidingEnts":[],"value":"ExpiryDate","hidingOrgs":[]},{"display":"Ext Ship From Site Name","hidingEnts":[],"value":"ExtShipFromSiteName","hidingOrgs":[]},{"display":"Line Ship To Site","hidingEnts":[],"value":"LineShipToSite","hidingOrgs":[]},{"display":"Generic Item","hidingEnts":[],"value":"GenericItem","hidingOrgs":[]},{"display":"Delivery Group Number","hidingEnts":[],"value":"DeliveryGroupNumber","hidingOrgs":[]},{"display":"Ext Item Name","hidingEnts":[],"value":"ExtItemName","hidingOrgs":[]},{"display":"Is Line Pending Authorization","hidingEnts":[],"value":"LinePendingAuthorization","hidingOrgs":[]},{"display":"Ext Requisition Number","hidingEnts":[],"value":"ExtRequisitionNumber","hidingOrgs":[]},{"display":"Requisition Mgmt Org","hidingEnts":[],"value":"RequisitionMgmtOrg","hidingOrgs":[]},{"display":"Line Incoterms","hidingEnts":[],"value":"LineIncoTerms","hidingOrgs":[]},{"display":"Submission Date","hidingEnts":[],"value":"SubmissionDate","hidingOrgs":[]},{"display":"Target Price","hidingEnts":[],"value":"TargetPrice","hidingOrgs":[]},{"display":"Purpose Code","hidingEnts":[],"value":"PurposeCode","hidingOrgs":[]},{"display":"Inco Terms Location","hidingEnts":[],"value":"IncoTermsLocation","hidingOrgs":[]},{"display":"Item","hidingEnts":[],"value":"Item","hidingOrgs":[]},{"display":"Ship To Site","hidingEnts":[],"value":"ShipToSite","hidingOrgs":[]},{"display":"Bill To Site","hidingEnts":[],"value":"BillToSite","hidingOrgs":[]},{"display":"Cost Center","hidingEnts":[],"value":"CostCenter","hidingOrgs":[]},{"display":"Budget Line","hidingEnts":[],"value":"BudgetLine","hidingOrgs":[]},{"display":"Line Ship To Location","hidingEnts":[],"value":"LineShipToLocation","hidingOrgs":[]},{"display":"Currency","hidingEnts":[],"value":"Currency","hidingOrgs":[]},{"display":"Budget","hidingEnts":[],"value":"Budget","hidingOrgs":[]},{"display":"Requested By","hidingEnts":[],"value":"Requestedby","hidingOrgs":[]},{"display":"Line Type","hidingEnts":[],"value":"LineType","hidingOrgs":[]},{"display":"Line Purchasing Address","hidingEnts":[],"value":"LinePurchasingAddress","hidingOrgs":[]},{"display":"Program","hidingEnts":[],"value":"Program","hidingOrgs":[]},{"display":"Remaining Quantity","hidingEnts":[],"value":"RemainingQuantity","hidingOrgs":[]},{"display":"Quantity","hidingEnts":[],"value":"Quantity","hidingOrgs":[]},{"display":"Total Amount","hidingEnts":[],"value":"TotalAmount","hidingOrgs":[]},{"display":"Vendor","hidingEnts":[],"value":"Vendor","hidingOrgs":[]},{"display":"Line Approval Date","hidingEnts":[],"value":"LineApprovalDate","hidingOrgs":[]},{"display":"Approval Date","hidingEnts":[],"value":"ApprovalDate","hidingOrgs":[]},{"display":"Planner Code","hidingEnts":[],"value":"PlannerCode","hidingOrgs":[]},{"display":"Inco Date","hidingEnts":[],"value":"IncoDate","hidingOrgs":[]},{"display":"Ship To Location","hidingEnts":[],"value":"ShipToLocation","hidingOrgs":[]},{"display":"Purchasing Org","hidingEnts":[],"value":"PurchasingOrg","hidingOrgs":[]},{"display":"Ship From Location","hidingEnts":[],"value":"ShipFromLocation","hidingOrgs":[]},{"display":"Is Pending Authorization","hidingEnts":[],"value":"PendingAuthorization","hidingOrgs":[]},{"display":"Ext Ship To Site Name","hidingEnts":[],"value":"ExtShipToSiteName","hidingOrgs":[]},{"display":"Ordered Quantity","hidingEnts":[],"value":"OrderedQuantity","hidingOrgs":[]},{"display":"Ship From Site","hidingEnts":[],"value":"ShipFromSite","hidingOrgs":[]},{"display":"Requested Delivery Date","hidingEnts":[],"value":"RequestedDeliveryDate","hidingOrgs":[]},{"display":"Target Line Amount","hidingEnts":[],"value":"TargetLineAmount","hidingOrgs":[]},{"display":"Line Ext Ship To Site Name","hidingEnts":[],"value":"LineExtShipToSiteName","hidingOrgs":[]},{"display":"Bill To Org","hidingEnts":[],"value":"BillToOrg","hidingOrgs":[]},{"display":"Fulfillment Org","hidingEnts":[],"value":"FulfillmentOrg","hidingOrgs":[]}]},"OMS.EquipmentSize":{"enumId":"OMS.EquipmentSize","success":true,"items":[{"display":"45","hidingEnts":[],"value":"45","hidingOrgs":[]},{"display":"48","hidingEnts":[],"value":"48","hidingOrgs":[]},{"display":"Select One","hidingEnts":[],"value":"Sellect One","hidingOrgs":[]},{"display":"53","hidingEnts":[],"value":"53","hidingOrgs":[]},{"display":"Other","hidingEnts":[],"value":"Other","hidingOrgs":[]}]},"OMS.OrderValidationType":{"enumId":"OMS.OrderValidationType","success":true,"items":[{"display":"Deployment Order Validation","hidingEnts":[],"value":"Deployment Order Validation","hidingOrgs":[]},{"display":"Seller Validation","hidingEnts":[],"value":"Seller Validation","hidingOrgs":[]},{"display":"Buyer Validation","hidingEnts":[],"value":"Buyer Validation","hidingOrgs":[]}]},"SCC.ConditionCode":{"enumId":"SCC.ConditionCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"SCC.Penalty":{"enumId":"SCC.Penalty","success":true,"items":[]},"SCC.CertifiedBy":{"enumId":"SCC.CertifiedBy","success":true,"items":[{"display":"No","hidingEnts":[],"value":"No","hidingOrgs":[]},{"display":"Yes","hidingEnts":[],"value":"Yes","hidingOrgs":[]},{"display":"Not Available","hidingEnts":[],"value":"Not Available","hidingOrgs":[]}]},"OMS.CollaborationType":{"enumId":"OMS.CollaborationType","success":true,"items":[{"display":"Sharing","hidingEnts":[],"value":"Sharing","hidingOrgs":[]},{"display":"Collaboration","hidingEnts":[],"value":"Collaboration","hidingOrgs":[]},{"display":"None","hidingEnts":[],"value":"None","hidingOrgs":[]}]},"SCH.TrackingEventReasonCodes":{"enumId":"SCH.TrackingEventReasonCodes","success":true,"items":[{"entName":"PepsiCo LatAm","display":"32 - FALTA DE AJUDANTE NO ATO DA ENTREGA","hidingEnts":[],"value":"PepsiCo LatAm.32 - FALTA DE AJUDANTE NO ATO DA ENTREGA","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***plant","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***plant","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***corporate","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***corporate","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"9 - teste","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.9 - teste","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Carrier Request","hidingEnts":[],"value":"Dollar General.Carrier - Carrier Request","hidingOrgs":[]},{"display":"Confirmed by Shipper","hidingEnts":["PepsiCo LatAm","Home Retail Group","SAFEWAY, INC."],"value":"Confirmed by Shipper","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Pulling to accommodate another load","hidingEnts":[],"value":"Home Retail Group.Pulling to accommodate another load","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant - Inventory Discrepancy","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant - Inventory Discrepancy","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron slow","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron slow","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Failed Load to be resubmitted for future week","hidingEnts":[],"value":"Home Retail Group.Failed Load to be resubmitted for future week","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Delay at Vendor","hidingOrgs":[]},{"entName":"Dollar General","display":"System Suggested","hidingEnts":[],"value":"Dollar General.System Suggested","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"15 - CLIENTE NÃO CARIMBA FORMULÁRIO/CANHOTO","hidingEnts":[],"value":"PepsiCo LatAm.15 - CLIENTE NÃO CARIMBA FORMULÁRIO/CANHOTO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"41 - MERCADORIA COM DATA AVANÇADA","hidingEnts":[],"value":"PepsiCo LatAm.41 - MERCADORIA COM DATA AVANÇADA","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Contingency Containers not delivered","hidingEnts":[],"value":"Home Retail Group.Contingency Containers not delivered","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Miscellaneous (Notes Required)","hidingEnts":[],"value":"Dollar General.Other - Miscellaneous (Notes Required)","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Road Closures","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"49 - PROBLEMA FISCAL / IMPOSTO NA NF","hidingEnts":[],"value":"PepsiCo LatAm.49 - PROBLEMA FISCAL / IMPOSTO NA NF","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Test","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Test","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"00 - SEM IMPACTO OTD","hidingEnts":[],"value":"PepsiCo LatAm.00 - SEM IMPACTO OTD","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"67 - ATRASO ALOCAÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.67 - ATRASO ALOCAÇÃO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"04 - AGUARDANDO TROCA DE NF NO CD","hidingEnts":[],"value":"PepsiCo LatAm.04 - AGUARDANDO TROCA DE NF NO CD","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"58 - SEM PEDIDO - VENDAS","hidingEnts":[],"value":"PepsiCo LatAm.58 - SEM PEDIDO - VENDAS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"55 - QUALIDADE DO PRODUTO","hidingEnts":[],"value":"PepsiCo LatAm.55 - QUALIDADE DO PRODUTO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"09 - ATRASO DA TRANSPORTADORA (ORIGEM)","hidingEnts":[],"value":"PepsiCo LatAm.09 - ATRASO DA TRANSPORTADORA (ORIGEM)","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Incorrect Set Structure","hidingEnts":[],"value":"Home Retail Group.Incorrect Set Structure","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"60 - SOBRAS","hidingEnts":[],"value":"PepsiCo LatAm.60 - SOBRAS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"59 - SINISTRO/ROUBO DE CARGA/FURTO","hidingEnts":[],"value":"PepsiCo LatAm.59 - SINISTRO/ROUBO DE CARGA/FURTO","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Appointment Not Available","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Product Unavailable","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"11 - AGENDAMENTO FORA DO LEAD TIME","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.11 - AGENDAMENTO FORA DO LEAD TIME","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"24 - DEMORA NA DESCARGA DE DEVOLUÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.24 - DEMORA NA DESCARGA DE DEVOLUÇÃO","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure |C","hidingEnts":[],"value":"SAFEWAY, INC..Truck/Equipment Failure |C","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron too fast","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron too fast","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"29 - ENDEREÇO INCORRETO","hidingEnts":[],"value":"PepsiCo LatAm.29 - ENDEREÇO INCORRETO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"34 - FALTA DE PROMOTOR NA LOJA","hidingEnts":[],"value":"PepsiCo LatAm.34 - FALTA DE PROMOTOR NA LOJA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"26 - DEVOLUÇÃO DE PALLET CHEP","hidingEnts":[],"value":"PepsiCo LatAm.26 - DEVOLUÇÃO DE PALLET CHEP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"38 - ARMAZENAGEM DE CABOTAGEM","hidingEnts":[],"value":"PepsiCo LatAm.38 - ARMAZENAGEM DE CABOTAGEM","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant Exception for Shortage","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant Exception for Shortage","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"31 - ERRO DE PALETIZAÇÃO - CD","hidingEnts":[],"value":"PepsiCo LatAm.31 - ERRO DE PALETIZAÇÃO - CD","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 90 WM window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 90 WM window","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Failed Load","hidingEnts":[],"value":"Home Retail Group.Failed Load","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"12 - ATRASO DA TRANSPORTADORA (DESTINO)","hidingEnts":[],"value":"PepsiCo LatAm.12 - ATRASO DA TRANSPORTADORA (DESTINO)","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"998 - DT/DLV CANCELADA NO SAP","hidingEnts":[],"value":"PepsiCo LatAm.998 - DT/DLV CANCELADA NO SAP","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Corrupt PO's","hidingEnts":[],"value":"Home Retail Group.Corrupt PO's","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"30 - ENTREGA NÃO AGENDADA NO CLIENTE","hidingEnts":[],"value":"PepsiCo LatAm.30 - ENTREGA NÃO AGENDADA NO CLIENTE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"68 - ATRASO ENTREGA DE DOCUMENTOS","hidingEnts":[],"value":"PepsiCo LatAm.68 - ATRASO ENTREGA DE DOCUMENTOS","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Port Closed","hidingEnts":[],"value":"Home Retail Group.Port Closed","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* LTL for Delivered Loads Only","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* LTL for Delivered Loads Only","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"100 - teste","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.100 - teste","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"74 - UNPICKING","hidingEnts":[],"value":"PepsiCo LatAm.74 - UNPICKING","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Capacity Constraints","hidingEnts":[],"value":"Home Retail Group.Capacity Constraints","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Traffic requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Traffic requested change","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Truck Missed","hidingEnts":[],"value":"KIK INTERNATIONAL.Truck Missed","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"07 - ARMAZENAGEM","hidingEnts":[],"value":"PepsiCo LatAm.07 - ARMAZENAGEM","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"17 - CLIENTE NÃO RECEBEU EDI INVOICE","hidingEnts":[],"value":"PepsiCo LatAm.17 - CLIENTE NÃO RECEBEU EDI INVOICE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"48 - PERMANECEU NA FILA DE OUTRO CLIENTE","hidingEnts":[],"value":"PepsiCo LatAm.48 - PERMANECEU NA FILA DE OUTRO CLIENTE","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Plant Cancelled","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Plant Cancelled","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"23 - COVID 19","hidingEnts":[],"value":"PepsiCo LatAm.23 - COVID 19","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"44 - PAGAMENTO DE DESCARGA / AJUDANTE","hidingEnts":[],"value":"PepsiCo LatAm.44 - PAGAMENTO DE DESCARGA / AJUDANTE","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Rail Delay","hidingEnts":[],"value":"Dollar General.Carrier - Rail Delay","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 94 customer window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 94 customer window","hidingOrgs":[]},{"display":"Appt Not Available","hidingEnts":["PepsiCo LatAm","SAFEWAY, INC."],"value":"Appt not available","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"71 - INTERJORNADA MOTORISTA","hidingEnts":[],"value":"PepsiCo LatAm.71 - INTERJORNADA MOTORISTA","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather |W","hidingEnts":[],"value":"SAFEWAY, INC..Weather |W","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Container not Dug Out","hidingEnts":[],"value":"Home Retail Group.Container not Dug Out","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Corporate requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Corporate requested change","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"02 - AGUARDANDO CARREGAMENTO NO CD","hidingEnts":[],"value":"PepsiCo LatAm.02 - AGUARDANDO CARREGAMENTO NO CD","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"Bryan Chung didn´t want it","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.Bryan Chung didn´t want it","hidingOrgs":[]},{"display":"Customer Requested Change","hidingEnts":["SAFEWAY, INC.","PepsiCo LatAm","Home Retail Group"],"value":"Customer requested change","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"10 - teste","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.10 - teste","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"25 - DEMURRAGE","hidingEnts":[],"value":"PepsiCo LatAm.25 - DEMURRAGE","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Plant","hidingEnts":[],"value":"SAFEWAY, INC..Plant","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"22 - COMPROMISSO FORA DO LEAD TIME","hidingEnts":[],"value":"PepsiCo LatAm.22 - COMPROMISSO FORA DO LEAD TIME","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Haulage Not Available","hidingEnts":[],"value":"Home Retail Group.Haulage Not Available","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"10 - ATRASO NO CARREGAMENTO (ORIGEM)","hidingEnts":[],"value":"PepsiCo LatAm.10 - ATRASO NO CARREGAMENTO (ORIGEM)","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures |W","hidingEnts":[],"value":"SAFEWAY, INC..Road Closures |W","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Border Issue","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"27 - DEVOLUÇÃO POR RETENÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.27 - DEVOLUÇÃO POR RETENÇÃO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"14 - CARGA TOMBADA","hidingEnts":[],"value":"PepsiCo LatAm.14 - CARGA TOMBADA","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Unable to deliver on Weekends/Bank Holidays","hidingEnts":[],"value":"Home Retail Group.Unable to deliver on Weekends/Bank Holidays","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***traffic","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***traffic","hidingOrgs":[]},{"entName":"Dollar General","display":"FOB ONLY - DC Capacity","hidingEnts":[],"value":"Dollar General.FOB ONLY - DC Capacity","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"01 - AGENDAMENTO FORA DO LEAD TIME","hidingEnts":[],"value":"PepsiCo LatAm.01 - AGENDAMENTO FORA DO LEAD TIME","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Delayed on Previous Job","hidingEnts":[],"value":"Home Retail Group.Delayed on Previous Job","hidingOrgs":[]},{"display":"AUTO CANDIDATE","globallyHidden":true,"hidingEnts":[],"value":"AUTO CANDIDATE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"36 - POSTO FISCAL","hidingEnts":[],"value":"PepsiCo LatAm.36 - POSTO FISCAL","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Plant requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Plant requested change","hidingOrgs":[]},{"entName":"CustomerA","display":"Shipment Change","hidingEnts":[],"value":"CustomerA.Shipment Change","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Customs Not Cleared","hidingEnts":[],"value":"Home Retail Group.Customs Not Cleared","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Missed Pickup/Delivery Appointment","hidingEnts":[],"value":"Dollar General.Carrier - Missed Pickup/Delivery Appointment","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"39 - INVERSÃO NO CARREGAMENTO / NF","hidingEnts":[],"value":"PepsiCo LatAm.39 - INVERSÃO NO CARREGAMENTO / NF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"52 - PROBLEMAS MECANICOS","hidingEnts":[],"value":"PepsiCo LatAm.52 - PROBLEMAS MECANICOS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"1 - CD/CDV PROCEDIMENTO DEMORADO / EXCESSO D","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.1 - CD/CDV PROCEDIMENTO DEMORADO / EXCESSO D","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC |D","hidingEnts":[],"value":"SAFEWAY, INC..Delay at DC |D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"21 - COLETAS CANCELADAS","hidingEnts":[],"value":"PepsiCo LatAm.21 - COLETAS CANCELADAS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"65 - ATRASO RETIRADA CARRO APÓS EMISSÃO NF","hidingEnts":[],"value":"PepsiCo LatAm.65 - ATRASO RETIRADA CARRO APÓS EMISSÃO NF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"57 - SEM PEDIDO - EXPIRADO / CANCELADO","hidingEnts":[],"value":"PepsiCo LatAm.57 - SEM PEDIDO - EXPIRADO / CANCELADO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"03 - AGUARDANDO LIBERAÇÃO SEFAZ / SUFRAMA","hidingEnts":[],"value":"PepsiCo LatAm.03 - AGUARDANDO LIBERAÇÃO SEFAZ / SUFRAMA","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Delivered to wrong site","hidingEnts":[],"value":"Home Retail Group.Delivered to wrong site","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Brett","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Brett","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Destination |D","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Destination |D","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Origin |V","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Origin |V","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"63 -ATRASO\\AGUARDO EMISSÃO NF ou DOC","hidingEnts":[],"value":"PepsiCo LatAm.63 -ATRASO\\AGUARDO EMISSÃO NF ou DOC","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Delayed at Port","hidingEnts":[],"value":"Home Retail Group.Delayed at Port","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"10 - ATRASO NO CARREGAMENTO","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.10 - ATRASO NO CARREGAMENTO","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Consolidating loads","hidingEnts":[],"value":"Home Retail Group.Consolidating loads","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"40 - LOJA EM BALANÇO","hidingEnts":[],"value":"PepsiCo LatAm.40 - LOJA EM BALANÇO","hidingOrgs":[]},{"display":"Confirmed by Market Maker","hidingEnts":["PepsiCo LatAm","Home Retail Group","SAFEWAY, INC."],"value":"Confirmed by Market Maker","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"42 - MERCADORIA INVERTIDA","hidingEnts":[],"value":"PepsiCo LatAm.42 - MERCADORIA INVERTIDA","hidingOrgs":[]},{"display":"Confirmed via Tender","hidingEnts":["PepsiCo LatAm","Home Retail Group","SAFEWAY, INC."],"value":"Confirmed via Tender","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"46 - PDV FECHADO","hidingEnts":[],"value":"PepsiCo LatAm.46 - PDV FECHADO","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Plant Delay","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Plant Delay","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Weather (Notes Required)","hidingEnts":[],"value":"Dollar General.Other - Weather (Notes Required)","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"19 - CLIENTE SEM SISTEMA","hidingEnts":[],"value":"PepsiCo LatAm.19 - CLIENTE SEM SISTEMA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"20 - COLETA DE DEVOLUÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.20 - COLETA DE DEVOLUÇÃO","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Mixed Container planning issues","hidingEnts":[],"value":"Home Retail Group.Mixed Container planning issues","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"66 - ALTA OCUPAÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.66 - ALTA OCUPAÇÃO","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"LTL Loads","hidingEnts":[],"value":"KIK INTERNATIONAL.LTL Loads","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"13 - AVARIA NA CARGA (Rasgada, Molhada e etc)","hidingEnts":[],"value":"PepsiCo LatAm.13 - AVARIA NA CARGA (Rasgada, Molhada e etc)","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"999 - DT NÃO PLANEJADA PELA TCT","hidingEnts":[],"value":"PepsiCo LatAm.999 - DT NÃO PLANEJADA PELA TCT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"16 - CLIENTE NÃO RECEBE CARGA CONJUGADA","hidingEnts":[],"value":"PepsiCo LatAm.16 - CLIENTE NÃO RECEBE CARGA CONJUGADA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"70 - ESCALA MOTORISTA","hidingEnts":[],"value":"PepsiCo LatAm.70 - ESCALA MOTORISTA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"06 - ARQUIVO XML","hidingEnts":[],"value":"PepsiCo LatAm.06 - ARQUIVO XML","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Weather","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"53 - PRODUTO NÃO CADASTRADO /FORA DE LINHA","hidingEnts":[],"value":"PepsiCo LatAm.53 - PRODUTO NÃO CADASTRADO /FORA DE LINHA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"28 - DIVERGÊNCIA DE PEDIDO / PREÇO","hidingEnts":[],"value":"PepsiCo LatAm.28 - DIVERGÊNCIA DE PEDIDO / PREÇO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"54 - PRODUTO SEM VALIDADE / ERRO CÓDIGO BARRAS","hidingEnts":[],"value":"PepsiCo LatAm.54 - PRODUTO SEM VALIDADE / ERRO CÓDIGO BARRAS","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Internal Use Only-Driver Issue","hidingEnts":[],"value":"SAFEWAY, INC..Internal Use Only-Driver Issue","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Message\")","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Other (please specify in \"Message\")","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Not dug out","hidingEnts":[],"value":"Home Retail Group.Not dug out","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Early Delivery","hidingEnts":[],"value":"Dollar General.Carrier - Early Delivery","hidingOrgs":[]},{"entName":"CustomerA","display":"test","hidingEnts":[],"value":"CustomerA.test","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Comments\")","hidingEnts":[],"value":"SAFEWAY, INC..Other (please specify in \"Comments\")","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - TransOps Request","hidingEnts":[],"value":"Dollar General.DG - TransOps Request","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Capacity/Planning","hidingEnts":[],"value":"Dollar General.Carrier - Capacity/Planning","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"1 - AGUARDANDO CARREGAMENTO NO CD","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.1 - AGUARDANDO CARREGAMENTO NO CD","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Breakdown/Accident","hidingEnts":[],"value":"Dollar General.Carrier - Breakdown/Accident","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"08 - ARQUIVO GNRE / PIN","hidingEnts":[],"value":"PepsiCo LatAm.08 - ARQUIVO GNRE / PIN","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"61 - VEICULO INCOMPATIVEL","hidingEnts":[],"value":"PepsiCo LatAm.61 - VEICULO INCOMPATIVEL","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Out of Hours","hidingEnts":[],"value":"Dollar General.Carrier - Out of Hours","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"51 - PROBLEMAS FINANCEIROS/PRORROGAÇÃO BOLETO","hidingEnts":[],"value":"PepsiCo LatAm.51 - PROBLEMAS FINANCEIROS/PRORROGAÇÃO BOLETO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"997 - Delivery excluída no SAP","hidingEnts":[],"value":"PepsiCo LatAm.997 - Delivery excluída no SAP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"33 - FALTA DE MERCADORIA NO ATO DA ENTREGA","hidingEnts":[],"value":"PepsiCo LatAm.33 - FALTA DE MERCADORIA NO ATO DA ENTREGA","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Created in Error","hidingEnts":[],"value":"Home Retail Group.Created in Error","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"10 - AGENDAMENTO FORA DO LEAD TIME","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.10 - AGENDAMENTO FORA DO LEAD TIME","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"43 - OUTROS","hidingEnts":[],"value":"PepsiCo LatAm.43 - OUTROS","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - Not Ready","hidingEnts":[],"value":"Dollar General.Vendor - Not Ready","hidingOrgs":[]},{"entName":"CustomerA","display":"testtrack","hidingEnts":[],"value":"CustomerA.testtrack","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"05 - ANTECIPAÇÃO DE COLETA / ENTREGA","hidingEnts":[],"value":"PepsiCo LatAm.05 - ANTECIPAÇÃO DE COLETA / ENTREGA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"45 - PAGAMENTO DE REPALETIZAÇÃO / TRANSBORDO","hidingEnts":[],"value":"PepsiCo LatAm.45 - PAGAMENTO DE REPALETIZAÇÃO / TRANSBORDO","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Load Swap","hidingEnts":[],"value":"Home Retail Group.Load Swap","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Change in delivery method","hidingEnts":[],"value":"Home Retail Group.Change in delivery method","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"50 - PROBLEMAS COM A SAÚDE DO MOTORISTA","hidingEnts":[],"value":"PepsiCo LatAm.50 - PROBLEMAS COM A SAÚDE DO MOTORISTA","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation |C","hidingEnts":[],"value":"SAFEWAY, INC..DOT Violation |C","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"11 - CD/CDV LONGA ESPERA / EXCESSO DE VEÍCULOS","hidingEnts":[],"value":"PepsiCo LatAm.11 - CD/CDV LONGA ESPERA / EXCESSO DE VEÍCULOS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"12 - AGUARDANDO CARREGAMENTO NO CD","hidingEnts":["PepsiCo LatAm"],"value":"PepsiCo LatAm.12 - AGUARDANDO CARREGAMENTO NO CD","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Transit time allows early delivery","hidingEnts":[],"value":"KIK INTERNATIONAL.Transit time allows early delivery","hidingOrgs":[]},{"display":"Others","hidingEnts":["PepsiCo LatAm","Home Retail Group","SAFEWAY, INC."],"value":"Others","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable |C","hidingEnts":[],"value":"SAFEWAY, INC..Equipment Unavailable |C","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Corporate - Vendor Responsible (ie Caps, Labe)","hidingEnts":[],"value":"KIK INTERNATIONAL.Corporate - Vendor Responsible (ie Caps, Labe)","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"62 - ATRASO TRAJETO\\TRÂNSITO","hidingEnts":[],"value":"PepsiCo LatAm.62 - ATRASO TRAJETO\\TRÂNSITO","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Vessel Delays","hidingEnts":[],"value":"Home Retail Group.Vessel Delays","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Unable to deliver on Nights","hidingEnts":[],"value":"Home Retail Group.Unable to deliver on Nights","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"35 - FALTA DE PROTOCOLO / SENHA","hidingEnts":[],"value":"PepsiCo LatAm.35 - FALTA DE PROTOCOLO / SENHA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"56 - RODIZIO DE VEÍCULO","hidingEnts":[],"value":"PepsiCo LatAm.56 - RODIZIO DE VEÍCULO","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor |V","hidingEnts":[],"value":"SAFEWAY, INC..Delay at Vendor |V","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Truck/Equipment Failure","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Equipment Unavailable","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Admin Error","hidingEnts":[],"value":"Home Retail Group.Admin Error","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"47 - PEDIDO EM DUPLICIDADE","hidingEnts":[],"value":"PepsiCo LatAm.47 - PEDIDO EM DUPLICIDADE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"72 - JANELA ACIMA DA CAPACIDADE","hidingEnts":[],"value":"PepsiCo LatAm.72 - JANELA ACIMA DA CAPACIDADE","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable |V","hidingEnts":[],"value":"SAFEWAY, INC..Product Unavailable |V","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue |B","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Border Issue |B","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"69 - CARRETA REPROVADA CHECK LIST","hidingEnts":[],"value":"PepsiCo LatAm.69 - CARRETA REPROVADA CHECK LIST","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Delay at DC","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..DOT Violation","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"18 - CLIENTE LONGA ESPERA/EXCESSO DE VEÍCULOS","hidingEnts":[],"value":"PepsiCo LatAm.18 - CLIENTE LONGA ESPERA/EXCESSO DE VEÍCULOS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"75 - FALTA DE SALDO","hidingEnts":[],"value":"PepsiCo LatAm.75 - FALTA DE SALDO","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Driver Issues","hidingEnts":[],"value":"Dollar General.Carrier - Driver Issues","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"37 - INCONFORMIDADE NO RECEBIMENTO (CLIENTE)","hidingEnts":[],"value":"PepsiCo LatAm.37 - INCONFORMIDADE NO RECEBIMENTO (CLIENTE)","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"64 - DIVERGÊNCIAS MAPA, HORÁRIO COLETA, ETC","hidingEnts":[],"value":"PepsiCo LatAm.64 - DIVERGÊNCIAS MAPA, HORÁRIO COLETA, ETC","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Haulage not arranged","hidingEnts":[],"value":"Home Retail Group.Haulage not arranged","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"73 - REPROGRAMAÇÃO","hidingEnts":[],"value":"PepsiCo LatAm.73 - REPROGRAMAÇÃO","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"No Appt - First Come First Serve only|V","hidingEnts":[],"value":"SAFEWAY, INC..No Appt - First Come First Serve only|V","hidingOrgs":[]}]},"OMS.VendorScorecardKPIs":{"enumId":"OMS.VendorScorecardKPIs","success":true,"items":[{"display":"Order Fill Rate","hidingEnts":[],"value":"Order Fill Rate","hidingOrgs":[]},{"display":"Order On time Ship Quantity Percent (Against Original)","hidingEnts":[],"value":"Order Ontime Ship Quantity Percent (Against Original)","hidingOrgs":[]},{"display":"Vendor Accuracy Percent","hidingEnts":[],"value":"Vendor accuracy Percent","hidingOrgs":[]},{"display":"Order On time Delivery Percent","hidingEnts":[],"value":"Order Ontime Delivery Percent","hidingOrgs":[]},{"display":"Order On time Ship Percent","hidingEnts":[],"value":"Order Ontime Ship Percent","hidingOrgs":[]},{"display":"Shipment Accuracy Percent ( Against Original)","hidingEnts":[],"value":"Shipment Accuracy Percent (Against Original)","hidingOrgs":[]},{"display":"Order In Window Delivery Percent","hidingEnts":[],"value":"Order InWindow Delivery Percent","hidingOrgs":[]},{"display":"Buyer Accuracy Percent","hidingEnts":[],"value":"Buyer Accuracy Percent","hidingOrgs":[]},{"display":"Order In Window Delivery Percent (By Quantity)","hidingEnts":[],"value":"Order InWindow Delivery Percent (By Quantity)","hidingOrgs":[]},{"display":"Order Promise Cut Percent","hidingEnts":[],"value":"Order Promise Cut Percent","hidingOrgs":[]},{"display":"Order On time Ship Percent (Against Original)","hidingEnts":[],"value":"Order Ontime Ship Percent (Against Original)","hidingOrgs":[]},{"display":"Order On time Delivery Percent (Against Original)","hidingEnts":[],"value":"Order Ontime Delivery Percent (Against Original)","hidingOrgs":[]},{"display":"Shipment Accuracy Percent","hidingEnts":[],"value":"Shipment Accuracy Percent","hidingOrgs":[]},{"display":"Order Received Vs Shipped Cut Percent","hidingEnts":[],"value":"Order Received vs Shipped Cut Percent","hidingOrgs":[]},{"display":"Order On time Ship Percent (By Quantity)","hidingEnts":[],"value":"Order Ontime Ship Percent (By Quantity)","hidingOrgs":[]},{"display":"Order On time Delivery Quantity Percent (Against Original)","hidingEnts":[],"value":"Order Ontime Delivery Quantity Percent (Against Original)","hidingOrgs":[]},{"display":"Order On time Delivery Percent (By Quantity)","hidingEnts":[],"value":"Order Ontime Delivery Percent (By Quantity)","hidingOrgs":[]}]},"OMS.OrderPromisingType":{"enumId":"OMS.OrderPromisingType","success":true,"items":[{"display":"Bucketized Order Forecast","hidingEnts":[],"value":"BucketizedOrderForecast","hidingOrgs":[]},{"display":"Inbound Supply","hidingEnts":[],"value":"InboundSupply","hidingOrgs":[]},{"display":"Simple","hidingEnts":[],"value":"Simple","hidingOrgs":[]}]},"OMS.PaymentDateType":{"enumId":"OMS.PaymentDateType","success":true,"items":[{"display":"Yearly","hidingEnts":[],"value":"YEARLY","hidingOrgs":[]},{"display":"Weekly","hidingEnts":[],"value":"WEEKLY","hidingOrgs":[]},{"display":"No Of Days","hidingEnts":[],"value":"NO_OF_DAYS","hidingOrgs":[]},{"display":"Monthly","hidingEnts":[],"value":"MONTHLY","hidingOrgs":[]}]},"SCC.FDAReasonCode":{"enumId":"SCC.FDAReasonCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"OMS.ParticipantType":{"enumId":"OMS.ParticipantType","success":true,"items":[{"display":"Auditor","hidingEnts":[],"value":"Auditor","hidingOrgs":[]},{"display":"Internal User","hidingEnts":[],"value":"Internal","hidingOrgs":[]},{"display":"External User","hidingEnts":[],"value":"External","hidingOrgs":[]},{"display":"Partner","hidingEnts":[],"value":"Partner","hidingOrgs":[]},{"display":"Reviewer","hidingEnts":[],"value":"Reviewer","hidingOrgs":[]}]},"SCC.ClaimType":{"enumId":"SCC.ClaimType","success":true,"items":[{"display":"Inadequate Fill Rate","hidingEnts":[],"value":"Inadequate FillRate","hidingOrgs":[]},{"display":"Over","hidingEnts":[],"value":"Over","hidingOrgs":[]},{"display":"Compliance Check","hidingEnts":[],"value":"Compliance Check","hidingOrgs":[]},{"display":"Late Delivery","hidingEnts":[],"value":"Late Delivery","hidingOrgs":[]},{"display":"Accident","hidingEnts":[],"value":"Accident","hidingOrgs":[]},{"display":"Contract Violation","hidingEnts":[],"value":"Contract Violation","hidingOrgs":[]},{"display":"Short","hidingEnts":[],"value":"Short","hidingOrgs":[]},{"display":"Damaged","hidingEnts":[],"value":"Damaged","hidingOrgs":[]},{"display":"Other","hidingEnts":[],"value":"Other","hidingOrgs":[]},{"display":"Theft","hidingEnts":[],"value":"Theft","hidingOrgs":[]}]},"OMS.InvoiceOnActionQtyType":{"enumId":"OMS.InvoiceOnActionQtyType","success":true,"items":[{"display":"NO_QTY","hidingEnts":[],"value":"NO_QTY","hidingOrgs":[]},{"display":"ON_RECEIPT","hidingEnts":[],"value":"ON_RECEIPT","hidingOrgs":[]},{"display":"ON_SHIP","hidingEnts":[],"value":"ON_SHIP","hidingOrgs":[]}]},"OMS.OrderUpdateAlertOrderType":{"enumId":"OMS.OrderUpdateAlertOrderType","success":true,"items":[{"display":"Return Order","hidingEnts":[],"value":"Return Order","hidingOrgs":[]},{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]},{"display":"Sales Order","hidingEnts":[],"value":"Sales Order","hidingOrgs":[]}]},"SCC.OrderClassification":{"enumId":"SCC.OrderClassification","success":true,"items":[{"display":"Non-Recurring","hidingEnts":[],"value":"Non-Recurring","hidingOrgs":[]},{"display":"Recurring","hidingEnts":[],"value":"Recurring","hidingOrgs":[]}]},"SCC.ShipmentStatus":{"enumId":"SCC.ShipmentStatus","success":true,"items":[{"display":"In-Gate","hidingEnts":[],"value":"In-Gate","hidingOrgs":[]},{"display":"Appointment Canceled","hidingEnts":[],"value":"Appointment Cancelled","hidingOrgs":[]},{"display":"Arrived At Site","hidingEnts":[],"value":"Arrived At Site","hidingOrgs":[]},{"display":"Refused by Consignee","hidingEnts":[],"value":"Refused by Consignee","hidingOrgs":[]},{"display":"Trap For Customer","hidingEnts":[],"value":"Trap for Customer","hidingOrgs":[]},{"display":"Carrier Plan Not Determined","hidingEnts":[],"value":"Carrier Plan Not Determined","hidingOrgs":[]},{"display":"Appointment Scheduled - Delivery","hidingEnts":[],"value":"Appointment Scheduled - Delivery","hidingOrgs":[]},{"display":"Released From Hold","hidingEnts":[],"value":"Released from Hold","hidingOrgs":[]},{"display":"Review Response","hidingEnts":[],"value":"Review Response","hidingOrgs":[]},{"display":"Past Cut-Off Time","hidingEnts":[],"value":"Past cut-off time","hidingOrgs":[]},{"display":"Canceled By Hub","hidingEnts":[],"value":"Cancelled By Hub","hidingOrgs":[]},{"display":"Confirmed On Board","hidingEnts":[],"value":"Confirmed On Board","hidingOrgs":[]},{"display":"Awaiting TCO Approve","hidingEnts":[],"value":"Awaiting TCO Approve","hidingOrgs":[]},{"display":"No Carriers Accepted","hidingEnts":[],"value":"No Carriers Accepted","hidingOrgs":[]},{"display":"Ready for Pickup","hidingEnts":[],"value":"Ready for Pickup","hidingOrgs":[]},{"display":"Holded","hidingEnts":[],"value":"Holded","hidingOrgs":[]},{"display":"Barge In","hidingEnts":[],"value":"Barge In","hidingOrgs":[]},{"display":"Ready for Delivery","hidingEnts":[],"value":"Ready for Delivery","hidingOrgs":[]},{"display":"Withdrawn","hidingEnts":[],"value":"Withdrawn","hidingOrgs":[]},{"display":"All Carriers Rejected","hidingEnts":[],"value":"All Carriers Rejected","hidingOrgs":[]},{"display":"Hazardous","hidingEnts":[],"value":"Hazardous","hidingOrgs":[]},{"display":"Tender In The Future","hidingEnts":[],"value":"Tender In The Future","hidingOrgs":[]},{"display":"Picked Up","hidingEnts":[],"value":"Picked Up","hidingOrgs":[]},{"display":"Awaiting Response","hidingEnts":[],"value":"Awaiting Response","hidingOrgs":[]},{"display":"Mechanical Breakdown","hidingEnts":[],"value":"Mechanical Breakdown","hidingOrgs":[]},{"display":"FDA Clearance","hidingEnts":[],"value":"FDA Clearance","hidingOrgs":[]},{"display":"Equipment Booked","hidingEnts":[],"value":"Equipment Booked","hidingOrgs":[]},{"display":"Received","hidingEnts":[],"value":"Received","hidingOrgs":[]},{"display":"Canceled By Carrier","hidingEnts":[],"value":"Cancelled By Carrier","hidingOrgs":[]},{"display":"Appointment Scheduled - Pickup","hidingEnts":[],"value":"Appointment Scheduled - Pickup","hidingOrgs":[]},{"display":"Ready To Tender","hidingEnts":[],"value":"Ready To Tender","hidingOrgs":[]},{"display":"Quote Accepted","hidingEnts":[],"value":"Quote Accepted","hidingOrgs":[]},{"display":"Accepted","hidingEnts":[],"value":"Accepted","hidingOrgs":[]},{"display":"Customs Cleared","hidingEnts":[],"value":"Customs Cleared","hidingOrgs":[]},{"display":"Freight Released","hidingEnts":[],"value":"Freight Released","hidingOrgs":[]},{"display":"Arrived At Customs","hidingEnts":[],"value":"Arrived at Customs","hidingOrgs":[]},{"display":"Quote Rejected","hidingEnts":[],"value":"Quote Rejected","hidingOrgs":[]},{"display":"Delivery Not Completed","hidingEnts":[],"value":"Delivery Not Completed","hidingOrgs":[]},{"display":"Vessel Arrived","hidingEnts":[],"value":"Vessel Arrived","hidingOrgs":[]},{"display":"No Carrier Commitments Available","hidingEnts":[],"value":"No Carrier Commitments available","hidingOrgs":[]},{"display":"Unloaded","hidingEnts":[],"value":"Unloaded","hidingOrgs":[]},{"display":"Arrived","hidingEnts":[],"value":"Arrived","hidingOrgs":[]},{"display":"ETA Date Changed","hidingEnts":[],"value":"ETA Date Changed","hidingOrgs":[]},{"display":"No Carriers in Tender Plan","hidingEnts":[],"value":"No Carriers in Tender Plan","hidingOrgs":[]},{"display":"Confirmed","hidingEnts":[],"value":"Confirmed","hidingOrgs":[]},{"display":"Split Cargo","hidingEnts":[],"value":"Split Cargo","hidingOrgs":[]},{"display":"Acknowledged","hidingEnts":[],"value":"Acknowledged","hidingOrgs":[]},{"display":"Barge Out","hidingEnts":[],"value":"Barge Out","hidingOrgs":[]},{"display":"Unable To Locate","hidingEnts":[],"value":"Unable to Locate","hidingOrgs":[]},{"display":"Intransit","hidingEnts":[],"value":"Intransit","hidingOrgs":[]},{"display":"Documents Verified","hidingEnts":[],"value":"Documents Verified","hidingOrgs":[]},{"display":"Discharged","hidingEnts":[],"value":"Discharged","hidingOrgs":[]},{"display":"Vessel Departed","hidingEnts":[],"value":"Vessel Departed","hidingOrgs":[]},{"display":"Trailer Spotted","hidingEnts":[],"value":"Trailer Spotted","hidingOrgs":[]},{"display":"Rail Out","hidingEnts":[],"value":"Rail Out","hidingOrgs":[]},{"display":"Canceled By Shipper","hidingEnts":[],"value":"Cancelled By Shipper","hidingOrgs":[]},{"display":"Pickup Date and Time Exceeded","hidingEnts":[],"value":"Pickup Date and Time Exceeded","hidingOrgs":[]},{"display":"Rail In","hidingEnts":[],"value":"Rail In","hidingOrgs":[]},{"display":"Open","hidingEnts":[],"value":"Open","hidingOrgs":[]},{"display":"Loading","hidingEnts":[],"value":"Loading","hidingOrgs":[]},{"display":"Deleted","hidingEnts":[],"value":"Deleted","hidingOrgs":[]},{"display":"Rollback","hidingEnts":[],"value":"Rollback","hidingOrgs":[]},{"display":"Available for Delivery","hidingEnts":[],"value":"Available for Delivery","hidingOrgs":[]},{"display":"Invalid Commitment Tier","hidingEnts":[],"value":"Invalid Commitment Tier","hidingOrgs":[]},{"display":"Loaded","hidingEnts":[],"value":"Loaded","hidingOrgs":[]},{"display":"International","hidingEnts":[],"value":"International","hidingOrgs":[]},{"display":"Departed From Site","hidingEnts":[],"value":"Departed From Site","hidingOrgs":[]},{"display":"Returned","hidingEnts":[],"value":"Returned","hidingOrgs":[]},{"display":"Manually Holded","hidingEnts":[],"value":"Manually Holded","hidingOrgs":[]},{"display":"Departed","hidingEnts":[],"value":"Departed","hidingOrgs":[]},{"display":"Delayed","hidingEnts":[],"value":"Delayed","hidingOrgs":[]},{"display":"En Route","hidingEnts":[],"value":"En Route","hidingOrgs":[]},{"display":"Delivered","hidingEnts":[],"value":"Delivered","hidingOrgs":[]},{"display":"Partial Shipment","hidingEnts":[],"value":"Partial Shipment","hidingOrgs":[]},{"display":"Attempted Pickup","hidingEnts":[],"value":"Attempted Pickup","hidingOrgs":[]},{"display":"Equipment Returned","hidingEnts":[],"value":"Equipment Returned","hidingOrgs":[]},{"display":"Unloading","hidingEnts":[],"value":"Unloading","hidingOrgs":[]},{"display":"Out-Gate","hidingEnts":[],"value":"Out-Gate","hidingOrgs":[]},{"display":"Documents Received","hidingEnts":[],"value":"Documents Received","hidingOrgs":[]},{"display":"Customs Hold","hidingEnts":[],"value":"Customs Hold","hidingOrgs":[]},{"display":"Weather Or Natural Disaster Related","hidingEnts":[],"value":"Weather or Natural Disaster Related","hidingOrgs":[]},{"display":"USDA Clearance","hidingEnts":[],"value":"USDA Clearance","hidingOrgs":[]},{"display":"ETA","hidingEnts":[],"value":"ETA","hidingOrgs":[]},{"display":"Appointment Rescheduled","hidingEnts":[],"value":"Appointment Rescheduled","hidingOrgs":[]},{"display":"Customs Submitted","hidingEnts":[],"value":"Customs Submitted","hidingOrgs":[]},{"display":"Appointment Scheduled","hidingEnts":[],"value":"Appointment Scheduled","hidingOrgs":[]},{"display":"Attempted Delivery","hidingEnts":[],"value":"Attempted Delivery","hidingOrgs":[]}]},"OMS.UserCodeType":{"enumId":"OMS.UserCodeType","success":true,"items":[{"display":"Account Payable","hidingEnts":[],"value":"Account Payable","hidingOrgs":[]},{"display":"Buyer Planner","hidingEnts":[],"value":"BuyerPlanner","hidingOrgs":[]},{"display":"Buyer","hidingEnts":[],"value":"Buyer","hidingOrgs":[]}]},"TMS.RatingType":{"enumId":"TMS.RatingType","success":true,"rows":[{"display":"Continuous Move","value":"Continuous Move"},{"display":"Dedicated","value":"Dedicated"},{"display":"Direct","value":"Direct"},{"display":"DTD","value":"DTD"},{"display":"DTP","value":"DTP"},{"display":"PTD","value":"PTD"},{"display":"PTP","value":"PTP"},{"display":"Rule 11","value":"Rule 11"},{"display":"Shipper Route","value":"Shipper Route"}],"items":[{"display":"Continuous Move","value":"Continuous Move"},{"display":"Dedicated","value":"Dedicated"},{"display":"Direct","value":"Direct"},{"display":"DTD","value":"DTD"},{"display":"DTP","value":"DTP"},{"display":"PTD","value":"PTD"},{"display":"PTP","value":"PTP"},{"display":"Rule 11","value":"Rule 11"},{"display":"Shipper Route","value":"Shipper Route"}]},"ScorecardMonths":{"enumId":"ScorecardMonths","success":true,"items":[{"display":"3 months","hidingEnts":[],"value":"3","hidingOrgs":[]},{"display":"4 months","hidingEnts":[],"value":"4","hidingOrgs":[]},{"display":"5 months","hidingEnts":[],"value":"5","hidingOrgs":[]}]},"OMS.AnalyticsScoreBucketization":{"enumId":"OMS.AnalyticsScoreBucketization","success":true,"items":[{"display":"Monthly","hidingEnts":[],"value":"Monthly","hidingOrgs":[]},{"display":"Yearly","hidingEnts":[],"value":"Yearly","hidingOrgs":[]},{"display":"Half Yearly","hidingEnts":[],"value":"Half Yearly","hidingOrgs":[]},{"display":"Quarterly","hidingEnts":[],"value":"Quarterly","hidingOrgs":[]}]},"OMS.AutoCreateFulfillmentOrder":{"enumId":"OMS.AutoCreateFulfillmentOrder","success":true,"items":[{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]}]},"SCC.OrderSubType":{"enumId":"SCC.OrderSubType","success":true,"items":[{"display":"VMI","hidingEnts":[],"value":"VMI","hidingOrgs":[]},{"display":"Rebalance","hidingEnts":[],"value":"Rebalance","hidingOrgs":[]},{"display":"No Receipt","hidingEnts":[],"value":"No Receipt","hidingOrgs":[]},{"display":"Consignment","hidingEnts":[],"value":"Consignment","hidingOrgs":[]},{"display":"Standard","hidingEnts":[],"value":"Standard","hidingOrgs":[]}]},"OMS.OrderTrackingEventType":{"enumId":"OMS.OrderTrackingEventType","success":true,"items":[{"display":"Hold Created","hidingEnts":[],"value":"Hold Created","hidingOrgs":[]},{"display":"Hold Overridden","hidingEnts":[],"value":"Hold Overridden","hidingOrgs":[]},{"display":"Request Schedule Reinstated","hidingEnts":[],"value":"Request Schedule Reinstated","hidingOrgs":[]},{"display":"Order Reinstated","hidingEnts":[],"value":"Order Reinstated","hidingOrgs":[]},{"display":"Line Added","hidingEnts":[],"value":"Line Added","hidingOrgs":[]},{"display":"Paid","hidingEnts":[],"value":"Paid","hidingOrgs":[]},{"display":"Request Schedule Added","hidingEnts":[],"value":"Request Schedule Added","hidingOrgs":[]},{"display":"Hold Reopened","hidingEnts":[],"value":"Hold Reopened","hidingOrgs":[]},{"display":"Order Promised","hidingEnts":[],"value":"Order Promised","hidingOrgs":[]},{"display":"ASN Received","hidingEnts":[],"value":"ASN Received","hidingOrgs":[]},{"display":"Receipt Closed","hidingEnts":[],"value":"Receipt Closed","hidingOrgs":[]},{"display":"Order Shipped","hidingEnts":[],"value":"Order Shipped","hidingOrgs":[]},{"display":"Order Created","hidingEnts":[],"value":"Order Created","hidingOrgs":[]},{"display":"Delivery Schedule Added","hidingEnts":[],"value":"Delivery Schedule Added","hidingOrgs":[]},{"display":"Order Submitted","hidingEnts":[],"value":"Order Submitted","hidingOrgs":[]},{"display":"Milestones Recomputed","hidingEnts":[],"value":"Milestones re-computed","hidingOrgs":[]},{"display":"Order Closed","hidingEnts":[],"value":"Order Closed","hidingOrgs":[]},{"display":"Invoice Created","hidingEnts":[],"value":"Invoice Created","hidingOrgs":[]},{"display":"Vendor Approved Order","hidingEnts":[],"value":"Vendor Approved Order","hidingOrgs":[]},{"display":"Order Approved","hidingEnts":[],"value":"Order Approved","hidingOrgs":[]},{"display":"Order Received","hidingEnts":[],"value":"Order Received","hidingOrgs":[]},{"display":"Delivery Closed","hidingEnts":[],"value":"Delivery Closed","hidingOrgs":[]},{"display":"Hold Released","hidingEnts":[],"value":"Hold Released","hidingOrgs":[]},{"display":"Hold Assigned","hidingEnts":[],"value":"Hold Assigned","hidingOrgs":[]},{"display":"Hold Deleted","hidingEnts":[],"value":"Hold Deleted","hidingOrgs":[]},{"display":"Order Force Closed","hidingEnts":[],"value":"Order Force Closed","hidingOrgs":[]},{"display":"Production Order Canceled","hidingEnts":[],"value":"Production Order Cancelled","hidingOrgs":[]},{"display":"Production Order Created","hidingEnts":[],"value":"Production Order Created","hidingOrgs":[]}]},"SCC.BufferInventoryStatus":{"enumId":"SCC.BufferInventoryStatus","success":true,"items":[{"display":"Very Low Stock","hidingEnts":[],"value":"VeryLowStock","hidingOrgs":[]},{"display":"Excess","hidingEnts":[],"value":"Excess","hidingOrgs":[]},{"display":"Low Stock","hidingEnts":[],"value":"LowStock","hidingOrgs":[]},{"display":"Sufficient Stock","hidingEnts":[],"value":"SufficientStock","hidingOrgs":[]},{"display":"Out Of Stock","hidingEnts":[],"value":"OutOfStock","hidingOrgs":[]},{"display":"Computed","hidingEnts":[],"value":"Computed","hidingOrgs":[]}]},"WeightUOM":{"enumId":"WeightUOM","success":true,"items":[{"display":"Pound","hidingEnts":[],"value":"POUND","hidingOrgs":[]},{"display":"Kilo","globallyHidden":true,"hidingEnts":[],"value":"KILO","hidingOrgs":[]},{"display":"KILOGRAM","hidingEnts":[],"value":"KILOGRAM","hidingOrgs":[]},{"display":"Gram","hidingEnts":[],"value":"GRAM","hidingOrgs":[]},{"display":"Ton","hidingEnts":[],"value":"TONS","hidingOrgs":[]},{"display":"Metric ton","globallyHidden":true,"hidingEnts":[],"value":"MTONS","hidingOrgs":[]},{"display":"Dry Pound","globallyHidden":true,"hidingEnts":[],"value":"DRY_POUND","hidingOrgs":[]},{"display":"Short ton","globallyHidden":true,"hidingEnts":[],"value":"STONS","hidingOrgs":[]},{"display":"OZ","hidingEnts":[],"value":"OZ","hidingOrgs":[]},{"display":"MILLIGRAM","hidingEnts":[],"value":"MILLIGRAM","hidingOrgs":[]}]},"ProblemOwnedOrShared":{"enumId":"ProblemOwnedOrShared","success":true,"items":[{"display":"Shared With Me","hidingEnts":[],"value":"SharedWithMe","hidingOrgs":[]},{"display":"Owned By Me","hidingEnts":[],"value":"OwnedByMe","hidingOrgs":[]}]},"SCC.PenaltyMethod":{"enumId":"SCC.PenaltyMethod","success":true,"items":[{"display":"Flat","hidingEnts":[],"value":"Flat","hidingOrgs":[]},{"display":"Per Day","hidingEnts":[],"value":"Per Day","hidingOrgs":[]},{"display":"Per Cut Percentage","hidingEnts":[],"value":"Per Cut Percentage","hidingOrgs":[]},{"display":"Per Qty UOM","hidingEnts":[],"value":"Per Qty UOM ","hidingOrgs":[]},{"display":"Percentage Of Base Price","hidingEnts":[],"value":"Percentage of Base Price","hidingOrgs":[]}]},"SCC.RegulationType":{"enumId":"SCC.RegulationType","success":true,"items":[{"display":"All","hidingEnts":[],"value":"All","hidingOrgs":[]},{"display":"IMDG","hidingEnts":[],"value":"IMDG","hidingOrgs":[]},{"display":"RID","hidingEnts":[],"value":"RID","hidingOrgs":[]},{"display":"ADR","hidingEnts":[],"value":"ADR","hidingOrgs":[]}]},"OMS.PriceCumulationType":{"enumId":"OMS.PriceCumulationType","success":true,"items":[{"display":"Per Order","hidingEnts":[],"value":"Per Order","hidingOrgs":[]},{"display":"Cumulative","hidingEnts":[],"value":"Cumulative","hidingOrgs":[]}]},"OMS.MilestoneProcessDiscoveryMethod":{"enumId":"OMS.MilestoneProcessDiscoveryMethod","success":true,"items":[{"display":"Lite","hidingEnts":[],"value":"Lite","hidingOrgs":[]},{"display":"Standard","hidingEnts":[],"value":"Standard","hidingOrgs":[]}]},"OMS.AutoPoAckMode":{"enumId":"OMS.AutoPoAckMode","success":true,"items":[{"display":"Accept To Open & Allow Change","hidingEnts":[],"value":"ACCEPT_TO_OPEN_ALLOW_CHANGE","hidingOrgs":[]},{"display":"Accept From Open","hidingEnts":[],"value":"ACCEPT_FROM_OPEN","hidingOrgs":[]},{"display":"Accept Till Open","hidingEnts":[],"value":"ACCEPT_TILL_OPEN","hidingOrgs":[]},{"display":"Accept To Open & Disallow Change","hidingEnts":[],"value":"ACCEPT_TO_OPEN_DISALLOW_CHANGE","hidingOrgs":[]},{"display":"Fully Back Ordered","hidingEnts":[],"value":"FULLY_BACK_ORDERED","hidingOrgs":[]}]},"OMS.PaymentType":{"enumId":"OMS.PaymentType","success":true,"items":[{"display":"Credit Card","hidingEnts":[],"value":"Credit Card","hidingOrgs":[]},{"display":"Account Credit","hidingEnts":[],"value":"Account Credit","hidingOrgs":[]},{"display":"Check","hidingEnts":[],"value":"Check","hidingOrgs":[]},{"display":"Cash","hidingEnts":[],"value":"Cash","hidingOrgs":[]},{"display":"Advance Purchase","hidingEnts":[],"value":"Advance Purchase","hidingOrgs":[]}]},"SCC.OrderType":{"enumId":"SCC.OrderType","success":true,"items":[{"display":"Production Order","hidingEnts":[],"value":"Production Order","hidingOrgs":[]},{"display":"Return Order","hidingEnts":[],"value":"Return Order","hidingOrgs":[]},{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Work Order","hidingEnts":[],"value":"Work Order","hidingOrgs":[]},{"display":"Order Revision","hidingEnts":[],"value":"Order Revision","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]},{"display":"Request","hidingEnts":[],"value":"Request","hidingOrgs":[]},{"display":"Blanket Order","hidingEnts":[],"value":"Blanket Order","hidingOrgs":[]},{"display":"Requisition","hidingEnts":[],"value":"Requisition","hidingOrgs":[]},{"display":"Sales Order","hidingEnts":[],"value":"Sales Order","hidingOrgs":[]}]},"WBProblemPriority":{"enumId":"WBProblemPriority","success":true,"items":[{"display":"Low","hidingEnts":[],"value":"Low","hidingOrgs":[]},{"display":"Medium","hidingEnts":[],"value":"Medium","hidingOrgs":[]},{"display":"High","hidingEnts":[],"value":"High","hidingOrgs":[]}]},"OMS.PreASNState":{"enumId":"OMS.PreASNState","success":true,"items":[{"display":"Draft","hidingEnts":[],"value":"Draft","hidingOrgs":[]},{"display":"Awaiting","hidingEnts":[],"value":"Awaiting","hidingOrgs":[]}]},"SCC.OrderLineType":{"enumId":"SCC.OrderLineType","success":true,"items":[{"display":"Freight Entered","hidingEnts":[],"value":"Freight Entered","hidingOrgs":[]},{"display":"Category","hidingEnts":[],"value":"Category","hidingOrgs":[]},{"display":"User Defined","hidingEnts":[],"value":"User Defined","hidingOrgs":[]},{"display":"Kit","hidingEnts":[],"value":"Kit","hidingOrgs":[]},{"display":"Product","hidingEnts":[],"value":"Product","hidingOrgs":[]},{"display":"Service","hidingEnts":[],"value":"Service","hidingOrgs":[]},{"display":"Freight Computed","hidingEnts":[],"value":"Freight Computed","hidingOrgs":[]},{"display":"Package","hidingEnts":[],"value":"Package","hidingOrgs":[]},{"display":"Complex","hidingEnts":[],"value":"Complex","hidingOrgs":[]}]},"IntrinsicEquipmentType":{"enumId":"IntrinsicEquipmentType","success":true,"items":[{"entName":"PepsiCo LatAm","display":"VTKG","hidingEnts":[],"value":"PepsiCo LatAm.VTKG","hidingOrgs":[]},{"display":"AIR_CARGO","globallyHidden":true,"hidingEnts":[],"value":"AIR_CARGO","hidingOrgs":[]},{"display":"CONTAINER_40_ FT_REF","globallyHidden":true,"hidingEnts":[],"value":"CONTAINER_40_FT_REF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"BONGO","hidingEnts":[],"value":"PepsiCo LatAm.BONGO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTKP","hidingEnts":[],"value":"PepsiCo LatAm.VTKP","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"OAK-40-Lead","hidingEnts":[],"value":"SAFEWAY, INC..OAK-40-Lead","hidingOrgs":[]},{"display":"AA1 (Van, closed air ride, 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AA1","hidingOrgs":[]},{"display":"3.5Ton","hidingEnts":["Piston Automotive"],"value":"3.5TON","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 40","hidingEnts":[],"value":"PepsiCo LatAm.FULL 40","hidingOrgs":[]},{"display":"AA2 (Van, closed air ride, 31-40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AA2","hidingOrgs":[]},{"display":"AA3 (Van, closed air ride, over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AA3","hidingOrgs":[]},{"display":"A5 (Tractor, air ride)","globallyHidden":true,"hidingEnts":[],"value":"A5","hidingOrgs":[]},{"display":"A6 (Tractor, other than air ride)","globallyHidden":true,"hidingEnts":[],"value":"A6","hidingOrgs":[]},{"display":"A7 (Flat bed, 30 feet and less, hooked in tandem as one unit)","globallyHidden":true,"hidingEnts":[],"value":"A7","hidingOrgs":[]},{"display":"A8 (Van, air ride, w/temperature and humidity control)","globallyHidden":true,"hidingEnts":[],"value":"A8","hidingOrgs":[]},{"display":"A9 (Van, closed, padded, w/air ride suspension 2nd & 3rd proviso only)","globallyHidden":true,"hidingEnts":[],"value":"A9","hidingOrgs":[]},{"display":"Tanker","hidingEnts":["Piston Automotive"],"value":"TANKER","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 48","hidingEnts":[],"value":"PepsiCo LatAm.FULL 48","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 45","hidingEnts":[],"value":"PepsiCo LatAm.FULL 45","hidingOrgs":[]},{"display":"QQ:DTA Same Day","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTA Same Day","hidingOrgs":[]},{"display":"QQ:DTA2D","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTA2D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 42","hidingEnts":[],"value":"PepsiCo LatAm.FULL 42","hidingOrgs":[]},{"display":"QA1 (Non milvan, 20 feet and less)","globallyHidden":true,"hidingEnts":[],"value":"QA1","hidingOrgs":[]},{"display":"Intermodal","hidingEnts":[],"value":"ITMD","hidingOrgs":[]},{"display":"QA 3","globallyHidden":true,"hidingEnts":[],"value":"QA3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT3S3","hidingOrgs":[]},{"display":"AD (Regular Dromedary)","globallyHidden":true,"hidingEnts":[],"value":"AD","hidingOrgs":[]},{"display":"QA 2","globallyHidden":true,"hidingEnts":[],"value":"QA2","hidingOrgs":[]},{"display":"FedEx 2 Day","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"FedEx 2 Day","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT3S2","hidingOrgs":[]},{"display":"QA5 (Non milvan, 40 feet)","globallyHidden":true,"hidingEnts":[],"value":"QA5","hidingOrgs":[]},{"display":"QA 4","globallyHidden":true,"hidingEnts":[],"value":"QA4","hidingOrgs":[]},{"display":"QA6 (Non milvan, 45 feet and over)","globallyHidden":true,"hidingEnts":[],"value":"QA6","hidingOrgs":[]},{"display":"AK (Van, refrigerated, perishable food)","globallyHidden":true,"hidingEnts":[],"value":"AK","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VREFR","hidingEnts":[],"value":"PepsiCo LatAm.VREFR","hidingOrgs":[]},{"display":"AN (Adjustable tilt bed trailer)","globallyHidden":true,"hidingEnts":[],"value":"AN","hidingOrgs":[]},{"display":"AO (Driveaway/Truckaway)","globallyHidden":true,"hidingEnts":[],"value":"AO","hidingOrgs":[]},{"display":"AB0 (Lowboy, level deck, 10 axles and over)","globallyHidden":true,"hidingEnts":[],"value":"AB0","hidingOrgs":[]},{"display":"AP (Aft steering unit)","globallyHidden":true,"hidingEnts":[],"value":"AP","hidingOrgs":[]},{"display":"AR (Van, refrigerated, other)","globallyHidden":true,"hidingEnts":[],"value":"AR","hidingOrgs":[]},{"display":"AB2 (Lowboy, level deck, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB2","hidingOrgs":[]},{"display":"AB3 (Lowboy, level deck, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB3","hidingOrgs":[]},{"display":"AS (Livestock transporter)","globallyHidden":true,"hidingEnts":[],"value":"AS","hidingOrgs":[]},{"display":"AB4 (Lowboy, level deck, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB4","hidingOrgs":[]},{"display":"AB5 (Lowboy, level deck, 5 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB5","hidingOrgs":[]},{"display":"AU (Container, shipper owned, environmental, temperature and humidity controlled.)","globallyHidden":true,"hidingEnts":[],"value":"AU","hidingOrgs":[]},{"display":"Parcel","hidingEnts":["Piston Automotive"],"value":"PARCEL","hidingOrgs":[]},{"display":"AB6 (Lowboy, double drop, air ride, w/outriggers, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB6","hidingOrgs":[]},{"display":"AB7 (Lowboy, level deck, 7 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB7","hidingOrgs":[]},{"display":"AX (Flat bed, all lengths (twist lock))","globallyHidden":true,"hidingEnts":[],"value":"AX","hidingOrgs":[]},{"display":"AB9 (Lowboy, level deck, 9 axles)","globallyHidden":true,"hidingEnts":[],"value":"AB9","hidingOrgs":[]},{"display":"TT:DTDEcon","globallyHidden":true,"hidingEnts":[],"value":"TT:DTDEcon","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402B2","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402B2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402B1","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402B1","hidingOrgs":[]},{"display":"KW1 (Rail - TOFC car)","globallyHidden":true,"hidingEnts":[],"value":"KW1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TQL","hidingEnts":[],"value":"PepsiCo LatAm.TQL","hidingOrgs":[]},{"display":"KW 2","globallyHidden":true,"hidingEnts":[],"value":"KW2","hidingOrgs":[]},{"display":"5 M","globallyHidden":true,"hidingEnts":[],"value":"FLATBED_16.5M","hidingOrgs":[]},{"display":"SS:DTA Same Day","globallyHidden":true,"hidingEnts":[],"value":"SS:DTA Same Day","hidingOrgs":[]},{"display":"FLT","globallyHidden":true,"hidingEnts":[],"value":"FLT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAGL","hidingEnts":[],"value":"PepsiCo LatAm.VCAGL","hidingOrgs":[]},{"display":"SS:ATAND","globallyHidden":true,"hidingEnts":[],"value":"SS:ATAND","hidingOrgs":[]},{"display":"AC2 (Expandable low bed trailer, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AC2","hidingOrgs":[]},{"display":"AC3 (Expandable low bed trailer, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AC3","hidingOrgs":[]},{"display":"CFS","hidingEnts":["Piston Automotive"],"value":"CFS","hidingOrgs":[]},{"display":"AC4 (Expandable low bed trailer, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AC4","hidingOrgs":[]},{"display":"TT:ATADAM","globallyHidden":true,"hidingEnts":[],"value":"TT:ATADAM","hidingOrgs":[]},{"display":"QQ:ATAEcon","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATAEcon","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CHASIS MED","hidingEnts":[],"value":"PepsiCo LatAm.CHASIS MED","hidingOrgs":[]},{"display":"Trailer on Flat Car","hidingEnts":["Piston Automotive"],"value":"TOFC","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT2S2","hidingOrgs":[]},{"display":"Container 40 Ft","hidingEnts":[],"value":"CONTAINER_40_FT","hidingOrgs":[]},{"display":"Multi Modal","hidingEnts":[],"value":"MULTI_MODAL","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT2S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TRN","hidingEnts":[],"value":"PepsiCo LatAm.TRN","hidingOrgs":[]},{"display":"Dry Van","hidingEnts":["Piston Automotive"],"value":"DRY_VAN","hidingOrgs":[]},{"display":"45RE","globallyHidden":true,"hidingEnts":[],"value":"45RE","hidingOrgs":[]},{"display":"TT:DTAAM","globallyHidden":true,"hidingEnts":[],"value":"TT:DTAAM","hidingOrgs":[]},{"display":"AD6 (Dromedary with Mechanical Restraining Device (MRD))","globallyHidden":true,"hidingEnts":[],"value":"AD6","hidingOrgs":[]},{"display":"RIGID_12_SP","hidingEnts":["Piston Automotive"],"value":"RIGID_12_SP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"30T","hidingEnts":[],"value":"PepsiCo LatAm.30T","hidingOrgs":[]},{"display":"Rail Reefer","hidingEnts":["Piston Automotive"],"value":"RAIL_REEFER","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABONL","hidingEnts":[],"value":"PepsiCo LatAm.RABONL","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"6TL","hidingEnts":[],"value":"PepsiCo LatAm.6TL","hidingOrgs":[]},{"display":"TT:ATA Same day","globallyHidden":true,"hidingEnts":[],"value":"TT:ATA Same day","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TOCO","hidingEnts":[],"value":"PepsiCo LatAm.TOCO","hidingOrgs":[]},{"display":"AE0 (Lowboy, double drop, 10 axles and over)","globallyHidden":true,"hidingEnts":[],"value":"AE0","hidingOrgs":[]},{"display":"QQ:ATAAM","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATAAM","hidingOrgs":[]},{"display":"Ltl In40","hidingEnts":["Piston Automotive"],"value":"LTL_IN40","hidingOrgs":[]},{"display":"AE2 (Lowboy, double drop, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE2","hidingOrgs":[]},{"display":"AE3 (Lowboy, double drop, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE3","hidingOrgs":[]},{"display":"AE4 (Lowboy, double drop, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE4","hidingOrgs":[]},{"display":"Freighter","hidingEnts":["Piston Automotive"],"value":"FREIGHTER","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VANDR","hidingEnts":[],"value":"PepsiCo LatAm.VANDR","hidingOrgs":[]},{"display":"AE5 (Lowboy, double drop, 5 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE5","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM20FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM20FT2S2","hidingOrgs":[]},{"display":"AE6 (Lowboy, double drop, w/outriggers, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE6","hidingOrgs":[]},{"display":"16_ PT","globallyHidden":true,"hidingEnts":[],"value":"16_PT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM20FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM20FT2S3","hidingOrgs":[]},{"display":"AE7 (Lowboy, double drop, 7 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE7","hidingOrgs":[]},{"display":"Thorton","hidingEnts":["Piston Automotive"],"value":"THORTON","hidingOrgs":[]},{"display":"AE9 (Lowboy, double drop, 9 axles)","globallyHidden":true,"hidingEnts":[],"value":"AE9","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTGS","hidingEnts":[],"value":"PepsiCo LatAm.VTGS","hidingOrgs":[]},{"display":"SS:ATADAM","globallyHidden":true,"hidingEnts":[],"value":"SS:ATADAM","hidingOrgs":[]},{"display":"Road Feeder Service","hidingEnts":["Piston Automotive"],"value":"RFS","hidingOrgs":[]},{"display":"SS:ATA3D","globallyHidden":true,"hidingEnts":[],"value":"SS:ATA3D","hidingOrgs":[]},{"display":"EE","globallyHidden":true,"hidingEnts":[],"value":"EE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABON1","hidingEnts":[],"value":"PepsiCo LatAm.RABON1","hidingOrgs":[]},{"display":"KZ2","hidingEnts":["Piston Automotive"],"value":"KZ2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABON2","hidingEnts":[],"value":"PepsiCo LatAm.RABON2","hidingOrgs":[]},{"display":"KZ1","hidingEnts":["Piston Automotive"],"value":"KZ1","hidingOrgs":[]},{"display":"KZ4 (Rail - Locomotive under own power, Not on own wheels)","globallyHidden":true,"hidingEnts":[],"value":"KZ4","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT3S3","hidingOrgs":[]},{"display":"KZ3 (Rail - Locomotive under own power, on own wheels)","globallyHidden":true,"hidingEnts":[],"value":"KZ3","hidingOrgs":[]},{"display":"KZ 5","globallyHidden":true,"hidingEnts":[],"value":"KZ5","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTKPL","hidingEnts":[],"value":"PepsiCo LatAm.VTKPL","hidingOrgs":[]},{"display":"AF1 (Flat bed, 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AF1","hidingOrgs":[]},{"display":"AF2 (Flat bed, 31-40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AF2","hidingOrgs":[]},{"display":"AF3 (Flat bed, over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AF3","hidingOrgs":[]},{"display":"AF4 (Flat bed w/Conestoga trailers 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AF4","hidingOrgs":[]},{"display":"AF5 (Flat bed w/Conestoga trailers 31- 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AF5","hidingOrgs":[]},{"display":"AF6 (Flat bed w/Conestoga trailers over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AF6","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VUC","hidingEnts":[],"value":"PepsiCo LatAm.VUC","hidingOrgs":[]},{"display":"TRCK_0.6T","hidingEnts":["Piston Automotive"],"value":"TRCK_0.6T","hidingOrgs":[]},{"display":"RIGID_14_SP","hidingEnts":["Piston Automotive"],"value":"RIGID_14_SP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCGS","hidingEnts":[],"value":"PepsiCo LatAm.VCGS","hidingOrgs":[]},{"display":"QQ:DTAND","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTAND","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT3S2","hidingOrgs":[]},{"display":"SS:ATA Same day","globallyHidden":true,"hidingEnts":[],"value":"SS:ATA Same day","hidingOrgs":[]},{"display":"TT:DTDND","globallyHidden":true,"hidingEnts":[],"value":"TT:DTDND","hidingOrgs":[]},{"display":"QQ:DTD3D","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTD3D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"3/4","hidingEnts":[],"value":"PepsiCo LatAm.3/4","hidingOrgs":[]},{"display":"SS:DTDAM","globallyHidden":true,"hidingEnts":[],"value":"SS:DTDAM","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"12TL","hidingEnts":[],"value":"PepsiCo LatAm.12TL","hidingOrgs":[]},{"display":"AG1 (Van, open, 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AG1","hidingOrgs":[]},{"display":"AG2 (Van, open, 31-40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AG2","hidingOrgs":[]},{"display":"AG3 (Van, open, over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AG3","hidingOrgs":[]},{"display":"AG4 (Tautliner Van w/Tarps, 30' or less)","globallyHidden":true,"hidingEnts":[],"value":"AG4","hidingOrgs":[]},{"display":"AG5 (Tautliner Van w/Tarps, 31' to 40')","globallyHidden":true,"hidingEnts":[],"value":"AG5","hidingOrgs":[]},{"display":"AG6 (Tautliner Van w/Tarps, over 40')","globallyHidden":true,"hidingEnts":[],"value":"AG6","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"24TS","hidingEnts":[],"value":"PepsiCo LatAm.24TS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"24TL","hidingEnts":[],"value":"PepsiCo LatAm.24TL","hidingOrgs":[]},{"display":"SS:DTDEcon","globallyHidden":true,"hidingEnts":[],"value":"SS:DTDEcon","hidingOrgs":[]},{"display":"QQ:DTD2D","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTD2D","hidingOrgs":[]},{"display":"FLATBED_17.5M","hidingEnts":["Piston Automotive"],"value":"FLATBED_17.5M","hidingOrgs":[]},{"display":"AH2 (Drop frame trailer, drop/step deck, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AH2","hidingOrgs":[]},{"display":"AH3 (Drop frame trailer, drop/step deck, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AH3","hidingOrgs":[]},{"display":"BDOUBLE_32_SP","hidingEnts":["Piston Automotive"],"value":"BDOUBLE_32_SP","hidingOrgs":[]},{"display":"TRCK_1.5T","hidingEnts":["Piston Automotive"],"value":"TRCK_1.5T","hidingOrgs":[]},{"display":"TRAILER_22_SP_REF","hidingEnts":["Piston Automotive"],"value":"TRAILER_22_SP_REF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 8M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 8M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT2S2","hidingOrgs":[]},{"entName":"CustomerA","display":"KK-Equip1","hidingEnts":[],"value":"CustomerA.KK-Equip1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT2S3","hidingOrgs":[]},{"display":"SS:ATA2D","globallyHidden":true,"hidingEnts":[],"value":"SS:ATA2D","hidingOrgs":[]},{"display":"Dedicated Truck","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"Dedicated Truck","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 40M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 40M3","hidingOrgs":[]},{"display":"SEMI_TRAILER_24_SP_REF","hidingEnts":["Piston Automotive"],"value":"SEMI_TRAILER_24_SP_REF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAP","hidingEnts":[],"value":"PepsiCo LatAm.VCAP","hidingOrgs":[]},{"display":"MEZZ_BDOUBLE_36_SP","hidingEnts":["Piston Automotive"],"value":"MEZZ_BDOUBLE_36_SP","hidingOrgs":[]},{"display":"AI2 (Drop frame trailer, drop/step deck, air ride, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AI2","hidingOrgs":[]},{"display":"AI3 (Drop frame trailer, drop/step deck, air ride, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AI3","hidingOrgs":[]},{"display":"SEMI_TRAILER_22_SP","hidingEnts":["Piston Automotive"],"value":"SEMI_TRAILER_22_SP","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"OAK-24-Overflow","hidingEnts":[],"value":"SAFEWAY, INC..OAK-24-Overflow","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 52M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 52M3","hidingOrgs":[]},{"display":"MEZZ_TRAILER_24_SP","hidingEnts":["Piston Automotive"],"value":"MEZZ_TRAILER_24_SP","hidingOrgs":[]},{"display":"AJ0 (Lowboy, level deck, air ride, 10 axles and over)","globallyHidden":true,"hidingEnts":[],"value":"AJ0","hidingOrgs":[]},{"display":"AJ2 (Lowboy, level deck, air ride, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ2","hidingOrgs":[]},{"display":"AJ3 (Lowboy, level deck, air ride, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ3","hidingOrgs":[]},{"display":"AJ4 (Lowboy, level deck, air ride, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ4","hidingOrgs":[]},{"display":"SS:DTD Same day","globallyHidden":true,"hidingEnts":[],"value":"SS:DTD Same day","hidingOrgs":[]},{"display":"AJ5 (Lowboy, level deck, air ride, 5 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ5","hidingOrgs":[]},{"display":"AJ6 (Lowboy, level deck, air ride, w/outriggers, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ6","hidingOrgs":[]},{"display":"AJ7 (Lowboy, level deck, air ride, 7 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ7","hidingOrgs":[]},{"display":"BDOUBLE_38_TONNE","hidingEnts":["Piston Automotive"],"value":"BDOUBLE_38_TONNE","hidingOrgs":[]},{"display":"AJ9 (Lowboy, level deck, air ride, 9 axles)","globallyHidden":true,"hidingEnts":[],"value":"AJ9","hidingOrgs":[]},{"display":"QQ:DTDEcon","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTDEcon","hidingOrgs":[]},{"display":"TT:DTD Same day","globallyHidden":true,"hidingEnts":[],"value":"TT:DTD Same day","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAPL","hidingEnts":[],"value":"PepsiCo LatAm.VCAPL","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TRUCK","hidingEnts":[],"value":"PepsiCo LatAm.TRUCK","hidingOrgs":[]},{"display":"9 RAL","globallyHidden":true,"hidingEnts":[],"value":"9RAL","hidingOrgs":[]},{"display":"Container 20 Ft","hidingEnts":[],"value":"CONTAINER_20_FT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VUCL","hidingEnts":[],"value":"PepsiCo LatAm.VUCL","hidingOrgs":[]},{"entName":"CustomerA","display":"48ft Container","hidingEnts":[],"value":"CustomerA.48ft Container","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Reefer - Team","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Reefer - Team","hidingOrgs":[]},{"display":"REEFER_8T","hidingEnts":["Piston Automotive"],"value":"REEFER_8T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"SEMI","hidingEnts":[],"value":"PepsiCo LatAm.SEMI","hidingOrgs":[]},{"display":"Ocean","hidingEnts":[],"value":"OCEA","hidingOrgs":[]},{"display":"KA","globallyHidden":true,"hidingEnts":[],"value":"KA","hidingOrgs":[]},{"display":"KC","globallyHidden":true,"hidingEnts":[],"value":"KC","hidingOrgs":[]},{"display":"KD","globallyHidden":true,"hidingEnts":[],"value":"KD","hidingOrgs":[]},{"display":"KE","globallyHidden":true,"hidingEnts":[],"value":"KE","hidingOrgs":[]},{"display":"KP","globallyHidden":true,"hidingEnts":[],"value":"KP","hidingOrgs":[]},{"display":"QQ:DTDND","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTDND","hidingOrgs":[]},{"display":"AL2 (Extendable flat bed trailer, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AL2","hidingOrgs":[]},{"display":"AL3 (Extendable flat bed trailer, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AL3","hidingOrgs":[]},{"display":"KS","globallyHidden":true,"hidingEnts":[],"value":"KS","hidingOrgs":[]},{"display":"AL4 (Extendable flat bed trailer, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AL4","hidingOrgs":[]},{"display":"KU","globallyHidden":true,"hidingEnts":[],"value":"KU","hidingOrgs":[]},{"display":"KX","globallyHidden":true,"hidingEnts":[],"value":"KX","hidingOrgs":[]},{"display":"KY","globallyHidden":true,"hidingEnts":[],"value":"KY","hidingOrgs":[]},{"display":"20_ PT","globallyHidden":true,"hidingEnts":[],"value":"20_PT","hidingOrgs":[]},{"display":"TRCK_5T","hidingEnts":["Piston Automotive"],"value":"TRCK_5T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAG","hidingEnts":[],"value":"PepsiCo LatAm.VCAG","hidingOrgs":[]},{"display":"All","hidingEnts":[],"value":"ALL","hidingOrgs":[]},{"display":"CONTAINER_20_FT_REF","hidingEnts":["Piston Automotive"],"value":"CONTAINER_20_FT_REF","hidingOrgs":[]},{"display":"TT:ATAAM","globallyHidden":true,"hidingEnts":[],"value":"TT:ATAAM","hidingOrgs":[]},{"display":"AM0 (Lowboy, double drop, air ride, 10 axles and over)","globallyHidden":true,"hidingEnts":[],"value":"AM0","hidingOrgs":[]},{"display":"AM2 (Lowboy, double drop, air ride, 2 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM2","hidingOrgs":[]},{"display":"AM3 (Lowboy, double drop, air ride, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM3","hidingOrgs":[]},{"display":"AM4 (Lowboy, double drop, air ride, 4 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM4","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTKGL","hidingEnts":[],"value":"PepsiCo LatAm.VTKGL","hidingOrgs":[]},{"display":"AM5 (Lowboy, double drop, air ride, 5 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM5","hidingOrgs":[]},{"display":"AM6 (Lowboy, double drop, air ride, w/outriggers, 3 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM6","hidingOrgs":[]},{"display":"AM7 (Lowboy, double drop, air ride, 7 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM7","hidingOrgs":[]},{"display":"AM9 (Lowboy, double drop, air ride, 9 axles)","globallyHidden":true,"hidingEnts":[],"value":"AM9","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 42M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 42M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VVANG","hidingEnts":[],"value":"PepsiCo LatAm.VVANG","hidingOrgs":[]},{"display":"TT:DTA Same Day","globallyHidden":true,"hidingEnts":[],"value":"TT:DTA Same Day","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VVANP","hidingEnts":[],"value":"PepsiCo LatAm.VVANP","hidingOrgs":[]},{"display":"MF","globallyHidden":true,"hidingEnts":[],"value":"MF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TORTON","hidingEnts":[],"value":"PepsiCo LatAm.TORTON","hidingOrgs":[]},{"display":"REEFER_10T","hidingEnts":["Piston Automotive"],"value":"REEFER_10T","hidingOrgs":[]},{"display":"Container 45 Ft","hidingEnts":[],"value":"CONTAINER_45_FT","hidingOrgs":[]},{"display":"UPS Ground","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"UPS Ground","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOPL","hidingEnts":[],"value":"PepsiCo LatAm.VTOPL","hidingOrgs":[]},{"display":"SS:DTA2D","globallyHidden":true,"hidingEnts":[],"value":"SS:DTA2D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 54M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 54M3","hidingOrgs":[]},{"display":"Rabon","hidingEnts":["Piston Automotive"],"value":"RABON","hidingOrgs":[]},{"display":"Cargo Flight","hidingEnts":["Piston Automotive"],"value":"CARGO_FLIGHT","hidingOrgs":[]},{"display":"LCL","globallyHidden":true,"hidingEnts":[],"value":"LCL","hidingOrgs":[]},{"display":"KB 2","globallyHidden":true,"hidingEnts":[],"value":"KB2","hidingOrgs":[]},{"display":"KB 1","globallyHidden":true,"hidingEnts":[],"value":"KB1","hidingOrgs":[]},{"display":"TT:DTD2D","globallyHidden":true,"hidingEnts":[],"value":"TT:DTD2D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 3","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 4","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 4","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 1","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 1","hidingOrgs":[]},{"display":"AO1 (Straight truck, enclosed van, air ride, 12 ft, 5,000 lb, max cargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 2","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 2","hidingOrgs":[]},{"display":"AO2 (Straight truck, enclosed van, air ride, 20 ft, 13,000 lb, max cargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO2","hidingOrgs":[]},{"display":"AO3 (Straight truck, enclosed van, air ride, 12 ft, 5,000 lb, max cargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO3","hidingOrgs":[]},{"display":"AO4 (Straight truck, enclosed van, air ride, 20 ft, 13,000 lb, maxcargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO4","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 5","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 5","hidingOrgs":[]},{"display":"AO5 (Straight truck, enclosed van, 20 ft, 13,000 lb, max cargo capacity, padded)","globallyHidden":true,"hidingEnts":[],"value":"AO5","hidingOrgs":[]},{"display":"Passenger Flight","hidingEnts":["Piston Automotive"],"value":"PAX_FLIGHT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEHÍCULO 6","hidingEnts":[],"value":"PepsiCo LatAm.VEHÍCULO 6","hidingOrgs":[]},{"display":"AO6 (Pickup truck, with cap, 18 ft. long, 500 lbs max cargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO6","hidingOrgs":[]},{"display":"MEZZ_BDOUBLE_34_SP","hidingEnts":["Piston Automotive"],"value":"MEZZ_BDOUBLE_34_SP","hidingOrgs":[]},{"display":"AO7 (Econo van, 17 ft long, 2,000 lbs max cargo capacity)","globallyHidden":true,"hidingEnts":[],"value":"AO7","hidingOrgs":[]},{"display":"AO8 (Dump trailer, 28 ft long, 2 axle, hydraulic powered lift)","globallyHidden":true,"hidingEnts":[],"value":"AO8","hidingOrgs":[]},{"display":"ANY","description":"Default Equipment Type for RepublicWireless","hidingEnts":[],"value":"ANY","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CABOT","hidingEnts":[],"value":"PepsiCo LatAm.CABOT","hidingOrgs":[]},{"display":"SS:DTA3D","globallyHidden":true,"hidingEnts":[],"value":"SS:DTA3D","hidingOrgs":[]},{"display":"18_ PT","globallyHidden":true,"hidingEnts":[],"value":"18_PT","hidingOrgs":[]},{"display":"TRCK_8T","hidingEnts":["Piston Automotive"],"value":"TRCK_8T","hidingOrgs":[]},{"display":"2 Day LTL","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"2 Day LTL","hidingOrgs":[]},{"display":"TT:DTAND","globallyHidden":true,"hidingEnts":[],"value":"TT:DTAND","hidingOrgs":[]},{"display":"TT:DTD3D","globallyHidden":true,"hidingEnts":[],"value":"TT:DTD3D","hidingOrgs":[]},{"display":"CUPU","globallyHidden":true,"hidingEnts":[],"value":"CUPU","hidingOrgs":[]},{"display":"RIGID_16_SP","hidingEnts":["Piston Automotive"],"value":"RIGID_16_SP","hidingOrgs":[]},{"display":"PKUP","hidingEnts":["Piston Automotive"],"value":"PKUP","hidingOrgs":[]},{"display":"SMPK","globallyHidden":true,"hidingEnts":[],"value":"SMPK","hidingOrgs":[]},{"display":"Container","hidingEnts":["Piston Automotive"],"value":"CONTAINER","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RAB1","hidingEnts":[],"value":"PepsiCo LatAm.RAB1","hidingOrgs":[]},{"entName":"QARetailer1","display":"Container 40 Ft Hc","hidingEnts":[],"value":"QARetailer1.Container 40 Ft Hc","hidingOrgs":[]},{"display":"SS:DTAEcon","globallyHidden":true,"hidingEnts":[],"value":"SS:DTAEcon","hidingOrgs":[]},{"display":"TT:DTA2D","globallyHidden":true,"hidingEnts":[],"value":"TT:DTA2D","hidingOrgs":[]},{"display":"SS:DTAND","globallyHidden":true,"hidingEnts":[],"value":"SS:DTAND","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 21M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 21M3","hidingOrgs":[]},{"display":"SS:DTD3D","globallyHidden":true,"hidingEnts":[],"value":"SS:DTD3D","hidingOrgs":[]},{"display":"RLTL","globallyHidden":true,"hidingEnts":[],"value":"RLTL","hidingOrgs":[]},{"display":"Flat Bed","hidingEnts":["Piston Automotive"],"value":"FLATBED","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"LAX-40-Lead","hidingEnts":[],"value":"SAFEWAY, INC..LAX-40-Lead","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TKPO","hidingEnts":[],"value":"PepsiCo LatAm.TKPO","hidingOrgs":[]},{"display":"Reefer","hidingEnts":["Piston Automotive"],"value":"REEFER","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABL","hidingEnts":[],"value":"PepsiCo LatAm.RABL","hidingOrgs":[]},{"display":"QM","globallyHidden":true,"hidingEnts":[],"value":"QM","hidingOrgs":[]},{"display":"A10 (410 Dromedary, 102\" L x 75 1/2\" H x 92\" W, 410 cubic feet)","globallyHidden":true,"hidingEnts":[],"value":"A10","hidingOrgs":[]},{"display":"QQ (Freight (Other than Freight Forwarder))","globallyHidden":true,"hidingEnts":[],"value":"QQ","hidingOrgs":[]},{"display":"A11 (Van, air ride, 45 ft or 48 ft, padded, equipped with crane)","globallyHidden":true,"hidingEnts":[],"value":"A11","hidingOrgs":[]},{"display":"TT:DTA3D","globallyHidden":true,"hidingEnts":[],"value":"TT:DTA3D","hidingOrgs":[]},{"display":"QU","globallyHidden":true,"hidingEnts":[],"value":"QU","hidingOrgs":[]},{"display":"A16 (Special Dromedary with MRO)","globallyHidden":true,"hidingEnts":[],"value":"A16","hidingOrgs":[]},{"display":"12_ PT","globallyHidden":true,"hidingEnts":[],"value":"12_PT","hidingOrgs":[]},{"display":"FedEx Ground","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"FedEx Ground","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 33M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 33M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 56M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 56M3","hidingOrgs":[]},{"display":"QQ:DTAAM","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTAAM","hidingOrgs":[]},{"display":"R2T","hidingEnts":["Piston Automotive"],"value":"R2T","hidingOrgs":[]},{"display":"RIGID_18_SP","hidingEnts":["Piston Automotive"],"value":"RIGID_18_SP","hidingOrgs":[]},{"display":"KF2 (Rail - Flat, any other type over 70' but not over 90')","globallyHidden":true,"hidingEnts":[],"value":"KF2","hidingOrgs":[]},{"display":"SEMI_TRAILER_25_TONNE","hidingEnts":["Piston Automotive"],"value":"SEMI_TRAILER_25_TONNE","hidingOrgs":[]},{"display":"KF 1","globallyHidden":true,"hidingEnts":[],"value":"KF1","hidingOrgs":[]},{"display":"9 OCN","globallyHidden":true,"hidingEnts":[],"value":"9OCN","hidingOrgs":[]},{"display":"A20 (Motor vehicle transport trailer)","globallyHidden":true,"hidingEnts":[],"value":"A20","hidingOrgs":[]},{"display":"In40","hidingEnts":["Piston Automotive"],"value":"IN40","hidingOrgs":[]},{"display":"IN45","hidingEnts":["Piston Automotive"],"value":"IN45","hidingOrgs":[]},{"display":"Rtk","hidingEnts":["Piston Automotive"],"value":"RTK","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"BALANCIN","hidingEnts":[],"value":"PepsiCo LatAm.BALANCIN","hidingOrgs":[]},{"display":"QQ:ATA3D","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATA3D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402R2","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402R2","hidingOrgs":[]},{"display":"In48","hidingEnts":["Piston Automotive"],"value":"IN48","hidingOrgs":[]},{"display":"9 AIR","globallyHidden":true,"hidingEnts":[],"value":"9AIR","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 53","hidingEnts":[],"value":"PepsiCo LatAm.REM 53","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CROSS","hidingEnts":[],"value":"PepsiCo LatAm.CROSS","hidingOrgs":[]},{"display":"SEMI_TRAILER_24_SP","hidingEnts":["Piston Automotive"],"value":"SEMI_TRAILER_24_SP","hidingOrgs":[]},{"display":"OCMM","globallyHidden":true,"hidingEnts":[],"value":"OCMM","hidingOrgs":[]},{"entName":"Piston Automotive","display":"QQ:Air","hidingEnts":[],"value":"Piston Automotive.QQ:Air","hidingOrgs":[]},{"display":"KG 1","globallyHidden":true,"hidingEnts":[],"value":"KG1","hidingOrgs":[]},{"display":"KG 2","globallyHidden":true,"hidingEnts":[],"value":"KG2","hidingOrgs":[]},{"display":"Ltl Rail","hidingEnts":["Piston Automotive"],"value":"LTL_RAIL","hidingOrgs":[]},{"display":"A30 (Removable gooseneck)","globallyHidden":true,"hidingEnts":[],"value":"A30","hidingOrgs":[]},{"display":"AT1 (Tank, 5001-8000 gallons)","globallyHidden":true,"hidingEnts":[],"value":"AT1","hidingOrgs":[]},{"display":"LRTK","globallyHidden":true,"hidingEnts":[],"value":"LRTK","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"LAX-40-Overflow","hidingEnts":[],"value":"SAFEWAY, INC..LAX-40-Overflow","hidingOrgs":[]},{"display":"AT2 (Tank, over 8000 gallons)","globallyHidden":true,"hidingEnts":[],"value":"AT2","hidingOrgs":[]},{"display":"IN 53 Heated","hidingEnts":["Piston Automotive"],"value":"IN_53_HEATED","hidingOrgs":[]},{"display":"SS (Charter)","globallyHidden":true,"hidingEnts":[],"value":"SS","hidingOrgs":[]},{"display":"Bus","hidingEnts":["Piston Automotive"],"value":"BUS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"SILOR","hidingEnts":[],"value":"PepsiCo LatAm.SILOR","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"MUDANZA","hidingEnts":[],"value":"PepsiCo LatAm.MUDANZA","hidingOrgs":[]},{"display":"MEZZ_TRAILER_22_SP","hidingEnts":["Piston Automotive"],"value":"MEZZ_TRAILER_22_SP","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"GH Trailer","hidingEnts":[],"value":"One Beliveau Enterprises.GH Trailer","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 40","hidingEnts":[],"value":"PepsiCo LatAm.REM 40","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 42","hidingEnts":[],"value":"PepsiCo LatAm.REM 42","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 45","hidingEnts":[],"value":"PepsiCo LatAm.REM 45","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 48","hidingEnts":[],"value":"PepsiCo LatAm.REM 48","hidingOrgs":[]},{"entName":"CustomerA","display":"12 Pallet Rigid","hidingEnts":[],"value":"CustomerA.12 Pallet Rigid","hidingOrgs":[]},{"display":"BDOUBLE_36_SP","hidingEnts":["Piston Automotive"],"value":"BDOUBLE_36_SP","hidingOrgs":[]},{"display":"KH 2","globallyHidden":true,"hidingEnts":[],"value":"KH2","hidingOrgs":[]},{"display":"KH 1","globallyHidden":true,"hidingEnts":[],"value":"KH1","hidingOrgs":[]},{"entName":"CustomerA","display":"14 Pallet Rigid","hidingEnts":[],"value":"CustomerA.14 Pallet Rigid","hidingOrgs":[]},{"display":"KH 4","globallyHidden":true,"hidingEnts":[],"value":"KH4","hidingOrgs":[]},{"display":"Ltl Trck","hidingEnts":[],"value":"LTL_TRCK","hidingOrgs":[]},{"display":"KH 3","globallyHidden":true,"hidingEnts":[],"value":"KH3","hidingOrgs":[]},{"display":"KH 6","globallyHidden":true,"hidingEnts":[],"value":"KH6","hidingOrgs":[]},{"display":"KH 5","globallyHidden":true,"hidingEnts":[],"value":"KH5","hidingOrgs":[]},{"display":"A40 (Flat bed trailer, hot shot, 40 ft and over)","globallyHidden":true,"hidingEnts":[],"value":"A40","hidingOrgs":[]},{"display":"TT (Freight Forwarder)","globallyHidden":true,"hidingEnts":[],"value":"TT","hidingOrgs":[]},{"display":"Bc50","hidingEnts":["Piston Automotive"],"value":"BC50","hidingOrgs":[]},{"display":"TT:ATAEcon","globallyHidden":true,"hidingEnts":[],"value":"TT:ATAEcon","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"UTILITARIO","hidingEnts":[],"value":"PepsiCo LatAm.UTILITARIO","hidingOrgs":[]},{"display":"9Pig","hidingEnts":["Piston Automotive"],"value":"9PIG","hidingOrgs":[]},{"display":"TT:DTAEcon","globallyHidden":true,"hidingEnts":[],"value":"TT:DTAEcon","hidingOrgs":[]},{"display":"REEFER_1T","hidingEnts":["Piston Automotive"],"value":"REEFER_1T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOGL","hidingEnts":[],"value":"PepsiCo LatAm.VTOGL","hidingOrgs":[]},{"display":"TRCK_2T","hidingEnts":[],"value":"TRCK_2T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"NV","hidingEnts":[],"value":"PepsiCo LatAm.NV","hidingOrgs":[]},{"display":"Ltl Dry","hidingEnts":["Piston Automotive"],"value":"LTL_DRY","hidingOrgs":[]},{"display":"A50 (Van, closed, padded/logistics type, freight only, w/air ride suspension, 40 ft+)","globallyHidden":true,"hidingEnts":[],"value":"A50","hidingOrgs":[]},{"display":"AV1 (Van, closed, 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AV1","hidingOrgs":[]},{"display":"AV2 (Van, closed, 31-40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AV2","hidingOrgs":[]},{"display":"AV3 (Van, closed, over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AV3","hidingOrgs":[]},{"display":"AV4 (Van, closed, Rollerbed, 40 ft, fixed rollers)","globallyHidden":true,"hidingEnts":[],"value":"AV4","hidingOrgs":[]},{"display":"AV5 (Van, closed, Rollerbed, 40 ft, retractable rollers)","globallyHidden":true,"hidingEnts":[],"value":"AV5","hidingOrgs":[]},{"display":"In53","hidingEnts":["Piston Automotive"],"value":"IN53","hidingOrgs":[]},{"display":"AV6 (Van, closed, Rollerbed, 45 ft and over, fixed rollers)","globallyHidden":true,"hidingEnts":[],"value":"AV6","hidingOrgs":[]},{"display":"AV7 (Van, closed, Rollerbed, 45 ft and over, retractable rollers)","globallyHidden":true,"hidingEnts":[],"value":"AV7","hidingOrgs":[]},{"display":"AV8 (Van, closed, 45 to 48 ft, 12' 4\" high)","globallyHidden":true,"hidingEnts":[],"value":"AV8","hidingOrgs":[]},{"display":"Bc60","hidingEnts":["Piston Automotive"],"value":"BC60","hidingOrgs":[]},{"display":"MEZZ_BDOUBLE_37_SP","hidingEnts":["Piston Automotive"],"value":"MEZZ_BDOUBLE_37_SP","hidingOrgs":[]},{"display":"QQ:ATA2D","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATA2D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 58M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 58M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 20","hidingEnts":[],"value":"PepsiCo LatAm.REM 20","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FRAC","hidingEnts":[],"value":"PepsiCo LatAm.FRAC","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 12M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 12M3","hidingOrgs":[]},{"display":"9 TRK","hidingEnts":["Piston Automotive"],"value":"9TRK","hidingOrgs":[]},{"display":"REEFER_2T","hidingEnts":["Piston Automotive"],"value":"REEFER_2T","hidingOrgs":[]},{"display":"RIGID_20_SP","hidingEnts":["Piston Automotive"],"value":"RIGID_20_SP","hidingOrgs":[]},{"display":"Ndsp","hidingEnts":["Piston Automotive"],"value":"NDSP","hidingOrgs":[]},{"display":"22_ PT","globallyHidden":true,"hidingEnts":[],"value":"22_PT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"SILO","hidingEnts":[],"value":"PepsiCo LatAm.SILO","hidingOrgs":[]},{"display":"Frozen","hidingEnts":["Piston Automotive"],"value":"FROZEN","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"LAX-24-Overflow","hidingEnts":[],"value":"SAFEWAY, INC..LAX-24-Overflow","hidingOrgs":[]},{"display":"WA","globallyHidden":true,"hidingEnts":[],"value":"WA","hidingOrgs":[]},{"display":"WE","globallyHidden":true,"hidingEnts":[],"value":"WE","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VANETTE","hidingEnts":[],"value":"PepsiCo LatAm.VANETTE","hidingOrgs":[]},{"display":"CMGL","globallyHidden":true,"hidingEnts":[],"value":"CMGL","hidingOrgs":[]},{"display":"KK 1","globallyHidden":true,"hidingEnts":[],"value":"KK1","hidingOrgs":[]},{"display":"WG","globallyHidden":true,"hidingEnts":[],"value":"WG","hidingOrgs":[]},{"display":"BDOUBLE_34_SP_REF","hidingEnts":["Piston Automotive"],"value":"BDOUBLE_34_SP_REF","hidingOrgs":[]},{"display":"WI","globallyHidden":true,"hidingEnts":[],"value":"WI","hidingOrgs":[]},{"display":"KK 2","globallyHidden":true,"hidingEnts":[],"value":"KK2","hidingOrgs":[]},{"display":"WK","globallyHidden":true,"hidingEnts":[],"value":"WK","hidingOrgs":[]},{"display":"WM","globallyHidden":true,"hidingEnts":[],"value":"WM","hidingOrgs":[]},{"display":"FedEx Express Saver","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"FedEx Express Saver","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CESP","hidingEnts":[],"value":"PepsiCo LatAm.CESP","hidingOrgs":[]},{"display":"WP","globallyHidden":true,"hidingEnts":[],"value":"WP","hidingOrgs":[]},{"display":"SS:ATAAM","globallyHidden":true,"hidingEnts":[],"value":"SS:ATAAM","hidingOrgs":[]},{"display":"SS:DTD2D","globallyHidden":true,"hidingEnts":[],"value":"SS:DTD2D","hidingOrgs":[]},{"display":"10_ PT","globallyHidden":true,"hidingEnts":[],"value":"10_PT","hidingOrgs":[]},{"display":"REEFER_5T","hidingEnts":["Piston Automotive"],"value":"REEFER_5T","hidingOrgs":[]},{"display":"QQ:DTAEcon","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTAEcon","hidingOrgs":[]},{"display":"QQ:DTDAM","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTDAM","hidingOrgs":[]},{"display":"KL 2","globallyHidden":true,"hidingEnts":[],"value":"KL2","hidingOrgs":[]},{"display":"TRCK_1T","hidingEnts":["Piston Automotive"],"value":"TRCK_1T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABL2","hidingEnts":[],"value":"PepsiCo LatAm.RABL2","hidingOrgs":[]},{"display":"KL 1","globallyHidden":true,"hidingEnts":[],"value":"KL1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABL1","hidingEnts":[],"value":"PepsiCo LatAm.RABL1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"LTL17","hidingEnts":[],"value":"PepsiCo LatAm.LTL17","hidingOrgs":[]},{"display":"Ltl Reefer","hidingEnts":["Piston Automotive"],"value":"LTL_REEFER","hidingOrgs":[]},{"display":"AY1 (Van, closed, 30 ft and less, double type single unit)","globallyHidden":true,"hidingEnts":[],"value":"AY1","hidingOrgs":[]},{"display":"AY2 (Van, closed, 30 ft and less, hooked in tandem as one unit)","globallyHidden":true,"hidingEnts":[],"value":"AY2","hidingOrgs":[]},{"display":"Container 45 Ft Hc","hidingEnts":["Piston Automotive"],"value":"CONTAINER_45_FT_HC","hidingOrgs":[]},{"display":"Van","hidingEnts":[],"value":"VAN","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"OAK-24-Lead","hidingEnts":[],"value":"SAFEWAY, INC..OAK-24-Lead","hidingOrgs":[]},{"display":"QQ:ATAND","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATAND","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"LAX-24-Lead","hidingEnts":[],"value":"SAFEWAY, INC..LAX-24-Lead","hidingOrgs":[]},{"display":"REEFER_1.5T","hidingEnts":["Piston Automotive"],"value":"REEFER_1.5T","hidingOrgs":[]},{"display":"TT:ATAND","globallyHidden":true,"hidingEnts":[],"value":"TT:ATAND","hidingOrgs":[]},{"display":"FedEx Overnight","description":"Ship Via mapping for RepublicWireless","hidingEnts":[],"value":"FedEx Overnight","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VBITP","hidingEnts":[],"value":"PepsiCo LatAm.VBITP","hidingOrgs":[]},{"display":"CONTAINER_45_FT_REF","hidingEnts":["Piston Automotive"],"value":"CONTAINER_45_FT_REF","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VBITG","hidingEnts":[],"value":"PepsiCo LatAm.VBITG","hidingOrgs":[]},{"display":"AZ1 (Flat bed, air ride, 30 ft and less)","globallyHidden":true,"hidingEnts":[],"value":"AZ1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT3S2","hidingOrgs":[]},{"display":"AZ2 (Flat bed, air ride, 31-40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AZ2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT3S3","hidingOrgs":[]},{"display":"AZ3 (Flat bed, air ride, over 40 ft)","globallyHidden":true,"hidingEnts":[],"value":"AZ3","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Dry Van - Team","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Dry Van - Team","hidingOrgs":[]},{"entName":"CustomerA","display":"KK-Equip","hidingEnts":[],"value":"CustomerA.KK-Equip","hidingOrgs":[]},{"display":"8X","globallyHidden":true,"hidingEnts":[],"value":"8X","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"16T","hidingEnts":[],"value":"PepsiCo LatAm.16T","hidingOrgs":[]},{"display":"TT:DTDAM","globallyHidden":true,"hidingEnts":[],"value":"TT:DTDAM","hidingOrgs":[]},{"display":"45GP","globallyHidden":true,"hidingEnts":[],"value":"45GP","hidingOrgs":[]},{"display":"BDOUBLE_34_SP","hidingEnts":["Piston Automotive"],"value":"BDOUBLE_34_SP","hidingOrgs":[]},{"display":"OC 20","globallyHidden":true,"hidingEnts":[],"value":"OC20","hidingOrgs":[]},{"display":"22GP","globallyHidden":true,"hidingEnts":[],"value":"22GP","hidingOrgs":[]},{"display":"Truck","hidingEnts":[],"value":"TRCK","hidingOrgs":[]},{"display":"Ltl Rtk","hidingEnts":["Piston Automotive"],"value":"LTL_RTK","hidingOrgs":[]},{"display":"REEFER_0.6T","hidingEnts":["Piston Automotive"],"value":"REEFER_0.6T","hidingOrgs":[]},{"display":"KO 1","globallyHidden":true,"hidingEnts":[],"value":"KO1","hidingOrgs":[]},{"display":"KO 3","globallyHidden":true,"hidingEnts":[],"value":"KO3","hidingOrgs":[]},{"display":"KO2 (Rail - Box any other type, over 52' 6\", but not over 60' 9\")","globallyHidden":true,"hidingEnts":[],"value":"KO2","hidingOrgs":[]},{"display":"FLATBED_9.6M","hidingEnts":["Piston Automotive"],"value":"FLATBED_9.6M","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCPS","hidingEnts":[],"value":"PepsiCo LatAm.VCPS","hidingOrgs":[]},{"display":"RFRR","globallyHidden":true,"hidingEnts":[],"value":"RFRR","hidingOrgs":[]},{"display":"SS:DTDND","globallyHidden":true,"hidingEnts":[],"value":"SS:DTDND","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT2S3","hidingOrgs":[]},{"display":"HHTL","hidingEnts":["Piston Automotive"],"value":"HHTL","hidingOrgs":[]},{"display":"Ltl 9Pig","hidingEnts":["Piston Automotive"],"value":"LTL_9PIG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TRTN","hidingEnts":[],"value":"PepsiCo LatAm.TRTN","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT2S2","hidingOrgs":[]},{"display":"QQ:ATADAM","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATADAM","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"BTREM","hidingEnts":[],"value":"PepsiCo LatAm.BTREM","hidingOrgs":[]},{"display":"FLATBED_13M","hidingEnts":["Piston Automotive"],"value":"FLATBED_13M","hidingOrgs":[]},{"display":"14_ PT","globallyHidden":true,"hidingEnts":[],"value":"14_PT","hidingOrgs":[]},{"display":"Ltl In53","hidingEnts":["Piston Automotive"],"value":"LTL_IN53","hidingOrgs":[]},{"display":"OC 45","globallyHidden":true,"hidingEnts":[],"value":"OC45","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"6T","hidingEnts":[],"value":"PepsiCo LatAm.6T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VRODG","hidingEnts":[],"value":"PepsiCo LatAm.VRODG","hidingOrgs":[]},{"display":"OCTP","hidingEnts":["Piston Automotive"],"value":"OCTP","hidingOrgs":[]},{"display":"OC 40","globallyHidden":true,"hidingEnts":[],"value":"OC40","hidingOrgs":[]},{"display":"1Ton","hidingEnts":["Piston Automotive"],"value":"1TON","hidingOrgs":[]},{"display":"Ltl In48","hidingEnts":["Piston Automotive"],"value":"LTL_IN48","hidingOrgs":[]},{"display":"QQ:DTD Same day","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTD Same day","hidingOrgs":[]},{"display":"9 OMX","globallyHidden":true,"hidingEnts":[],"value":"9OMX","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VRODP","hidingEnts":[],"value":"PepsiCo LatAm.VRODP","hidingOrgs":[]},{"display":"OC 48","globallyHidden":true,"hidingEnts":[],"value":"OC48","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOP","hidingEnts":[],"value":"PepsiCo LatAm.VTOP","hidingOrgs":[]},{"display":"SS:DTAAM","globallyHidden":true,"hidingEnts":[],"value":"SS:DTAAM","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"24T","hidingEnts":[],"value":"PepsiCo LatAm.24T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTPS","hidingEnts":[],"value":"PepsiCo LatAm.VTPS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"12T","hidingEnts":[],"value":"PepsiCo LatAm.12T","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C160M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C160M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"LIVIANO","hidingEnts":[],"value":"PepsiCo LatAm.LIVIANO","hidingOrgs":[]},{"display":"KR 2","globallyHidden":true,"hidingEnts":[],"value":"KR2","hidingOrgs":[]},{"display":"KR 1","globallyHidden":true,"hidingEnts":[],"value":"KR1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCOI","hidingEnts":[],"value":"PepsiCo LatAm.VCOI","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCOG","hidingEnts":[],"value":"PepsiCo LatAm.VCOG","hidingOrgs":[]},{"display":"Rail","hidingEnts":[],"value":"RAIL","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 5M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 5M3","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"OAK-40-Overflow","hidingEnts":[],"value":"SAFEWAY, INC..OAK-40-Overflow","hidingOrgs":[]},{"display":"NON_OP_REEFER","hidingEnts":["Piston Automotive"],"value":"NON_OP_REEFER","hidingOrgs":[]},{"display":"LTL","globallyHidden":true,"hidingEnts":[],"value":"LTL","hidingOrgs":[]},{"display":"TT:ATA3D","globallyHidden":true,"hidingEnts":[],"value":"TT:ATA3D","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFULL48","hidingEnts":[],"value":"PepsiCo LatAm.REMFULL48","hidingOrgs":[]},{"display":"Container 40 Ft Hc","hidingEnts":[],"value":"CONTAINER_40_FT_HC","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TQ","hidingEnts":[],"value":"PepsiCo LatAm.TQ","hidingOrgs":[]},{"display":"TRHH","hidingEnts":["Piston Automotive"],"value":"TRHH","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAMIONETA","hidingEnts":[],"value":"PepsiCo LatAm.CAMIONETA","hidingOrgs":[]},{"display":"TT:ATA2D","globallyHidden":true,"hidingEnts":[],"value":"TT:ATA2D","hidingOrgs":[]},{"display":"KT 2","globallyHidden":true,"hidingEnts":[],"value":"KT2","hidingOrgs":[]},{"display":"KT 1","globallyHidden":true,"hidingEnts":[],"value":"KT1","hidingOrgs":[]},{"display":"KT 3","globallyHidden":true,"hidingEnts":[],"value":"KT3","hidingOrgs":[]},{"display":"QQ:ATA Same day","globallyHidden":true,"hidingEnts":[],"value":"QQ:ATA Same day","hidingOrgs":[]},{"display":"SS:ATAEcon","globallyHidden":true,"hidingEnts":[],"value":"SS:ATAEcon","hidingOrgs":[]},{"display":"24_ PT","globallyHidden":true,"hidingEnts":[],"value":"24_PT","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOG","hidingEnts":[],"value":"PepsiCo LatAm.VTOG","hidingOrgs":[]},{"display":"QQ:DTA3D","globallyHidden":true,"hidingEnts":[],"value":"QQ:DTA3D","hidingOrgs":[]},{"display":"TRCK_10T","hidingEnts":["Piston Automotive"],"value":"TRCK_10T","hidingOrgs":[]}]},"OMS.ItemInfoAttributes":{"enumId":"OMS.ItemInfoAttributes","success":true,"items":[]},"SCC.ShipmentPackageType":{"enumId":"SCC.ShipmentPackageType","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"TMS.EquipmentModel":{"enumId":"TMS.EquipmentModel","success":true,"rows":[{"display":"Default","value":"Default"}],"items":[{"display":"Default","value":"Default"}]},"SCC.Size":{"enumId":"SCC.Size","success":true,"items":[{"display":"45","hidingEnts":[],"value":"45","hidingOrgs":[]},{"display":"48","hidingEnts":[],"value":"48","hidingOrgs":[]},{"display":"53","hidingEnts":[],"value":"53","hidingOrgs":[]},{"display":"Other","hidingEnts":[],"value":"Other","hidingOrgs":[]}]},"FreightTerms":{"enumId":"FreightTerms","success":true,"items":[{"display":"Collect","hidingEnts":[],"value":"COLLECT","hidingOrgs":[]},{"display":"Prepaid","hidingEnts":[],"value":"PREPAID","hidingOrgs":[]},{"display":"Third Party","hidingEnts":[],"value":"THIRD_PARTY","hidingOrgs":[]}]},"OMS.AutoCreateShipment":{"enumId":"OMS.AutoCreateShipment","success":true,"items":[{"display":"New","hidingEnts":[],"value":"New","hidingOrgs":[]},{"display":"No","hidingEnts":[],"value":"No","hidingOrgs":[]},{"display":"Awaiting Approval","hidingEnts":[],"value":"Awaiting Approval","hidingOrgs":[]},{"display":"In Promising","hidingEnts":[],"value":"In Promising","hidingOrgs":[]},{"display":"Open","hidingEnts":[],"value":"Open","hidingOrgs":[]}]},"SCH.AppointmentChangeReasonCodes":{"enumId":"SCH.AppointmentChangeReasonCodes","success":true,"items":[{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***plant","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***plant","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***corporate","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***corporate","hidingOrgs":[]},{"entName":"QARetailer1","display":"Different slot","hidingEnts":[],"value":"QARetailer1.Different slot","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Carrier Request","hidingEnts":["Dollar General"],"value":"Dollar General.Carrier - Carrier Request","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Stock not available","hidingEnts":[],"value":"Home Retail Group.Stock not available","hidingOrgs":[]},{"entName":"Dollar General","display":"RBCL USE ONLY","hidingEnts":[],"value":"Dollar General.RBCL USE ONLY","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant - Inventory Discrepancy","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant - Inventory Discrepancy","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Delivered to Wrong Site","hidingEnts":[],"value":"Home Retail Group.Delivered to Wrong Site","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron slow","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron slow","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor","hidingEnts":[],"value":"SAFEWAY, INC..Delay at Vendor","hidingOrgs":[]},{"entName":"Dollar General","display":"System Suggested","hidingEnts":["Dollar General"],"value":"Dollar General.System Suggested","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Miscellaneous (Notes Required)","hidingEnts":["Dollar General"],"value":"Dollar General.Other - Miscellaneous (Notes Required)","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures","hidingEnts":[],"value":"SAFEWAY, INC..Road Closures","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Test","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Test","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - Product not Ready","hidingEnts":["Dollar General"],"value":"Dollar General.Vendor - Product not Ready","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - Approved","hidingEnts":["Dollar General"],"value":"Dollar General.DG - Approved","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Moved to accommodate another supplier","hidingEnts":[],"value":"Home Retail Group.Moved to accommodate another supplier","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable","hidingEnts":[],"value":"SAFEWAY, INC..Product Unavailable","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure |C","hidingEnts":[],"value":"SAFEWAY, INC..Truck/Equipment Failure |C","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron too fast","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron too fast","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - Approved (Notes Required)","hidingEnts":["Dollar General"],"value":"Dollar General.DG - Approved (Notes Required)","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant Exception for Shortage","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant Exception for Shortage","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 90 WM window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 90 WM window","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Admin Error delivery restrictions missed","hidingEnts":[],"value":"Home Retail Group.Admin Error delivery restrictions missed","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Failed Load","hidingEnts":[],"value":"Home Retail Group.Failed Load","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* LTL for Delivered Loads Only","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* LTL for Delivered Loads Only","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Driver","hidingEnts":[],"value":"Dollar General.Carrier - Driver","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Traffic requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Traffic requested change","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Truck Missed","hidingEnts":[],"value":"KIK INTERNATIONAL.Truck Missed","hidingOrgs":[]},{"entName":"Dollar General","display":"Weather - (Notes Required)","hidingEnts":[],"value":"Dollar General.Weather - (Notes Required)","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Container not dug out","hidingEnts":[],"value":"Home Retail Group.Container not dug out","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Plant Cancelled","hidingEnts":["SAFEWAY, INC."],"value":"SAFEWAY, INC..Plant Cancelled","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Rail Delay","hidingEnts":[],"value":"Dollar General.Carrier - Rail Delay","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 94 customer window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 94 customer window","hidingOrgs":[]},{"display":"Appt Not Available","hidingEnts":["Dollar General","Home Retail Group"],"value":"Appt not available","hidingOrgs":[]},{"entName":"Home Retail Group","display":"No Drivers available","hidingEnts":[],"value":"Home Retail Group.No Drivers available","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather |W","hidingEnts":[],"value":"SAFEWAY, INC..Weather |W","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Haulage not available","hidingEnts":[],"value":"Home Retail Group.Haulage not available","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Port Closed due to Bad Weather","hidingEnts":[],"value":"Home Retail Group.Port Closed due to Bad Weather","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Corporate requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Corporate requested change","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Capacity constraints","hidingEnts":[],"value":"Home Retail Group.Capacity constraints","hidingOrgs":[]},{"display":"Customer Requested Change","hidingEnts":["Dollar General"],"value":"Customer requested change","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Train Delays","hidingEnts":[],"value":"Home Retail Group.Train Delays","hidingOrgs":[]},{"entName":"Home Retail Group","display":"not passed through to sterling/WMOS","hidingEnts":[],"value":"Home Retail Group.not passed through to sterling/WMOS","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures |W","hidingEnts":[],"value":"SAFEWAY, INC..Road Closures |W","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue","hidingEnts":[],"value":"SAFEWAY, INC..Border Issue","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Unable to deliver on nights","hidingEnts":[],"value":"Home Retail Group.Unable to deliver on nights","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***traffic","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***traffic","hidingOrgs":[]},{"entName":"Dollar General","display":"Incorrect Transit","hidingEnts":[],"value":"Dollar General.Incorrect Transit","hidingOrgs":[]},{"entName":"Dollar General","display":"FOB ONLY - DC Capacity","hidingEnts":["Dollar General"],"value":"Dollar General.FOB ONLY - DC Capacity","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Delayed on Previous Job","hidingEnts":[],"value":"Home Retail Group.Delayed on Previous Job","hidingOrgs":[]},{"display":"AUTO CANDIDATE","hidingEnts":["Grocery Outlet Inc."],"value":"AUTO CANDIDATE","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Plant requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Plant requested change","hidingOrgs":[]},{"entName":"Home Retail Group","display":"K&N Unable to deliver","hidingEnts":[],"value":"Home Retail Group.K&N Unable to deliver","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Missed Pickup/Delivery Appointment","hidingEnts":["Dollar General"],"value":"Dollar General.Carrier - Missed Pickup/Delivery Appointment","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC |D","hidingEnts":[],"value":"SAFEWAY, INC..Delay at DC |D","hidingOrgs":[]},{"entName":"Home Retail Group","display":"K&N Failed to Collect","hidingEnts":[],"value":"Home Retail Group.K&N Failed to Collect","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Destination |D","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Destination |D","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Origin |V","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Origin |V","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - First Available Appt.","hidingEnts":[],"value":"Dollar General.Vendor - First Available Appt.","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Appt Change Reason Code 1","hidingEnts":[],"value":"One Beliveau Enterprises.Appt Change Reason Code 1","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - Approved Early (Notes Required)","hidingEnts":["Dollar General"],"value":"Dollar General.DG - Approved Early (Notes Required)","hidingOrgs":[]},{"entName":"Dollar General","display":"DC - Approved","hidingEnts":["Dollar General"],"value":"Dollar General.DC - Approved","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Mixed container planning issues","hidingEnts":[],"value":"Home Retail Group.Mixed container planning issues","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Weather (Notes Required)","hidingEnts":["Dollar General"],"value":"Dollar General.Other - Weather (Notes Required)","hidingOrgs":[]},{"entName":"Dollar General","display":"TransOps - Pull Forward","hidingEnts":["Dollar General"],"value":"Dollar General.TransOps - Pull Forward","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - Pull Forward (Notes Required)","hidingEnts":[],"value":"Dollar General.DG - Pull Forward (Notes Required)","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Missed Pickup/Delivery Appt.","hidingEnts":[],"value":"Dollar General.Carrier - Missed Pickup/Delivery Appt.","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Stock needed earlier than planned","hidingEnts":[],"value":"Home Retail Group.Stock needed earlier than planned","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"LTL Loads","hidingEnts":[],"value":"KIK INTERNATIONAL.LTL Loads","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather","hidingEnts":[],"value":"SAFEWAY, INC..Weather","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Message\")","hidingEnts":[],"value":"SAFEWAY, INC..Other (please specify in \"Message\")","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Not Cleared Customs","hidingEnts":[],"value":"Home Retail Group.Not Cleared Customs","hidingOrgs":[]},{"entName":"CustomerA","display":"test","hidingEnts":[],"value":"CustomerA.test","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Early Delivery","hidingEnts":[],"value":"Dollar General.Carrier - Early Delivery","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Comments\")","hidingEnts":[],"value":"SAFEWAY, INC..Other (please specify in \"Comments\")","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - TransOps Request","hidingEnts":["Dollar General"],"value":"Dollar General.DG - TransOps Request","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Capacity/Planning","hidingEnts":[],"value":"Dollar General.Carrier - Capacity/Planning","hidingOrgs":[]},{"entName":"QARetailer1","display":"No Candidates Provided","hidingEnts":[],"value":"QARetailer1.No Candidates Provided","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Driver called in sick","hidingEnts":[],"value":"Home Retail Group.Driver called in sick","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Breakdown/Accident","hidingEnts":[],"value":"Dollar General.Carrier - Breakdown/Accident","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Out of Hours","hidingEnts":["Dollar General"],"value":"Dollar General.Carrier - Out of Hours","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Production Delay","hidingEnts":[],"value":"Home Retail Group.Production Delay","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Mixed SI missed off request","hidingEnts":[],"value":"Home Retail Group.Mixed SI missed off request","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - Not Ready","hidingEnts":[],"value":"Dollar General.Vendor - Not Ready","hidingOrgs":[]},{"entName":"CustomerA","display":"testappt","hidingEnts":[],"value":"CustomerA.testappt","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Load Swap","hidingEnts":[],"value":"Home Retail Group.Load Swap","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Change in delivery method","hidingEnts":[],"value":"Home Retail Group.Change in delivery method","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation |C","hidingEnts":[],"value":"SAFEWAY, INC..DOT Violation |C","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Labelling Issue/Stock not Labelled","hidingEnts":[],"value":"Home Retail Group.Labelling Issue/Stock not Labelled","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Transit time allows early delivery","hidingEnts":[],"value":"KIK INTERNATIONAL.Transit time allows early delivery","hidingOrgs":[]},{"entName":"Grocery Outlet Inc.","display":"Missed Appointment","hidingEnts":[],"value":"Grocery Outlet Inc..Missed Appointment","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable |C","hidingEnts":[],"value":"SAFEWAY, INC..Equipment Unavailable |C","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - First Avail. Appt.","hidingEnts":["Dollar General"],"value":"Dollar General.Vendor - First Avail. Appt.","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Container Delayed at port","hidingEnts":[],"value":"Home Retail Group.Container Delayed at port","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Corporate - Vendor Responsible (ie Caps, Labe)","hidingEnts":[],"value":"KIK INTERNATIONAL.Corporate - Vendor Responsible (ie Caps, Labe)","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - Approved Late (Notes Required)","hidingEnts":[],"value":"Dollar General.DG - Approved Late (Notes Required)","hidingOrgs":[]},{"entName":"Dollar General","display":"DC First Available","hidingEnts":["Dollar General"],"value":"Dollar General.DC First Available","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Vessel Delays","hidingEnts":[],"value":"Home Retail Group.Vessel Delays","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor |V","hidingEnts":[],"value":"SAFEWAY, INC..Delay at Vendor |V","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Admin Error Vendor mistake","hidingEnts":[],"value":"Home Retail Group.Admin Error Vendor mistake","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure","hidingEnts":[],"value":"SAFEWAY, INC..Truck/Equipment Failure","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable","hidingEnts":[],"value":"SAFEWAY, INC..Equipment Unavailable","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Admin Error","hidingEnts":[],"value":"Home Retail Group.Admin Error","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable |V","hidingEnts":[],"value":"SAFEWAY, INC..Product Unavailable |V","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue |B","hidingEnts":[],"value":"SAFEWAY, INC..Border Issue |B","hidingOrgs":[]},{"display":"Cannot Make Appointment","hidingEnts":[],"value":"Cannot Make Appointment","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC","hidingEnts":[],"value":"SAFEWAY, INC..Delay at DC","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation","hidingEnts":[],"value":"SAFEWAY, INC..DOT Violation","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Driver Issues","hidingEnts":["Dollar General"],"value":"Dollar General.Carrier - Driver Issues","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Haulage not arranged","hidingEnts":[],"value":"Home Retail Group.Haulage not arranged","hidingOrgs":[]},{"entName":"Dollar General","display":"Suzanne's Fault","hidingEnts":["Dollar General"],"value":"Dollar General.Suzanne's Fault","hidingOrgs":[]},{"entName":"Home Retail Group","display":"Swapping Loads","hidingEnts":["Home Retail Group"],"value":"Home Retail Group.Swapping Loads","hidingOrgs":[]}]},"FreightClass":{"enumId":"FreightClass","success":true,"items":[{"display":"55","hidingEnts":[],"value":"55","hidingOrgs":[]},{"display":"150","hidingEnts":[],"value":"150","hidingOrgs":[]},{"display":"250","hidingEnts":[],"value":"250","hidingOrgs":[]},{"display":"175","hidingEnts":[],"value":"175","hidingOrgs":[]},{"display":"110","hidingEnts":[],"value":"110","hidingOrgs":[]},{"display":"100","hidingEnts":[],"value":"100","hidingOrgs":[]},{"display":"200","hidingEnts":[],"value":"200","hidingOrgs":[]},{"display":"300","hidingEnts":[],"value":"300","hidingOrgs":[]},{"display":"125","hidingEnts":[],"value":"125","hidingOrgs":[]},{"display":"400","hidingEnts":[],"value":"400","hidingOrgs":[]},{"display":"500","hidingEnts":[],"value":"500","hidingOrgs":[]},{"display":"70","hidingEnts":[],"value":"70","hidingOrgs":[]},{"display":"60","hidingEnts":[],"value":"60","hidingOrgs":[]},{"display":"92.5","hidingEnts":[],"value":"92.5","hidingOrgs":[]},{"display":"50","hidingEnts":[],"value":"50","hidingOrgs":[]},{"display":"None","hidingEnts":[],"value":"NONE","hidingOrgs":[]},{"display":"85","hidingEnts":[],"value":"85","hidingOrgs":[]},{"display":"77.5","hidingEnts":[],"value":"77.5","hidingOrgs":[]},{"display":"65","hidingEnts":[],"value":"65","hidingOrgs":[]}]},"SCC.INCOTerms":{"enumId":"SCC.INCOTerms","success":true,"items":[{"display":"CIF","hidingEnts":[],"value":"CIF","hidingOrgs":[]},{"display":"DTP","hidingEnts":[],"value":"DTP","hidingOrgs":[]},{"display":"FCA","hidingEnts":[],"value":"FCA","hidingOrgs":[]},{"display":"DPP","hidingEnts":[],"value":"DPP","hidingOrgs":[]},{"display":"EXW","hidingEnts":[],"value":"EXW","hidingOrgs":[]},{"display":"CPT","hidingEnts":[],"value":"CPT","hidingOrgs":[]},{"display":"DPU","hidingEnts":[],"value":"DPU","hidingOrgs":[]},{"display":"CIP","hidingEnts":[],"value":"CIP","hidingOrgs":[]},{"display":"CFR","hidingEnts":[],"value":"CFR","hidingOrgs":[]},{"display":"DDP","hidingEnts":[],"value":"DDP","hidingOrgs":[]},{"display":"DAP","hidingEnts":[],"value":"DAP","hidingOrgs":[]},{"display":"DAT","hidingEnts":[],"value":"DAT","hidingOrgs":[]},{"display":"FOB","hidingEnts":[],"value":"FOB","hidingOrgs":[]},{"display":"FAS","hidingEnts":[],"value":"FAS","hidingOrgs":[]},{"display":"CNI","hidingEnts":[],"value":"CNI","hidingOrgs":[]}]},"SCC.ContainerStatus":{"enumId":"SCC.ContainerStatus","success":true,"items":[{"display":"Less Than Container Load","hidingEnts":[],"value":"LCL","hidingOrgs":[]},{"display":"Empty","hidingEnts":[],"value":"EMPTY","hidingOrgs":[]},{"display":"Full Container Load","hidingEnts":[],"value":"FCL","hidingOrgs":[]}]},"OMS.FOBPoint":{"enumId":"OMS.FOBPoint","success":true,"items":[{"display":"Origin","hidingEnts":[],"value":"Origin","hidingOrgs":[]},{"display":"Delivered","hidingEnts":[],"value":"Delivered","hidingOrgs":[]}]},"DistanceUOM":{"enumId":"DistanceUOM","success":true,"items":[{"display":"Kilometer","hidingEnts":[],"value":"KILOMETER","hidingOrgs":[]},{"display":"Mile","hidingEnts":[],"value":"MILE","hidingOrgs":[]},{"display":"MILLIMETER","hidingEnts":[],"value":"MILLIMETER","hidingOrgs":[]},{"display":"YARD","hidingEnts":[],"value":"YARD","hidingOrgs":[]},{"display":"METER","hidingEnts":[],"value":"METER","hidingOrgs":[]},{"display":"INCH","hidingEnts":[],"value":"INCH","hidingOrgs":[]},{"display":"CENTIMETER","hidingEnts":[],"value":"CENTIMETER","hidingOrgs":[]},{"display":"FOOT","hidingEnts":[],"value":"FOOT","hidingOrgs":[]}]},"OMS.ResponseSyncToModelType":{"enumId":"OMS.ResponseSyncToModelType","success":true,"items":[{"display":"Manual","hidingEnts":[],"value":"Manual","hidingOrgs":[]},{"display":"Auto Acknowledge","hidingEnts":[],"value":"AutoAcknowledge","hidingOrgs":[]}]},"OMS.InvoiceActiveCode":{"enumId":"OMS.InvoiceActiveCode","success":true,"items":[{"display":"Active On Receipt","hidingEnts":[],"value":"ActiveOnReceipt","hidingOrgs":[]},{"display":"Active On Ship","hidingEnts":[],"value":"ActiveOnShip","hidingOrgs":[]},{"display":"Consignment","hidingEnts":[],"value":"Consignment","hidingOrgs":[]}]},"OMS.StockingCheckMode":{"enumId":"OMS.StockingCheckMode","success":true,"items":[{"display":"Not Stocked Out","hidingEnts":[],"value":"NotStockedOut","hidingOrgs":[]},{"display":"Sufficient Quantity","hidingEnts":[],"value":"SufficientQuantity","hidingOrgs":[]}]},"OMS.TransCostComputationMode":{"enumId":"OMS.TransCostComputationMode","success":true,"items":[{"display":"By Distance","hidingEnts":[],"value":"By Distance","hidingOrgs":[]},{"display":"By Volume","hidingEnts":[],"value":"By Volume","hidingOrgs":[]},{"display":"Fixed","hidingEnts":[],"value":"Fixed","hidingOrgs":[]}]},"OMS.EDIChangeTypeCode":{"enumId":"OMS.EDIChangeTypeCode","success":true,"items":[{"display":"PQ","hidingEnts":[],"value":"PQ","hidingOrgs":[]},{"display":"PR","hidingEnts":[],"value":"PR","hidingOrgs":[]},{"display":"DI","hidingEnts":[],"value":"DI","hidingOrgs":[]},{"display":"MU","hidingEnts":[],"value":"MU","hidingOrgs":[]},{"display":"AI","hidingEnts":[],"value":"AI","hidingOrgs":[]},{"display":"RA","hidingEnts":[],"value":"RA","hidingOrgs":[]},{"display":"CT","hidingEnts":[],"value":"CT","hidingOrgs":[]},{"display":"PC","hidingEnts":[],"value":"PC","hidingOrgs":[]},{"display":"QD","hidingEnts":[],"value":"QD","hidingOrgs":[]},{"display":"RE","hidingEnts":[],"value":"RE","hidingOrgs":[]},{"display":"NC","hidingEnts":[],"value":"NC","hidingOrgs":[]},{"display":"QI","hidingEnts":[],"value":"QI","hidingOrgs":[]},{"display":"CA","hidingEnts":[],"value":"CA","hidingOrgs":[]},{"display":"RQ","hidingEnts":[],"value":"RQ","hidingOrgs":[]}]},"OMS.OrderState":{"enumId":"OMS.OrderState","success":true,"items":[{"display":"New","hidingEnts":[],"value":"New","hidingOrgs":[]},{"display":"In Transit","hidingEnts":[],"value":"In Transit","hidingOrgs":[]},{"display":"Buyer Confirmed With Changes","hidingEnts":[],"value":"Buyer Confirmed With Changes","hidingOrgs":[]},{"display":"Received","hidingEnts":[],"value":"Received","hidingOrgs":[]},{"display":"In Promising","hidingEnts":[],"value":"In Promising","hidingOrgs":[]},{"display":"Buyer Change Requested","hidingEnts":[],"value":"Buyer Change Requested","hidingOrgs":[]},{"display":"Partially Shipped","hidingEnts":[],"value":"Partially Shipped","hidingOrgs":[]},{"display":"In Fulfillment","hidingEnts":[],"value":"In Fulfillment","hidingOrgs":[]},{"display":"Open","hidingEnts":[],"value":"Open","hidingOrgs":[]},{"display":"Vendor Confirmed With Changes","hidingEnts":[],"value":"Vendor Confirmed With Changes","hidingOrgs":[]},{"display":"Vendor Change Requested","hidingEnts":[],"value":"Vendor Change Requested","hidingOrgs":[]},{"display":"Draft","hidingEnts":[],"value":"Draft","hidingOrgs":[]},{"display":"Awaiting Approval","hidingEnts":[],"value":"Awaiting Approval","hidingOrgs":[]},{"display":"Partially Received","hidingEnts":[],"value":"Partially Received","hidingOrgs":[]}]},"OMS.LeadTime":{"enumId":"OMS.LeadTime","success":true,"items":[{"display":"Business Days","hidingEnts":[],"value":"Buesness Days","hidingOrgs":[]},{"display":"Hours","hidingEnts":[],"value":"Hours","hidingOrgs":[]},{"display":"Weeks","hidingEnts":[],"value":"Weeks","hidingOrgs":[]},{"display":"Days","hidingEnts":[],"value":"Days","hidingOrgs":[]}]},"OMS.SurveyQuestionCategory":{"enumId":"OMS.SurveyQuestionCategory","success":true,"items":[{"display":"EDI & Integration Capability","hidingEnts":[],"value":"EDI & Integration Capability","hidingOrgs":[]},{"display":"Introduction","hidingEnts":[],"value":"Introduction","hidingOrgs":[]},{"display":"Quality","hidingEnts":[],"value":"Quality","hidingOrgs":[]},{"display":"Brief Profile","hidingEnts":[],"value":"Brief Profile","hidingOrgs":[]},{"display":"Information","hidingEnts":[],"value":"Information","hidingOrgs":[]},{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]},{"display":"Supplier Capacity","hidingEnts":[],"value":"Supplier Capacity","hidingOrgs":[]},{"display":"Reviewer","hidingEnts":[],"value":"Reviewer","hidingOrgs":[]},{"display":"General questions","hidingEnts":[],"value":"General questions","hidingOrgs":[]},{"display":"VMI Model used","hidingEnts":[],"value":"VMI Model used","hidingOrgs":[]},{"display":"Company information","hidingEnts":[],"value":"Company information","hidingOrgs":[]}]},"SCC.OwnerCode":{"enumId":"SCC.OwnerCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"OMS.BucketizationType":{"enumId":"OMS.BucketizationType","success":true,"items":[{"display":"Yearly","hidingEnts":[],"value":"YEARLY","hidingOrgs":[]},{"display":"Weekly","hidingEnts":[],"value":"WEEKLY","hidingOrgs":[]},{"display":"Monthly","hidingEnts":[],"value":"MONTHLY","hidingOrgs":[]}]},"OMS.TaxProvider":{"enumId":"OMS.TaxProvider","success":true,"items":[{"display":"5% Flat Tax","hidingEnts":[],"value":"5%FlatTax","hidingOrgs":[]},{"display":"27% Flat Tax","hidingEnts":[],"value":"27%FlatTax","hidingOrgs":[]},{"display":"Avatax","hidingEnts":[],"value":"Avatax","hidingOrgs":[]}]},"OMS.ReopenOrderType":{"enumId":"OMS.ReopenOrderType","success":true,"items":[{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]}]},"OMS.CancelCollaborationStatus":{"enumId":"OMS.CancelCollaborationStatus","success":true,"items":[{"display":"Cancel Request Rejected","hidingEnts":[],"value":"Cancel Request Rejected","hidingOrgs":[]},{"display":"Cancel Request Approved","hidingEnts":[],"value":"Cancel Request Approved","hidingOrgs":[]},{"display":"Cancel Request Withdrawn","hidingEnts":[],"value":"Cancel Request Withdrawn","hidingOrgs":[]},{"display":"Cancel Requested","hidingEnts":[],"value":"Cancel Requested","hidingOrgs":[]}]},"SCC.ItemType":{"enumId":"SCC.ItemType","success":true,"items":[{"entName":"DelMonte","display":"J","hidingEnts":[],"value":"DelMonte.J","hidingOrgs":[]},{"entName":"DelMonte","display":"F","hidingEnts":[],"value":"DelMonte.F","hidingOrgs":[]},{"display":"Scenario Planning","hidingEnts":[],"value":"ScenarioPlanning","hidingOrgs":[]},{"entName":"DelMonte","display":"E","hidingEnts":[],"value":"DelMonte.E","hidingOrgs":[]},{"display":"Product","hidingEnts":[],"value":"Product","hidingOrgs":[]},{"display":"Complex","hidingEnts":[],"value":"Complex","hidingOrgs":[]},{"entName":"DelMonte","display":"R","hidingEnts":[],"value":"DelMonte.R","hidingOrgs":[]},{"display":"Phantom","hidingEnts":[],"value":"Phantom","hidingOrgs":[]},{"entName":"DelMonte","display":"P","hidingEnts":[],"value":"DelMonte.P","hidingOrgs":[]},{"entName":"Walmart","display":"3","hidingEnts":[],"value":"Walmart.3","hidingOrgs":[]},{"entName":"VendorA","display":"FG","hidingEnts":[],"value":"VendorA.FG","hidingOrgs":[]},{"entName":"Walmart","display":"33","hidingEnts":[],"value":"Walmart.33","hidingOrgs":[]},{"entName":"The Kroger Co.","display":"P","hidingEnts":[],"value":"The Kroger Co..P","hidingOrgs":[]},{"display":"Tool","hidingEnts":[],"value":"Tool","hidingOrgs":[]},{"display":"Service","hidingEnts":[],"value":"Service","hidingOrgs":[]},{"display":"Material","hidingEnts":[],"value":"Material","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"Z002","hidingEnts":[],"value":"The Kraft Heinz Co.Z002","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"Z001","hidingEnts":[],"value":"The Kraft Heinz Co.Z001","hidingOrgs":[]},{"entName":"AutoCPG","display":"P","hidingEnts":[],"value":"AutoCPG.P","hidingOrgs":[]},{"entName":"Walmart","display":"131313","hidingEnts":[],"value":"Walmart.131313","hidingOrgs":[]},{"entName":"DelMonte","display":"7","hidingEnts":[],"value":"DelMonte.7","hidingOrgs":[]},{"display":"Kit","hidingEnts":[],"value":"Kit","hidingOrgs":[]},{"entName":"OneCPG","display":"Phantom","hidingEnts":[],"value":"OneCPG.Phantom","hidingOrgs":[]},{"entName":"Walmart","display":"P","hidingEnts":[],"value":"Walmart.P","hidingOrgs":[]},{"display":"Package","hidingEnts":[],"value":"Package","hidingOrgs":[]},{"entName":"OneCPG","display":"Packaging","hidingEnts":[],"value":"OneCPG.Packaging","hidingOrgs":[]}]},"SCC.HazmatPackagingGroup":{"enumId":"SCC.HazmatPackagingGroup","success":true,"items":[{"display":"II","hidingEnts":[],"value":"II","hidingOrgs":[]},{"display":"III","hidingEnts":[],"value":"III","hidingOrgs":[]},{"display":"I","hidingEnts":[],"value":"I","hidingOrgs":[]}]},"OMS.ReasonCode":{"enumId":"OMS.ReasonCode","success":true,"items":[{"display":"Transportation Delay","hidingEnts":[],"value":"Transportation Delay","hidingOrgs":[]},{"display":"Capacity Constraints","hidingEnts":[],"value":"Capacity Constraints","hidingOrgs":[]},{"display":"Late Order Change By Buyer","hidingEnts":[],"value":"Late Order Change by Buyer","hidingOrgs":[]},{"display":"Raw Material Availability","hidingEnts":[],"value":"Raw Material Availability","hidingOrgs":[]},{"display":"Supplier Error","hidingEnts":[],"value":"Supplier Error","hidingOrgs":[]}]},"OMS.AutoAckOnTimeLapsedMode":{"enumId":"OMS.AutoAckOnTimeLapsedMode","success":true,"items":[{"display":"Reject","hidingEnts":[],"value":"Reject","hidingOrgs":[]},{"display":"Accept","hidingEnts":[],"value":"Accept","hidingOrgs":[]}]},"OMS.BuyerAcceptance":{"enumId":"OMS.BuyerAcceptance","success":true,"items":[{"display":"Accept To Open & Allow Change","hidingEnts":[],"value":"ACCEPT_TO_OPEN_ALLOW_CHANGE","hidingOrgs":[]},{"display":"Accept To New-Open & Disallow Change","hidingEnts":[],"value":"ACCEPT_TO_NEW_OPEN_DISALLOW_CHANGE","hidingOrgs":[]},{"display":"Accept To New","hidingEnts":[],"value":"ACCEPT_TO_NEW","hidingOrgs":[]},{"display":"Accept To Open & Disallow Change","hidingEnts":[],"value":"ACCEPT_TO_OPEN_DISALLOW_CHANGE","hidingOrgs":[]},{"display":"Accept To New-Open & Allow Change","hidingEnts":[],"value":"ACCEPT_TO_NEW_OPEN_ALLOW_CHANGE","hidingOrgs":[]}]},"SCC.TransporatationMode":{"enumId":"SCC.TransporatationMode","success":true,"items":[{"display":"Air-Ground","hidingEnts":[],"value":"Air-Ground","hidingOrgs":[]},{"display":"Intermodal","hidingEnts":[],"value":"Intermodal","hidingOrgs":[]},{"display":"Rail","hidingEnts":[],"value":"Rail","hidingOrgs":[]},{"display":"LTL","hidingEnts":[],"value":"LTL","hidingOrgs":[]},{"display":"Parcel","hidingEnts":[],"value":"Parcel","hidingOrgs":[]},{"display":"Ocean-LCL","hidingEnts":[],"value":"Ocean-LCL","hidingOrgs":[]},{"display":"TL","hidingEnts":[],"value":"TL","hidingOrgs":[]},{"display":"Ocean","hidingEnts":[],"value":"Ocean","hidingOrgs":[]},{"display":"Air","hidingEnts":[],"value":"Air","hidingOrgs":[]}]},"OMS.OrderUpdateAlertFields":{"enumId":"OMS.OrderUpdateAlertFields","success":true,"items":[{"display":"Ship To Address","hidingEnts":[],"value":"ShipToAddress","hidingOrgs":[]},{"display":"Order Priority","hidingEnts":[],"value":"OrderPriority","hidingOrgs":[]},{"display":"Actual Delivery Date","hidingEnts":[],"value":"ActualDeliveryDate","hidingOrgs":[]},{"display":"Price Per","hidingEnts":[],"value":"PricePer","hidingOrgs":[]},{"display":"Line Incoterms","hidingEnts":[],"value":"LineIncoTerms","hidingOrgs":[]},{"display":"Payment Terms","hidingEnts":[],"value":"PaymentTerms","hidingOrgs":[]},{"display":"Buyer Not Acceptable","hidingEnts":[],"value":"BuyerNotAcceptable","hidingOrgs":[]},{"display":"Item","hidingEnts":[],"value":"Item","hidingOrgs":[]},{"display":"Incoterms","hidingEnts":[],"value":"IncoTerms","hidingOrgs":[]},{"display":"Ship To Site","hidingEnts":[],"value":"ShipToSite","hidingOrgs":[]},{"display":"Request Delivery Date","hidingEnts":[],"value":"RequestDeliveryDate","hidingOrgs":[]},{"display":"Request Ship Date","hidingEnts":[],"value":"RequestShipDate","hidingOrgs":[]},{"display":"Owning Site","hidingEnts":[],"value":"OwningSite","hidingOrgs":[]},{"display":"Request Unit Price","hidingEnts":[],"value":"RequestUnitPrice","hidingOrgs":[]},{"display":"Request Quantity","hidingEnts":[],"value":"RequestQuantity","hidingOrgs":[]},{"display":"Is Emergency","hidingEnts":[],"value":"IsEmergency","hidingOrgs":[]},{"display":"Promise Inco Date","hidingEnts":[],"value":"PromiseIncoDate","hidingOrgs":[]},{"display":"Country Of Manufacturing","hidingEnts":[],"value":"CountryOfManufacturing","hidingOrgs":[]},{"display":"Ext Ship To Site","hidingEnts":[],"value":"ExtShipToSite","hidingOrgs":[]},{"display":"HTS Code","hidingEnts":[],"value":"HTSCode","hidingOrgs":[]},{"display":"Vendor Reject Reason Code","hidingEnts":[],"value":"VendorRejectReasonCode","hidingOrgs":[]},{"display":"Request Min Item Expiry Date","hidingEnts":[],"value":"RequestMinItemExpiryDate","hidingOrgs":[]},{"display":"Request Price Per","hidingEnts":[],"value":"RequestPricePer","hidingOrgs":[]},{"display":"Is Expedite","hidingEnts":[],"value":"IsExpedite","hidingOrgs":[]},{"display":"Promise Unit Price","hidingEnts":[],"value":"PromiseUnitPrice","hidingOrgs":[]},{"display":"Line Cancel Collaboration Status","hidingEnts":[],"value":"LnCancelCollaborationStatus","hidingOrgs":[]},{"display":"Rs Cancel Collaboration Status","hidingEnts":[],"value":"RsCancelCollaborationStatus","hidingOrgs":[]},{"display":"Program","hidingEnts":[],"value":"Program","hidingOrgs":[]},{"display":"Ship From Address","hidingEnts":[],"value":"ShipFromAddress","hidingOrgs":[]},{"display":"Promise Price Per","hidingEnts":[],"value":"PromisePricePer","hidingOrgs":[]},{"display":"Total Amount","hidingEnts":[],"value":"TotalAmount","hidingOrgs":[]},{"display":"Actual Ship Date","hidingEnts":[],"value":"ActualShipDate","hidingOrgs":[]},{"display":"Promise Delivery Date","hidingEnts":[],"value":"PromiseDeliveryDate","hidingOrgs":[]},{"display":"Trans Mode","hidingEnts":[],"value":"TransMode","hidingOrgs":[]},{"display":"Promise Ship Date","hidingEnts":[],"value":"PromiseShipDate","hidingOrgs":[]},{"display":"Unit Price","hidingEnts":[],"value":"UnitPrice","hidingOrgs":[]},{"display":"Promise Quantity","hidingEnts":[],"value":"PromiseQuantity","hidingOrgs":[]},{"display":"Order Sub Type","hidingEnts":[],"value":"OrderSubType","hidingOrgs":[]},{"display":"Ship From Site","hidingEnts":[],"value":"ShipFromSite","hidingOrgs":[]},{"display":"Planner Notes","hidingEnts":[],"value":"PlannerNotes","hidingOrgs":[]},{"display":"Request Inco Date","hidingEnts":[],"value":"RequestIncoDate","hidingOrgs":[]},{"display":"Promise Item","hidingEnts":[],"value":"PromiseItem","hidingOrgs":[]},{"display":"Promise Min Item Expiry Date","hidingEnts":[],"value":"PromiseMinItemExpiryDate","hidingOrgs":[]},{"display":"Ext Ship From Site","hidingEnts":[],"value":"ExtShipFromSite","hidingOrgs":[]},{"display":"Cancel Collaboration Status","hidingEnts":[],"value":"CancelCollaborationStatus","hidingOrgs":[]},{"display":"Vendor Not Acceptable","hidingEnts":[],"value":"VendorNotAcceptable","hidingOrgs":[]},{"display":"Vendor Notes","hidingEnts":[],"value":"VendorNotes","hidingOrgs":[]}]},"OMS.CustomerScorecardOrderType":{"enumId":"OMS.CustomerScorecardOrderType","success":true,"items":[{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]},{"display":"Sales Order","hidingEnts":[],"value":"Sales Order","hidingOrgs":[]}]},"OMS.UpdateInFulfillment":{"enumId":"OMS.UpdateInFulfillment","success":true,"items":[{"display":"No","hidingEnts":[],"value":"No","hidingOrgs":[]},{"display":"Collaborative","hidingEnts":[],"value":"Collaborative","hidingOrgs":[]},{"display":"Non-Collaborative","hidingEnts":[],"value":"Non-Collaborative","hidingOrgs":[]}]},"WBProblemState":{"enumId":"WBProblemState","success":true,"items":[{"display":"New","hidingEnts":[],"value":"New","hidingOrgs":[]},{"display":"In Progress","hidingEnts":[],"value":"In Progress","hidingOrgs":[]},{"display":"Resolved","hidingEnts":[],"value":"Resolved","hidingOrgs":[]},{"display":"Cancelled","hidingEnts":[],"value":"Cancelled","hidingOrgs":[]}]},"OMS.FOBCode":{"enumId":"OMS.FOBCode","success":true,"items":[{"display":"03-COLLECT NO FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"03-COLLECT NO FUEL SURCHARGE ALLOWED","hidingOrgs":[]},{"display":"04-PRE PAY FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"04-PRE PAY FUEL SURCHARGE ALLOWED","hidingOrgs":[]},{"display":"02- PAY &  ADD NO FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"02-PAY & ADD NO FUEL SURCHARGE ALLOWED","hidingOrgs":[]},{"display":"06-COLLECT FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"06-COLLECT FUEL SURCHARGE ALLOWED","hidingOrgs":[]},{"display":"01-PRE PAY NO FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"01-PRE PAY NO FUEL SURCHARGE ALLOWED","hidingOrgs":[]},{"display":"05- PAY &  ADD FUEL SURCHARGE ALLOWED","hidingEnts":[],"value":"05-PAY & ADD FUEL SURCHARGE ALLOWED","hidingOrgs":[]}]},"SCC.OrderPromiseState":{"enumId":"SCC.OrderPromiseState","success":true,"items":[{"display":"Required","hidingEnts":[],"value":"Required","hidingOrgs":[]},{"display":"Complete","hidingEnts":[],"value":"Complete","hidingOrgs":[]},{"display":"Failed","hidingEnts":[],"value":"Failed","hidingOrgs":[]},{"display":"Ready For Promise","hidingEnts":[],"value":"Ready For Promise","hidingOrgs":[]}]},"OMS.WorkbenchPIVMode":{"enumId":"OMS.WorkbenchPIVMode","success":true,"items":[{"display":"Material PIV","hidingEnts":[],"value":"Material PIV","hidingOrgs":[]},{"display":"Retail DC PIV","hidingEnts":[],"value":"Retail DC PIV","hidingOrgs":[]}]},"SCC.PurposeCode":{"enumId":"SCC.PurposeCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"LoadType":{"enumId":"LoadType","success":true,"rows":[{"display":"Clamp","value":"CLAMP"},{"display":"Cooking","value":"Cooking"},{"display":"Counter Balance","value":"COUNTER BALANCE"},{"display":"Floor Load","value":"FLOOR_LOAD"},{"display":"Free Standing Furniture","value":"FREE STANDING FURNITURE"},{"display":"Handball","value":"HANDBALL"},{"display":"Mixed","value":"MIXED"},{"display":"None","value":"NONE"},{"display":"Palletized","value":"PALLETIZED"},{"display":"Refrigeration","value":"Refrigeration"},{"display":"Roll","value":"ROLL"},{"display":"Roll With Breakdown","value":"ROLL_WITH_BREAKDOWN"},{"display":"Slip Sheet","value":"SLIP_SHEET"},{"display":"Stillage","value":"STILLAGE"},{"display":"Tiered Rack","value":"TIERED_RACK"},{"display":"UPM 10","value":"UPM_10"},{"display":"UPM 100","value":"UPM_100"},{"display":"UPM 120","value":"UPM_120"},{"display":"UPM 13","value":"UPM_13"},{"display":"UPM 15","value":"UPM_15"},{"display":"UPM 150","value":"UPM_150"},{"display":"UPM 20","value":"UPM_20"},{"display":"UPM 200","value":"UPM_200"},{"display":"UPM 25","value":"UPM_25"},{"display":"UPM 250","value":"UPM_250"},{"display":"UPM 30","value":"UPM_30"},{"display":"UPM 300","value":"UPM_300"},{"display":"UPM 35","value":"UPM_35"},{"display":"UPM 40","value":"UPM_40"},{"display":"UPM 45","value":"UPM_45"},{"display":"UPM 5","value":"UPM_5"},{"display":"UPM 50","value":"UPM_50"},{"display":"UPM 55","value":"UPM_55"},{"display":"UPM 60","value":"UPM_60"},{"display":"UPM 65","value":"UPM_65"},{"display":"UPM 70","value":"UPM_70"},{"display":"UPM 75","value":"UPM_75"},{"display":"UPM 80","value":"UPM_80"},{"display":"UPM 85","value":"UPM_85"},{"display":"UPM 90","value":"UPM_90"},{"display":"UPM 95","value":"UPM_95"},{"display":"Washing","value":"Washing"}],"items":[{"display":"Clamp","value":"CLAMP"},{"display":"Cooking","value":"Cooking"},{"display":"Counter Balance","value":"COUNTER BALANCE"},{"display":"Floor Load","value":"FLOOR_LOAD"},{"display":"Free Standing Furniture","value":"FREE STANDING FURNITURE"},{"display":"Handball","value":"HANDBALL"},{"display":"Mixed","value":"MIXED"},{"display":"None","value":"NONE"},{"display":"Palletized","value":"PALLETIZED"},{"display":"Refrigeration","value":"Refrigeration"},{"display":"Roll","value":"ROLL"},{"display":"Roll With Breakdown","value":"ROLL_WITH_BREAKDOWN"},{"display":"Slip Sheet","value":"SLIP_SHEET"},{"display":"Stillage","value":"STILLAGE"},{"display":"Tiered Rack","value":"TIERED_RACK"},{"display":"UPM 10","value":"UPM_10"},{"display":"UPM 100","value":"UPM_100"},{"display":"UPM 120","value":"UPM_120"},{"display":"UPM 13","value":"UPM_13"},{"display":"UPM 15","value":"UPM_15"},{"display":"UPM 150","value":"UPM_150"},{"display":"UPM 20","value":"UPM_20"},{"display":"UPM 200","value":"UPM_200"},{"display":"UPM 25","value":"UPM_25"},{"display":"UPM 250","value":"UPM_250"},{"display":"UPM 30","value":"UPM_30"},{"display":"UPM 300","value":"UPM_300"},{"display":"UPM 35","value":"UPM_35"},{"display":"UPM 40","value":"UPM_40"},{"display":"UPM 45","value":"UPM_45"},{"display":"UPM 5","value":"UPM_5"},{"display":"UPM 50","value":"UPM_50"},{"display":"UPM 55","value":"UPM_55"},{"display":"UPM 60","value":"UPM_60"},{"display":"UPM 65","value":"UPM_65"},{"display":"UPM 70","value":"UPM_70"},{"display":"UPM 75","value":"UPM_75"},{"display":"UPM 80","value":"UPM_80"},{"display":"UPM 85","value":"UPM_85"},{"display":"UPM 90","value":"UPM_90"},{"display":"UPM 95","value":"UPM_95"},{"display":"Washing","value":"Washing"}]},"OMS.OrderReferenceDateType":{"enumId":"OMS.OrderReferenceDateType","success":true,"items":[{"display":"Request Delivery Date","hidingEnts":[],"value":"RequestDeliveryDate","hidingOrgs":[]},{"display":"Actual Delivery Date","hidingEnts":[],"value":"ActualDeliveryDate","hidingOrgs":[]},{"display":"Agreed Ship Date","hidingEnts":[],"value":"AgreedShipDate","hidingOrgs":[]},{"display":"Agreed Delivery Date","hidingEnts":[],"value":"AgreedDeliveryDate","hidingOrgs":[]},{"display":"Promise Delivery Date","hidingEnts":[],"value":"PromiseDeliveryDate","hidingOrgs":[]}]},"OMS.OrderOrigin":{"enumId":"OMS.OrderOrigin","success":true,"items":[{"display":"External","hidingEnts":[],"value":"EXTERNAL","hidingOrgs":[]},{"display":"UI","hidingEnts":[],"value":"UI","hidingOrgs":[]},{"display":"ONE","hidingEnts":[],"value":"ONE","hidingOrgs":[]},{"display":"ONE Manual","hidingEnts":[],"value":"ONE Manual","hidingOrgs":[]}]},"OMS.CommunicationType":{"enumId":"OMS.CommunicationType","success":true,"items":[{"display":"Email","hidingEnts":[],"value":"Email","hidingOrgs":[]},{"display":"Portal","hidingEnts":[],"value":"Portal","hidingOrgs":[]},{"display":"EDI","hidingEnts":[],"value":"EDI","hidingOrgs":[]},{"display":"Fax","hidingEnts":[],"value":"Fax","hidingOrgs":[]}]},"OMS.SACCodeType":{"enumId":"OMS.SACCodeType","success":true,"items":[{"display":"Charge","hidingEnts":[],"value":"Charge","hidingOrgs":[]},{"display":"Allowance","hidingEnts":[],"value":"Allowance","hidingOrgs":[]},{"display":"Penalty","hidingEnts":[],"value":"Penalty","hidingOrgs":[]}]},"OMS.RegionCode":{"enumId":"OMS.RegionCode","success":true,"items":[{"display":"GB","hidingEnts":[],"value":"GB","hidingOrgs":[]},{"display":"CN","hidingEnts":[],"value":"CN","hidingOrgs":[]},{"display":"US","hidingEnts":[],"value":"US","hidingOrgs":[]}]},"SCC.FCCReasonCode":{"enumId":"SCC.FCCReasonCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"OMS.UOMConversionMode":{"enumId":"OMS.UOMConversionMode","success":true,"items":[{"display":"Strict","hidingEnts":[],"value":"Strict","hidingOrgs":[]},{"display":"Legacy","hidingEnts":[],"value":"Legacy","hidingOrgs":[]},{"display":"Relaxed","hidingEnts":[],"value":"Relaxed","hidingOrgs":[]}]},"OMS.PricingModel":{"enumId":"OMS.PricingModel","success":true,"items":[{"display":"Volume Tiered","hidingEnts":[],"value":"Volume Tiered","hidingOrgs":[]},{"display":"Time Varying","hidingEnts":[],"value":"Time Varying","hidingOrgs":[]},{"display":"Price  Per","hidingEnts":[],"value":"Price Per","hidingOrgs":[]},{"display":"Time Varying &  Volume Tiered","hidingEnts":[],"value":"Time Varying & Volume Tiered","hidingOrgs":[]},{"display":"Fixed","hidingEnts":[],"value":"Fixed","hidingOrgs":[]}]},"OMS.SourcingPolicyName":{"enumId":"OMS.SourcingPolicyName","success":true,"items":[{"display":"Avl Split by Order count between Vendors","hidingEnts":[],"value":"SplitBetweenVendorsByOrderCountAvl","hidingOrgs":[]},{"display":"Avl Split by Order Quantity between Vendors %","hidingEnts":[],"value":"AvlSplitByOrderQuantityBetweenVendors","hidingOrgs":[]},{"display":"AVL Split by Volume %-age between vendors","hidingEnts":[],"value":"SplitBetweenVendorsByVolumePercentageAvl","hidingOrgs":[]},{"display":"Contract Split by PO Count between vendors","hidingEnts":[],"value":"SPLIT_BETWEEN_VENDORS","hidingOrgs":[]},{"display":"Lowest Price AVL","hidingEnts":[],"value":"LowestPriceAvl","hidingOrgs":[]},{"display":"Oldest Contract","hidingEnts":[],"value":"OLDEST_CONTRACT","hidingOrgs":[]},{"display":"AVL Split Between Vendors by Split %","hidingEnts":[],"value":"SplitBetweenVendorsBySplitPercentageAvl","hidingOrgs":[]},{"display":"Lowest Price Contract","hidingEnts":[],"value":"LOWEST_PRICE_CONTRACT","hidingOrgs":[]},{"display":"Contract Split by Volume %-age between vendors","hidingEnts":[],"value":"SPLIT_BETWEEN_VENDORS_VOLUME_BY_PERCENTAGE","hidingOrgs":[]},{"display":"Contract Split by %-age between vendors","hidingEnts":[],"value":"SPLIT_BETWEEN_VENDORS_BY_PERCENTAGE","hidingOrgs":[]},{"display":"AVL Split by Volume between vendors","hidingEnts":[],"value":"SplitBetweenVendorsByVolumeAvl","hidingOrgs":[]},{"display":"Expiring Soon Contract","hidingEnts":[],"value":"EXPIRING_SOON_CONTRACT","hidingOrgs":[]},{"display":"Contract Split by Volume between vendors","hidingEnts":[],"value":"SPLIT_BETWEEN_VENDORS_VOLUME","hidingOrgs":[]}]},"OMS.ValuesForForceCloseBackorder":{"enumId":"OMS.ValuesForForceCloseBackorder","success":true,"items":[{"display":"Use Blank Values","hidingEnts":[],"value":"Blank","hidingOrgs":[]},{"display":"Use Ordered Values","hidingEnts":[],"value":"Ordered","hidingOrgs":[]}]},"OMS.AuthorizationType":{"enumId":"OMS.AuthorizationType","success":true,"items":[{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]},{"display":"Contract","hidingEnts":[],"value":"Contract","hidingOrgs":[]}]},"QuantityUOM":{"enumId":"QuantityUOM","success":true,"items":[{"display":"Car Load, Rail","hidingEnts":[],"value":"RAIL_CAR","hidingOrgs":[]},{"display":"Pascal/Sec","hidingEnts":[],"value":"PASCALSPERSECOND","hidingOrgs":[]},{"display":"100 Ft","hidingEnts":[],"value":"HUNDREDFEET","hidingOrgs":[]},{"display":"Cubic Centimeter","hidingEnts":[],"value":"CUBICCENTIMETER","hidingOrgs":[]},{"display":"Kilogram/Kilogram Of Product","hidingEnts":[],"value":"KILOGRAMSPERKILOGRAMOFPRODUCT","hidingOrgs":[]},{"display":"KGs/Millimeter Width","hidingEnts":[],"value":"KILOGRAMS/MILLIMETERWIDTH","hidingOrgs":[]},{"display":"Milligrams/Cubic Meter","hidingEnts":[],"value":"MILLIGRAMSPERCUBICMETER","hidingOrgs":[]},{"display":"Millimeter-Minimum","hidingEnts":[],"value":"MILLIMETER-MINIMUM","hidingOrgs":[]},{"display":"Kilo Liter","hidingEnts":[],"value":"KILOLITER","hidingOrgs":[]},{"display":"Heat Lots","hidingEnts":[],"value":"HEATLOTS","hidingOrgs":[]},{"display":"Wattage","hidingEnts":[],"value":"WATTAGE","hidingOrgs":[]},{"display":"Inces/Second/Second (Acceleration)","hidingEnts":[],"value":"INCHESPERSECONDPERSECOND(ACCELERATION)","hidingOrgs":[]},{"display":"Standard Cubic Ft","hidingEnts":[],"value":"STANDARDCUBICFOOT","hidingOrgs":[]},{"display":"1000 Ft (Board)","hidingEnts":[],"value":"THOUSANDFEET(BOARD)","hidingOrgs":[]},{"display":"Atmosphere","hidingEnts":[],"value":"ATMOSPHERE","hidingOrgs":[]},{"display":"Forward Reel","hidingEnts":[],"value":"FORWARD_REEL","hidingOrgs":[]},{"display":"Radian/Second","hidingEnts":[],"value":"RADIANSPERSECOND","hidingOrgs":[]},{"display":"Pica","hidingEnts":[],"value":"PICA","hidingOrgs":[]},{"display":"Percent/1000 Hours","hidingEnts":[],"value":"PERCENTPER1000HOURS","hidingOrgs":[]},{"display":"Liters/Day","hidingEnts":[],"value":"LITERSPERDAY","hidingOrgs":[]},{"display":"Gross Ton","hidingEnts":[],"value":"GROSSTON","hidingOrgs":[]},{"display":"Inches, Decimal-Avg","hidingEnts":[],"value":"INCHES,DECIMAL--AVERAGE","hidingOrgs":[]},{"display":"Million Cubic Ft","hidingEnts":[],"value":"MILLIONCUBICFEET","hidingOrgs":[]},{"display":"Hundred","hidingEnts":[],"value":"HUNDRED","hidingOrgs":[]},{"display":"hPa","hidingEnts":[],"value":"HECTOPASCAL","hidingOrgs":[]},{"display":"Counts/Inch","hidingEnts":[],"value":"COUNTSPERINCH","hidingOrgs":[]},{"display":"Directory Books","hidingEnts":[],"value":"DIRECTORYBOOKS","hidingOrgs":[]},{"display":"Trunk, Salesmen Sample","hidingEnts":[],"value":"TRUNK_SAMPLE","hidingOrgs":[]},{"display":"Pallet Lift","hidingEnts":[],"value":"PALLET_LIFT","hidingOrgs":[]},{"display":"Cover","hidingEnts":[],"value":"COVER","hidingOrgs":[]},{"display":"SET","hidingEnts":[],"value":"SET","hidingOrgs":[]},{"display":"Ocular Insert System","hidingEnts":[],"value":"OCULARINSERTSYSTEM","hidingOrgs":[]},{"display":"Wetton","hidingEnts":[],"value":"WETTON","hidingOrgs":[]},{"display":"Square Yard","hidingEnts":[],"value":"SQUAREYARD","hidingOrgs":[]},{"display":"Days","hidingEnts":[],"value":"DAYS","hidingOrgs":[]},{"display":"Joule/Kelvin","hidingEnts":[],"value":"JOULEPERKELVIN","hidingOrgs":[]},{"display":"Sandwich","hidingEnts":[],"value":"SANDWICH","hidingOrgs":[]},{"display":"Square Meter","hidingEnts":[],"value":"SQUAREMETER","hidingOrgs":[]},{"display":"Square Millimeter","hidingEnts":[],"value":"SQUAREMILLIMETER","hidingOrgs":[]},{"display":"10K Yards","hidingEnts":[],"value":"TENTHOUSANDYARDS","hidingOrgs":[]},{"display":"Hank","hidingEnts":[],"value":"HANK","hidingOrgs":[]},{"display":"1000 Pounds/Square Inch","hidingEnts":[],"value":"1000POUNDSPERSQUAREINCH","hidingOrgs":[]},{"display":"Household Goods Containers","hidingEnts":[],"value":"CONTAINER_HG","hidingOrgs":[]},{"display":"Roll","hidingEnts":[],"value":"ROLL","hidingOrgs":[]},{"display":"Jug","hidingEnts":[],"value":"JUG","hidingOrgs":[]},{"display":"Credits","hidingEnts":[],"value":"CREDITS","hidingOrgs":[]},{"display":"Wrap","hidingEnts":[],"value":"WRAP","hidingOrgs":[]},{"display":"Mega Gram","hidingEnts":[],"value":"MEGAGRAM","hidingOrgs":[]},{"display":"BTU/Hour","hidingEnts":[],"value":"BTU'sPerHour","hidingOrgs":[]},{"display":"Crate","hidingEnts":[],"value":"CRATE","hidingOrgs":[]},{"display":"Pound/Thousand","hidingEnts":[],"value":"POUNDSPERTHOUSAND","hidingOrgs":[]},{"display":"Cubic Inches","hidingEnts":[],"value":"CUBICINCHES","hidingOrgs":[]},{"display":"Suitcase","hidingEnts":[],"value":"SUITCASE","hidingOrgs":[]},{"display":"MMSCF/Day","hidingEnts":[],"value":"MMSCF/DAY","hidingOrgs":[]},{"display":"Parts/Thousand","hidingEnts":[],"value":"PARTSPERTHOUSAND","hidingOrgs":[]},{"display":"Quarter Dozen","hidingEnts":[],"value":"QUARTERDOZEN","hidingOrgs":[]},{"display":"KG/Square Centimeter","hidingEnts":[],"value":"KILOGRAMPERSQUARECENTIMETER","hidingOrgs":[]},{"display":"Bucket","hidingEnts":[],"value":"BUCKET","hidingOrgs":[]},{"display":"Fixed Rate","hidingEnts":[],"value":"FIXEDRATE","hidingOrgs":[]},{"display":"Access Lines","hidingEnts":[],"value":"ACCESSLINES","hidingOrgs":[]},{"display":"Skid, elevating or lift truck","hidingEnts":[],"value":"SKID_LIFT_TRUCK","hidingOrgs":[]},{"display":"Lite","hidingEnts":[],"value":"LITE","hidingOrgs":[]},{"display":"Tote Knockdown","hidingEnts":[],"value":"TOTE_ KNOCKDOWN","hidingOrgs":[]},{"display":"Layer(s)","hidingEnts":[],"value":"LAYER(S)","hidingOrgs":[]},{"display":"100th Of A Carat","hidingEnts":[],"value":"HUNDRETHOFACARAT","hidingOrgs":[]},{"display":"Thousand","hidingEnts":[],"value":"THOUSAND","hidingOrgs":[]},{"display":"Micro Inch","hidingEnts":[],"value":"MICROINCH","hidingOrgs":[]},{"display":"Decagram","hidingEnts":[],"value":"DECAGRAM","hidingOrgs":[]},{"display":"English, (Feet, Inches)","hidingEnts":[],"value":"ENGLISH,(FEET,INCHES)","hidingOrgs":[]},{"display":"Rack Half","hidingEnts":[],"value":"RACK_ HALF","hidingOrgs":[]},{"display":"Each/Month","hidingEnts":[],"value":"EACHPERMONTH","hidingOrgs":[]},{"display":"Pounds/Inch of Length","hidingEnts":[],"value":"POUNDSPERINCHOFLENGTH","hidingOrgs":[]},{"display":"1000 Cubic Ft/Day","hidingEnts":[],"value":"THOUSANDCUBICFEETPERDAY","hidingOrgs":[]},{"display":"Side of Beef","hidingEnts":[],"value":"BEEF_SIDE","hidingOrgs":[]},{"display":"Billions Of Dollars","hidingEnts":[],"value":"BILLIONSOFDOLLARS","hidingOrgs":[]},{"display":"Hogshead","hidingEnts":[],"value":"HOGSHEAD","hidingOrgs":[]},{"display":"Fibers/Cubic Centimeter Of AI","hidingEnts":[],"value":"FIBERSPERCUBICCENTIMETEROFAI","hidingOrgs":[]},{"display":"Deci Newton-Meter","hidingEnts":[],"value":"DECINEWTON-METER","hidingOrgs":[]},{"display":"Mega Joule","hidingEnts":[],"value":"MEGAJOULE","hidingOrgs":[]},{"display":"Performance Unit","hidingEnts":[],"value":"PERFORMANCEUNIT","hidingOrgs":[]},{"display":"Assortment","hidingEnts":[],"value":"ASSORTMENT","hidingOrgs":[]},{"display":"Inches, Fraction-Min","hidingEnts":[],"value":"INCHES,FRACTION--MINIMUM","hidingOrgs":[]},{"display":"Kilo Segments","hidingEnts":[],"value":"KILOSEGMENTS","hidingOrgs":[]},{"display":"Gallon Oth","hidingEnts":[],"value":"GALLON_OTH","hidingOrgs":[]},{"display":"kJ","hidingEnts":[],"value":"KILOJOULE","hidingOrgs":[]},{"display":"Mutually Defined","hidingEnts":[],"value":"MUTUALLYDEFINED","hidingOrgs":[]},{"display":"Sleeve","hidingEnts":[],"value":"SLEEVE","hidingOrgs":[]},{"display":"Metric Net Ton","hidingEnts":[],"value":"METRICNETTON","hidingOrgs":[]},{"display":"Penny Weight","hidingEnts":[],"value":"PENNYWEIGHT","hidingOrgs":[]},{"display":"Grams/cc","hidingEnts":[],"value":"GRAMSPERCUBICCENTIMETER","hidingOrgs":[]},{"display":"Count/Minute","hidingEnts":[],"value":"COUNTPERMINUTE","hidingOrgs":[]},{"display":"1000 Ft","hidingEnts":[],"value":"THOUSANDFEET","hidingOrgs":[]},{"display":"Double-length Skid","hidingEnts":[],"value":"SKID_DOUBLE","hidingOrgs":[]},{"display":"Liquid Bulk","hidingEnts":[],"value":"BULK_LIQUID","hidingOrgs":[]},{"display":"Bin","hidingEnts":[],"value":"BIN","hidingOrgs":[]},{"display":"Bottle","hidingEnts":[],"value":"BOTTLE","hidingOrgs":[]},{"display":"Therms","hidingEnts":[],"value":"THERMS","hidingOrgs":[]},{"display":"Pound/Cubic Ft","hidingEnts":[],"value":"POUNDSPERCUBICFOOT","hidingOrgs":[]},{"display":"Pounds/Pound Of Product","hidingEnts":[],"value":"POUNDSPERPOUNDOFPRODUCT","hidingOrgs":[]},{"display":"Theretical Pounds","hidingEnts":[],"value":"THEORETICALPOUNDS","hidingOrgs":[]},{"display":"Standard Advertising Units (SAUS)","hidingEnts":[],"value":"STANDARDADVERTISINGUNITS(SAUS)","hidingOrgs":[]},{"display":"Containers of Bulk Cargo","hidingEnts":[],"value":"CONTAINER_BULK","hidingOrgs":[]},{"display":"Lift Van","hidingEnts":[],"value":"LIFTVAN","hidingOrgs":[]},{"display":"Truck","hidingEnts":[],"value":"TRUCK","hidingOrgs":[]},{"display":"Half Pages-Electronic","hidingEnts":[],"value":"HALFPAGES-ELECTRONIC","hidingOrgs":[]},{"display":"Status Mile","hidingEnts":[],"value":"STATUTEMILE","hidingOrgs":[]},{"display":"Net Ten (2,000LB)","hidingEnts":[],"value":"NETTON(2,000LB).","hidingOrgs":[]},{"display":"Imperial Gallons","hidingEnts":[],"value":"IMPERIALGALLONS","hidingOrgs":[]},{"display":"Peck, Dry Impanel","hidingEnts":[],"value":"PECK,DRYIMPANEL","hidingOrgs":[]},{"display":"Seismic Cline","hidingEnts":[],"value":"SEISMICLINE","hidingOrgs":[]},{"display":"Quarter KG","hidingEnts":[],"value":"QUARTERKILOGRAM","hidingOrgs":[]},{"display":"Storage Units","hidingEnts":[],"value":"STORAGEUNITS","hidingOrgs":[]},{"display":"BULK","hidingEnts":[],"value":"BULK","hidingOrgs":[]},{"display":"Carrier","hidingEnts":[],"value":"CARRIER","hidingOrgs":[]},{"display":"Foot Pounds","hidingEnts":[],"value":"FOOTPOUNDS","hidingOrgs":[]},{"display":"100 Count","hidingEnts":[],"value":"HUNDREDCOUNT","hidingOrgs":[]},{"display":"Fields","hidingEnts":[],"value":"FIELDS","hidingOrgs":[]},{"display":"In","hidingEnts":[],"value":"IN","hidingOrgs":[]},{"display":"Heads of Beef","hidingEnts":[],"value":"BEEF_HEAD","hidingOrgs":[]},{"display":"Dyne","hidingEnts":[],"value":"DYNE","hidingOrgs":[]},{"display":"Kilobyte","hidingEnts":[],"value":"KILOBYTE","hidingOrgs":[]},{"display":"Gallons/1000 Cubic Feet","hidingEnts":[],"value":"GALLONSPERTHOUSANDCUBICFEET","hidingOrgs":[]},{"display":"Pounds/Cubic Inch","hidingEnts":[],"value":"POUNDSPERCUBICINCH","hidingOrgs":[]},{"display":"Liter","hidingEnts":[],"value":"LITER","hidingOrgs":[]},{"display":"Pounds/Air-Dry Metriction","hidingEnts":[],"value":"POUNDSPERAIR-DRYMETRICTON","hidingOrgs":[]},{"display":"Micrograms/Cubic Meter","hidingEnts":[],"value":"MICROGRAMSPERCUBICMETER","hidingOrgs":[]},{"display":"On Hanger or Rack in Boxes","hidingEnts":[],"value":"ON_HANGER","hidingOrgs":[]},{"display":"Mat","hidingEnts":[],"value":"MAT","hidingOrgs":[]},{"display":"1000 Impressions","hidingEnts":[],"value":"THOUSANDIMPRESSIONS","hidingOrgs":[]},{"display":"Bulk Bag","hidingEnts":[],"value":"BULK_BAG","hidingOrgs":[]},{"display":"Trunk and Chest","hidingEnts":[],"value":"TRUNK","hidingOrgs":[]},{"display":"Pint","hidingEnts":[],"value":"PINT","hidingOrgs":[]},{"display":"Thousand Pieces","hidingEnts":[],"value":"THOUSANDPIECES","hidingOrgs":[]},{"display":"Pipe Rack","hidingEnts":[],"value":"PIPE_RACK","hidingOrgs":[]},{"display":"Net KG","hidingEnts":[],"value":"NETKILOGRAMS","hidingOrgs":[]},{"display":"Great Gross (Dozen Gross)","hidingEnts":[],"value":"GREATGROSS(DOZENGROSS)","hidingOrgs":[]},{"display":"Short Ton","hidingEnts":[],"value":"SHORTTON","hidingOrgs":[]},{"display":"Tablet","hidingEnts":[],"value":"TABLET","hidingOrgs":[]},{"display":"Calorie","hidingEnts":[],"value":"CALORIE","hidingOrgs":[]},{"display":"Poise","hidingEnts":[],"value":"POISE","hidingOrgs":[]},{"display":"Disc (Disc)","hidingEnts":[],"value":"DISK(DISC)","hidingOrgs":[]},{"display":"Display","hidingEnts":[],"value":"DISPLAY_SH","hidingOrgs":[]},{"display":"BTU/Pound","hidingEnts":[],"value":"BTU'sPerPound","hidingOrgs":[]},{"display":"Spin Cylinders","hidingEnts":[],"value":"SPIN_CYLINDERS","hidingOrgs":[]},{"display":"Pims","hidingEnts":[],"value":"PIMS","hidingOrgs":[]},{"display":"Inces/Second/Second (Vibration ACC)","hidingEnts":[],"value":"INCHESPERSECONDPERSECOND(VIBRATIONACC)","hidingOrgs":[]},{"display":"Cell","hidingEnts":[],"value":"CELL","hidingOrgs":[]},{"display":"Wrapped","hidingEnts":[],"value":"WRAPPED","hidingOrgs":[]},{"display":"Wait/Second","hidingEnts":[],"value":"WATTSPERPOUND","hidingOrgs":[]},{"display":"Dru","hidingEnts":[],"value":"DRU","hidingOrgs":[]},{"display":"Sheet","hidingEnts":[],"value":"SHEET","hidingOrgs":[]},{"display":"Milliequivalence Caustic Potash/Gram Of Product","hidingEnts":[],"value":"MILLEQUIVALENCECAUSTICPOTASH/GRAMOFPRODUCT","hidingOrgs":[]},{"display":"Inches, Fraction-Actual","hidingEnts":[],"value":"INCHES,FRACTION--ACTUAL","hidingOrgs":[]},{"display":"Milligrams/Liter","hidingEnts":[],"value":"MILLIGRAMSPERLITER","hidingOrgs":[]},{"display":"Ohm","hidingEnts":[],"value":"OHM","hidingOrgs":[]},{"display":"Inches/Second (Vibration Velocity)","hidingEnts":[],"value":"INCHESPERSECOND(VIBRATIONVELOCITY)","hidingOrgs":[]},{"display":"Half Gallon","hidingEnts":[],"value":"HALFGALLON","hidingOrgs":[]},{"display":"100 KWH","hidingEnts":[],"value":"THOUSANDKILOWATTHOURS","hidingOrgs":[]},{"display":"Container, MAC-ISO","hidingEnts":[],"value":"CONTAINER_MIL_AIR","hidingOrgs":[]},{"display":"Basket or Hamper","hidingEnts":[],"value":"BASKET","hidingOrgs":[]},{"display":"463L Air Pallet","hidingEnts":[],"value":"AIR_PALLET","hidingOrgs":[]},{"display":"Cage","hidingEnts":[],"value":"CAGE","hidingOrgs":[]},{"display":"Unit","hidingEnts":[],"value":"UNIT","hidingOrgs":[]},{"display":"Fathom","hidingEnts":[],"value":"FATHOM","hidingOrgs":[]},{"display":"Troy Ounce","hidingEnts":[],"value":"TROYOUNCE","hidingOrgs":[]},{"display":"Kilo Pascal","hidingEnts":[],"value":"KILOPASCAL","hidingOrgs":[]},{"display":"Tons","hidingEnts":[],"value":"TONS","hidingOrgs":[]},{"display":"Inch Pound","hidingEnts":[],"value":"INCHPOUND","hidingOrgs":[]},{"display":"Tote Half","hidingEnts":[],"value":"TOTE_ HALF","hidingOrgs":[]},{"display":"Box","hidingEnts":[],"value":"BOX","hidingOrgs":[]},{"display":"Siemens","hidingEnts":[],"value":"SIEMENS","hidingOrgs":[]},{"display":"Square Decimeter","hidingEnts":[],"value":"SQUAREDECIMETER","hidingOrgs":[]},{"display":"Pounds/Inch Of Width","hidingEnts":[],"value":"POUNDSPERINCHOFWIDTH","hidingOrgs":[]},{"display":"Three-Pack","hidingEnts":[],"value":"THREEPACK","hidingOrgs":[]},{"display":"Twenty","hidingEnts":[],"value":"TWENTY","hidingOrgs":[]},{"display":"peck, Dry U.S.","hidingEnts":[],"value":"PECK,DRYU.S.","hidingOrgs":[]},{"display":"Ft/Minute","hidingEnts":[],"value":"FEETPERMINUTE","hidingOrgs":[]},{"display":"Kilo Curie Unit of Radiation","hidingEnts":[],"value":"KILOCURIEUNITOFRADIATION","hidingOrgs":[]},{"display":"Tract Foot","hidingEnts":[],"value":"TRACKFOOT","hidingOrgs":[]},{"display":"Ream","hidingEnts":[],"value":"REAM","hidingOrgs":[]},{"display":"Dry Pound","hidingEnts":[],"value":"DRY_POUND","hidingOrgs":[]},{"display":"Electron Volt","hidingEnts":[],"value":"ELECTRONVOLT","hidingOrgs":[]},{"display":"10K Gallon Tak Car","hidingEnts":[],"value":"10000GALLONTANKCAR","hidingOrgs":[]},{"display":"Newton/Meter","hidingEnts":[],"value":"NEWTONSPERMETER","hidingOrgs":[]},{"display":"300 KG Bulk Bag","hidingEnts":[],"value":"300KILOGRAMBULKBAG","hidingOrgs":[]},{"display":"Shares","hidingEnts":[],"value":"SHARES","hidingOrgs":[]},{"display":"Million British Thermal Units/Year","hidingEnts":[],"value":"MILLIONBRITISHTHERMALUNITSPER","hidingOrgs":[]},{"display":"Acre","hidingEnts":[],"value":"ACRE","hidingOrgs":[]},{"display":"Large Spray","hidingEnts":[],"value":"LARGESPRAY","hidingOrgs":[]},{"display":"Eight-Pack","hidingEnts":[],"value":"EIGHT-PACK","hidingOrgs":[]},{"display":"Kilo Watt Demand","hidingEnts":[],"value":"KILOWATTDEMAND","hidingOrgs":[]},{"display":"Horse Power Days/Air Dry Metri","hidingEnts":[],"value":"HORSEPOWERDAYSPERAIRDRYMETRI","hidingOrgs":[]},{"display":"Mega Byte","hidingEnts":[],"value":"MEGABYTE","hidingOrgs":[]},{"display":"Torr","hidingEnts":[],"value":"TORR","hidingOrgs":[]},{"display":"Wattage Hours","hidingEnts":[],"value":"WATTAGEHOURS","hidingOrgs":[]},{"display":"Net Pounds","hidingEnts":[],"value":"POUNDSNET","hidingOrgs":[]},{"display":"Hectometer","hidingEnts":[],"value":"HECTOMETER","hidingOrgs":[]},{"display":"Kiloroentgen","hidingEnts":[],"value":"KILOROENTGEN","hidingOrgs":[]},{"display":"Oz","hidingEnts":[],"value":"OZ","hidingOrgs":[]},{"display":"Six Pack","hidingEnts":[],"value":"SIXPACK","hidingOrgs":[]},{"display":"Bundle","hidingEnts":[],"value":"BUNDLE","hidingOrgs":[]},{"display":"Conference Points","hidingEnts":[],"value":"CONFERENCEPOINTS","hidingOrgs":[]},{"display":"1000 Cubic Ft","hidingEnts":[],"value":"THOUSANDCUBICFEET","hidingOrgs":[]},{"display":"Visit","hidingEnts":[],"value":"VISIT","hidingOrgs":[]},{"display":"Milliwatt","hidingEnts":[],"value":"MILLIWATT","hidingOrgs":[]},{"display":"Milligrams/Sq Meter","hidingEnts":[],"value":"MILLIGRAMSPERSQUAREMETER","hidingOrgs":[]},{"display":"Container","hidingEnts":[],"value":"CONTAINER","hidingOrgs":[]},{"display":"100 Ft-Linear","hidingEnts":[],"value":"HUNDREDFEET-LINEAR","hidingOrgs":[]},{"display":"SEAVAN","hidingEnts":[],"value":"SEAVAN","hidingOrgs":[]},{"display":"Dozen","hidingEnts":[],"value":"DOZEN","hidingOrgs":[]},{"display":"Points","hidingEnts":[],"value":"POINT","hidingOrgs":[]},{"display":"Pound/Square Inch Guage","hidingEnts":[],"value":"POUNDSPERSQUAREINCHGAUGE","hidingOrgs":[]},{"display":"Pt","hidingEnts":[],"value":"PT","hidingOrgs":[]},{"display":"20K Gallon Tank Car","hidingEnts":[],"value":"20,000GALLONTANKCAR","hidingOrgs":[]},{"display":"Becquerel","hidingEnts":[],"value":"BECQUEREL","hidingOrgs":[]},{"display":"Tote","hidingEnts":[],"value":"TOTE","hidingOrgs":[]},{"display":"Milliroentgen","hidingEnts":[],"value":"MILLIROENTGEN","hidingOrgs":[]},{"display":"Square Meter/Second","hidingEnts":[],"value":"SQUAREMETERSPERSECOND","hidingOrgs":[]},{"display":"Mho","hidingEnts":[],"value":"MHO","hidingOrgs":[]},{"display":"Ring","hidingEnts":[],"value":"RING","hidingOrgs":[]},{"display":"Metric Gross Ton","hidingEnts":[],"value":"METRICGROSSTON","hidingOrgs":[]},{"display":"Triwall Box","hidingEnts":[],"value":"BOX_TRIWALL","hidingOrgs":[]},{"display":"Barrel/Day","hidingEnts":[],"value":"BARRELSPERDAY","hidingOrgs":[]},{"display":"Twenty-Five","hidingEnts":[],"value":"TWENTY-FIVE","hidingOrgs":[]},{"display":"Thsouand Cubic Meters","hidingEnts":[],"value":"THOUSANDCUBICMETERS","hidingOrgs":[]},{"display":"Deciliter","hidingEnts":[],"value":"DECILITER","hidingOrgs":[]},{"display":"Each","hidingEnts":[],"value":"EACH","hidingOrgs":[]},{"display":"Ft & Decimal","hidingEnts":[],"value":"FEETANDDECIMAL","hidingOrgs":[]},{"display":"Inches, Decimal-Actual","hidingEnts":[],"value":"INCHES,DECIMAL--ACTUAL","hidingOrgs":[]},{"display":"Total Car Count","hidingEnts":[],"value":"TOTALCARCOUNT","hidingOrgs":[]},{"display":"Cake","hidingEnts":[],"value":"CAKE","hidingOrgs":[]},{"display":"Volt","hidingEnts":[],"value":"VOLT","hidingOrgs":[]},{"display":"Loose","hidingEnts":[],"value":"LOOSE","hidingOrgs":[]},{"display":"Gross","hidingEnts":[],"value":"GROSS","hidingOrgs":[]},{"display":"Keg","hidingEnts":[],"value":"KEG","hidingOrgs":[]},{"display":"Gallons/Day","hidingEnts":[],"value":"GALLONS/DAY","hidingOrgs":[]},{"display":"Hydraulic Horse Power","hidingEnts":[],"value":"HYDRAULICHORSEPOWER","hidingOrgs":[]},{"display":"MIL","hidingEnts":[],"value":"MIL","hidingOrgs":[]},{"display":"Tenth Minuts","hidingEnts":[],"value":"TENTHMINUTES","hidingOrgs":[]},{"display":"Millimol","hidingEnts":[],"value":"MILLIMOLE","hidingOrgs":[]},{"display":"Vial","hidingEnts":[],"value":"VIAL","hidingOrgs":[]},{"display":"Truck Load","hidingEnts":[],"value":"TRUCKLOAD","hidingOrgs":[]},{"display":"PACK","hidingEnts":[],"value":"PACK","hidingOrgs":[]},{"display":"Intermodal Trailer Load (Rail)","hidingEnts":[],"value":"TRAILER","hidingOrgs":[]},{"display":"Reel","hidingEnts":[],"value":"REEL","hidingOrgs":[]},{"display":"Equivalence Gallons","hidingEnts":[],"value":"EQUIVALENTGALLONS","hidingOrgs":[]},{"display":"Millimeter-Nominal","hidingEnts":[],"value":"MILLIMETER-NOMINAL","hidingOrgs":[]},{"display":"Billet","hidingEnts":[],"value":"BILLET","hidingOrgs":[]},{"display":"10 Square Yards","hidingEnts":[],"value":"TENSQUAREYARDS","hidingOrgs":[]},{"display":"Centigram","hidingEnts":[],"value":"CENTIGRAM","hidingOrgs":[]},{"display":"Syringe","hidingEnts":[],"value":"SYRINGE","hidingOrgs":[]},{"display":"1bil BTU/h","hidingEnts":[],"value":"BILLIONBTUPERHOUR","hidingOrgs":[]},{"display":"Thousand Of An Inch","hidingEnts":[],"value":"THOUSANDTHSOFANINCH","hidingOrgs":[]},{"display":"Air Dry Metriction","hidingEnts":[],"value":"AIRDRYMETRICTON","hidingOrgs":[]},{"display":"Linear Yard","hidingEnts":[],"value":"LINEARYARD","hidingOrgs":[]},{"display":"Group","hidingEnts":[],"value":"GROUP","hidingOrgs":[]},{"display":"Sf","hidingEnts":[],"value":"SF","hidingOrgs":[]},{"display":"Grams/100 Grams","hidingEnts":[],"value":"GRAMSPER100GRAMS","hidingOrgs":[]},{"display":"Si","hidingEnts":[],"value":"SI","hidingOrgs":[]},{"display":"Messages","hidingEnts":[],"value":"MESSAGES","hidingOrgs":[]},{"display":"Lb Solids","hidingEnts":[],"value":"LB SOLIDS","hidingOrgs":[]},{"display":"Spool","hidingEnts":[],"value":"SPOOL","hidingOrgs":[]},{"display":"Bun","hidingEnts":[],"value":"BUN","hidingOrgs":[]},{"display":"Data Records","hidingEnts":[],"value":"DATARECORDS","hidingOrgs":[]},{"display":"Linear Yard/Pound","hidingEnts":[],"value":"LINEARYARDSPERPOUND","hidingOrgs":[]},{"display":"Case Other","hidingEnts":[],"value":"CASE_OTHER","hidingOrgs":[]},{"display":"Gill (Impe 6AI)","hidingEnts":[],"value":"GILL(IMPE6AI)","hidingOrgs":[]},{"display":"Monetary Value","hidingEnts":[],"value":"MONETARYVALUE","hidingOrgs":[]},{"display":"Ration","hidingEnts":[],"value":"RATION","hidingOrgs":[]},{"display":"Pound/Sq Ft","hidingEnts":[],"value":"POUNDSPERSQ.FT.","hidingOrgs":[]},{"display":"Tin","hidingEnts":[],"value":"TI","hidingOrgs":[]},{"display":"Gram","hidingEnts":[],"value":"GRAM","hidingOrgs":[]},{"display":"Percent","hidingEnts":[],"value":"PERCENT","hidingOrgs":[]},{"display":"Mass Pounds","hidingEnts":[],"value":"MASSPOUNDS","hidingOrgs":[]},{"display":"Calls","hidingEnts":[],"value":"CALLS","hidingOrgs":[]},{"display":"Dyne/Square Centimeter","hidingEnts":[],"value":"DYNEPERSQUARECENTIMETER","hidingOrgs":[]},{"display":"Pages-Facsimile","hidingEnts":[],"value":"PAGES-FACSIMILE","hidingOrgs":[]},{"display":"Milligram","hidingEnts":[],"value":"MILLIGRAM","hidingOrgs":[]},{"display":"Miles","hidingEnts":[],"value":"MILES","hidingOrgs":[]},{"display":"Square Ft","hidingEnts":[],"value":"SQUAREFOOT","hidingOrgs":[]},{"display":"Pintu S Dry","hidingEnts":[],"value":"PINTU.S.DRY","hidingOrgs":[]},{"display":"Ft, Inches & Decimal","hidingEnts":[],"value":"FEET,INCHESANDDECIMAL","hidingOrgs":[]},{"display":"Troy Pound","hidingEnts":[],"value":"TROYPOUND","hidingOrgs":[]},{"display":"Bolt","hidingEnts":[],"value":"BOLT","hidingOrgs":[]},{"display":"Liner Bag Dry","hidingEnts":[],"value":"LINER_BAG_DRY","hidingOrgs":[]},{"display":"Joules","hidingEnts":[],"value":"JOULES","hidingOrgs":[]},{"display":"Inhaler","hidingEnts":[],"value":"INHALER","hidingOrgs":[]},{"display":"Mega Grams/Hour","hidingEnts":[],"value":"MEGAGRAMSPERHOUR","hidingOrgs":[]},{"display":"Batting Pound","hidingEnts":[],"value":"BATTTINGPOUND","hidingOrgs":[]},{"display":"Package","hidingEnts":[],"value":"PACKAGE","hidingOrgs":[]},{"display":"Wet Pounds","hidingEnts":[],"value":"WET POUNDS","hidingOrgs":[]},{"display":"Pounds Equivalent","hidingEnts":[],"value":"POUNDSEQUIVALENT","hidingOrgs":[]},{"display":"Millimeter-Actual","hidingEnts":[],"value":"MILLIMETER-ACTUAL","hidingOrgs":[]},{"display":"1000 KGs","hidingEnts":[],"value":"THOUSANDKILOGRAMS","hidingOrgs":[]},{"display":"Long Ton","hidingEnts":[],"value":"LONGTON","hidingOrgs":[]},{"display":"100 Board Ft","hidingEnts":[],"value":"100BOARDFEET","hidingOrgs":[]},{"display":"Anit-Hemopilic Factor (AHF) Unit","hidingEnts":[],"value":"ANIT-HEMOPHILICFACTOR(AHF)UNIT","hidingOrgs":[]},{"display":"Page","hidingEnts":[],"value":"PAGE","hidingOrgs":[]},{"display":"Number Of Screens","hidingEnts":[],"value":"NUMBEROFSCREENS","hidingOrgs":[]},{"display":"Fluid Ounce (Imperial)","hidingEnts":[],"value":"FLUIDOUNCE(IMPERIAL)","hidingOrgs":[]},{"display":"Linear Centimeter","hidingEnts":[],"value":"LINEARCENTIMETER","hidingOrgs":[]},{"display":"Years","hidingEnts":[],"value":"YEARS","hidingOrgs":[]},{"display":"Million Particles/Cubic Ft","hidingEnts":[],"value":"MILLIONPARTICLESPERCUBICFOOT","hidingOrgs":[]},{"display":"KIT","hidingEnts":[],"value":"KIT","hidingOrgs":[]},{"display":"Tierce","hidingEnts":[],"value":"TIERCE","hidingOrgs":[]},{"display":"Combo","hidingEnts":[],"value":"COMBO","hidingOrgs":[]},{"display":"Pages-Electronic","hidingEnts":[],"value":"PAGES-ELECTRONIC","hidingOrgs":[]},{"display":"Working Months","hidingEnts":[],"value":"WORKINGMONTHS","hidingOrgs":[]},{"display":"Mol","hidingEnts":[],"value":"MOLE","hidingOrgs":[]},{"display":"Additional Minutes","hidingEnts":[],"value":"ADDITIONALMINUTES","hidingOrgs":[]},{"display":"Ampere Turn/Centimeter","hidingEnts":[],"value":"AMPERETURNPERCENTIMETER","hidingOrgs":[]},{"display":"Minutes Or Messages","hidingEnts":[],"value":"MINUTESORMESSAGES","hidingOrgs":[]},{"display":"Bunks","hidingEnts":[],"value":"BUNKS","hidingOrgs":[]},{"display":"Kiloliter/Hour","hidingEnts":[],"value":"KILOLITERPERHOUR","hidingOrgs":[]},{"display":"Thsouand Pound Gross","hidingEnts":[],"value":"THOUSANDPOUNDSGROSS","hidingOrgs":[]},{"display":"mA","hidingEnts":[],"value":"MILLIAMPERE","hidingOrgs":[]},{"display":"Pipeline","hidingEnts":[],"value":"PIPELINE","hidingOrgs":[]},{"display":"Wheeled Carrier","hidingEnts":[],"value":"WHEELED_CARRIER","hidingOrgs":[]},{"display":"Shot","hidingEnts":[],"value":"SHOT","hidingOrgs":[]},{"display":"25 KG Bulk Bag","hidingEnts":[],"value":"25KILOGRAMBULKBAG","hidingOrgs":[]},{"display":"Nanosecond","hidingEnts":[],"value":"NANOSECOND","hidingOrgs":[]},{"display":"Cubic Decimeter","hidingEnts":[],"value":"CUBICDECIMETER","hidingOrgs":[]},{"display":"Seven Pack","hidingEnts":[],"value":"SEVENPACK","hidingOrgs":[]},{"display":"Inches/Minute","hidingEnts":[],"value":"INCHESPERMINUTE","hidingOrgs":[]},{"display":"Kilogram/Litre Of Product","hidingEnts":[],"value":"KILOGRAMSPERLITEROFPRODUCT","hidingOrgs":[]},{"display":"Hundred Weight (Short)","hidingEnts":[],"value":"HUNDREDWEIGHT(SHORT)","hidingOrgs":[]},{"display":"Container, Commercial","hidingEnts":[],"value":"CONTAINER_COMM","hidingOrgs":[]},{"display":"1000 AGS","hidingEnts":[],"value":"THOUSANDSAGS","hidingOrgs":[]},{"display":"Kilometers","hidingEnts":[],"value":"KILOMETERS","hidingOrgs":[]},{"display":"Carton","hidingEnts":[],"value":"CARTON","hidingOrgs":[]},{"display":"Rack","hidingEnts":[],"value":"RACK","hidingOrgs":[]},{"display":"Pail","hidingEnts":[],"value":"PAIL","hidingOrgs":[]},{"display":"Candela","hidingEnts":[],"value":"CANDELA","hidingOrgs":[]},{"display":"Hours","hidingEnts":[],"value":"HOURS","hidingOrgs":[]},{"display":"Book","hidingEnts":[],"value":"BOOK","hidingOrgs":[]},{"display":"Micro Curie","hidingEnts":[],"value":"MICROCURIE","hidingOrgs":[]},{"display":"Thirty-Six","hidingEnts":[],"value":"THIRTY-SIX","hidingOrgs":[]},{"display":"Sessions","hidingEnts":[],"value":"SESSIONS","hidingOrgs":[]},{"display":"Reverse Reel","hidingEnts":[],"value":"REVERSE_REEL","hidingOrgs":[]},{"display":"Seismic Level","hidingEnts":[],"value":"SEISMICLEVEL","hidingOrgs":[]},{"display":"Minutes","hidingEnts":[],"value":"MINUTES","hidingOrgs":[]},{"display":"Section","hidingEnts":[],"value":"SECTION","hidingOrgs":[]},{"display":"Pair","hidingEnts":[],"value":"PAIR","hidingOrgs":[]},{"display":"Kilo Pound/Square Inch (KSI)","hidingEnts":[],"value":"KILOPOUNDSPERSQUAREINCH(KSI)","hidingOrgs":[]},{"display":"Centimeter/Second","hidingEnts":[],"value":"CENTIMETERSPERSECOND","hidingOrgs":[]},{"display":"km/h","hidingEnts":[],"value":"KILOMETERSPERHOUR","hidingOrgs":[]},{"display":"Quire","hidingEnts":[],"value":"QUIRE","hidingOrgs":[]},{"display":"Square Rod","hidingEnts":[],"value":"SQUAREROD","hidingOrgs":[]},{"display":"US Gallons/Minue","hidingEnts":[],"value":"U.S.GALLONSPERMINUTE","hidingOrgs":[]},{"display":"Card","hidingEnts":[],"value":"CARD","hidingOrgs":[]},{"display":"Kilogram/Place Of Product","hidingEnts":[],"value":"KILOGRAMSPERPLACEOFPRODUCT","hidingOrgs":[]},{"display":"20 Millimeter","hidingEnts":[],"value":"MILLIMETERH2O","hidingOrgs":[]},{"display":"Tray","hidingEnts":[],"value":"TRAY","hidingOrgs":[]},{"display":"Gallon/Thousand","hidingEnts":[],"value":"GALLONSPERTHOUSAND","hidingOrgs":[]},{"display":"Milli Pascals","hidingEnts":[],"value":"MILLIPASCALS","hidingOrgs":[]},{"display":"Cubic Centimeter/Second","hidingEnts":[],"value":"CUBICCENTIMETERSPERSECOND","hidingOrgs":[]},{"display":"Yard","hidingEnts":[],"value":"YARD","hidingOrgs":[]},{"display":"Wet Kilo","hidingEnts":[],"value":"WETKILO","hidingOrgs":[]},{"display":"Liner Bag Liquid","hidingEnts":[],"value":"LINER_BAG","hidingOrgs":[]},{"display":"Super Bulk Bag","hidingEnts":[],"value":"SUPERBULKBAG","hidingOrgs":[]},{"display":"(MM of Water)","hidingEnts":[],"value":"MILLILITERSOFWATER","hidingOrgs":[]},{"display":"Theoretical Tonnes","hidingEnts":[],"value":"THEORETICALTONNES","hidingOrgs":[]},{"display":"KGS/Square Meter, KGS","hidingEnts":[],"value":"KILOGRAMSPERSQUAREMETER,KILOGRAMS,","hidingOrgs":[]},{"display":"Locomotive Count","hidingEnts":[],"value":"LOCOMOTIVECOUNT","hidingOrgs":[]},{"display":"Total Car Mile","hidingEnts":[],"value":"TOTALCARMILE","hidingOrgs":[]},{"display":"Tote Can","hidingEnts":[],"value":"TOTE_CAN","hidingOrgs":[]},{"display":"Person","hidingEnts":[],"value":"PERSON","hidingOrgs":[]},{"display":"Micro Farad","hidingEnts":[],"value":"MICROFARAD","hidingOrgs":[]},{"display":"Decigram","hidingEnts":[],"value":"DECIGRAM","hidingOrgs":[]},{"display":"Micro Liter","hidingEnts":[],"value":"MICROLITER","hidingOrgs":[]},{"display":"Joule/KG","hidingEnts":[],"value":"JOULEPERKILOGRAM","hidingOrgs":[]},{"display":"Cask","hidingEnts":[],"value":"CASK","hidingOrgs":[]},{"display":"Case","hidingEnts":[],"value":"CASE","hidingOrgs":[]},{"display":"100 Cubic Ft","hidingEnts":[],"value":"HUNDREDCUBICFEET","hidingOrgs":[]},{"display":"Solid Pounds","hidingEnts":[],"value":"SOLIDPOUNDS","hidingOrgs":[]},{"display":"Insurance Policy","hidingEnts":[],"value":"INSURANCEPOLICY","hidingOrgs":[]},{"display":"kg/s","hidingEnts":[],"value":"KILOGRAMSPERSECOND","hidingOrgs":[]},{"display":"Dozen Pair","hidingEnts":[],"value":"DOZENPAIR","hidingOrgs":[]},{"display":"Count","hidingEnts":[],"value":"COUNT","hidingOrgs":[]},{"display":"Kilo Packets","hidingEnts":[],"value":"KILOPACKETS","hidingOrgs":[]},{"display":"Cradle","hidingEnts":[],"value":"CRADLE","hidingOrgs":[]},{"display":"Bulk Card Load","hidingEnts":[],"value":"BULKCARLOAD","hidingOrgs":[]},{"display":"Sqaure Mile","hidingEnts":[],"value":"SQUAREMILE","hidingOrgs":[]},{"display":"Telecommunications Lines In Service","hidingEnts":[],"value":"TELECOMMUNICATIONSLINESINSERVICE","hidingOrgs":[]},{"display":"Kilo","hidingEnts":[],"value":"KILO","hidingOrgs":[]},{"display":"m3/s","hidingEnts":[],"value":"CUBICMETERSPERSECOND","hidingOrgs":[]},{"display":"Length","hidingEnts":[],"value":"LENGTH","hidingOrgs":[]},{"display":"Pounds/Ream","hidingEnts":[],"value":"POUNDSPERREAM","hidingOrgs":[]},{"display":"Half Pint","hidingEnts":[],"value":"HALFPINT","hidingOrgs":[]},{"display":"Gram Gold","hidingEnts":[],"value":"GRAMGOLD","hidingOrgs":[]},{"display":"Volts (DC)","hidingEnts":[],"value":"VOLTS(DIRECTCURRENT)","hidingOrgs":[]},{"display":"Kilowatt Hour","hidingEnts":[],"value":"KILOWATTHOUR","hidingOrgs":[]},{"display":"Quarter of Beef","hidingEnts":[],"value":"BEEF_QUARTER","hidingOrgs":[]},{"display":"Small Spray","hidingEnts":[],"value":"SMALLSPRAY","hidingOrgs":[]},{"display":"Envelope","hidingEnts":[],"value":"ENVELOPE","hidingOrgs":[]},{"display":"Usgae/Telecommunications Line","hidingEnts":[],"value":"USAGEPERTELECOMMUNICATIONSLINE","hidingOrgs":[]},{"display":"Segment","hidingEnts":[],"value":"SEGMENT","hidingOrgs":[]},{"display":"64th Of An Inch","hidingEnts":[],"value":"SIXTY-FOURTHSOFANINCH","hidingOrgs":[]},{"display":"Revenue Ton Miles","hidingEnts":[],"value":"REVENUETONMILES","hidingOrgs":[]},{"display":"100 KGS","hidingEnts":[],"value":"100KILOGRAMS","hidingOrgs":[]},{"display":"Square Inch","hidingEnts":[],"value":"SQUAREINCH","hidingOrgs":[]},{"display":"Ream-Metric Measure","hidingEnts":[],"value":"REAM-METRICMEASURE","hidingOrgs":[]},{"display":"Dry Tom","hidingEnts":[],"value":"DRYTOM","hidingOrgs":[]},{"display":"Ampere/Meter","hidingEnts":[],"value":"AMPERESPERMETER","hidingOrgs":[]},{"display":"Pounds/1000 Square Ft","hidingEnts":[],"value":"POUNDSPER1000SQUAREFEET","hidingOrgs":[]},{"display":"Garments on Hangers","hidingEnts":[],"value":"GARMENT","hidingOrgs":[]},{"display":"Pounds, Decimal-Pounds/Square","hidingEnts":[],"value":"POUNDS,DECIMAL-POUNDSPERSQUA","hidingOrgs":[]},{"display":"Telecommunication Sports","hidingEnts":[],"value":"TELECOMMUNICATIONSPORTS","hidingOrgs":[]},{"display":"Bytes","hidingEnts":[],"value":"BYTES","hidingOrgs":[]},{"display":"Millimeters Of Mercury","hidingEnts":[],"value":"MILLIMETERSOFMERCURY","hidingOrgs":[]},{"display":"Cubic Ft/Hour","hidingEnts":[],"value":"CUBICFEETPERHOUR","hidingOrgs":[]},{"display":"Magnetic Tapes","hidingEnts":[],"value":"MAGNETICTAPES","hidingOrgs":[]},{"display":"Megawatt","hidingEnts":[],"value":"MEGAWATT","hidingOrgs":[]},{"display":"Percent Weight","hidingEnts":[],"value":"PERCENTWEIGHT","hidingOrgs":[]},{"display":"Hectoliter","hidingEnts":[],"value":"HECTOLITER","hidingOrgs":[]},{"display":"Kbq","hidingEnts":[],"value":"KILOBECQUERELUNITOFRADIATION","hidingOrgs":[]},{"display":"Microfiche Sheet","hidingEnts":[],"value":"MICROFICHESHEET","hidingOrgs":[]},{"display":"Car Count","hidingEnts":[],"value":"CARCOUNT","hidingOrgs":[]},{"display":"Ounce Inch","hidingEnts":[],"value":"OUNCEINCH","hidingOrgs":[]},{"display":"g/mol","hidingEnts":[],"value":"GRAMSPERMOLE","hidingOrgs":[]},{"display":"KG/Cubic Meter","hidingEnts":[],"value":"KILOGRAMSPERCUBICMETER","hidingOrgs":[]},{"display":"Theoretical KG","hidingEnts":[],"value":"THEORETICALKILOGRAMS","hidingOrgs":[]},{"display":"Micromho","hidingEnts":[],"value":"MICROMHO","hidingOrgs":[]},{"display":"Beam","hidingEnts":[],"value":"BEAM","hidingOrgs":[]},{"display":"1000 Square Ft","hidingEnts":[],"value":"THOUSANDSQUAREFEET","hidingOrgs":[]},{"display":"CAN","hidingEnts":[],"value":"CAN","hidingOrgs":[]},{"display":"100 Pound Drum","hidingEnts":[],"value":"100POUNDDRUM","hidingOrgs":[]},{"display":"Ounce Ft","hidingEnts":[],"value":"OUNCEFOOT","hidingOrgs":[]},{"display":"Cap","hidingEnts":[],"value":"CAP","hidingOrgs":[]},{"display":"Rail (Semiconductor)","hidingEnts":[],"value":"RAIL","hidingOrgs":[]},{"display":"Car","hidingEnts":[],"value":"CAR","hidingOrgs":[]},{"display":"Base Box","hidingEnts":[],"value":"BASEBOX","hidingOrgs":[]},{"display":"Man Month","hidingEnts":[],"value":"MANMONTH","hidingOrgs":[]},{"display":"Barge","hidingEnts":[],"value":"BARGE","hidingOrgs":[]},{"display":"Pascal","hidingEnts":[],"value":"PASCAL","hidingOrgs":[]},{"display":"Inches Of Water","hidingEnts":[],"value":"INCHESOFWATER","hidingOrgs":[]},{"display":"Tenth Cubic Ft","hidingEnts":[],"value":"TENTHCUBICFOOT","hidingOrgs":[]},{"display":"Picosecond","hidingEnts":[],"value":"PICOSECOND","hidingOrgs":[]},{"display":"Pound/Square Inch","hidingEnts":[],"value":"POUNDSPERSQ.INCH","hidingOrgs":[]},{"display":"Sheet-Metric Measure","hidingEnts":[],"value":"SHEET-METRICMEASURE","hidingOrgs":[]},{"display":"Ton Miles","hidingEnts":[],"value":"TONMILES","hidingOrgs":[]},{"display":"Series","hidingEnts":[],"value":"SERIES","hidingOrgs":[]},{"display":"Ampoule","hidingEnts":[],"value":"AMPOULE","hidingOrgs":[]},{"display":"Gallon","hidingEnts":[],"value":"GALLON","hidingOrgs":[]},{"display":"Actual Pounds","hidingEnts":[],"value":"ACTUAL_POUNDS","hidingOrgs":[]},{"display":"Meal","hidingEnts":[],"value":"MEAL","hidingOrgs":[]},{"display":"Avg Mitute/Call","hidingEnts":[],"value":"AVERAGEMINUTESPERCALL","hidingOrgs":[]},{"display":"Five-Pack","hidingEnts":[],"value":"FIVE-PACK","hidingOrgs":[]},{"display":"Pico Farad","hidingEnts":[],"value":"PICOFARAD","hidingOrgs":[]},{"display":"Metric Ton KGS","hidingEnts":[],"value":"METRICTONKILOGRAMS","hidingOrgs":[]},{"display":"Ten","hidingEnts":[],"value":"TEN","hidingOrgs":[]},{"display":"Firkin","hidingEnts":[],"value":"FIRKIN","hidingOrgs":[]},{"display":"Two Pack","hidingEnts":[],"value":"TWO_PACK","hidingOrgs":[]},{"display":"Teu","hidingEnts":[],"value":"TEU","hidingOrgs":[]},{"display":"Kilo Pascal/MilliMeter","hidingEnts":[],"value":"KILOPASCALSPERMILLIMETER","hidingOrgs":[]},{"display":"Draize Score","hidingEnts":[],"value":"DRAIZESCORE","hidingOrgs":[]},{"display":"Giga Joules","hidingEnts":[],"value":"GIGAJOULES","hidingOrgs":[]},{"display":"Jumbo","hidingEnts":[],"value":"JUMBO","hidingOrgs":[]},{"display":"Unit Less","hidingEnts":[],"value":"UNITLESS","hidingOrgs":[]},{"display":"Vehicles","hidingEnts":[],"value":"VEHICLE","hidingOrgs":[]},{"display":"Cubic Meter","hidingEnts":[],"value":"CUBICMETER","hidingOrgs":[]},{"display":"Radian/Second Squared","hidingEnts":[],"value":"RADIANSPERSECONDSQUARED","hidingOrgs":[]},{"display":"Cube","hidingEnts":[],"value":"CUBE","hidingOrgs":[]},{"display":"Mega Hertz","hidingEnts":[],"value":"MEGAHERTZ","hidingOrgs":[]},{"display":"Inches, Fraction-Nominal","hidingEnts":[],"value":"INCHES,FRACTION-NOMINAL","hidingOrgs":[]},{"display":"Hertz","hidingEnts":[],"value":"HERTZ","hidingOrgs":[]},{"display":"Decimeter","hidingEnts":[],"value":"DECIMETER","hidingOrgs":[]},{"display":"Flask","hidingEnts":[],"value":"FLASK","hidingOrgs":[]},{"display":"Lifetime","hidingEnts":[],"value":"LIFETIME","hidingOrgs":[]},{"display":"Net Imperial Gallons","hidingEnts":[],"value":"NETIMPERIALGALLONS","hidingOrgs":[]},{"display":"100 Boxes","hidingEnts":[],"value":"HUNDREDBOXES","hidingOrgs":[]},{"display":"1000 Linear Inches","hidingEnts":[],"value":"THOUSANDLINEARINCHES","hidingOrgs":[]},{"display":"Pounds/Ft","hidingEnts":[],"value":"POUNDSPERFOOT","hidingOrgs":[]},{"display":"m/s2","hidingEnts":[],"value":"METERSPERSECONDSQUARED","hidingOrgs":[]},{"display":"Base Weight","hidingEnts":[],"value":"BASEWEIGHT","hidingOrgs":[]},{"display":"Quarter Hours","hidingEnts":[],"value":"QUARTERHOURS","hidingOrgs":[]},{"display":"Foot","hidingEnts":[],"value":"FOOT","hidingOrgs":[]},{"display":"Packed","hidingEnts":[],"value":"PACKED","hidingOrgs":[]},{"display":"KG/Milliliter","hidingEnts":[],"value":"KILOGRAMSPERMILLIMETER","hidingOrgs":[]},{"display":"Grain","hidingEnts":[],"value":"GRAIN","hidingOrgs":[]},{"display":"100 Punds (CWT)","hidingEnts":[],"value":"HUNDREDPOUNDS(CWT)","hidingOrgs":[]},{"display":"Electronic Mailboxes","hidingEnts":[],"value":"ELECTRONICMAILBOXES","hidingOrgs":[]},{"display":"Joint","hidingEnts":[],"value":"JOINT","hidingOrgs":[]},{"display":"Bulk Pack","hidingEnts":[],"value":"BULKPACK","hidingOrgs":[]},{"display":"Roentgen","hidingEnts":[],"value":"ROENTGEN","hidingOrgs":[]},{"display":"Impressions","hidingEnts":[],"value":"IMPRESSIONS","hidingOrgs":[]},{"display":"Metric Long Ton","hidingEnts":[],"value":"METRICLONGTON","hidingOrgs":[]},{"display":"Milliliter","hidingEnts":[],"value":"MILLILITER","hidingOrgs":[]},{"display":"Inch","hidingEnts":[],"value":"INCH","hidingOrgs":[]},{"display":"20 Ft Container","hidingEnts":[],"value":"20FOOTCONTAINER","hidingOrgs":[]},{"display":"Unpacked","hidingEnts":[],"value":"UNPACKED","hidingOrgs":[]},{"display":"Flo-bin","hidingEnts":[],"value":"FLO-BIN","hidingOrgs":[]},{"display":"Pad","hidingEnts":[],"value":"PAD","hidingOrgs":[]},{"display":"Test Specific Scale","hidingEnts":[],"value":"TESTSPECIFICSCALE","hidingOrgs":[]},{"display":"Deal","hidingEnts":[],"value":"DEAL","hidingOrgs":[]},{"display":"Ten Yards","hidingEnts":[],"value":"TENYARDS","hidingOrgs":[]},{"display":"Liners/Minute","hidingEnts":[],"value":"LITERSPERMINUTE","hidingOrgs":[]},{"display":"Lumpsum","hidingEnts":[],"value":"LUMPSUM","hidingOrgs":[]},{"display":"Thou Sq In","hidingEnts":[],"value":"THOU SQ IN","hidingOrgs":[]},{"display":"Ounce AV","hidingEnts":[],"value":"OUNCE-AV","hidingOrgs":[]},{"display":"Nine Pack","hidingEnts":[],"value":"NINEPACK","hidingOrgs":[]},{"display":"Caboose Count","hidingEnts":[],"value":"CABOOSECOUNT","hidingOrgs":[]},{"display":"Linear Ft","hidingEnts":[],"value":"LINEAR FT","hidingOrgs":[]},{"display":"Eleven Pack","hidingEnts":[],"value":"ELEVENPACK","hidingOrgs":[]},{"display":"Transdermal Patch","hidingEnts":[],"value":"TRANSDERMALPATCH","hidingOrgs":[]},{"display":"Twenty-Four","hidingEnts":[],"value":"TWENTY-FOUR","hidingOrgs":[]},{"display":"Inches/Second (Linear Speed)","hidingEnts":[],"value":"INCHESPERSECOND(LINEARSPEED)","hidingOrgs":[]},{"display":"Meter/Second/Second 4K","hidingEnts":[],"value":"METERSPERSECONDPERSECOND4K","hidingOrgs":[]},{"display":"Hamper","hidingEnts":[],"value":"HAMPER","hidingOrgs":[]},{"display":"Parts/Million","hidingEnts":[],"value":"PARTSPERMILLION","hidingOrgs":[]},{"display":"Coulomb","hidingEnts":[],"value":"COULOMB","hidingOrgs":[]},{"display":"2 Week","hidingEnts":[],"value":"TWOWEEK","hidingOrgs":[]},{"display":"500 KG Bulk Bag","hidingEnts":[],"value":"500KILOGRAMBULKBAG","hidingOrgs":[]},{"display":"Roll-Metric Measure","hidingEnts":[],"value":"ROLL-METRICMEASURE","hidingOrgs":[]},{"display":"Milli-Ohm","hidingEnts":[],"value":"MILLIOHM","hidingOrgs":[]},{"display":"40 Ft Container","hidingEnts":[],"value":"40FOOTCONTAINER","hidingOrgs":[]},{"display":"Milli Volts","hidingEnts":[],"value":"MILLIVOLTS","hidingOrgs":[]},{"display":"Centimeter","hidingEnts":[],"value":"CENTIMETER","hidingOrgs":[]},{"display":"Split Tank Truck","hidingEnts":[],"value":"SPLITTANKTRUCK","hidingOrgs":[]},{"display":"Cubic Ft","hidingEnts":[],"value":"CUBICFEET","hidingOrgs":[]},{"display":"1000 Meters","hidingEnts":[],"value":"1000METERS","hidingOrgs":[]},{"display":"Quaft","hidingEnts":[],"value":"QUAFT","hidingOrgs":[]},{"display":"Cartridge","hidingEnts":[],"value":"CARTRIDGE","hidingOrgs":[]},{"display":"Hundred Yards","hidingEnts":[],"value":"HUNDREDYARDS","hidingOrgs":[]},{"display":"Machine/Unit","hidingEnts":[],"value":"MACHINE/UNIT","hidingOrgs":[]},{"display":"Pounds Gross","hidingEnts":[],"value":"POUNDS_GROSS","hidingOrgs":[]},{"display":"Packet","hidingEnts":[],"value":"PACKET","hidingOrgs":[]},{"display":"Grams per Liter","hidingEnts":[],"value":"GRAMS_PER_LITER","hidingOrgs":[]},{"display":"Ounces/Square Ft","hidingEnts":[],"value":"OUNCESPERSQUAREFOOT","hidingOrgs":[]},{"display":"1000 Gallons","hidingEnts":[],"value":"THOUSANDGALLONS","hidingOrgs":[]},{"display":"100 Sq Ft","hidingEnts":[],"value":"HUNDREDSQUAREFEET","hidingOrgs":[]},{"display":"Load","hidingEnts":[],"value":"LOAD","hidingOrgs":[]},{"display":"Millimeter-Average","hidingEnts":[],"value":"MILLIMETER-AVERAGE","hidingOrgs":[]},{"display":"Misc","hidingEnts":[],"value":"MISC","hidingOrgs":[]},{"display":"Cabinet","hidingEnts":[],"value":"CABINET","hidingOrgs":[]},{"display":"Stick","hidingEnts":[],"value":"STICK","hidingOrgs":[]},{"display":"Celsius","hidingEnts":[],"value":"CELSIUS","hidingOrgs":[]},{"display":"Tank","hidingEnts":[],"value":"TANK","hidingOrgs":[]},{"display":"Kilo Volt Amperes","hidingEnts":[],"value":"KILOVOLTAMPERES","hidingOrgs":[]},{"display":"Brush","hidingEnts":[],"value":"BRUSH","hidingOrgs":[]},{"display":"Pounds-Percentage","hidingEnts":[],"value":"POUNDS-PERCENTAGE","hidingOrgs":[]},{"display":"Batch","hidingEnts":[],"value":"BATCH","hidingOrgs":[]},{"display":"Rack Knockdown","hidingEnts":[],"value":"RACK_ KNOCKDOWN","hidingOrgs":[]},{"display":"BTU/Cubic Ft","hidingEnts":[],"value":"BTU'sPerCubicFoot","hidingOrgs":[]},{"display":"Kilogram/Air Dry Metrictions","hidingEnts":[],"value":"KILOGRAMSPERAIRDRYMETRICTONS","hidingOrgs":[]},{"display":"Curie","hidingEnts":[],"value":"CURIE","hidingOrgs":[]},{"display":"News Paper Gateline","hidingEnts":[],"value":"NEWSPAPERAGATELINE","hidingOrgs":[]},{"display":"Oersteds","hidingEnts":[],"value":"OERSTEDS","hidingOrgs":[]},{"display":"Gross Gallons","hidingEnts":[],"value":"GROSSGALLONS","hidingOrgs":[]},{"display":"Bing Chest","hidingEnts":[],"value":"BING_CHEST","hidingOrgs":[]},{"display":"Net Barrels","hidingEnts":[],"value":"NETBARRELS","hidingOrgs":[]},{"display":"Double-length Rack","hidingEnts":[],"value":"RACK_DOUBLE","hidingOrgs":[]},{"display":"Fuel Usage (Gallons)","hidingEnts":[],"value":"FUELUSAGE(GALLONS)","hidingOrgs":[]},{"display":"Estimate","hidingEnts":[],"value":"ESTIMATE","hidingOrgs":[]},{"display":"Board","hidingEnts":[],"value":"BOARD","hidingOrgs":[]},{"display":"Quart","hidingEnts":[],"value":"QUART","hidingOrgs":[]},{"display":"Inches, Decimal-Min","hidingEnts":[],"value":"INCHES,DECIMAL-MINIMUM","hidingOrgs":[]},{"display":"Ft/Second","hidingEnts":[],"value":"FEETPERSECOND","hidingOrgs":[]},{"display":"10 Square Ft","hidingEnts":[],"value":"TENSQUAREFEET","hidingOrgs":[]},{"display":"Belt","hidingEnts":[],"value":"BELT","hidingOrgs":[]},{"display":"Weight/Square Inch","hidingEnts":[],"value":"WEIGHTPERSQUAREINCH","hidingOrgs":[]},{"display":"Dram","hidingEnts":[],"value":"DRAM","hidingOrgs":[]},{"display":"Print Point","hidingEnts":[],"value":"PRINTPOINT","hidingOrgs":[]},{"display":"Overwrap","hidingEnts":[],"value":"OVERWRAP","hidingOrgs":[]},{"display":"Quart. Dry U.S.","hidingEnts":[],"value":"QUART,DRYU.S.","hidingOrgs":[]},{"display":"Treatments","hidingEnts":[],"value":"TREATMENTS","hidingOrgs":[]},{"display":"Flake Ton","hidingEnts":[],"value":"FLAKETON","hidingOrgs":[]},{"display":"Grams/100 Centimeters","hidingEnts":[],"value":"GRAMSPER100CENTIMETERS","hidingOrgs":[]},{"display":"Carat","hidingEnts":[],"value":"CARAT","hidingOrgs":[]},{"display":"Million BTU(s)","hidingEnts":[],"value":"MILLIONBTU'S","hidingOrgs":[]},{"display":"Joule/Gram","hidingEnts":[],"value":"JOULEPERGRAM","hidingOrgs":[]},{"display":"Train","hidingEnts":[],"value":"TRAIN","hidingOrgs":[]},{"display":"Chest","hidingEnts":[],"value":"CHEST","hidingOrgs":[]},{"display":"Micro Meter","hidingEnts":[],"value":"MICROMETER","hidingOrgs":[]},{"display":"1000 BTU","hidingEnts":[],"value":"1000BTU","hidingOrgs":[]},{"display":"Volts Ampere/Pound","hidingEnts":[],"value":"VOLT-AMPEREPERPOUND","hidingOrgs":[]},{"display":"Volt Ampere/KG","hidingEnts":[],"value":"VOLTAMPEREPERKILOGRAM","hidingOrgs":[]},{"display":"20 Pack","hidingEnts":[],"value":"20-PACK","hidingOrgs":[]},{"display":"Column Inches","hidingEnts":[],"value":"COLUMNINCHES","hidingOrgs":[]},{"display":"Degree","hidingEnts":[],"value":"DEGREE","hidingOrgs":[]},{"display":"Kilo Character","hidingEnts":[],"value":"KILOCHARACTERS","hidingOrgs":[]},{"display":"1000 Casings","hidingEnts":[],"value":"THOUSANDCASINGS","hidingOrgs":[]},{"display":"Car Load","hidingEnts":[],"value":"CARLOAD","hidingOrgs":[]},{"display":"Revolutions/Minute","hidingEnts":[],"value":"REVOLUTIONSPERMINUTE","hidingOrgs":[]},{"display":"Frame","hidingEnts":[],"value":"FRAME","hidingOrgs":[]},{"display":"Skid","hidingEnts":[],"value":"SKID","hidingOrgs":[]},{"display":"Actual Tonnes","hidingEnts":[],"value":"ACTUALTONNES","hidingOrgs":[]},{"display":"Cheeses","hidingEnts":[],"value":"CHEESES","hidingOrgs":[]},{"display":"Deciliter/Gram","hidingEnts":[],"value":"DECILITERPERGRAM","hidingOrgs":[]},{"display":"Actual KG","hidingEnts":[],"value":"ACTUALKILOGRAMS","hidingOrgs":[]},{"display":"Empty Car","hidingEnts":[],"value":"EMPTYCAR","hidingOrgs":[]},{"display":"MILVAN","hidingEnts":[],"value":"MILVAN","hidingOrgs":[]},{"display":"Panel","hidingEnts":[],"value":"PANEL","hidingOrgs":[]},{"display":"Ammo Pack","hidingEnts":[],"value":"AMMO_PACK","hidingOrgs":[]},{"display":"Linear Inch","hidingEnts":[],"value":"LINEARINCH","hidingOrgs":[]},{"display":"Cop","hidingEnts":[],"value":"COP","hidingOrgs":[]},{"display":"Waft/KG","hidingEnts":[],"value":"WAFTSPERKILOGRAM","hidingOrgs":[]},{"display":"Kilo Volt Amperes Reactive","hidingEnts":[],"value":"KILOVOLTAMPERESREACTIVE","hidingOrgs":[]},{"display":"Volts (AC)","hidingEnts":[],"value":"VOLTS(ALTERNATINGCURRENT)","hidingOrgs":[]},{"display":"Imperial Gallons/Minute","hidingEnts":[],"value":"IMPERIALGALLONSPERMINUTE","hidingOrgs":[]},{"display":"Pallet (Lift)","hidingEnts":[],"value":"PALLET(LIFT)","hidingOrgs":[]},{"display":"Quart, Imperial","hidingEnts":[],"value":"QUART,IMPERIAL","hidingOrgs":[]},{"display":"Vehicle in Operating Condition","hidingEnts":[],"value":"VEHICLE_OPERATIONAL","hidingOrgs":[]},{"display":"Assembly","hidingEnts":[],"value":"ASSEMBLY","hidingOrgs":[]},{"display":"1000 Sq Centimeter","hidingEnts":[],"value":"THOUSANDSQ.CENTIMETERS","hidingOrgs":[]},{"display":"Thousand Linear Yards","hidingEnts":[],"value":"THOUSAND_LINEAR_YARDS","hidingOrgs":[]},{"display":"kA","hidingEnts":[],"value":"KILOAMPERE","hidingOrgs":[]},{"display":"Calorie/Cubic Centimeter","hidingEnts":[],"value":"CALORIESPERCUBICCENTIMETER","hidingOrgs":[]},{"display":"Tote Double","hidingEnts":[],"value":"TOTE_ DOUBLE","hidingOrgs":[]},{"display":"Chains (Land Survey)","hidingEnts":[],"value":"CHAINS(LANDSURVEY)","hidingOrgs":[]},{"display":"Outfit","hidingEnts":[],"value":"OUTFIT","hidingOrgs":[]},{"display":"Rod","hidingEnts":[],"value":"ROD","hidingOrgs":[]},{"display":"Inches, Fraction-Avg","hidingEnts":[],"value":"INCHES,FRACTION--AVERAGE","hidingOrgs":[]},{"display":"Parts/Billion","hidingEnts":[],"value":"PARTSPERBILLION","hidingOrgs":[]},{"display":"Caboose Mile","hidingEnts":[],"value":"CABOOSEMILE","hidingOrgs":[]},{"display":"Mega Joule/KG","hidingEnts":[],"value":"MEGAJOULEPERKILOGRAM","hidingOrgs":[]},{"display":"Dollar/Hour","hidingEnts":[],"value":"DOLLARSPERHOURS","hidingOrgs":[]},{"display":"MODULE","hidingEnts":[],"value":"MODULE","hidingOrgs":[]},{"display":"Capsule","hidingEnts":[],"value":"CAPSULE","hidingOrgs":[]},{"display":"100 Pack","hidingEnts":[],"value":"100-PACK","hidingOrgs":[]},{"display":"KG Decimal","hidingEnts":[],"value":"KILOGRAMSDECIMAL","hidingOrgs":[]},{"display":"Hundred Troy Ounces","hidingEnts":[],"value":"HUNDREDTROYOUNCES","hidingOrgs":[]},{"display":"Sitas","hidingEnts":[],"value":"SITAS","hidingOrgs":[]},{"display":"Round","hidingEnts":[],"value":"ROUND","hidingOrgs":[]},{"display":"Mesh","hidingEnts":[],"value":"MESH","hidingOrgs":[]},{"display":"10 KG Drum","hidingEnts":[],"value":"10KILOGRAMDRUM","hidingOrgs":[]},{"display":"Theoretical Tons","hidingEnts":[],"value":"THEORETICALTONS","hidingOrgs":[]},{"display":"100 Lineal Yards","hidingEnts":[],"value":"100LINEALYARDS","hidingOrgs":[]},{"display":"Cubic Ft/Minute","hidingEnts":[],"value":"CUBICFEETPERMINUTE","hidingOrgs":[]},{"display":"Gross Yard","hidingEnts":[],"value":"GROSSYARD","hidingOrgs":[]},{"display":"Tub","hidingEnts":[],"value":"TUB","hidingOrgs":[]},{"display":"Ball","hidingEnts":[],"value":"BALL","hidingOrgs":[]},{"display":"Bale","hidingEnts":[],"value":"BALE","hidingOrgs":[]},{"display":"Ton/Hour","hidingEnts":[],"value":"TONPERHOUR","hidingOrgs":[]},{"display":"Kilo Gauss","hidingEnts":[],"value":"KILOGAUSS","hidingOrgs":[]},{"display":"Dollars US","hidingEnts":[],"value":"DOLLARS,U.S.","hidingOrgs":[]},{"display":"Tenth Hours","hidingEnts":[],"value":"TENTHHOURS","hidingOrgs":[]},{"display":"Train Mile","hidingEnts":[],"value":"TRAINMILE","hidingOrgs":[]},{"display":"Jar","hidingEnts":[],"value":"JAR","hidingOrgs":[]},{"display":"Bushel","hidingEnts":[],"value":"BUSHEL","hidingOrgs":[]},{"display":"Miles/Hour","hidingEnts":[],"value":"MILESPERHOUR","hidingOrgs":[]},{"display":"Car Set","hidingEnts":[],"value":"CARSET","hidingOrgs":[]},{"display":"100 Sheets","hidingEnts":[],"value":"HUNDREDSHEETS","hidingOrgs":[]},{"display":"Cylinder","hidingEnts":[],"value":"CYLINDER","hidingOrgs":[]},{"display":"100 KGs","hidingEnts":[],"value":"HUNDREDKILOGRAMS","hidingOrgs":[]},{"display":"Millgrams/KG","hidingEnts":[],"value":"MILLIGRAMSPERKILOGRAM","hidingOrgs":[]},{"display":"Micro Molar","hidingEnts":[],"value":"MICROMOLAR","hidingOrgs":[]},{"display":"Platform","hidingEnts":[],"value":"PLATFORM","hidingOrgs":[]},{"display":"Months","hidingEnts":[],"value":"MONTHS","hidingOrgs":[]},{"display":"Mega Becquarel","hidingEnts":[],"value":"MEGABECQUAREL","hidingOrgs":[]},{"display":"Container, Navy Cargo","hidingEnts":[],"value":"CONTAINER_NAVY","hidingOrgs":[]},{"display":"Cubic Yard","hidingEnts":[],"value":"CUBICYARD","hidingOrgs":[]},{"display":"Kilo Volt Amperes Reactive Hour","hidingEnts":[],"value":"KILOVOLTAMPERESREACTIVEHOUR","hidingOrgs":[]},{"display":"Duffle Bag","hidingEnts":[],"value":"DUFFLE_BAG","hidingOrgs":[]},{"display":"Bushel, Dry Imperial","hidingEnts":[],"value":"BUSHEL,DRYIMPERIAL","hidingOrgs":[]},{"display":"Carmile","hidingEnts":[],"value":"CARMILE","hidingOrgs":[]},{"display":"Fridge","hidingEnts":[],"value":"FRIDGE","hidingOrgs":[]},{"display":"Pages/Inch","hidingEnts":[],"value":"PAGESPERINCH","hidingOrgs":[]},{"display":"Cup","hidingEnts":[],"value":"CUP","hidingOrgs":[]},{"display":"Mililitre/Square Centimeter","hidingEnts":[],"value":"MILLILITERSPERSQUARECENTIMETER","hidingOrgs":[]},{"display":"Powder Filler Vials","hidingEnts":[],"value":"POWDER-FILLERVIALS","hidingOrgs":[]},{"display":"Actual Pounds","hidingEnts":[],"value":"ACTUALPOUNDS","hidingOrgs":[]},{"display":"Cubic Meters","hidingEnts":[],"value":"CUBIC_METERS","hidingOrgs":[]},{"display":"Bobbin","hidingEnts":[],"value":"BOBBIN","hidingOrgs":[]},{"display":"Actual Tons","hidingEnts":[],"value":"ACTUALTONS","hidingOrgs":[]},{"display":"band","hidingEnts":[],"value":"BAND","hidingOrgs":[]},{"display":"1000 Cubic Ft","hidingEnts":[],"value":"1000CUBICFEET","hidingOrgs":[]},{"display":"Gage Systems","hidingEnts":[],"value":"GAGESYSTEMS","hidingOrgs":[]},{"display":"kV","hidingEnts":[],"value":"KILOVOLT","hidingOrgs":[]},{"display":"Newton-Meter","hidingEnts":[],"value":"NEWTON-METER","hidingOrgs":[]},{"display":"Centi Stokes","hidingEnts":[],"value":"CENTISTOKES","hidingOrgs":[]},{"display":"Milliliter/KG","hidingEnts":[],"value":"MILLILITERSPERKILOGRAM","hidingOrgs":[]},{"display":"Strip","hidingEnts":[],"value":"STRIP","hidingOrgs":[]},{"display":"Shipment","hidingEnts":[],"value":"SHIPMENT","hidingOrgs":[]},{"display":"Volts/Meter","hidingEnts":[],"value":"VOLTSPERMETER","hidingOrgs":[]},{"display":"Cones","hidingEnts":[],"value":"CONES","hidingOrgs":[]},{"display":"Mega Joule/Cubic Meter","hidingEnts":[],"value":"MEGAJOULE/CUBICMETER","hidingOrgs":[]},{"display":"Microsecond","hidingEnts":[],"value":"MICROSECOND","hidingOrgs":[]},{"display":"Piece","hidingEnts":[],"value":"PIECE","hidingOrgs":[]},{"display":"Pump","hidingEnts":[],"value":"PUMP","hidingOrgs":[]},{"display":"115 Kilogram Drum","hidingEnts":[],"value":"115KILOGRAMDRUM","hidingOrgs":[]},{"display":"Pen Calories","hidingEnts":[],"value":"PENCALORIES","hidingOrgs":[]},{"display":"Counts/Centimeter","hidingEnts":[],"value":"COUNTSPERCENTIMETER","hidingOrgs":[]},{"display":"COMPOSITEPRODUCTPOUNDS(TOTALW","hidingEnts":[],"value":"COMPOSITEPRODUCTPOUNDS(TOTALW","hidingOrgs":[]},{"display":"Nautical Mile","hidingEnts":[],"value":"NAUTICALMILE","hidingOrgs":[]},{"display":"Fifty","hidingEnts":[],"value":"FIFTY","hidingOrgs":[]},{"display":"On Own Wheel","hidingEnts":[],"value":"ON_OWN_WHEEL","hidingOrgs":[]},{"display":"Ampere","hidingEnts":[],"value":"AMPERE","hidingOrgs":[]},{"display":"Gauss","hidingEnts":[],"value":"GAUSS","hidingOrgs":[]},{"display":"Not Gallons","hidingEnts":[],"value":"NOTGALLONS","hidingOrgs":[]},{"display":"Metric Ton","hidingEnts":[],"value":"METRICTON","hidingOrgs":[]},{"display":"Pound/Square Inch Absolute","hidingEnts":[],"value":"POUNDSPERSQUAREINCHABSOLUTE","hidingOrgs":[]},{"display":"Stage","hidingEnts":[],"value":"STAGE","hidingOrgs":[]},{"display":"Skein","hidingEnts":[],"value":"SKEIN","hidingOrgs":[]},{"display":"Decibels","hidingEnts":[],"value":"DECIBELS","hidingOrgs":[]},{"display":"Seconds","hidingEnts":[],"value":"SECONDS","hidingOrgs":[]},{"display":"Giga Becquerei","hidingEnts":[],"value":"GIGABECQUEREI","hidingOrgs":[]},{"display":"Run","hidingEnts":[],"value":"RUN","hidingOrgs":[]},{"display":"Fluid Ounce","hidingEnts":[],"value":"FLUIDOUNCE","hidingOrgs":[]},{"display":"Millisecond","hidingEnts":[],"value":"MILLISECOND","hidingOrgs":[]},{"display":"Plate","hidingEnts":[],"value":"PLATE","hidingOrgs":[]},{"display":"Calorie/Gram","hidingEnts":[],"value":"CALORIESPERGRAM","hidingOrgs":[]},{"display":"Mega Pascals","hidingEnts":[],"value":"MEGAPASCALS","hidingOrgs":[]},{"display":"Noil","hidingEnts":[],"value":"NOIL","hidingOrgs":[]},{"display":"Wine Gallon","hidingEnts":[],"value":"WINEGALLON","hidingOrgs":[]},{"display":"Pound/Hour","hidingEnts":[],"value":"POUNDSPERHOUR","hidingOrgs":[]},{"display":"100 Cubic Meters","hidingEnts":[],"value":"HUNDREDCUBICMETERS","hidingOrgs":[]},{"display":"Can Case","hidingEnts":[],"value":"CAN_CASE","hidingOrgs":[]},{"display":"Grams/Sq Meter","hidingEnts":[],"value":"GRAMSPERSQ.METER","hidingOrgs":[]},{"display":"Labour Hours","hidingEnts":[],"value":"LABORHOURS","hidingOrgs":[]},{"display":"Microgram","hidingEnts":[],"value":"MICROGRAM","hidingOrgs":[]},{"display":"Coil","hidingEnts":[],"value":"COIL","hidingOrgs":[]},{"display":"MSC VAN","hidingEnts":[],"value":"MSCVAN","hidingOrgs":[]},{"display":"Quarter Mile","hidingEnts":[],"value":"QUARTERMILE","hidingOrgs":[]},{"display":"Quarter (Time)","hidingEnts":[],"value":"QUARTER(TIME)","hidingOrgs":[]},{"display":"1000 Linear Meters","hidingEnts":[],"value":"THOUSANDLINEARMETERS","hidingOrgs":[]},{"display":"British Thermal Unit","hidingEnts":[],"value":"BRITISHTHERMALUNIT(BTU)","hidingOrgs":[]},{"display":"Thousand Of Dollars","hidingEnts":[],"value":"THOUSANDSOFDOLLARS","hidingOrgs":[]},{"display":"Hundred Weight (Long)","hidingEnts":[],"value":"HUNDREDWEIGHT(LONG)","hidingOrgs":[]},{"display":"Pound/Piece Of Product","hidingEnts":[],"value":"POUNDSPERPIECEOFPRODUCT","hidingOrgs":[]},{"display":"Half Liter","hidingEnts":[],"value":"HALFLITER","hidingOrgs":[]},{"display":"Milli Curies","hidingEnts":[],"value":"MILLICURIE","hidingOrgs":[]},{"display":"Message Hours","hidingEnts":[],"value":"MESSAGEHOURS","hidingOrgs":[]},{"display":"Avg Telecommunications In Service","hidingEnts":[],"value":"TELECOMMUNICATIONSLINESINSERVICE-AVERAGE","hidingOrgs":[]},{"display":"CUBICMILLIMETER","hidingEnts":[],"value":"CUBICMILLIMETER","hidingOrgs":[]},{"display":"Dynes/Centimeter","hidingEnts":[],"value":"DYNESPERCENTIMETER","hidingOrgs":[]},{"display":"Dry Bulk","hidingEnts":[],"value":"BULK_DRY","hidingOrgs":[]},{"display":"Tube","hidingEnts":[],"value":"TUBE","hidingOrgs":[]},{"display":"Farad","hidingEnts":[],"value":"FARAD","hidingOrgs":[]},{"display":"Square Centimeter","hidingEnts":[],"value":"SQUARECENTIMETER","hidingOrgs":[]},{"display":"Grams/Milliliter","hidingEnts":[],"value":"GRAMSPERMILLILITER","hidingOrgs":[]},{"display":"Multiwall Container","hidingEnts":[],"value":"CONTAINER_MULTIWALL","hidingOrgs":[]},{"display":"CONEX - Container Express","hidingEnts":[],"value":"CONTAINER_EXPRESS","hidingOrgs":[]},{"display":"Hectogram","hidingEnts":[],"value":"HECTOGRAM","hidingOrgs":[]},{"display":"Newton","hidingEnts":[],"value":"NEWTON","hidingOrgs":[]},{"display":"Tank Truck","hidingEnts":[],"value":"TANK_TRUCK","hidingOrgs":[]},{"display":"Microns","hidingEnts":[],"value":"MICRONS","hidingOrgs":[]},{"display":"Ounces/Square Ft/0.01 Inch","hidingEnts":[],"value":"OUNCESPERSQUAREFOOTPER0.01I","hidingOrgs":[]},{"display":"kOhm","hidingEnts":[],"value":"KILOOHM","hidingOrgs":[]},{"display":"Barrel/Minute","hidingEnts":[],"value":"BARRELSPERMINUTE","hidingOrgs":[]},{"display":"Centiliter","hidingEnts":[],"value":"CENTILITER","hidingOrgs":[]},{"display":"Pint, Imperial","hidingEnts":[],"value":"PINT,IMPERIAL","hidingOrgs":[]},{"display":"1000 Ft (Linear)","hidingEnts":[],"value":"THOUSANDFEET(LINEAR)","hidingOrgs":[]},{"display":"Half dozen","hidingEnts":[],"value":"HALF_DOZEN","hidingOrgs":[]},{"display":"Inches, Decimal-Nominal","hidingEnts":[],"value":"INCHES,DECIMAL--NOMINAL","hidingOrgs":[]},{"display":"Net Liters","hidingEnts":[],"value":"NETLITERS","hidingOrgs":[]},{"display":"Pound","hidingEnts":[],"value":"POUND","hidingOrgs":[]},{"display":"Batt","hidingEnts":[],"value":"BATT","hidingOrgs":[]},{"display":"Fahrenheit","hidingEnts":[],"value":"FAHRENHEIT","hidingOrgs":[]},{"display":"Resets","hidingEnts":[],"value":"RESETS","hidingOrgs":[]},{"display":"Pengrams (Protein)","hidingEnts":[],"value":"PENGRAMS(PROTEIN)","hidingOrgs":[]},{"display":"Dispenser","hidingEnts":[],"value":"DISPENSER","hidingOrgs":[]},{"display":"Metric","hidingEnts":[],"value":"METRIC","hidingOrgs":[]},{"display":"Liquid Pounds","hidingEnts":[],"value":"LIQUIDPOUNDS","hidingOrgs":[]},{"display":"Failure Rate In Time","hidingEnts":[],"value":"FAILURERATEINTIME","hidingOrgs":[]},{"display":"Saybolt Universal Second","hidingEnts":[],"value":"SAYBOLDUNIVERSALSECOND","hidingOrgs":[]},{"display":"Brake Horse Power","hidingEnts":[],"value":"BRAKEHORSEPOWER","hidingOrgs":[]},{"display":"KGS/Meter","hidingEnts":[],"value":"KILOGRAMS/METER","hidingOrgs":[]},{"display":"Lifts","hidingEnts":[],"value":"LIFTS","hidingOrgs":[]},{"display":"Multi-Roll Pack","hidingEnts":[],"value":"MULTI_ROLLPACK","hidingOrgs":[]},{"display":"Coil Group","hidingEnts":[],"value":"COILGROUP","hidingOrgs":[]},{"display":"Number Of Lines","hidingEnts":[],"value":"NUMBEROFLINES","hidingOrgs":[]},{"display":"Kilo Pascal Square Meter/Gram","hidingEnts":[],"value":"KILOPASCALSQUAREMETERSPERGRAM","hidingOrgs":[]},{"display":"Pages-Hard Copy","hidingEnts":[],"value":"PAGES-HARDCOPY","hidingOrgs":[]},{"display":"Aluminium Pound Only","hidingEnts":[],"value":"ALUMINUMPOUNDSONLY","hidingOrgs":[]},{"display":"Pallet Unit Load","hidingEnts":[],"value":"PALLET_UNIT_LOAD","hidingOrgs":[]},{"display":"Half-Sheet","hidingEnts":[],"value":"HALFSHEET","hidingOrgs":[]},{"display":"Pounds/Gallon","hidingEnts":[],"value":"POUNDSPERGALLON","hidingOrgs":[]},{"display":"Stokes","hidingEnts":[],"value":"STOKES","hidingOrgs":[]},{"display":"Sack","hidingEnts":[],"value":"SACK","hidingOrgs":[]},{"display":"Cubic Meter/Hour","hidingEnts":[],"value":"CUBICMETERPERHOUR","hidingOrgs":[]},{"display":"Millgrams/Hour","hidingEnts":[],"value":"MILLIGRAMSPERHOUR","hidingOrgs":[]},{"display":"Microwatt","hidingEnts":[],"value":"MICROWATT","hidingOrgs":[]},{"display":"Cycles","hidingEnts":[],"value":"CYCLES","hidingOrgs":[]},{"display":"Board Ft","hidingEnts":[],"value":"BOARDFEET","hidingOrgs":[]},{"display":"Square","hidingEnts":[],"value":"SQUARE","hidingOrgs":[]},{"display":"Log","hidingEnts":[],"value":"LOG","hidingOrgs":[]},{"display":"Cassette","hidingEnts":[],"value":"CASSETTE","hidingOrgs":[]},{"display":"Lot","hidingEnts":[],"value":"LOT","hidingOrgs":[]},{"display":"Private Vehicle","hidingEnts":[],"value":"PRIVATE_VEHICLE","hidingOrgs":[]},{"display":"Millgrams/Square Inch","hidingEnts":[],"value":"MILLIGRAMSPERSQUAREINCH","hidingOrgs":[]},{"display":"Meter","hidingEnts":[],"value":"METER","hidingOrgs":[]},{"display":"Cone","hidingEnts":[],"value":"CONE","hidingOrgs":[]},{"display":"15 KG Drum","hidingEnts":[],"value":"15KILOGRAMDRUM","hidingOrgs":[]},{"display":"Grams/Square Centimeter","hidingEnts":[],"value":"GRAMSPERSQUARECENTIMETER","hidingOrgs":[]},{"display":"Van Pack","hidingEnts":[],"value":"VAN_PACK","hidingOrgs":[]},{"display":"Voltage","hidingEnts":[],"value":"VOLTAGE","hidingOrgs":[]},{"display":"Millibar","hidingEnts":[],"value":"MILLIBAR","hidingOrgs":[]},{"display":"Shook","hidingEnts":[],"value":"SHOOK","hidingOrgs":[]},{"display":"Linear Meter","hidingEnts":[],"value":"LINEARMETER","hidingOrgs":[]},{"display":"Curl Units","hidingEnts":[],"value":"CURLUNITS","hidingOrgs":[]},{"display":"Number Of Mults","hidingEnts":[],"value":"NUMBEROFMULTS","hidingOrgs":[]},{"display":"Running Operating Hours","hidingEnts":[],"value":"RUNNINGOROPERATINGHOURS","hidingOrgs":[]},{"display":"Centigrade, Celsius","hidingEnts":[],"value":"CENTIGRADE,CELSIUS","hidingOrgs":[]},{"display":"Cartridge Needle","hidingEnts":[],"value":"CARTRIDGENEEDLE","hidingOrgs":[]},{"display":"1000 Sheets","hidingEnts":[],"value":"THOUSANDSHEETS","hidingOrgs":[]},{"display":"Four Pack","hidingEnts":[],"value":"FOUR_PACK","hidingOrgs":[]},{"display":"Tank Car","hidingEnts":[],"value":"TANK_CAR","hidingOrgs":[]},{"display":"Kilo Volt Amperes Reactive Demand","hidingEnts":[],"value":"KILOVOLTAMPERESREACTIVEDEMAND","hidingOrgs":[]},{"display":"Kelvin","hidingEnts":[],"value":"KELVIN","hidingOrgs":[]},{"display":"Suppository","hidingEnts":[],"value":"SUPPOSITORY","hidingOrgs":[]},{"display":"Millgrams/Square Ft/Side","hidingEnts":[],"value":"MILLIGRAMPERSQ.FT.PERSIDE","hidingOrgs":[]},{"display":"Milliliter/Milute","hidingEnts":[],"value":"MILLILITERPERMINUTE","hidingOrgs":[]},{"display":"Nanometer","hidingEnts":[],"value":"NANOMETER","hidingOrgs":[]},{"display":"Season","hidingEnts":[],"value":"SEASON","hidingOrgs":[]},{"display":"Hopper Truck","hidingEnts":[],"value":"HOPPER","hidingOrgs":[]},{"display":"Ten Pack","hidingEnts":[],"value":"TEN_PACK","hidingOrgs":[]},{"display":"Million Units","hidingEnts":[],"value":"MILLIONUNITS","hidingOrgs":[]},{"display":"Ft, Inches & Fraction","hidingEnts":[],"value":"FEET,INCHESANDFRACTION","hidingOrgs":[]},{"display":"Carboy","hidingEnts":[],"value":"CARBOY","hidingOrgs":[]},{"display":"Kilo Watt","hidingEnts":[],"value":"KILOWATT","hidingOrgs":[]},{"display":"Five Hundre","hidingEnts":[],"value":"FIVEHUNDRED","hidingOrgs":[]},{"display":"50 Pound Bag","hidingEnts":[],"value":"50POUNDBAG","hidingOrgs":[]},{"display":"Sqaure Ft/Second","hidingEnts":[],"value":"SQUAREFEETPERSECOND","hidingOrgs":[]},{"display":"Core","hidingEnts":[],"value":"CORE","hidingOrgs":[]},{"display":"Mixed","hidingEnts":[],"value":"MIXED","hidingOrgs":[]},{"display":"Drum","hidingEnts":[],"value":"DRUM","hidingOrgs":[]},{"display":"Milliliter/Second","hidingEnts":[],"value":"MILLILITERPERSECOND","hidingOrgs":[]},{"display":"Ounces/Square Yard","hidingEnts":[],"value":"OUNCESPERSQUAREYARD","hidingOrgs":[]},{"display":"Pair Inches","hidingEnts":[],"value":"PAIRINCHES","hidingOrgs":[]},{"display":"Shelf Package","hidingEnts":[],"value":"SHELFPACKAGE","hidingOrgs":[]},{"display":"Slip Sheet","hidingEnts":[],"value":"SLIP_SHEET","hidingOrgs":[]},{"display":"Gross Barrels","hidingEnts":[],"value":"GROSSBARRELS","hidingOrgs":[]},{"display":"Pallet","hidingEnts":[],"value":"PALLET","hidingOrgs":[]},{"display":"Job","hidingEnts":[],"value":"JOB","hidingOrgs":[]},{"display":"Horse Power","hidingEnts":[],"value":"HORSEPOWER","hidingOrgs":[]},{"display":"Meter/Minute","hidingEnts":[],"value":"METERSPERMINUTE","hidingOrgs":[]},{"display":"Blcok","hidingEnts":[],"value":"BLOCK","hidingOrgs":[]},{"display":"Pitch","hidingEnts":[],"value":"PITCH","hidingOrgs":[]},{"display":"Engine Container","hidingEnts":[],"value":"CONTAINER_ENGINE","hidingOrgs":[]},{"display":"Connector","hidingEnts":[],"value":"CONNECTOR","hidingOrgs":[]},{"display":"Locomotive Mile","hidingEnts":[],"value":"LOCOMOTIVEMILE","hidingOrgs":[]},{"display":"Gross KG","hidingEnts":[],"value":"GROSSKILOGRAM","hidingOrgs":[]},{"display":"Wheel","hidingEnts":[],"value":"WHEEL","hidingOrgs":[]},{"display":"Cost","hidingEnts":[],"value":"COST","hidingOrgs":[]},{"display":"Link","hidingEnts":[],"value":"LINK","hidingOrgs":[]},{"display":"Box with inner container","hidingEnts":[],"value":"BOX_CONTAINER","hidingOrgs":[]},{"display":"Week","hidingEnts":[],"value":"WEEK","hidingOrgs":[]},{"display":"Grams/KG","hidingEnts":[],"value":"GRAMSPERKILOGRAM","hidingOrgs":[]},{"display":"Lug","hidingEnts":[],"value":"LUG","hidingOrgs":[]},{"display":"Half Hour","hidingEnts":[],"value":"HALFHOUR","hidingOrgs":[]},{"display":"Kilograms/Square Meter","hidingEnts":[],"value":"KILOGRAMSPERSQUAREMETER","hidingOrgs":[]},{"display":"Overtime Hours","hidingEnts":[],"value":"OVERTIMEHOURS","hidingOrgs":[]},{"display":"BAG","hidingEnts":[],"value":"BAG","hidingOrgs":[]},{"display":"Millions Of Dollars","hidingEnts":[],"value":"MILLIONSOFDOLLARS","hidingOrgs":[]},{"display":"Centi Poise (CPS)","hidingEnts":[],"value":"CENTIPOISE(CPS)","hidingOrgs":[]},{"display":"Meter/Second","hidingEnts":[],"value":"METERSPERSECOND","hidingOrgs":[]},{"display":"KG","hidingEnts":[],"value":"KILOGRAM","hidingOrgs":[]},{"display":"Bar","hidingEnts":[],"value":"BAR","hidingOrgs":[]},{"display":"Two Hundred Fifty","hidingEnts":[],"value":"TWOHUNDREDFIFTY","hidingOrgs":[]},{"display":"kHz","hidingEnts":[],"value":"KILOHERTZ","hidingOrgs":[]},{"display":"Barrel","hidingEnts":[],"value":"BARREL","hidingOrgs":[]}]},"OMS.SourcingOutputType":{"enumId":"OMS.SourcingOutputType","success":true,"items":[{"display":"Order Forecast","hidingEnts":[],"value":"OrderForecast","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"PurchaseOrder","hidingOrgs":[]},{"display":"Enhanced Order","hidingEnts":[],"value":"EnhancedOrder","hidingOrgs":[]},{"display":"Enhanced Order And Order Forecast","hidingEnts":[],"value":"EnhancedOrderAndOrderForecast","hidingOrgs":[]}]},"OMS.PartnerScorecardDateType":{"enumId":"OMS.PartnerScorecardDateType","success":true,"items":[{"display":"Gregorian","hidingEnts":[],"value":"Gregorian","hidingOrgs":[]},{"display":"Fiscal","hidingEnts":[],"value":"Fiscal","hidingOrgs":[]}]},"OMS.ContractLineType":{"enumId":"OMS.ContractLineType","success":true,"items":[{"display":"Category","hidingEnts":[],"value":"Category","hidingOrgs":[]},{"display":"Product","hidingEnts":[],"value":"Product","hidingOrgs":[]},{"display":"Service","hidingEnts":[],"value":"Service","hidingOrgs":[]}]},"TMS.BrokerServiceLevel":{"enumId":"TMS.BrokerServiceLevel","success":true,"rows":[{"display":"Default","value":"Default"}],"items":[{"display":"Default","value":"Default"}]},"SCC.ServiceClassCode":{"enumId":"SCC.ServiceClassCode","success":true,"items":[{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]}]},"VolumeUOM":{"enumId":"VolumeUOM","success":true,"items":[{"display":"Cord","globallyHidden":true,"hidingEnts":[],"value":"CORD","hidingOrgs":[]},{"display":"QUART","hidingEnts":[],"value":"QUART","hidingOrgs":[]},{"display":"Cucm","hidingEnts":[],"value":"CUCM","hidingOrgs":[]},{"display":"Load","globallyHidden":true,"hidingEnts":[],"value":"LOAD","hidingOrgs":[]},{"display":"Cumt","hidingEnts":[],"value":"CUMT","hidingOrgs":[]},{"display":"Cuin","hidingEnts":[],"value":"CUIN","hidingOrgs":[]},{"display":"CUMM","hidingEnts":[],"value":"CUMM","hidingOrgs":[]},{"display":"Board Feet","globallyHidden":true,"hidingEnts":[],"value":"BOARD_FT","hidingOrgs":[]},{"display":"Barge","globallyHidden":true,"hidingEnts":[],"value":"BARGE","hidingOrgs":[]},{"display":"HECTOLITER","hidingEnts":[],"value":"HECTOLITER","hidingOrgs":[]},{"display":"PINT","hidingEnts":[],"value":"PINT","hidingOrgs":[]},{"display":"Cuft","hidingEnts":[],"value":"CUFT","hidingOrgs":[]},{"display":"Measurement Ton","globallyHidden":true,"hidingEnts":[],"value":"MTON","hidingOrgs":[]},{"display":"Cubic Yard","hidingEnts":[],"value":"CUYD","hidingOrgs":[]},{"display":"Cubic Decimeters","globallyHidden":true,"hidingEnts":[],"value":"CUDM","hidingOrgs":[]},{"display":"Gallon","hidingEnts":[],"value":"GALLON","hidingOrgs":[]},{"display":"MILLILITER","hidingEnts":[],"value":"MILLILITER","hidingOrgs":[]},{"display":"Hundreds of Measurement Tons","globallyHidden":true,"hidingEnts":[],"value":"MTON_100","hidingOrgs":[]},{"display":"Car","globallyHidden":true,"hidingEnts":[],"value":"CAR","hidingOrgs":[]},{"display":"LITER","hidingEnts":[],"value":"LITER","hidingOrgs":[]},{"display":"100 Board Feet","globallyHidden":true,"hidingEnts":[],"value":"BOARD_FT_100","hidingOrgs":[]},{"display":"Container","globallyHidden":true,"hidingEnts":[],"value":"FEU","hidingOrgs":[]},{"display":"Volumetric Unit","globallyHidden":true,"hidingEnts":[],"value":"VUNIT","hidingOrgs":[]},{"display":"Fluid Ounce","hidingEnts":[],"value":"FL_OZ","hidingOrgs":[]},{"display":"BARREL","hidingEnts":[],"value":"BARREL","hidingOrgs":[]}]},"LinearUOM":{"enumId":"LinearUOM","success":true,"items":[{"display":"MM","hidingEnts":[],"value":"MM","hidingOrgs":[]},{"display":"Meter","hidingEnts":[],"value":"METER","hidingOrgs":[]},{"display":"Inch","hidingEnts":[],"value":"INCH","hidingOrgs":[]},{"display":"cm","hidingEnts":[],"value":"CM","hidingOrgs":[]},{"display":"Foot","hidingEnts":[],"value":"FOOT","hidingOrgs":[]}]},"TMS.RoutingGroup":{"enumId":"TMS.RoutingGroup","success":true,"items":[{"display":"CFS/CY","hidingEnts":[],"value":"CFS/CY","hidingOrgs":[]},{"display":"CFS","hidingEnts":[],"value":"CFS","hidingOrgs":[]},{"entName":"CustomerA","display":"Default","hidingEnts":[],"value":"CustomerA.Default","hidingOrgs":[]},{"display":"CY/CY","hidingEnts":[],"value":"CY/CY","hidingOrgs":[]},{"display":"CY/CFS","hidingEnts":[],"value":"CY/CFS","hidingOrgs":[]},{"display":"CY","hidingEnts":[],"value":"CY","hidingOrgs":[]},{"display":"Default","hidingEnts":[],"value":"Default","hidingOrgs":[]},{"display":"CFS/CFS","hidingEnts":[],"value":"CFS/CFS","hidingOrgs":[]}]},"OMS.ScorecardOrderType":{"enumId":"OMS.ScorecardOrderType","success":true,"items":[{"display":"Deployment Order","hidingEnts":[],"value":"Deployment Order","hidingOrgs":[]},{"display":"Purchase Order","hidingEnts":[],"value":"Purchase Order","hidingOrgs":[]}]},"SCH.ShipmentChangeReasonCodes":{"enumId":"SCH.ShipmentChangeReasonCodes","success":true,"items":[{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***plant","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***plant","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC |D","hidingEnts":[],"value":"SAFEWAY, INC..Delay at DC |D","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***corporate","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***corporate","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Carrier Request","hidingEnts":[],"value":"Dollar General.Carrier - Carrier Request","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Destination |D","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Destination |D","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available Origin |V","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available Origin |V","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant - Inventory Discrepancy","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant - Inventory Discrepancy","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron slow","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron slow","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor","hidingEnts":[],"value":"SAFEWAY, INC..Delay at Vendor","hidingOrgs":[]},{"entName":"Dollar General","display":"System Suggested","hidingEnts":[],"value":"Dollar General.System Suggested","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Others","hidingEnts":[],"value":"SAFEWAY, INC..Others","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Miscellaneous (Notes Required)","hidingEnts":[],"value":"Dollar General.Other - Miscellaneous (Notes Required)","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures","hidingEnts":[],"value":"SAFEWAY, INC..Road Closures","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Test","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Test","hidingOrgs":[]},{"entName":"Dollar General","display":"Other - Weather (Notes Required)","hidingEnts":[],"value":"Dollar General.Other - Weather (Notes Required)","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"LTL Loads","hidingEnts":[],"value":"KIK INTERNATIONAL.LTL Loads","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Appointment Not Available","hidingEnts":[],"value":"SAFEWAY, INC..Appointment Not Available","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable","hidingEnts":[],"value":"SAFEWAY, INC..Product Unavailable","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather","hidingEnts":[],"value":"SAFEWAY, INC..Weather","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure |C","hidingEnts":[],"value":"SAFEWAY, INC..Truck/Equipment Failure |C","hidingOrgs":[]},{"entName":"One Beliveau Enterprises","display":"Byron too fast","hidingEnts":["One Beliveau Enterprises"],"value":"One Beliveau Enterprises.Byron too fast","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Plant Exception for Shortage","hidingEnts":[],"value":"KIK INTERNATIONAL.Plant Exception for Shortage","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 90 WM window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 90 WM window","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Message\")","hidingEnts":[],"value":"SAFEWAY, INC..Other (please specify in \"Message\")","hidingOrgs":[]},{"entName":"CustomerA","display":"test","hidingEnts":[],"value":"CustomerA.test","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Early Delivery","hidingEnts":[],"value":"Dollar General.Carrier - Early Delivery","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Other (please specify in \"Comments\")","hidingEnts":[],"value":"SAFEWAY, INC..Other (please specify in \"Comments\")","hidingOrgs":[]},{"entName":"Dollar General","display":"DG - TransOps Request","hidingEnts":[],"value":"Dollar General.DG - TransOps Request","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Capacity/Planning","hidingEnts":[],"value":"Dollar General.Carrier - Capacity/Planning","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Breakdown/Accident","hidingEnts":[],"value":"Dollar General.Carrier - Breakdown/Accident","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* LTL for Delivered Loads Only","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* LTL for Delivered Loads Only","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Out of Hours","hidingEnts":[],"value":"Dollar General.Carrier - Out of Hours","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Traffic requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Traffic requested change","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Truck Missed","hidingEnts":[],"value":"KIK INTERNATIONAL.Truck Missed","hidingOrgs":[]},{"entName":"Dollar General","display":"Vendor - Not Ready","hidingEnts":[],"value":"Dollar General.Vendor - Not Ready","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***KIK use only* 94 customer window","hidingEnts":[],"value":"KIK INTERNATIONAL.***KIK use only* 94 customer window","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Rail Delay","hidingEnts":[],"value":"Dollar General.Carrier - Rail Delay","hidingOrgs":[]},{"display":"Appt Not Available","hidingEnts":[],"value":"Appt not available","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Weather |W","hidingEnts":[],"value":"SAFEWAY, INC..Weather |W","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Corporate requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Corporate requested change","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation |C","hidingEnts":[],"value":"SAFEWAY, INC..DOT Violation |C","hidingOrgs":[]},{"display":"Customer Requested Change","hidingEnts":[],"value":"Customer requested change","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Transit time allows early delivery","hidingEnts":[],"value":"KIK INTERNATIONAL.Transit time allows early delivery","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable |C","hidingEnts":[],"value":"SAFEWAY, INC..Equipment Unavailable |C","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"Corporate - Vendor Responsible (ie Caps, Labe)","hidingEnts":[],"value":"KIK INTERNATIONAL.Corporate - Vendor Responsible (ie Caps, Labe)","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Road Closures |W","hidingEnts":[],"value":"SAFEWAY, INC..Road Closures |W","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue","hidingEnts":[],"value":"SAFEWAY, INC..Border Issue","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at Vendor |V","hidingEnts":[],"value":"SAFEWAY, INC..Delay at Vendor |V","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Truck/Equipment Failure","hidingEnts":[],"value":"SAFEWAY, INC..Truck/Equipment Failure","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"***use only if instructed***traffic","hidingEnts":[],"value":"KIK INTERNATIONAL.***use only if instructed***traffic","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Equipment Unavailable","hidingEnts":[],"value":"SAFEWAY, INC..Equipment Unavailable","hidingOrgs":[]},{"entName":"Dollar General","display":"FOB ONLY - DC Capacity","hidingEnts":[],"value":"Dollar General.FOB ONLY - DC Capacity","hidingOrgs":[]},{"display":"AUTO CANDIDATE","hidingEnts":[],"value":"AUTO CANDIDATE","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Product Unavailable |V","hidingEnts":[],"value":"SAFEWAY, INC..Product Unavailable |V","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Border Issue |B","hidingEnts":[],"value":"SAFEWAY, INC..Border Issue |B","hidingOrgs":[]},{"entName":"KIK INTERNATIONAL","display":"KIK Plant requested change","hidingEnts":[],"value":"KIK INTERNATIONAL.KIK Plant requested change","hidingOrgs":[]},{"entName":"CustomerA","display":"Shipment Change","hidingEnts":[],"value":"CustomerA.Shipment Change","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"Delay at DC","hidingEnts":[],"value":"SAFEWAY, INC..Delay at DC","hidingOrgs":[]},{"entName":"SAFEWAY, INC.","display":"DOT Violation","hidingEnts":[],"value":"SAFEWAY, INC..DOT Violation","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Driver Issues","hidingEnts":[],"value":"Dollar General.Carrier - Driver Issues","hidingOrgs":[]},{"entName":"Dollar General","display":"Carrier - Missed Pickup/Delivery Appointment","hidingEnts":[],"value":"Dollar General.Carrier - Missed Pickup/Delivery Appointment","hidingOrgs":[]}]},"OMS.DeviationReasonCode":{"enumId":"OMS.DeviationReasonCode","success":true,"items":[{"display":"Default Reason Code","hidingEnts":[],"value":"DefaultReasonCode","hidingOrgs":[]}]},"TravelDirection":{"enumId":"TravelDirection","success":true,"items":[{"display":"Westbound","hidingEnts":[],"value":"West","hidingOrgs":[]},{"display":"Eastbound","hidingEnts":[],"value":"East","hidingOrgs":[]}]},"SCC.ShipmentType":{"enumId":"SCC.ShipmentType","success":true,"items":[{"entName":"PepsiCo LatAm","display":"CAM C 40M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 40M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOP","hidingEnts":[],"value":"PepsiCo LatAm.VTOP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAP","hidingEnts":[],"value":"PepsiCo LatAm.VCAP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTKG","hidingEnts":[],"value":"PepsiCo LatAm.VTKG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"UTILITARIO","hidingEnts":[],"value":"PepsiCo LatAm.UTILITARIO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 21M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 21M3","hidingOrgs":[]},{"entName":"Lactalis American Group","display":"Siggi's Dairy","hidingEnts":[],"value":"Lactalis American Group.Siggi's Dairy","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 42M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 42M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VBITP","hidingEnts":[],"value":"PepsiCo LatAm.VBITP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTKP","hidingEnts":[],"value":"PepsiCo LatAm.VTKP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 40","hidingEnts":[],"value":"PepsiCo LatAm.FULL 40","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM20FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM20FT2S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VVANG","hidingEnts":[],"value":"PepsiCo LatAm.VVANG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM20FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM20FT2S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VBITG","hidingEnts":[],"value":"PepsiCo LatAm.VBITG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTGS","hidingEnts":[],"value":"PepsiCo LatAm.VTGS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"NV","hidingEnts":[],"value":"PepsiCo LatAm.NV","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT3S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VVANP","hidingEnts":[],"value":"PepsiCo LatAm.VVANP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 48","hidingEnts":[],"value":"PepsiCo LatAm.FULL 48","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT3S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TKPO","hidingEnts":[],"value":"PepsiCo LatAm.TKPO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"TORTON","hidingEnts":[],"value":"PepsiCo LatAm.TORTON","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 45","hidingEnts":[],"value":"PepsiCo LatAm.FULL 45","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FULL 42","hidingEnts":[],"value":"PepsiCo LatAm.FULL 42","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 52M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 52M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABON1","hidingEnts":[],"value":"PepsiCo LatAm.RABON1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTPS","hidingEnts":[],"value":"PepsiCo LatAm.VTPS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C160M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C160M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"LIVIANO","hidingEnts":[],"value":"PepsiCo LatAm.LIVIANO","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABON2","hidingEnts":[],"value":"PepsiCo LatAm.RABON2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT3S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT3S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT3S3","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT3S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 58M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 58M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 20","hidingEnts":[],"value":"PepsiCo LatAm.REM 20","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"FRAC","hidingEnts":[],"value":"PepsiCo LatAm.FRAC","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCOI","hidingEnts":[],"value":"PepsiCo LatAm.VCOI","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 56M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 56M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 33M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 33M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCOG","hidingEnts":[],"value":"PepsiCo LatAm.VCOG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 12M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 12M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 54M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 54M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VUC","hidingEnts":[],"value":"PepsiCo LatAm.VUC","hidingOrgs":[]},{"entName":"Lactalis American Group","display":"Lactalis","hidingEnts":[],"value":"Lactalis American Group.Lactalis","hidingOrgs":[]},{"display":"3PL Managed","hidingEnts":[],"value":"3PL Managed","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCGS","hidingEnts":[],"value":"PepsiCo LatAm.VCGS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 5M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 5M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402B2","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402B2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT3S2","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT3S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402B1","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402B1","hidingOrgs":[]},{"display":"Type 2","hidingEnts":[],"value":"Type2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 4","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 4","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 3","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 2","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 2","hidingOrgs":[]},{"entName":"QARetailer1","display":"DeactivatedType","hidingEnts":["QARetailer1"],"value":"QARetailer1.DeactivatedType","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 1","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 6","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 6","hidingOrgs":[]},{"entName":"Lactalis American Group","display":"Stonyfield","hidingEnts":[],"value":"Lactalis American Group.Stonyfield","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VEH�CULO 5","hidingEnts":[],"value":"PepsiCo LatAm.VEH�CULO 5","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"BALANCIN","hidingEnts":[],"value":"PepsiCo LatAm.BALANCIN","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFULL48","hidingEnts":[],"value":"PepsiCo LatAm.REMFULL48","hidingOrgs":[]},{"display":"Brokered","hidingEnts":[],"value":"Brokered","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REMFL402R2","hidingEnts":[],"value":"PepsiCo LatAm.REMFL402R2","hidingOrgs":[]},{"entName":"CustomerA","display":"Type3","hidingEnts":[],"value":"CustomerA.Type3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 53","hidingEnts":[],"value":"PepsiCo LatAm.REM 53","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VANETTE","hidingEnts":[],"value":"PepsiCo LatAm.VANETTE","hidingOrgs":[]},{"display":"Type 1","hidingEnts":[],"value":"Type1","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"SEMI","hidingEnts":[],"value":"PepsiCo LatAm.SEMI","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCPS","hidingEnts":[],"value":"PepsiCo LatAm.VCPS","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT2S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CESP","hidingEnts":[],"value":"PepsiCo LatAm.CESP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CHASIS MED","hidingEnts":[],"value":"PepsiCo LatAm.CHASIS MED","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM40FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM40FT2S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT2S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAMIONETA","hidingEnts":[],"value":"PepsiCo LatAm.CAMIONETA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM53FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM53FT2S3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"MUDANZA","hidingEnts":[],"value":"PepsiCo LatAm.MUDANZA","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 40","hidingEnts":[],"value":"PepsiCo LatAm.REM 40","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 42","hidingEnts":[],"value":"PepsiCo LatAm.REM 42","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 45","hidingEnts":[],"value":"PepsiCo LatAm.REM 45","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"RABON","hidingEnts":[],"value":"PepsiCo LatAm.RABON","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM 48","hidingEnts":[],"value":"PepsiCo LatAm.REM 48","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VRODG","hidingEnts":[],"value":"PepsiCo LatAm.VRODG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"CAM C 8M3","hidingEnts":[],"value":"PepsiCo LatAm.CAM C 8M3","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT2S2","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT2S2","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VRODP","hidingEnts":[],"value":"PepsiCo LatAm.VRODP","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VTOG","hidingEnts":[],"value":"PepsiCo LatAm.VTOG","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"REM45FT2S3","hidingEnts":[],"value":"PepsiCo LatAm.REM45FT2S3","hidingOrgs":[]},{"entName":"Lactalis American Group","display":"Commonwealth Dairy","hidingEnts":[],"value":"Lactalis American Group.Commonwealth Dairy","hidingOrgs":[]},{"entName":"PepsiCo LatAm","display":"VCAG","hidingEnts":[],"value":"PepsiCo LatAm.VCAG","hidingOrgs":[]}]},"SCC.ItemCategory":{"enumId":"SCC.ItemCategory","success":true,"items":[{"entName":"DelMonte","display":"3A-DMB LBL FOOD SERVICE","hidingEnts":[],"value":"DelMonte.3A-DMB LBL FOOD SERVICE","hidingOrgs":[]},{"entName":"DelMonte","display":"2W-DPP MULTIWALL BAGS","hidingEnts":[],"value":"DelMonte.2W-DPP MULTIWALL BAGS","hidingOrgs":[]},{"entName":"DelMonte","display":"3F-DMB VEG LBL CUT/STK","hidingEnts":[],"value":"DelMonte.3F-DMB VEG LBL CUT/STK","hidingOrgs":[]},{"entName":"DelMonte","display":"M5-GUMS","hidingEnts":[],"value":"DelMonte.M5-GUMS","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"40","hidingEnts":[],"value":"The Kraft Heinz Co.40","hidingOrgs":[]},{"entName":"DelMonte","display":"7U-CLEAR SHRINK","hidingEnts":[],"value":"DelMonte.7U-CLEAR SHRINK","hidingOrgs":[]},{"entName":"DelMonte","display":"C9-POULTRY EDIBLE","hidingEnts":[],"value":"DelMonte.C9-POULTRY EDIBLE","hidingOrgs":[]},{"entName":"DelMonte","display":"C5-BEEF INEDIBLE","hidingEnts":[],"value":"DelMonte.C5-BEEF INEDIBLE","hidingOrgs":[]},{"entName":"DelMonte","display":"A3-FRESH VEGETABLES","hidingEnts":[],"value":"DelMonte.A3-FRESH VEGETABLES","hidingOrgs":[]},{"entName":"DelMonte","display":"**-Temporary Gemms","hidingEnts":[],"value":"DelMonte.**-Temporary Gemms","hidingOrgs":[]},{"entName":"DelMonte","display":"7B-DPP PREFORMED RESIN BAGS","hidingEnts":[],"value":"DelMonte.7B-DPP PREFORMED RESIN BAGS","hidingOrgs":[]},{"entName":"DelMonte","display":"7A-DPP ROLLSTOCK FOIL/NON FOIL","hidingEnts":[],"value":"DelMonte.7A-DPP ROLLSTOCK FOIL/NON FOIL","hidingOrgs":[]},{"entName":"DelMonte","display":"A0-GARLIC & ONION","hidingEnts":[],"value":"DelMonte.A0-GARLIC & ONION","hidingOrgs":[]},{"entName":"DelMonte","display":"5A-DPP LBL BRAND RESIN","hidingEnts":[],"value":"DelMonte.5A-DPP LBL BRAND RESIN","hidingOrgs":[]},{"entName":"DelMonte","display":"L5-SAUCES","hidingEnts":[],"value":"DelMonte.L5-SAUCES","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"50","hidingEnts":[],"value":"The Kraft Heinz Co.50","hidingOrgs":[]},{"entName":"DelMonte","display":"L3-SPICES","hidingEnts":[],"value":"DelMonte.L3-SPICES","hidingOrgs":[]},{"entName":"DelMonte","display":"J0-SWEETENERS - OTHER","hidingEnts":[],"value":"DelMonte.J0-SWEETENERS - OTHER","hidingOrgs":[]},{"entName":"DelMonte","display":"20","hidingEnts":[],"value":"DelMonte.20","hidingOrgs":[]},{"entName":"DelMonte","display":"6D-DMB VEG CANS","hidingEnts":[],"value":"DelMonte.6D-DMB VEG CANS","hidingOrgs":[]},{"entName":"DelMonte","display":"6E-DMB TOM CANS","hidingEnts":[],"value":"DelMonte.6E-DMB TOM CANS","hidingOrgs":[]},{"entName":"DelMonte","display":"K2-FLAVORS & BASES","hidingEnts":[],"value":"DelMonte.K2-FLAVORS & BASES","hidingOrgs":[]},{"entName":"DelMonte","display":"G2-GRAINS - WHOLE","hidingEnts":[],"value":"DelMonte.G2-GRAINS - WHOLE","hidingOrgs":[]},{"entName":"DelMonte","display":"6B-DMB METAL CLOSURES","hidingEnts":[],"value":"DelMonte.6B-DMB METAL CLOSURES","hidingOrgs":[]},{"entName":"DelMonte","display":"3C-DMB LBL DM 2ND BRAND","hidingEnts":[],"value":"DelMonte.3C-DMB LBL DM 2ND BRAND","hidingOrgs":[]},{"entName":"DelMonte","display":"7H-DPP ZIPPER","hidingEnts":[],"value":"DelMonte.7H-DPP ZIPPER","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"60","hidingEnts":[],"value":"The Kraft Heinz Co.60","hidingOrgs":[]},{"entName":"DelMonte","display":"DM-FATS & OILS SEMI SOLID","hidingEnts":[],"value":"DelMonte.DM-FATS & OILS SEMI SOLID","hidingOrgs":[]},{"entName":"DelMonte","display":"A8-FRUITS","hidingEnts":[],"value":"DelMonte.A8-FRUITS","hidingOrgs":[]},{"entName":"AutoCPG","display":"BRAND","hidingEnts":[],"value":"AutoCPG.BRAND","hidingOrgs":[]},{"entName":"DelMonte","display":"0I-VEG CRTN/SLV","hidingEnts":[],"value":"DelMonte.0I-VEG CRTN/SLV","hidingOrgs":[]},{"entName":"Walmart","display":"1231","hidingEnts":[],"value":"Walmart.1231","hidingOrgs":[]},{"entName":"VendorA","display":"isVenAFGCat1","hidingEnts":[],"value":"VendorA.isVenAFGCat1","hidingOrgs":[]},{"entName":"DelMonte","display":"3D-SSF SUP LBL PRIVATE LABEL","hidingEnts":[],"value":"DelMonte.3D-SSF SUP LBL PRIVATE LABEL","hidingOrgs":[]},{"entName":"DelMonte","display":"CG-PROTEIN MEALS","hidingEnts":[],"value":"DelMonte.CG-PROTEIN MEALS","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"70","hidingEnts":[],"value":"The Kraft Heinz Co.70","hidingOrgs":[]},{"entName":"DelMonte","display":"0E-SSF SUP CRTNS COLLEGE INN","hidingEnts":[],"value":"DelMonte.0E-SSF SUP CRTNS COLLEGE INN","hidingOrgs":[]},{"entName":"DelMonte","display":"1A-DPP CORRUGATE SNACKS","hidingEnts":[],"value":"DelMonte.1A-DPP CORRUGATE SNACKS","hidingOrgs":[]},{"entName":"DelMonte","display":"H1-SOYBEAN FLOUR","hidingEnts":[],"value":"DelMonte.H1-SOYBEAN FLOUR","hidingOrgs":[]},{"entName":"DelMonte","display":"6H-DMB TOM ENDS","hidingEnts":[],"value":"DelMonte.6H-DMB TOM ENDS","hidingOrgs":[]},{"entName":"DelMonte","display":"3E-DMB FRT LBL CUT/STK","hidingEnts":[],"value":"DelMonte.3E-DMB FRT LBL CUT/STK","hidingOrgs":[]},{"entName":"DelMonte","display":"G0-FLOURS","hidingEnts":[],"value":"DelMonte.G0-FLOURS","hidingOrgs":[]},{"entName":"DelMonte","display":"86","hidingEnts":[],"value":"DelMonte.86","hidingOrgs":[]},{"entName":"DelMonte","display":"A7-FRUIT & VEGETABLE PUREE","hidingEnts":[],"value":"DelMonte.A7-FRUIT & VEGETABLE PUREE","hidingOrgs":[]},{"entName":"DelMonte","display":"1B-DPP CORRUGATE DRY/WET","hidingEnts":[],"value":"DelMonte.1B-DPP CORRUGATE DRY/WET","hidingOrgs":[]},{"entName":"DelMonte","display":"81","hidingEnts":[],"value":"DelMonte.81","hidingOrgs":[]},{"entName":"DelMonte","display":"82","hidingEnts":[],"value":"DelMonte.82","hidingOrgs":[]},{"entName":"DelMonte","display":"83","hidingEnts":[],"value":"DelMonte.83","hidingOrgs":[]},{"entName":"DelMonte","display":"K1-YEAST","hidingEnts":[],"value":"DelMonte.K1-YEAST","hidingOrgs":[]},{"entName":"DelMonte","display":"2Y-ALL MISC PACKAGING","hidingEnts":[],"value":"DelMonte.2Y-ALL MISC PACKAGING","hidingOrgs":[]},{"entName":"DelMonte","display":"4D-DPP LBL BRAND PRES/SEN","hidingEnts":[],"value":"DelMonte.4D-DPP LBL BRAND PRES/SEN","hidingOrgs":[]},{"entName":"DelMonte","display":"A1-DEHYDRATED VEGETABLES","hidingEnts":[],"value":"DelMonte.A1-DEHYDRATED VEGETABLES","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"80","hidingEnts":[],"value":"The Kraft Heinz Co.80","hidingOrgs":[]},{"entName":"DelMonte","display":"7S-DMB Displays","hidingEnts":[],"value":"DelMonte.7S-DMB Displays","hidingOrgs":[]},{"entName":"DelMonte","display":"1D-DPP BULK BINS CORRUGATE","hidingEnts":[],"value":"DelMonte.1D-DPP BULK BINS CORRUGATE","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"01","hidingEnts":[],"value":"The Kraft Heinz Co.01","hidingOrgs":[]},{"entName":"DelMonte","display":"1C-DPP DISPLAYS","hidingEnts":[],"value":"DelMonte.1C-DPP DISPLAYS","hidingOrgs":[]},{"entName":"DelMonte","display":"C3-ANIMAL BI-PRODUCT","hidingEnts":[],"value":"DelMonte.C3-ANIMAL BI-PRODUCT","hidingOrgs":[]},{"entName":"DelMonte","display":"CM-RIGID RESIN - PP","hidingEnts":[],"value":"DelMonte.CM-RIGID RESIN - PP","hidingOrgs":[]},{"entName":"DelMonte","display":"70","hidingEnts":[],"value":"DelMonte.70","hidingOrgs":[]},{"entName":"DelMonte","display":"1N-DMB TOM CORRUGATE","hidingEnts":[],"value":"DelMonte.1N-DMB TOM CORRUGATE","hidingOrgs":[]},{"entName":"DelMonte","display":"CO-ANIMAL DIGEST","hidingEnts":[],"value":"DelMonte.CO-ANIMAL DIGEST","hidingOrgs":[]},{"entName":"DelMonte","display":"CF-Co-Packer C-FEE","hidingEnts":[],"value":"DelMonte.CF-Co-Packer C-FEE","hidingOrgs":[]},{"entName":"DelMonte","display":"7W-STRETCH","hidingEnts":[],"value":"DelMonte.7W-STRETCH","hidingOrgs":[]},{"entName":"DelMonte","display":"3G-DMB TOM LBL CUT/STK","hidingEnts":[],"value":"DelMonte.3G-DMB TOM LBL CUT/STK","hidingOrgs":[]},{"entName":"DelMonte","display":"9B-DPP RIGID RESIN","hidingEnts":[],"value":"DelMonte.9B-DPP RIGID RESIN","hidingOrgs":[]},{"entName":"DelMonte","display":"0A-DPP CRTN BRAND SNACKS","hidingEnts":[],"value":"DelMonte.0A-DPP CRTN BRAND SNACKS","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"90","hidingEnts":[],"value":"The Kraft Heinz Co.90","hidingOrgs":[]},{"entName":"DelMonte","display":"A4-FROZEN VEGETABLES","hidingEnts":[],"value":"DelMonte.A4-FROZEN VEGETABLES","hidingOrgs":[]},{"entName":"DelMonte","display":"6F-DMB FRT ENDS","hidingEnts":[],"value":"DelMonte.6F-DMB FRT ENDS","hidingOrgs":[]},{"entName":"DelMonte","display":"1M-DMB VEG CORRUGATE","hidingEnts":[],"value":"DelMonte.1M-DMB VEG CORRUGATE","hidingOrgs":[]},{"entName":"The Kroger Co.","display":"PACKAGE","hidingEnts":[],"value":"The Kroger Co..PACKAGE","hidingOrgs":[]},{"entName":"DelMonte","display":"6J-DPP ENDS","hidingEnts":[],"value":"DelMonte.6J-DPP ENDS","hidingOrgs":[]},{"entName":"DelMonte","display":"H0-SOYBEAN BI-PRODUCT","hidingEnts":[],"value":"DelMonte.H0-SOYBEAN BI-PRODUCT","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"10","hidingEnts":[],"value":"The Kraft Heinz Co.10","hidingOrgs":[]},{"entName":"DelMonte","display":"0M-DPP CRTN WET","hidingEnts":[],"value":"DelMonte.0M-DPP CRTN WET","hidingOrgs":[]},{"entName":"DelMonte","display":"1L-DMB FRT CORRUGATE","hidingEnts":[],"value":"DelMonte.1L-DMB FRT CORRUGATE","hidingOrgs":[]},{"entName":"DelMonte","display":"6G-DMB VEG ENDS","hidingEnts":[],"value":"DelMonte.6G-DMB VEG ENDS","hidingOrgs":[]},{"entName":"DelMonte","display":"7G-DPP BULK BAGS RESIN","hidingEnts":[],"value":"DelMonte.7G-DPP BULK BAGS RESIN","hidingOrgs":[]},{"entName":"DelMonte","display":"0H-FRT CRTN/SLV","hidingEnts":[],"value":"DelMonte.0H-FRT CRTN/SLV","hidingOrgs":[]},{"entName":"DelMonte","display":"D0-FATS - EDIBLE","hidingEnts":[],"value":"DelMonte.D0-FATS - EDIBLE","hidingOrgs":[]},{"entName":"DelMonte","display":"0J-TOM CRTN/SLV","hidingEnts":[],"value":"DelMonte.0J-TOM CRTN/SLV","hidingOrgs":[]},{"entName":"DelMonte","display":"M3-CHEMICALS","hidingEnts":[],"value":"DelMonte.M3-CHEMICALS","hidingOrgs":[]},{"entName":"AutoCPG","display":"PET","hidingEnts":[],"value":"AutoCPG.PET","hidingOrgs":[]},{"entName":"The Kraft Heinz Co","display":"20","hidingEnts":[],"value":"The Kraft Heinz Co.20","hidingOrgs":[]},{"entName":"Walmart","display":"PACKAGE","hidingEnts":[],"value":"Walmart.PACKAGE","hidingOrgs":[]},{"entName":"DelMonte","display":"7C-DPP SHRINKFILM PRINTED","hidingEnts":[],"value":"DelMonte.7C-DPP SHRINKFILM PRINTED","hidingOrgs":[]},{"entName":"DelMonte","display":"G4-STARCH","hidingEnts":[],"value":"DelMonte.G4-STARCH","hidingOrgs":[]},{"entName":"DelMonte","display":"B0-FRUIT JUICE CONCENTRATE","hidingEnts":[],"value":"DelMonte.B0-FRUIT JUICE CONCENTRATE","hidingOrgs":[]},{"entName":"DelMonte","display":"3I-DPP LBL PL PAPER CUT/STK","hidingEnts":[],"value":"DelMonte.3I-DPP LBL PL PAPER CUT/STK","hidingOrgs":[]},{"entName":"DelMonte","display":"G6-VINEGAR","hidingEnts":[],"value":"DelMonte.G6-VINEGAR","hidingOrgs":[]},{"entName":"DelMonte","display":"M4-COLORINGS","hidingEnts":[],"value":"DelMonte.M4-COLORINGS","hidingOrgs":[]},{"entName":"DelMonte","display":"0C-DPP CRTN PRIVATE LABEL","hidingEnts":[],"value":"DelMonte.0C-DPP CRTN PRIVATE LABEL","hidingOrgs":[]},{"entName":"DelMonte","display":"3H-DPP LBL BRAND PAPER CUT/STK","hidingEnts":[],"value":"DelMonte.3H-DPP LBL BRAND PAPER CUT/STK","hidingOrgs":[]},{"entName":"DelMonte","display":"40","hidingEnts":[],"value":"DelMonte.40","hidingOrgs":[]},{"entName":"Walmart","display":"212","hidingEnts":[],"value":"Walmart.212","hidingOrgs":[]},{"entName":"RepublicWireless","display":"Kitted phone","hidingEnts":[],"value":"RepublicWireless.Kitted phone","hidingOrgs":[]},{"entName":"DelMonte","display":"G1-GRAINS - PROCESSED","hidingEnts":[],"value":"DelMonte.G1-GRAINS - PROCESSED","hidingOrgs":[]},{"entName":"DelMonte","display":"3B-DMB LBL EXPORT","hidingEnts":[],"value":"DelMonte.3B-DMB LBL EXPORT","hidingOrgs":[]},{"entName":"DelMonte","display":"6C-DMB FRT CANS","hidingEnts":[],"value":"DelMonte.6C-DMB FRT CANS","hidingOrgs":[]}]},"OMS.SurveyComponentType":{"enumId":"OMS.SurveyComponentType","success":true,"items":[{"display":"Email","hidingEnts":[],"value":"Email","hidingOrgs":[]},{"display":"Matrix-Col","hidingEnts":[],"value":"Matrix-Col","hidingOrgs":[]},{"display":"Rating","hidingEnts":[],"value":"Rating","hidingOrgs":[]},{"display":"Multi-text","hidingEnts":[],"value":"Multitext","hidingOrgs":[]},{"display":"SCAC Code","hidingEnts":[],"value":"SCAC-Code","hidingOrgs":[]},{"display":"e-Signature","hidingEnts":[],"value":"signaturepad","hidingOrgs":[]},{"display":"Date Time","hidingEnts":[],"value":"DateTime","hidingOrgs":[]},{"display":"Matrix","hidingEnts":[],"value":"Matrix","hidingOrgs":[]},{"display":"Number","hidingEnts":[],"value":"Number","hidingOrgs":[]},{"display":"Matrix Dynamic","hidingEnts":[],"value":"MatrixDynamic","hidingOrgs":[]},{"display":"Checkbox","hidingEnts":[],"value":"Checkbox","hidingOrgs":[]},{"display":"Boolean","hidingEnts":[],"value":"Boolean","hidingOrgs":[]},{"display":"Quantity UOM","hidingEnts":[],"value":"QuantityUOM","hidingOrgs":[]},{"display":"Comment","hidingEnts":[],"value":"Comment","hidingOrgs":[]},{"display":"Weight UOM","hidingEnts":[],"value":"WeightUOM","hidingOrgs":[]},{"display":"Dropdown","hidingEnts":[],"value":"Dropdown","hidingOrgs":[]},{"display":"Radio","hidingEnts":[],"value":"Radio","hidingOrgs":[]},{"display":"Text","hidingEnts":[],"value":"Text","hidingOrgs":[]},{"display":"EIN","hidingEnts":[],"value":"EIN","hidingOrgs":[]},{"display":"Info","hidingEnts":[],"value":"Info","hidingOrgs":[]},{"display":"Matrix Dropdown","hidingEnts":[],"value":"MatrixDropdown","hidingOrgs":[]},{"display":"Date","hidingEnts":[],"value":"Date","hidingOrgs":[]},{"display":"Volume UOM","hidingEnts":[],"value":"VolumeUOM","hidingOrgs":[]},{"display":"Linear UOM","hidingEnts":[],"value":"LinearUOM","hidingOrgs":[]},{"display":"Time Zone","hidingEnts":[],"value":"TimeZone","hidingOrgs":[]},{"display":"DUNS","hidingEnts":[],"value":"DUNS","hidingOrgs":[]},{"display":"Country","hidingEnts":[],"value":"Country","hidingOrgs":[]},{"display":"File","hidingEnts":[],"value":"File","hidingOrgs":[]},{"display":"Matrix-Row","hidingEnts":[],"value":"Matrix-Row","hidingOrgs":[]}]}};

         // ================== DONE LOADING GlobalEnumCache ======================

         // ================== LOADING GlobalModules =============================

         GlobalModules = {"IVP":"InventoryPlanning","TOSX":"TransportationPlanning","CHCS":"ChainOfCustody","RTL":"Retail","DPX":"DemandPlanner","RPL":"Replenishment","MFG":"Manufacturing","QCAD":"QualityComplianceAndDisposition","DAWH":"DataWarehouse","GSDM":"GlobalSDM","RPTI":"RetailProcessTemplate","MDM":"MasterDataManagement","RDTS":"RDTSequencing","LNTL":"LogisticsTelemetry","PLT":"PlatformCoreModels","SNOP":"SNOP","ENSG":"EnterpriseSocialGraph","LCNS":"Licensing","GLGC":"GLGClient","AMS":"AssetManagement","RISK":"RiskResilience","FIN":"Financials","BLCL":"BillingClient","SMRO":"MROManagement","SCC":"SupplyChainCore","SCPT":"SCProcessTemplate","OMS":"OMS","FISE":"FieldService","CMDC":"CMDClient","ATPC":"ATPCore","SCH":"Scheduling","TMS":"Transportation","WMS":"WarehouseManagement","YMS":"YardManagement"};
         // ================== DONE LOADING GlobalModules ========================

         // ================== LOADING FeatureList =============================

         FeatureList = [{"name":"TMS.GlobalTrade","description":"Global Trade related features. Container moves, customs and other."},{"name":"TMS.Visibility","description":""},{"name":"TMS.Execution","description":""},{"name":"TMS.Scheduling","description":""},{"name":"TMS.Financials","description":""}];
         // ================== DONE LOADING FeatureList ========================
      </script>
      <script type="text/javascript">
         // ================== LOADING SimulatedToday ============================

            top['isSimulatedTodayEnabled'] = false;
         // ================== Done LOADING SimulatedToday =======================
      </script>
      <style type="text/css">
         html, body, .neoDesktop > div#main {
         margin: 0;
         height: 100%;
         width: 100%;
         overflow: hidden;
         font-family: "Open Sans", Arial, Helvetica, sans-serif;
         }
         input[type=text]::-ms-clear { /* Workaround for: https://github.com/facebook/react/issues/6822 */
         display: none;
         }
         #neo-load {
         background: url("/oms/img/neo/circular-progress-light.gif") no-repeat;
         background-size: 30px 30px;
         position: absolute;
         top: 50%;
         left: 45%;
         height: 30px;
         padding-top: 5px;
         padding-left: 40px;
         color: #333;
         font-size: 16px;
         font-family: 'Open Sans', sans-serif;
         }
      </style>
      <script>
         // JS injections from One Modules

               try {
                 window.Desktop.isDTCI = false;
               } catch(e) {
                 console.error(e);
               }

         // End JS injections from One Modules
      </script>
      <script>
         function initUimm(uimm) {
           window.desktop.uimm = uimm;
           if (window.Desktop && window.Desktop.reloadMenuItems) {
             window.Desktop.reloadMenuItems();
           }
         }

         function loadUIMM(instanceMenuURL) {
           // loading UIMM
           fetch(instanceMenuURL)
             .then(r => r.json())
             .then(initUimm)
             .then(() => GlobalInitPromise)
             .then(() => {

               let renderDesktopScript = document.createElement('script');
               renderDesktopScript.src = '/oms/public/jsmodules/One/desktop/RenderNeoDesktop.js?cachekey=?cachekey=6beffd669779ee639b9bc6ad8ec837b0';
               document.body.appendChild(renderDesktopScript);
             });
         }

         function loadDesktopContext(desktopContext) {
           var legacyBaseURL = desktopContext.RequireJS.legacyBaseURL;

           if (typeof require == 'object') {
             require.baseUrl = desktopContext.RequireJS.baseURL;
             require.paths = {
               CC3: legacyBaseURL + '/CC3',
               One: legacyBaseURL + '/One',
               jquery: legacyBaseURL + '/jquery',
               underscore: legacyBaseURL + '/underscore',
               modulethemes: legacyBaseURL + '/modulethemes',
               neothemes: legacyBaseURL + '/neothemes',
               text: '/oms/public/require-text'
             }
           }

           UserProfile = GlobalUserProfile = desktopContext.userProfile;
           GlobalScreens = desktopContext.globalScreens;
           GlobalThemes = desktopContext.globalThemes;
           GlobalState = desktopContext.globalState;
           GlobalUserDefinedPagesCache = desktopContext.pagesCache;
           OnePushServer.enabled = isPushServerEnabledGlobally && GlobalState.PushDisabledByUser !== true;

           desktop = window.desktop || {};
           desktop.jsmodules = desktopContext.jsmodules;
           desktop.serverTZ = 'America/New_York';

           loadUIMM(desktopContext.instanceMenuURL);


         }

         docReady(function() {
           loadDesktopContext({"globalState":{"QuickSearchShipmentsForCarrier":{"addedFields":[]},"TMS.AllAppointmentRequestsForCarrier":{"addedFields":[]}},"neoAssistantLlmType":"None","factsAvailableAgainstSubscribedFeatures":false,"labelsURL":"/oms/rest/i18n/labels/163438/en?md5=f7110189031979ed94fefc13e49b581f","pagesCache":{"modelLevels":{"GridTuningProfile":true,"EnumConversionDef":true,"DocumentType":true,"EnumDependency":true,"CrossReferenceValue":true,"EnumFieldDependency":true,"UserGroup":true,"OrgListPerm":true,"NeoNotification":true,"DocTypePerm":true,"TMS.CustomsTransaction":true,"OAuthOBClientApp":true,"ValueChain":true}},"RequireJS":{"baseURL":"/oms/rest/jsmodule/163438/en","legacyBaseURL":"/oms/rest/js/163438/en"},"hasNeoUserBotPerm":false,"contextAutoReloadInterval":60,"globalScreens":{"PLT.TLVCustomizationByEnterprise":"PLT.TLVCustomizationByEnterprise","TMS.TrackingEvent-Create":"TMS.TrackingEvent-Create","PLT.DisplayGlobalSearch":"PLT.DisplayGlobalSearch","PLT.NeoAlertsSlideout":"PLT.NeoAlertsSlideout","PLT.TLVCustomizationByOrganization":"PLT.TLVCustomizationByOrganization","PLT.NeoProblemsSlideout":"PLT.NeoProblemsSlideout","PLT.TLVCustomizationByUser":"PLT.TLVCustomizationByUser","TMS.Cost-Details":"TMS.Cost-Details","PLT.AskNeoSlideout":"PLT.AskNeoSlideout","PLT.NeoChatSlideout":"PLT.NeoChatSlideout"},"userProfile":{"preferences":{"PreferredLocaleLanguage":"en","PreferredCountry":"US","AdditionalProperties":"{\"userCacheKey\":1745917688311,\"serverCacheKey\":\"6beffd669779ee639b9bc6ad8ec837b0\"}"},"formats":{"date":{"all":{"TimeToTheMinute":"g:i a","DateToTheDay":"M j, Y","DateWithTimeZone":"M j, Y g:i a","DateToTheMinute":"M j, Y g:i a","LocalDateToTheDay":"M j, Y","TimeWithoutZone":"g:i a","DateToTheHour":"M j, Y g a","DateToTheMonth":"M, Y"},"jsCalendar":{"TimeToTheMinute":"%l:%M %p","DateToTheDay":"%b %e, %Y","DateWithTimeZone":"%b %e, %Y %l:%M %p","DateToTheMinute":"%b %e, %Y %l:%M %p","LocalDateToTheDay":"%b %e, %Y","TimeWithoutZone":"%l:%M %p","DateToTheHour":"%b %e, %Y %l %p","DateToTheMonth":"%b, %Y"},"format":"M j, Y"},"number":{"maxDecimalDigits":5,"decimalSeparator":".","minDecimalDigits":0,"format":"#,##0.#####","groupingSeparator":",","groupingSize":3,"leadingZero":true},"currency":{"maxDecimalDigits":2,"decimalSeparator":".","minDecimalDigits":2,"format":"#,##0.00","groupingSeparator":",","groupingSize":3,"leadingZero":true},"time":{"amSymbol":"AM","showAmPm":true,"timeZoneId":"America/New_York","stdTimeZone":"EST","format":"g:i a","timeZone":"EDT","pmSymbol":"PM","showTimeZone":false,"dstTimeZone":"EDT"}},"hasLegacyDetailActions":true,"maxTabExceededBehavior":"warn","display":{"perspective_animated_transition":false},"context":{"IsUsingAlias":false,"UserName":"NFI FREIGHT","UserEntId":81877,"EntId":81877,"FirstName":"Albert","RoleTypeName":"TMS.CARRIER_MANAGER","OrgName":"National Freight","ValueChainName":"TransportationValueChain","RoleId":163438,"IsInternal":false,"EmailAddress":"<EMAIL>","OrgId":81877,"RoleName":"NFI FREIGHT","UserEntName":"National Freight","UserId":224025,"SecondaryEmail":"","ValueChainId":9495,"LastName":"Yerkes","inheritedRoleTypes":["FIN.FINANCIAL_MANAGER","PARTNER_RELATIONSHIP_MANAGER","Default For Organization Role Types"],"EntName":"National Freight","AuditRoleTypeName":"TMS.CARRIER_MANAGER"},"themeId":"themefile","theme":"modulethemes/DefaultTheme","maxTabs":8},"success":true,"plasmaEnabled":true,"jsmodules":{},"transitionInfoURL":"/oms/rest/date/transition-info/en?md5=8b75314e94f27bef4b138b515f5c8141","instanceMenuURL":"/oms/rest/menu?md5=a7b7eb37450d1f8948a9de7514106e1e","items":[],"globalThemes":[{"displayValue":"Accessibility Theme","value":"modulethemes/AccessibilityTheme"},{"displayValue":"Blue Theme","value":"modulethemes/BlueTheme"},{"displayValue":"Default Theme","value":"modulethemes/DefaultTheme"},{"displayValue":"Gold Theme","value":"modulethemes/GoldTheme"},{"displayValue":"Green Theme","value":"modulethemes/GreenTheme"},{"displayValue":"Red Theme","value":"modulethemes/RedTheme"},{"displayValue":"Silver Theme","value":"modulethemes/SilverTheme"}]});


             //URLUtil is a singleton and it registers itself to window.top. That causes issues in Neo because of how iframes are handled.
             //Until ProblemDetailPresenter is rewritten in react with new Chat component, we need to keep this fix.
             require(['One/desktop/URLUtil']);




           <!-- ================== Setting Keep Alive Intervals ================================= -->
           window.top.userInteracting = false; // flag to track user interaction through keyboard or mouse
           setInterval(() => {
             // Keep session active if user interacting
             if (window.top.userInteracting) {
               window.top.userInteracting = false;

               var xhr = new XMLHttpRequest();
               xhr.open('GET', '/oms/rest/mysession/keep-alive');
               xhr.send();

               // Keep session active in Standalone ESG instance
               if (window.top.Desktop.ESG.enabled && !window.top.Desktop.ESG.embedded) {
                 xhr = new XMLHttpRequest();
                 xhr.open('GET', window.top.Desktop.ESG.url + '/oms/rest/mysession/keep-alive');
                 xhr.withCredentials = true;
                 xhr.send();
               }
             }
           }, window.top.Desktop.keepAlivePollInterval * 1000);
         });
      </script>
   </head>
   <body class="neoDesktop">
      <script type="text/javascript">
         // Display a loading img and text before loading the external JS/CSS.
         var loadDiv = document.createElement('div');
         loadDiv.id = 'neo-load';
         loadDiv.innerHTML = 'Logging in...';
         document.body.appendChild(loadDiv);
      </script>
      <script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"93855c18ae1059e1","serverTiming":{"name":{"cfExtPri":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.4.0-1-g37f21b1","token":"51081231df53418795f5eaecd4ddd4f3"}' crossorigin="anonymous"></script>
   </body>
</html>