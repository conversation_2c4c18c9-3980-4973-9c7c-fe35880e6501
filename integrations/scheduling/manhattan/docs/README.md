#### API Examples

All requests are sent as JSON to the endpoint http://localhost:8000/ using POST method. Here are examples for each supported operation:

[Architecture Diagram](./architecture.png)
![architecture.png](./architecture.png)

##### Data Retrieval

###### Validate Appointments

Sends available facilities related to the provided city and state.

Request Payload:

```json
{
  "integration": "scheduling",
  "platform": "Manhattan",
  "action": "ValidateAppointment",
  "userId": "789",
  "serviceId": "910",
  "integrationId": "456",
  "warehouse": {
    "name": "",
    "city": "Butner",
    "state": "NC",
    "zipCode": "",
    "country": "",
    "stopType": ""
  },
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Success Response:

```json
{
  "success": true,
  "message": "Appointment validated successfully",
  "errors": null,
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "-",
      "duration": 60,
      "location": "",
      "notes": "",
      "reference": "",
      "scheduledTime": "-",
      "status": "UNSCHEDULED",
      "warehouse": {
        "city": "Butner",
        "country": "",
        "name": "",
        "addressLine1": "",
        "id": "",
        "openSlots": null,
        "state": "NC",
        "stopType": "",
        "website": "",
        "zipCode": ""
      },
      "extended": {
        "facilities": {
          "DCDA09": "DCDA09 (1703 East D Street, Butner, NC, 27509, United States)",
          "DCDA09-03": "DCDA09-03 (1741 East C Street, Butner, NC, 27509, United States)",
          "DCDA09-31": "DCDA09-31 (1741 East C Street, Butner, NC, 27509, United States)",
          "DCDA09-61": "DCDA09-61 (900 East D Street, Butner, NC, 27509, United States)",
          "DCDA09G": "DCDA09G (1703 East D Street, Butner, NC, 27509, United States)",
          "DCDA09P": "DCDA09P (1703 East D Street, Butner, NC, 27509, United States)"
        }
      }
    }
  ],
  "pagination": null
}
```

Failure Response:

```json
{
  "success": false,
  "message": "No facilities found",
  "errors": ["Failed to fetch or select facilities"],
  "platformData": null,
  "appointments": null,
  "pagination": null
}
```

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

Request Payload:

```json
{
  "integration": "scheduling",
  "platform": "Manhattan",
  "action": "GetOpenSlots",
  "userId": "789",
  "serviceId": "910",
  "integrationId": "456",
  "poId": "12926701",
  "appointmentId": "015265372",
  "startDate": "2025-07-20",
  "endDate": "2025-07-22",
  "facilityId": "DCDA09P",
  "facilityText": "DCDA09P (1703 East D Street, Butner, NC, 27509, United States)",
  "appointmentType": "Live Unload",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Success Response:

```json
{
  "success": true,
  "message": "Recommended slots fetched successfully",
  "errors": null,
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "-",
      "duration": 60,
      "notes": "",
      "poNums": "12926701",
      "scheduledTime": "-",
      "status": "UNSCHEDULED",
      "warehouse": {
        "city": "Butner",
        "country": "United States",
        "name": "",
        "addressLine1": "",
        "id": "",
        "openSlots": [
          {
            "duration": 60,
            "scheduledTime": "2025-07-18T21:00:00.000Z"
          },
          {
            "duration": 60,
            "scheduledTime": "2025-07-19T21:00:00.000Z"
          },
          {
            "duration": 60,
            "scheduledTime": "2025-07-19T21:30:00.000Z"
          }
        ],
        "state": "NC",
        "stopType": "",
        "website": "",
        "zipCode": "27509"
      },
      "extended": null
    }
  ]
}
```

##### Appointment Management

###### Get Appointment

Not implemented yet.

###### Create Appointment / Re-Schedule Appointment

Creates a new appointment.

We already have shipment details on this platform, we fetch those using PO ID, and then make an appointment.

Request Payload:

```json
{
  "integration": "scheduling",
  "platform": "Manhattan",
  "action": "MakeAppointment",
  "userId": "789",
  "serviceId": "910",
  "integrationId": "456",
  "appointments": [
    {
      "appointmentId": "",
      "proId": "",
      "appointmentTime": "2025-07-18T21:00:00.000Z",
      "facilityId": "DCDA09P",
      "appointmentType": "Live Unload"
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Success Response:

```json
{
  "success": true,
  "message": "Appointments processed successfully",
  "errors": [],
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "324980824324",
      "scheduledTime": "2025-04-25T09:00:00",
      "duration": 60,
      "status": "SCHEDULED",
      "extended": {
        "poId": "12926701"
      },
      "notes": "Appointment created successfully"
    }
  ]
}
```

Failure Response

```json
{
  "success": false,
  "message": "Some appointments could not be made",
  "errors": [],
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "-",
      "scheduledTime": "2025-04-25T09:00:00",
      "duration": 60,
      "status": "UNSCHEDULED",
      "extended": {
        "poId": "12926701"
      },
      "notes": "Failed to create appointment due to scheduling conflict"
    }
  ]
}
```

###### Re-Schedule Appointment

Re-schedules an appointment.

We already have appointment ID after creating appointment. Using it we reschedule the appointment.

Request Payload:

```json
{
  "integration": "scheduling",
  "platform": "Manhattan",
  "action": "UpdateAppointment",
  "userId": "789",
  "serviceId": "910",
  "integrationId": "456",
  "appointments": [
    {
      "poId": "12926701",
      "appointmentId": "3223523",
      "appointmentTime": "2025-07-18T21:00:00.000Z",
      "facilityId": "DCDA09P",
      "appointmentType": "Live Unload"
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Success Response:

```json
{
  "success": true,
  "message": "Appointments updated successfully",
  "errors": [],
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "324980824324",
      "scheduledTime": "2025-04-25T09:00:00",
      "duration": 60,
      "status": "SCHEDULED",
      "extended": {
        "poId": "12926701"
      },
      "notes": "Appointment created successfully"
    }
  ]
}
```

Failure Response

```json
{
  "success": false,
  "message": "Some appointments could not be updated",
  "errors": [],
  "platformData": null,
  "appointments": [
    {
      "appointmentId": "-",
      "scheduledTime": "2025-04-25T09:00:00",
      "duration": 60,
      "status": "UNSCHEDULED",
      "extended": {
        "poId": "12926701"
      },
      "notes": "Appointment ID is required for update"
    }
  ]
}
```

###### Cancel Appointment

Not implemented yet.

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```
