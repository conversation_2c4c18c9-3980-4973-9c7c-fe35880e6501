"""Manhattan Selenium scheduling implementation."""

import functools
import time
from datetime import datetime
from typing import Op<PERSON>, <PERSON><PERSON>, Type

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import <PERSON><PERSON><PERSON><PERSON>ait

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from integrations.scheduling.manhattan.models import (
    ManhattanAppointmentData,
    ManhattanCancelAppointmentRequest,
    ManhattanGetAppointmentRequest,
    ManhattanGetAppointmentResponse,
    ManhattanGetLoadTypesRequest,
    ManhattanGetOpenSlotsRequest,
    ManhattanGetWarehouseRequest,
    ManhattanLoginRequest,
    ManhattanMakeAppointmentRequest,
    ManhattanUpdateAppointmentRequest,
    ManhattanValidateAppointmentRequest,
)
from integrations.scheduling.manhattan.utils import (
    change_iframe,
    click_add_button,
    click_appointment_link,
    click_edit_button,
    click_on_toolbar,
    fetch_or_select_facilities,
    fetch_recommended_slots,
    fill_appointment_details,
    get_appointment_id,
    handle_override_if_present,
    parse_warehouse_details,
    parse_warehouse_name,
    search_appointment,
    select_appointment_if_found,
    select_slot,
)
from integrations.scheduling.models import (
    Appointment,
    AppointmentData,
    CancelAppointmentResponse,
    Credentials,
    GetLoadTypesResponse,
    GetOpenSlotsResponse,
    GetWarehouseResponse,
    LoginResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
    ValidateAppointmentResponse,
    WarehouseDetails,
)
from models.base import BaseResponse
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

# Manhattan element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "username"),
    "password_field": (By.ID, "password"),
    "login_button": (By.ID, "loginButton"),
    "login_error": (By.ID, "error-msg-lbl"),
    "error_container": (By.ID, "error-msg-cnt"),
    "error_icon": (By.ID, "error-img"),
    "loading_div": (By.ID, "loadingDiv"),
    "logged_in_indicator": (By.ID, "button-1013-btnIconEl"),
}

MANHATTAN_STATIC_URL = (
    "https://ahold-tlm.logistics.com/manh/resources/images/ma-touch.png"
)

MANHATTAN_LOGIN_URL = "https://ahold-mip.logistics.com/login.jsp"


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        driver.implicitly_wait(2)
        count = 3
        while count > 0:
            if not is_element_present(
                driver, LOCATORS["logged_in_indicator"], timeout=2
            ):
                logger.debug("Checking logged in indicator...")
                count -= 1
            else:
                break
        driver.implicitly_wait(10)
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting Manhattan login process")

        username_element = wait_for_element(driver, LOCATORS["username_field"])
        if not username_element:
            logger.error("Login page not loaded properly, re-loading")
            driver.get(MANHATTAN_LOGIN_URL)
            wait_for_page_load(driver)
            wait_for_element(driver, LOCATORS["username_field"])

        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        logger.debug(f"Current Manhattan URL: {driver.current_url}")

        driver.implicitly_wait(2)
        try:
            if is_element_present(driver, LOCATORS["error_icon"], timeout=2):
                if driver.find_element(*LOCATORS["error_icon"]).is_displayed():
                    error_msg = get_element_text(
                        driver, LOCATORS["login_error"]
                    )
                    if error_msg and error_msg.strip():
                        logger.error(f"Manhattan Login failed: {error_msg}")
                        driver.implicitly_wait(10)
                        return False, error_msg
        except Exception:
            # Silently continue if no error message found
            pass
        driver.implicitly_wait(10)

        driver.get(PLATFORM_URLS["manhattan"])
        wait_for_page_load(driver)

        if PLATFORM_URLS["manhattan"] not in driver.current_url:
            logger.error(
                "Failed to redirect to Manhattan platform URL after login, retrying..."
            )
            driver.get(PLATFORM_URLS["manhattan"])
            wait_for_page_load(driver)

        WebDriverWait(driver, 20).until(
            lambda d: d.find_element(By.TAG_NAME, "body")
            .get_attribute("innerHTML")
            .strip()
            != ""
        )

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"Manhattan Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("manhattan", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            driver.get(MANHATTAN_STATIC_URL)

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["manhattan"])

            wait_for_page_load(driver)

            loading_locator = (
                By.XPATH,
                "//div[contains(@class, 'x-mask-msg-text') and contains(text(), 'Loading WebTop')]",
            )
            driver.implicitly_wait(2)

            username_element = wait_for_element(
                driver, LOCATORS["username_field"], timeout=2
            )
            if username_element:
                raise Exception("Login page detected, clearing cookies")

            try:
                WebDriverWait(driver, 10).until(
                    lambda d: d.find_element(By.TAG_NAME, "body")
                    .get_attribute("innerHTML")
                    .strip()
                    != ""
                )
            except Exception as e:
                logger.warning(f"Error waiting for page load: {str(e)}")

            wait_for_element(
                driver,
                loading_locator,
                timeout=2,
                condition=EC.visibility_of_element_located,
            )
            driver.implicitly_wait(10)
            # Wait for loading spinner to disappear
            wait_for_element(
                driver,
                loading_locator,
                timeout=10,
                condition=EC.invisibility_of_element_located,
            )
            logger.info("Loading spinner is gone, continuing...")

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(MANHATTAN_LOGIN_URL)
        wait_for_page_load(driver)

        # When site is loaded with invalid cookies, it gives 401 forbidden error
        # Due to which deleting is required
        driver.delete_all_cookies()

        driver.get(MANHATTAN_LOGIN_URL)
        wait_for_page_load(driver)

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )

            new_cookies = driver.get_cookies()
            save_cookies("manhattan", credentials.username, new_cookies)

            loading_locator = (
                By.XPATH,
                "//div[contains(@class, 'x-mask-msg-text') and contains(text(), 'Loading WebTop')]",
            )
            wait_for_element(
                driver,
                loading_locator,
                timeout=10,
                condition=EC.visibility_of_element_located,
            )
            # Wait for loading spinner to disappear
            wait_for_element(
                driver,
                loading_locator,
                timeout=10,
                condition=EC.invisibility_of_element_located,
            )
            logger.info("Loading spinner is gone, continuing...")

            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: ManhattanLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to Manhattan.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: ManhattanGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from Manhattan.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(ValidateAppointmentResponse)
def validate_appointment(
    request: ManhattanValidateAppointmentRequest, driver: WebDriver
) -> ValidateAppointmentResponse:
    """Validate an appointment in Manhattan.

    Args:
        request: Validate appointment request
        driver: WebDriver instance provided by decorator
    Returns:
        ValidateAppointmentResponse
    """
    try:
        is_toolbar_clicked = click_on_toolbar(driver)
        if not is_toolbar_clicked:
            return ValidateAppointmentResponse(
                success=False,
                message="Failed to click on toolbar",
                errors=["Toolbar click failed"],
            )

        is_appt_link_clicked = click_appointment_link(driver)
        if not is_appt_link_clicked:
            return ValidateAppointmentResponse(
                success=False,
                message="Failed to click on appointment link",
                errors=["Appointment link click failed"],
            )

        change_iframe(
            driver,
            (
                By.XPATH,
                "//iframe[contains(@src, '/appointment/ui/jsf/appointmentList.jsflps')]",
            ),
        )

        add_button_clicked = click_add_button(driver)
        if not add_button_clicked:
            return ValidateAppointmentResponse(
                success=False,
                message="Failed to click on add button",
                errors=["Add button click failed"],
            )

        city, state = request.warehouse.city, request.warehouse.state

        iframe_locator = (By.XPATH, "//iframe[@id='uxiframe-1110-iframeEl']")
        change_iframe(driver, iframe_locator)

        wait_for_page_load(driver)

        facilities = fetch_or_select_facilities(
            driver,
            city=city,
            state=state,
        )

        if not facilities:
            return ValidateAppointmentResponse(
                success=False,
                message="No facilities found",
                errors=["Failed to fetch or select facilities"],
            )

        appointments = [
            Appointment(
                appointmentId="-",
                duration=60,
                scheduledTime="-",
                status="UNSCHEDULED",
                warehouse=WarehouseDetails(
                    city=city,
                    state=state,
                ),
                extended={"facilities": facilities},
            )
        ]

        return ValidateAppointmentResponse(
            success=True,
            message="Appointment validated successfully",
            appointments=appointments,
        )
    except Exception as e:
        return ValidateAppointmentResponse(
            success=False,
            message="Failed to validate appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetOpenSlotsResponse)
def get_open_slots(
    request: ManhattanGetOpenSlotsRequest, driver: WebDriver
) -> GetOpenSlotsResponse:
    """Get open appointment slots from Manhattan.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        po_id = request.poId
        facility_id = request.facilityId
        facility_text = request.facilityText
        start_date = request.startDate
        end_date = request.endDate
        appointment_type = request.appointmentType
        appointment_id = request.appointmentId

        is_toolbar_clicked = click_on_toolbar(driver)
        if not is_toolbar_clicked:
            return GetOpenSlotsResponse(
                success=False,
                message="Failed to click on toolbar",
                errors=["Toolbar click failed"],
            )

        is_appt_link_clicked = click_appointment_link(driver)
        if not is_appt_link_clicked:
            return GetOpenSlotsResponse(
                success=False,
                message="Failed to click on appointment link",
                errors=["Appointment link click failed"],
            )

        change_iframe(
            driver,
            (
                By.XPATH,
                "//iframe[contains(@src, '/appointment/ui/jsf/appointmentList.jsflps')]",
            ),
        )

        warehouse_name = ""

        if appointment_id and search_appointment(driver, appointment_id):
            appointment_selected = select_appointment_if_found(driver)
            if not appointment_selected:
                return GetOpenSlotsResponse(
                    success=False,
                    message="Failed to select appointment",
                    errors=["Failed to select appointment"],
                )

            warehouse_name = parse_warehouse_name(driver)

            is_edit_button_clicked = click_edit_button(driver)
            if not is_edit_button_clicked:
                return GetOpenSlotsResponse(
                    success=False,
                    message="Failed to click on edit button",
                    errors=["Edit button click failed"],
                )
        else:
            add_button_clicked = click_add_button(driver)
            if not add_button_clicked:
                return GetOpenSlotsResponse(
                    success=False,
                    message="Failed to click on add button",
                    errors=["Add button click failed"],
                )

            iframe_locator = (
                By.XPATH,
                "//iframe[@id='uxiframe-1110-iframeEl']",
            )
            change_iframe(driver, iframe_locator)

            wait_for_page_load(driver)

            details_filled, msg = fill_appointment_details(
                driver,
                po_id=po_id,
                facility_id=facility_id,
                scheduled_datetime=datetime.strptime(start_date, "%Y-%m-%d"),
                appointment_type=appointment_type,
            )
            if not details_filled:
                return GetOpenSlotsResponse(
                    success=False,
                    message="Failed to fill appointment details",
                    errors=[msg],
                )

        recommended_slots, msg = fetch_recommended_slots(
            driver,
            start_date=start_date,
            end_date=end_date,
        )

        warehouse_details = parse_warehouse_details(
            facility_text=facility_text, open_slots=recommended_slots
        )

        if warehouse_name:
            warehouse_details.name = warehouse_name

        appointments = [
            AppointmentData(
                appointmentId=appointment_id if appointment_id else "-",
                duration=60,
                scheduledTime="-",
                status="UNSCHEDULED" if not appointment_id else "SCHEDULED",
                notes="",
                poNums=po_id,
                warehouse=warehouse_details,
            )
        ]

        return GetOpenSlotsResponse(
            success=True,
            message="Recommended slots fetched successfully",
            appointments=appointments,
        )
    except Exception as e:
        return GetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: ManhattanGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from Manhattan.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: ManhattanCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in Manhattan.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        return CancelAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment canceling not implemented"],
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(ManhattanGetAppointmentResponse)
def get_appointment(
    request: ManhattanGetAppointmentRequest, driver: WebDriver
) -> ManhattanGetAppointmentResponse:
    """Get an appointment from Manhattan.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        appointment_id = request.appointment.appointmentId
        po_id = request.appointment.poId

        is_toolbar_clicked = click_on_toolbar(driver)
        if not is_toolbar_clicked:
            return ManhattanGetAppointmentResponse(
                success=False,
                message="Failed to click on toolbar",
                errors=["Toolbar click failed"],
            )

        is_appt_link_clicked = click_appointment_link(driver)
        if not is_appt_link_clicked:
            return ManhattanGetAppointmentResponse(
                success=False,
                message="Failed to click on appointment link",
                errors=["Appointment link click failed"],
            )

        change_iframe(
            driver,
            (
                By.XPATH,
                "//iframe[contains(@src, '/appointment/ui/jsf/appointmentList.jsflps')]",
            ),
        )

        appointment_found = search_appointment(driver, appointment_id)
        if not appointment_found:
            return ManhattanGetAppointmentResponse(
                success=False,
                message=f"Appointment with ID {appointment_id} not found",
                errors=[f"Appointment with ID {appointment_id} not found"],
            )

        wait_for_element(
            driver,
            (By.ID, "dataForm:listView:dataTable:0:startTime"),
            condition=EC.visibility_of_element_located,
        )
        start_time = driver.find_element(
            By.ID, "dataForm:listView:dataTable:0:startTime"
        ).text
        appointment_type = driver.find_element(
            By.ID, "dataForm:listView:dataTable:0:type"
        ).text
        appointment_status = driver.find_element(
            By.ID, "dataForm:listView:dataTable:0:apptType"
        ).text
        facility_id = driver.find_element(
            By.ID, "dataForm:listView:dataTable:0:facility"
        ).text

        return ManhattanGetAppointmentResponse(
            success=True,
            message="Appointment fetched successfully",
            appointments=[
                ManhattanAppointmentData(
                    appointmentId=appointment_id,
                    poId=po_id,
                    appointmentTime=start_time,
                    appointmentType=appointment_type,
                    status=appointment_status,
                    facilityId=facility_id,
                    notes="Appointment fetched successfully",
                )
            ],
        )
    except Exception as e:
        return ManhattanGetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(MakeAppointmentResponse)
def make_appointment(
    request: ManhattanMakeAppointmentRequest, driver: WebDriver
) -> MakeAppointmentResponse:
    """Create an appointment in Manhattan.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    appointments = []
    all_errors = []
    try:
        for appt in request.appointments:
            po_id = appt.poId
            facility_id = appt.facilityId
            facility_text = appt.facilityText
            appointment_time = appt.appointmentTime
            appointment_type = appt.appointmentType

            warehouse_details = None
            if facility_text:
                warehouse_details = parse_warehouse_details(
                    facility_text=facility_text
                )

            failed_appointment = Appointment(
                appointmentId=f"{po_id}",
                duration=60,
                scheduledTime=appointment_time,
                status="UNSCHEDULED",
                notes="",
                extended={
                    "poId": po_id,
                },
                warehouse=warehouse_details,
            )

            driver.switch_to.default_content()

            is_toolbar_clicked = click_on_toolbar(driver)
            if not is_toolbar_clicked:
                all_errors.append("Toolbar click failed")
                failed_appointment.notes = "Toolbar click failed"
                appointments.append(failed_appointment)
                continue

            is_appt_link_clicked = click_appointment_link(driver)
            if not is_appt_link_clicked:
                all_errors.append("Appointment link click failed")
                failed_appointment.notes = "Appointment link click failed"
                appointments.append(failed_appointment)
                continue

            change_iframe(
                driver,
                (
                    By.XPATH,
                    "//iframe[contains(@src, '/appointment/ui/jsf/appointmentList.jsflps')]",
                ),
            )

            add_button_clicked = click_add_button(driver)
            if not add_button_clicked:
                all_errors.append("Add button click failed")
                failed_appointment.notes = "Add button click failed"
                appointments.append(failed_appointment)
                continue

            iframe_locator = (
                By.XPATH,
                "//iframe[@id='uxiframe-1110-iframeEl']",
            )
            change_iframe(driver, iframe_locator)

            wait_for_page_load(driver)

            scheduled_datetime = datetime.strptime(
                appointment_time, "%Y-%m-%dT%H:%M:%S"
            )

            details_filled, msg = fill_appointment_details(
                driver,
                po_id=po_id,
                facility_id=facility_id,
                scheduled_datetime=scheduled_datetime,
                appointment_type=appointment_type,
            )
            if not details_filled:
                all_errors.append(msg)
                failed_appointment.notes = msg
                appointments.append(failed_appointment)
                continue

            is_slot_selected, msg = select_slot(driver, scheduled_datetime)
            if not is_slot_selected:
                all_errors.append(msg)
                failed_appointment.notes = msg
                appointments.append(failed_appointment)
                continue

            save_button = wait_for_element(
                driver,
                (By.ID, "apptList_btn_12"),
                timeout=10,
            )
            if not save_button:
                all_errors.append("Save button not found")
                failed_appointment.notes = "Save button not found"
                appointments.append(failed_appointment)
                continue

            safe_click(driver, save_button)

            handle_override_if_present(driver)

            wait_for_page_load(driver)

            warehouse_name = parse_warehouse_name(driver)
            if warehouse_name:
                warehouse_details.name = warehouse_name

            appointments.append(
                Appointment(
                    appointmentId=get_appointment_id(driver),
                    duration=60,
                    scheduledTime=appointment_time,
                    status="SCHEDULED",
                    extended={
                        "poId": po_id,
                    },
                    notes="Appointment created successfully",
                    warehouse=warehouse_details,
                )
            )

        if all_errors:
            return MakeAppointmentResponse(
                success=False,
                message="Some appointments could not be made",
                errors=all_errors,
                appointments=appointments,
            )

        return MakeAppointmentResponse(
            success=True,
            message="Appointment made successfully",
            appointments=appointments,
        )
    except Exception as e:
        return MakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(UpdateAppointmentResponse)
def update_appointment(
    request: ManhattanUpdateAppointmentRequest, driver: WebDriver
) -> UpdateAppointmentResponse:
    """Update an appointment in Manhattan.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        appointments = []
        all_errors = []
        for appt in request.appointments:
            appointment_id = appt.appointmentId
            po_id = appt.poId
            appointment_time = appt.appointmentTime
            scheduled_datetime = datetime.strptime(
                appointment_time, "%Y-%m-%dT%H:%M:%S"
            )

            failed_appointment = Appointment(
                appointmentId=appointment_id,
                duration=60,
                scheduledTime=appointment_time,
                status="UNSCHEDULED",
                notes="",
                extended={
                    "poId": po_id,
                },
            )

            try:
                driver.switch_to.default_content()

                if not appointment_id:
                    all_errors.append("Appointment ID is required for update")
                    failed_appointment.notes = (
                        "Appointment ID is required for update"
                    )
                    appointments.append(failed_appointment)
                    continue

                is_toolbar_clicked = click_on_toolbar(driver)
                if not is_toolbar_clicked:
                    all_errors.append("Toolbar click failed")
                    failed_appointment.notes = "Toolbar click failed"
                    appointments.append(failed_appointment)
                    continue

                is_appt_link_clicked = click_appointment_link(driver)
                if not is_appt_link_clicked:
                    all_errors.append("Appointment link click failed")
                    failed_appointment.notes = "Appointment link click failed"
                    appointments.append(failed_appointment)
                    continue

                change_iframe(
                    driver,
                    (
                        By.XPATH,
                        "//iframe[contains(@src, '/appointment/ui/jsf/appointmentList.jsflps')]",
                    ),
                )

                appointment_found = search_appointment(driver, appointment_id)
                if not appointment_found:
                    all_errors.append(
                        f"Appointment with ID {appointment_id} not found"
                    )
                    failed_appointment.notes = (
                        f"Appointment with ID {appointment_id} not found"
                    )
                    appointments.append(failed_appointment)
                    continue

                appointment_selected, msg = select_appointment_if_found(driver)
                if not appointment_selected:
                    all_errors.append(msg)
                    failed_appointment.notes = msg
                    appointments.append(failed_appointment)
                    continue

                is_edit_button_clicked = click_edit_button(driver)
                if not is_edit_button_clicked:
                    all_errors.append("Edit button click failed")
                    failed_appointment.notes = "Edit button click failed"
                    appointments.append(failed_appointment)
                    continue

                is_slot_selected, msg = select_slot(driver, scheduled_datetime)
                if not is_slot_selected:
                    all_errors.append(msg)
                    failed_appointment.notes = msg
                    appointments.append(failed_appointment)
                    continue

                save_button = wait_for_element(
                    driver,
                    (By.ID, "apptList_btn_12"),
                    timeout=10,
                )
                if not save_button:
                    all_errors.append("Save button not found")
                    failed_appointment.notes = "Save button not found"
                    appointments.append(failed_appointment)
                    continue

                safe_click(driver, save_button)

                handle_override_if_present(driver)

                appointments.append(
                    Appointment(
                        appointmentId=get_appointment_id(driver),
                        duration=60,
                        scheduledTime=appointment_time,
                        status="SCHEDULED",
                        extended={
                            "poId": po_id,
                        },
                        notes="Appointment updated successfully",
                    )
                )

            except Exception as e:
                all_errors.append(
                    f"Failed to switch to appointment iframe: {str(e)}"
                )
                failed_appointment.notes = (
                    f"Failed to switch to appointment iframe: {str(e)}"
                )
                appointments.append(failed_appointment)
                continue

        if all_errors:
            return UpdateAppointmentResponse(
                success=False,
                message="Some appointments could not be updated",
                errors=all_errors,
                appointments=appointments,
            )

        return UpdateAppointmentResponse(
            success=True,
            message="Appointment made successfully",
            appointments=appointments,
        )
    except Exception as e:
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )
