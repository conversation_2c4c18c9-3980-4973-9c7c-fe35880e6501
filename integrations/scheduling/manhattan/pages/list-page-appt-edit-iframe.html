<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ajax="http://www.exadel.com/vcp/components/ajax"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="/a4j/g/3_3_3.Finalorg.ajax4jsf.javascript.AjaxScript.xhtml*.jsf*.jsfx*.jsflps" type="text/javascript"></script><link id="mpsstyles" rel="stylesheet" type="text/css" href="/lps/lpsstyles">
<link id="LPSStyles" rel="stylesheet" type="text/css" href="/lps/resources/common/css/LPSStyles.css">
<link id="LPSLayoutStyles" rel="stylesheet" type="text/css" href="/lps/resources/layout/css/LPSLayoutStyles.css">
<link rel="stylesheet" type="text/css" href="/lps/resources/common/css/richfaces.css">
<link rel="shortcut icon" href="/lps/resources/layout/images/favicon.ico">

<script type="text/javascript">
//<![CDATA[
    // Global java script var for flagging HFI.
    var UI8 = true;
    var HFI = UI8;
    var MPS = UI8;
//]]>
</script><script language="JavaScript" src="/lps/resources/common/UI08Common.js"></script><title>Appointments | Manhattan Associates</title></head><body scroll="no" link="#0000FF" vlink="#0000FF" alink="#0000FF" class="-gbl_mtc" style="cursor: default;"><div class="msdpop -fcs_msbg" id="ms_pop1" style="display: none;"> <div class="pop_sdw -pdlg_sdw"></div>  <div class="msdpopbdr -fcs_msbg"> <table cellspacing="0" cellpadding="0" border="0"> <tbody><tr><td><div class="msdpopbody" id="ms_popcnt1"><ol class="msdpopol"><li unselectable="on" class="mspopunsel" id="ms_popall1">All</li><li unselectable="on" class="mspopunsel" id="ms_popli1_0"></li><li unselectable="on" class="mspopunsel" id="ms_popli1_1">Scheduled</li><li unselectable="on" class="mspopunsel" id="ms_popli1_2">Checked In</li><li unselectable="on" class="mspopunsel" id="ms_popli1_3">Complete</li><li unselectable="on" class="mspopunsel" id="ms_popli1_4">Canceled</li><li unselectable="on" class="mspopunsel" id="ms_popli1_5">Countered</li><li unselectable="on" class="mspopunsel" id="ms_popli1_6">Requested</li><li unselectable="on" class="mspopunsel" id="ms_popli1_7">In Progress</li><li unselectable="on" class="mspopunsel" id="ms_popli1_8">Suspended</li></ol></div></td></tr><tr><td align="middle"><div class="msdpopftr -fcs_msbg"><button class="button -btn_bbg -btn_bbh -btn_bbc -btn_bbhc -btn_bt -btn_bth" id="ms_popok1" tabindex="1">OK</button><button class="button -btn_bbg -btn_bbh -btn_bbc -btn_bbhc -btn_bt -btn_bth" id="ms_popcancel1" tabindex="2">Cancel</button></div></td></tr></tbody></table></div></div><div class="asbasdd" id="as_bas6_dd" style="display: none;"></div><div class="asbasdd" id="as_bas5_dd" style="display: none;"></div><div class="asbasdd" id="as_bas4_dd" style="display: none;"></div><div class="asbasdd" id="as_bas3_dd" style="display: none;"></div><div class="asbasdd" id="as_bas2_dd" style="display: none;"></div><div class="asbasdd" id="as_bas1_dd" style="display: none;"></div><span id="scppCommon"></span><script language="JavaScript" src="/lps/resources/calendar/scripts/calendar.js"></script><span id="scppCalendarDef"></span><script language="JavaScript" src="/lcom/common/js/csrf.js"></script><span id="waf_csrf"></span>

<script type="text/javascript">
//<![CDATA[
    jQuery.noConflict() ;
    browser_Styles ();
	UI8Layout.setExpLayout(true);
//]]>
</script>



<form id="dataForm" name="dataForm" method="post" action="/appointment/ui/jsf/appointmentList.xhtml" enctype="application/x-www-form-urlencoded" onsubmit="checkHyperClicking()">
<input type="hidden" name="dataForm" value="dataForm" tabindex="3">
<input type="hidden" name="uniqueToken" value="6" tabindex="4"><input id="MANH-CSRFToken" type="hidden" name="MANH-CSRFToken" value="UaYFyclBUMZlEaGbKqskYiY40ZwG540p4oLmBhcBKgw=" tabindex="5"><script type="text/javascript">
CSRF.Conf.init({tokenHolderId:"MANH-CSRFToken",tokenName:"MANH-CSRFToken",tokenValue:"UaYFyclBUMZlEaGbKqskYiY40ZwG540p4oLmBhcBKgw=",enabled:true});
</script>
    <script type="text/javascript">
    //<![CDATA[
        var isServlet = false;
        var helpURL="/lcom/common/jsp/helphelper.jsp?server=960AE9B07F6B5033C7901237699DFC5FCD42DD4FCBE39DDD157E4AD998114A00&uri=%2Fappointment%2Fui%2Fjsf%2FappointmentList.jsflps";
		var showSaveAsTile = "true";
        var dialogHelpURL=null;
        var stkId ="\x20";
        var customizationExists = eval("true");
        var ucl_home_url =  "";
        document.getElementById('dataForm').addParam = _addParam;
    //]]>
    </script>
	<input type="hidden" id="helpurlEle" name="helpurlEle" value="/lcom/common/jsp/helphelper.jsp?server=960AE9B07F6B5033C7901237699DFC5FCD42DD4FCBE39DDD157E4AD998114A00&amp;uri=%2Fappointment%2Fui%2Fjsf%2FappointmentList.jsflps" tabindex="6">

   <script>
   //<![CDATA[
        _script_pg_1 ();
   //]]>
   </script>


    <div id="drag_1" style="position: absolute; z-index: 30000;"><div id="dataForm:er_d1" name="dataForm:er_d1" class="dialog_cont"><div class="dialog_inner">
        <div class="erolpop -pdlg_eob">
        <div class="pop_sdw"></div>
        <table class="pophead" cellspacing="0" cellpadding="0" border="0">
            <tbody>
                <tr>
                    <td>
                    <div class="pop_hdr -pdlg_dhbg">
                    <table class="pop_hdr_inner" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <tbody>
                            <tr>
                                <td class="pop_dragHandler" id="er_d1_dh1" unselectable="on"><div unselectable="on" class="pop_title erpoptitle">Messages (<span unselectable="on" id="er_d1_mcnt">0</span>)</div></td>
                                <td class="er_dw_mintd" id="er_d1_mtd1"><div class="erpopmin" id="er_d1_m1"></div></td>
                                <td class="er_dw_clstd"><div class="pop_close" id="er_d1_c1"></div></td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    </td>
                </tr>
                <tr>
                    <td>
                    <div class="pop_bdr -pdlg_dhbg">
                       <div class="pop_body_err -pdlg_dbg" id="er_d1_bid"></div>
                    </div>
                    </td>
                </tr>
            </tbody>
        </table>
        </div></div></div><script type="text/javascript">
UI8Layout.data.put("er_d1",{onClose:"",dialogClientId:"dataForm:er_d1",dragHandleId:"er_d1_dh1",onDialog:"",closeClientId:"er_d1_c1"});
</script>

    <script type="text/javascript">
    //<![CDATA[
         var ui8er = UI8Layout.ErrorOverLay;
         ui8er.init ();
    //]]>
    </script><div id="dataForm:find_d1" name="dataForm:find_d1" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:bdt_1_dHId"><div class="pop_title -pdlg_dttc"> </div></td><td><div class="pop_close" id="dataForm:bdt_1_cCId"><input type="button" tabindex="7"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg">
                    <iframe id="iframe1" width="490px" frameborder="0" scrolling="no" height="453px"></iframe></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("find_d1",{onClose:"onFindClose",dialogClientId:"dataForm:find_d1",dragHandleId:"dataForm:bdt_1_dHId",onDialog:"",closeClientId:"dataForm:bdt_1_cCId"});
</script><span id="dataForm:changePrtReqAjaxPanel"></span><span id="dataForm:reportAjaxPanel"></span><div id="dataForm:NewTrailerWindow" name="dataForm:NewTrailerWindow" class="dialog_cont" style="height:610px;width:780px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:NewTrailerWindow_temp_dHId"><div class="pop_title -pdlg_dttc">New Trailer</div></td><td><div class="pop_close" id="dataForm:NewTrailerWindow_temp_cCId"><input type="button" tabindex="8"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:610px;width:780px;">
				<div class="popcon"><span id="dataForm:NewTrailerWindow_Alra4j">

	<script>
	/*
		var newTrailerClicked = document.getElementById("dataForm:trailerButtonClicked");
		if (  !(  newTrailerClicked == null || newTrailerClicked == 'undefined')  ) {
			if (  !(newTrailerClicked.value == 'no'  ) ){
				var url =newTrailerClicked.value;
				newTrailerClicked.value = "no";
				//document.getElementById("newTrailerFrame").src =url;
				}
			}
			*/
	</script>

	<iframe id="newTrailerFrame" height="550" width="750" frameborder="0"></iframe>
</span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("NewTrailerWindow",{onClose:"",dialogClientId:"dataForm:NewTrailerWindow",dragHandleId:"dataForm:NewTrailerWindow_temp_dHId",onDialog:"",closeClientId:"dataForm:NewTrailerWindow_temp_cCId"});
</script><div id="dataForm:ApptDockLookUpDockIdDialog" name="dataForm:ApptDockLookUpDockIdDialog" class="dialog_cont" style="height:200px;width:250px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:dcklkpdocId_id_dHId"><div class="pop_title -pdlg_dttc">Dock</div></td><td><div class="pop_close" id="dataForm:dcklkpdocId_id_cCId"><input type="button" tabindex="9"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;">
                    <div class="popcon"><span id="dataForm:dcklkpdocId_Alra4j">

		<input type="hidden" name="apptDockLookUpTextName" id="apptDockLookUpTextName" tabindex="10"><script language="JavaScript" src="/lps/resources/panel/scripts/panel.js"></script><div class="pnltopdiv" id="PANEL_apptDckLkp__Search_Panel1_top"><table id="PANEL_apptDckLkp__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_apptDckLkp__Search_Panel1" class="pnlcondiv"><table id="dataForm:apptDckLkp22_pg_1">
<tbody>
<tr>
<td><script language="JavaScript" src="/lps/resources/caption/scripts/caption.js"></script><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="apptDckLkpInSrch"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:dockSearchText" type="text" name="dataForm:dockSearchText" value="*" tabindex="11"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:capptDckLkp_locgetlist22" name="dataForm:capptDckLkp_locgetlist22" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:capptDckLkp_locgetlist22','parameters':{'dataForm:capptDckLkp_locgetlist22':'dataForm:capptDckLkp_locgetlist22'} } );return false;" value="Find " type="button" tabindex="12"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:apptDckLkpFilter_Detail"><div class="pnltopdiv" id="PANEL_cpptDckLkp22_Find_Result_pannel_top"><div id="tr_cpptDckLkp22_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:pptDckLkpnlotFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="apptDckLkp_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:apptDockLookUpText" name="dataForm:apptDockLookUpText" size="5" ondblclick="selectApptDockLookUp();return false;" style="width:50%" tabindex="13">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:apptDckLkp22Filter_SelectButton" name="dataForm:apptDckLkp22Filter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptDckLkp22Filter_SelectButton','oncomplete':function(request,event,data){selectApptDockLookUp();return false;},'parameters':{'dataForm:apptDckLkp22Filter_SelectButton':'dataForm:apptDckLkp22Filter_SelectButton'} } );return false;" value="Select" type="button" tabindex="14"></div></td>
</tr>
</tbody>
</table>
</div></div></span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptDockLookUpDockIdDialog",{onClose:"",dialogClientId:"dataForm:ApptDockLookUpDockIdDialog",dragHandleId:"dataForm:dcklkpdocId_id_dHId",onDialog:"",closeClientId:"dataForm:dcklkpdocId_id_cCId"});
</script><div id="dataForm:ApptDockDoorLookUpDialog" name="dataForm:ApptDockDoorLookUpDialog" class="dialog_cont" style="height:200px;width:250px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:dckDoorlkp_id_dHId"><div class="pop_title -pdlg_dttc">Dock Door</div></td><td><div class="pop_close" id="dataForm:dckDoorlkp_id_cCId"><input type="button" tabindex="15"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;">
				<div class="popcon"><span id="dataForm:dckDoorlkp_Ch_Alra4j"><script language="JavaScript" src="/appointment/ui/jsf/ApptDockAndDoorLookup.js"></script><span id="dataForm:dockDoorFilter_script"></span>

		<input type="hidden" name="dockDoorTextName" id="dockDoorTextName" tabindex="16">
		<input type="hidden" name="dockDoorFacility" id="dockDoorFacility" tabindex="17">
		<input type="hidden" name="dockNameText" id="dockNameText" tabindex="18"><div class="pnltopdiv" id="PANEL_dockDoorFilter__Search_Panel1_top"><table id="PANEL_dockDoorFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_dockDoorFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:dockDoorFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="dockDoorCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:dockDoorSearch" type="text" name="dataForm:dockDoorSearch" value="*" onkeypress="if (event.keyCode == 13){ getApptDockDoorLookUpOnKeyPress(); return false; }" tabindex="19"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:dockDoorFilter_locgetlist" name="dataForm:dockDoorFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:dockDoorFilter_locgetlist','parameters':{'dataForm:dockDoorFilter_locgetlist':'dataForm:dockDoorFilter_locgetlist'} } );return false;" value="Find " type="button" tabindex="20"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:dockDoorFilter_Detail"><div class="pnltopdiv" id="PANEL_dockDoorFilter_Find_Result_pannel_top"><div id="tr_dockDoorFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:dockDoorFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="dockDoorFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:dockDoorText" name="dataForm:dockDoorText" size="5" ondblclick="selectApptDockDoorLookUp();return false;" style="width:50%" tabindex="21">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:dockDoorFilter_SelectButton" name="dataForm:dockDoorFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:dockDoorFilter_SelectButton','oncomplete':function(request,event,data){selectApptDockDoorLookUp();return false;},'parameters':{'dataForm:dockDoorFilter_SelectButton':'dataForm:dockDoorFilter_SelectButton'} } );return false;" value="Select" type="button" tabindex="22"></div></td>
</tr>
</tbody>
</table>
</div></div></span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptDockDoorLookUpDialog",{onClose:"",dialogClientId:"dataForm:ApptDockDoorLookUpDialog",dragHandleId:"dataForm:dckDoorlkp_id_dHId",onDialog:"",closeClientId:"dataForm:dckDoorlkp_id_cCId"});
</script><div id="dataForm:ApptPODialog" name="dataForm:ApptPODialog" class="dialog_cont" style="height:450px;width:450px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:apptPOD_id_dHId"><div class="pop_title -pdlg_dttc">Find Purchase Order</div></td><td><div class="pop_close" id="dataForm:apptPOD_id_cCId"><input type="button" tabindex="23"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:450px;width:450px;">
				<div class="popcon"><span id="dataForm:apptPOD_Alra4j"><script language="JavaScript" src="/appointment/lookup/ui/ApptPOLookUp.js"></script><span id="dataForm:apptPOLkp_script"></span>

	<input type="hidden" name="apptPOTextName" tabindex="24"><span id="dataForm:apptPOLkp_DetailFirst"><div class="pnltopdiv" id="PANEL_apptPOLkp__Search_Panel1_top"><table id="PANEL_apptPOLkp__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_apptPOLkp__Search_Panel1" class="pnlcondiv"><table id="dataForm:gl289" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Billing method:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:billingMeth" name="dataForm:billingMeth" size="1" tabindex="25">	<option value="">Select one</option>
	<option value="1">Collect</option>
	<option value="3">Consignee Bill</option>
	<option value="5">Cost &amp; Freight</option>
	<option value="6">Delivery Duty Paid</option>
	<option value="7">Free Domicile</option>
	<option value="4">Free on Board</option>
	<option value="0">Prepaid</option>
	<option value="2">Third Party</option>
	<option value="8">UPS Third Party Freight Collect</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">PO on Shipment:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:poOnShipment" type="checkbox" name="dataForm:poOnShipment" tabindex="26"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><table id="dataForm:gl289Btn" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Search:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:apptPOSearch" type="text" name="dataForm:apptPOSearch" value="" tabindex="27">&nbsp;&nbsp;<input class="btn" id="dataForm:apptPOLkp_locgetlist" name="dataForm:apptPOLkp_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptPOLkp_locgetlist','parameters':{'dataForm:apptPOLkp_locgetlist':'dataForm:apptPOLkp_locgetlist'} } );return false;" value="Find " type="button" tabindex="28"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl289Btn","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:apptPOLkp_Detail"><div class="pnltopdiv" id="PANEL_apptPOLkp_Find_Result_pannel_top"><div id="tr_apptPOLkp_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:gl27" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:apptPOText" name="dataForm:apptPOText" size="10" ondblclick="selectApptPO();return false;" style="width:400px" tabindex="29">	<option value="A-100009">A-100009</option>
	<option value="A-100006">A-100006</option>
	<option value="A-100028">A-100028</option>
	<option value="A-100005">A-100005</option>
	<option value="A-100027">A-100027</option>
	<option value="A-100008">A-100008</option>
	<option value="A-100007">A-100007</option>
	<option value="A-100029">A-100029</option>
	<option value="A-100045">A-100045</option>
	<option value="A-100004">A-100004</option>
	<option value="A-100026">A-100026</option>
	<option value="A-100003">A-100003</option>
	<option value="A-100020">A-100020</option>
	<option value="A-100042">A-100042</option>
	<option value="A-100064">A-100064</option>
	<option value="A-100041">A-100041</option>
	<option value="A-100000">A-100000</option>
	<option value="A-100022">A-100022</option>
	<option value="A-100021">A-100021</option>
	<option value="A-100065">A-100065</option>
	<option value="A-100060">A-100060</option>
	<option value="A-100040">A-100040</option>
	<option value="A-100062">A-100062</option>
	<option value="A-100039">A-100039</option>
	<option value="A-100038">A-100038</option>
	<option value="A-100013">A-100013</option>
	<option value="A-100035">A-100035</option>
	<option value="A-100012">A-100012</option>
	<option value="A-100034">A-100034</option>
	<option value="A-100056">A-100056</option>
	<option value="A-100015">A-100015</option>
	<option value="A-100037">A-100037</option>
	<option value="A-100014">A-100014</option>
	<option value="A-100036">A-100036</option>
	<option value="A-100031">A-100031</option>
	<option value="A-100053">A-100053</option>
	<option value="A-100030">A-100030</option>
	<option value="A-100011">A-100011</option>
	<option value="A-100055">A-100055</option>
	<option value="A-100010">A-100010</option>
	<option value="A-100054">A-100054</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input class="btn" id="dataForm:apptPOLkp_SelectButton" name="dataForm:apptPOLkp_SelectButton" onclick="selectApptPO();return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptPOLkp_SelectButton','parameters':{'dataForm:apptPOLkp_SelectButton':'dataForm:apptPOLkp_SelectButton'} } );return false;" value="Select" type="button" tabindex="30"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table></div></div></span><span id="dataForm:captionExeed">More than 40 records found</span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptPODialog",{onClose:"",dialogClientId:"dataForm:ApptPODialog",dragHandleId:"dataForm:apptPOD_id_dHId",onDialog:"",closeClientId:"dataForm:apptPOD_id_cCId"});
</script><div id="dataForm:EmailDialog" name="dataForm:EmailDialog" class="dialog_cont" style="height:450px;width:450px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:EmailD_id_dHId"><div class="pop_title -pdlg_dttc">Email</div></td><td><div class="pop_close" id="dataForm:EmailD_id_cCId"><input type="button" tabindex="31"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:450px;width:450px;">
				<div class="popcon"><span id="dataForm:emailD_Alra4j">
<span id="dataForm:emailPopUpPanel" border="false"><a href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-c133-ad04-84d0">Open in Mail Client</a>
	<br>
		<b>Appointment(s) Summary</b>
	<br>
	<h2>Appointments</h2>
	<br><span style="white-space:normal">Appointment(s) data were valid at the time this email was sent. Appointment(s) are subject to change and might be modified later. This information is intended for use of the email recipient only.</span></span>
</span>
				</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("EmailDialog",{onClose:"",dialogClientId:"dataForm:EmailDialog",dragHandleId:"dataForm:EmailD_id_dHId",onDialog:"",closeClientId:"dataForm:EmailD_id_cCId"});
</script><div id="dataForm:Recommendations_Dialog" name="dataForm:Recommendations_Dialog" class="dialog_cont" style="height:350px;width:650px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/ribbon/images/about.gif"></div></td><td class="pop_dragHandler" id="dataForm:Recommendations_DialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Recommendations</div></td><td><div class="pop_close" id="dataForm:Recommendations_DialogTemplate_cCId"><input type="button" tabindex="32"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:350px;width:650px;">
	                <div class="popcon"><span id="dataForm:Recommendations_Dialog_DetailAJAXPanel_1"><span id="dataForm:recolistpanel" border="false"><script type="text/javascript">
_u8_ely_1({isAjax:false,list:[],ortext:"Override",name:"error_OverLay1"});
</script><script language="JavaScript" src="/lps/resources/table/scripts/sortable.js"></script><script language="JavaScript" src="/lps/resources/table/scripts/datatable.js"></script><script language="JavaScript" src="/lps/resources/table/scripts/tableCommon.js"></script><input type="hidden" name="dataForm:recommendationTable_deleteHidden" value="" id="dataForm:recommendationTable_deleteHidden" tabindex="33"><input type="hidden" name="dataForm:recommendationTable_selectedRows" value="#:#" id="dataForm:recommendationTable_selectedRows" tabindex="34"><div class="datatbl_contr" id="dataForm:recommendationTable_container" style="overflow-y: hidden;"><div id="dataForm:recommendationTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:recommendationTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:recommendationTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:recommendationTable:isSortButtonClick" id="dataForm:recommendationTable:isSortButtonClick" value="" tabindex="35"><input type="hidden" name="dataForm:recommendationTable:sortDir" id="dataForm:recommendationTable:sortDir" value="desc" tabindex="36"><input type="hidden" name="dataForm:recommendationTable:colCount" id="dataForm:recommendationTable:colCount" value="" tabindex="37"><input type="hidden" name="dataForm:recommendationTable:tableClicked" id="dataForm:recommendationTable:tableClicked" value="" tabindex="38"><input type="hidden" name="dataForm:recommendationTable:tableResized" id="dataForm:recommendationTable:tableResized" value="false" tabindex="39"><div class="advtbl_contr_head" id="dataForm:recommendationTable_headDiv"><table id="dataForm:recommendationTable" cellspacing="0">
<colgroup>
<col>
<col><col class="tbl_colHidden"></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox">&nbsp;</td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:recommendationTable:rc3" class="titleCase">Slots</span></td>
<td class="tbl_colHidden advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header " align="left"><span style="display:none">&nbsp;</span></td>
</tr></thead></table></div><div id="dataForm:recommendationTable_bodyDiv" class="advtbl_contr_body"><table id="dataForm:recommendationTable_body" cellspacing="0"><colgroup>
<col>
<col><col class="tbl_colHidden"></colgroup><tbody>
<tr ondblclick="return doDefaultAction ('dataForm:recommendationTable:0:defaultactionbutton');" class="advtbl_row -dg_tr -dg_tsr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="radio" name="checkAll_c_dataForm:recommendationTable" id="checkAll_c_dataForm:recommendationTable" style="margin:2px;" value="0" tabindex="40"><input type="hidden" id="dataForm:recommendationTable:0:PK_0" name="dataForm:recommendationTable:0:PK_0" tabindex="41"></td><td style="white-space: nowrap;" align="left" class="advtbl_col advtbl_body_col"><span id="dataForm:recommendationTable:0:recommendationSlotDesc">Start-Time:7/17/25 18:30 Departure-Time:7/17/25 19:30</span><input id="dataForm:recommendationTable:0:recommendationId" type="hidden" name="dataForm:recommendationTable:0:recommendationId" value="0" tabindex="42"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="tbl_colHidden"><a href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="dataForm:recommendationTable:0:defaultactionbutton" name="dataForm:recommendationTable:0:defaultactionbutton" onclick="setSelectedRecommendation();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:recommendationTable:0:defaultactionbutton','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('Recommendations_Dialog');},'parameters':{'dataForm:recommendationTable:0:defaultactionbutton':'dataForm:recommendationTable:0:defaultactionbutton','dataForm:selectedUirecommendation':0} } );return false;">DefaultAction</a> <span style="display:none">&nbsp;</span></td>
</tr>
<tr id="dataForm:recommendationTable:nodataRow" class="advtbl_row trhide"><td class="advtbl_col advtbl_body_col tdhide" colspan="3" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="recommendationTable_hdnMaxIndexHldr" name="recommendationTable_hdnMaxIndexHldr" value="1" tabindex="43"></table></div><div class="emptyHoriScrollDiv"></div></div>
<input type="hidden" id="dataForm:recommendationTable_trs_pageallrowskey" name="dataForm:recommendationTable_trs_pageallrowskey" value="" tabindex="44"><input type="hidden" id="dataForm:recommendationTable_selectedRows" name="dataForm:recommendationTable_selectedRows" value="" tabindex="45"><input type="hidden" id="dataForm:recommendationTable_selectedIdList" name="dataForm:recommendationTable_selectedIdList" value="" tabindex="46"><input type="hidden" id="dataForm:recommendationTable_trs_allselectedrowskey" name="dataForm:recommendationTable_trs_allselectedrowskey" value="recommendationTable$:$1752205181860" tabindex="47"><script type="text/javascript">var  dataFormrecommendationTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='recommendationTable';
tableObjectArray[count]=dataFormrecommendationTable_tableObj;dataFormrecommendationTable_tableObj.bind(document.getElementById('dataForm:recommendationTable_container'), document.getElementById('dataForm:recommendationTable_headDiv'), document.getElementById('dataForm:recommendationTable_bodyDiv'), document.getElementById('dataForm:recommendationTable_scrollDiv'),document.getElementById('dataForm:recommendationTable_scrollDivBody'),document.getElementById('dataForm:recommendationTable_button'),true,1,1,'dataForm:recommendationTable','edit','yes','no','0','bottom','view',0,2147483647,'yes','no','even','odd','Invalid Table','dataForm:recommendationTable_selectedIdList','false','recommendationTable','true' ,0,8,'false','Synced','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
var dataForm_recommendationTable_r3="true";var dataForm_recommendationTable_defaultactioncolid="true";</script>
<script type="text/javascript">
UI8Layout.ondialogTableLoad("recommendationTable");
</script><input class="btn" id="dataForm:selectBtn" name="dataForm:selectBtn" onclick="setSelectedRecommendation();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:selectBtn','oncomplete':function(request,event,data){closeRecomDialog();},'parameters':{'dataForm:selectBtn':'dataForm:selectBtn'} } );return false;" value="Select" alt="Select" style="cursor:pointer" type="button" tabindex="48"><input class="btn" id="dataForm:recocancelBtn" name="dataForm:recocancelBtn" onclick="UI8Layout.doDialogCloseById('Recommendations_Dialog');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:recocancelBtn','parameters':{'dataForm:recocancelBtn':'dataForm:recocancelBtn'} } );return false;" value="Cancel" alt="Cancel" style="cursor:pointer" type="button" tabindex="49"><span class="groupBtnSpace">&nbsp;</span>
       	<script type="text/javascript">
		//// selectRowByIndex('recommendationTable','0');
		document.getElementById("checkAll_c_dataForm:recommendationTable").click();
	</script></span>

</span>
	                </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("Recommendations_Dialog",{onClose:"",dialogClientId:"dataForm:Recommendations_Dialog",dragHandleId:"dataForm:Recommendations_DialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:Recommendations_DialogTemplate_cCId"});
</script><div id="dataForm:TrailerDialog" name="dataForm:TrailerDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:trlrBrcdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Trailer</div></td><td><div class="pop_close" id="dataForm:trlrBrcdOuter_cCId"><input type="button" tabindex="50"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:400px;width:350px;"><span id="dataForm:trlrBrcdA4J"><div id="trlrBrcdDivInner"><script language="JavaScript" src="/appointment/lookup/js/trailerLookUp.js"></script><span id="dataForm:trlrFilter_script"></span>

		<input type="hidden" name="trailerTextName" tabindex="51"><div class="pnltopdiv" id="PANEL_trlrFilter__Search_Panel1_top"><table id="PANEL_trlrFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_trlrFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:trlrFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="trailerNameCap"><span title="">Trailer Name:</span><span class="notRequired">p</span><br><input id="dataForm:trailerName" type="text" name="dataForm:trailerName" value="*" tabindex="52"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="trailerBrcdCap"><span title="">Trailer Barcode:</span><span class="notRequired">p</span><br><input id="dataForm:trailerBarcode" type="text" name="dataForm:trailerBarcode" value="*" tabindex="53"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:trlrLkpBtn" name="dataForm:trlrLkpBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trlrLkpBtn','parameters':{'dataForm:trlrLkpBtn':'dataForm:trlrLkpBtn'} } );return false;" value="Find " type="button" tabindex="54"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:trlr_Detail"><div class="pnltopdiv" id="PANEL_trlr_Detail_Find_Result_pannel_top"><div id="tr_trlr_Detail_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:trlrGrid1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="trlr_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:trailerText" name="dataForm:trailerText" size="10" ondblclick="selectTrailer();return false;" style="width:50%" tabindex="55">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:trlr_SelectButton" name="dataForm:trlr_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trlr_SelectButton','oncomplete':function(request,event,data){selectTrailer();return false;},'parameters':{'dataForm:trlr_SelectButton':'dataForm:trlr_SelectButton'} } );return false;" value="Select" type="button" tabindex="56"></div></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("TrailerDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:TrailerDialog",dragHandleId:"dataForm:trlrBrcdOuter_dHId",onDialog:"",closeClientId:"dataForm:trlrBrcdOuter_cCId"});
</script><div id="dataForm:TractorDialog" name="dataForm:TractorDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:tractorOuter_dHId"><div class="pop_title -pdlg_dttc">Find Tractor</div></td><td><div class="pop_close" id="dataForm:tractorOuter_cCId"><input type="button" tabindex="57"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:400px;width:350px;"><span id="dataForm:tractorA4J"><div id="tractorDivInner"><script language="JavaScript" src="/appointment/lookup/js/tractorLookUp.js"></script><span id="dataForm:trlrFilter_script"></span>

		<input type="hidden" name="tractorTextName" tabindex="58"><div class="pnltopdiv" id="PANEL_trctrFilter__Search_Panel1_top"><table id="PANEL_trctrFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_trctrFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:trctrFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="tractorNameCap"><span title="">Tractor Name:</span><span class="notRequired">p</span><br><input id="dataForm:tractorName" type="text" name="dataForm:tractorName" value="*" tabindex="59"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:trctrLkpBtn" name="dataForm:trctrLkpBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trctrLkpBtn','parameters':{'dataForm:trctrLkpBtn':'dataForm:trctrLkpBtn'} } );return false;" value="Find " type="button" tabindex="60"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:trctr_Detail"><div class="pnltopdiv" id="PANEL_trctr_Detail_Find_Result_pannel_top"><div id="tr_trctr_Detail_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:trctrGrid1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="trctr_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:tractorText" name="dataForm:tractorText" size="10" ondblclick="selectTractor();return false;" style="width:50%" tabindex="61">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:trctr_SelectButton" name="dataForm:trctr_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trctr_SelectButton','oncomplete':function(request,event,data){selectTractor();return false;},'parameters':{'dataForm:trctr_SelectButton':'dataForm:trctr_SelectButton'} } );return false;" value="Select" type="button" tabindex="62"></div></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("TractorDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:TractorDialog",dragHandleId:"dataForm:tractorOuter_dHId",onDialog:"",closeClientId:"dataForm:tractorOuter_cCId"});
</script><div id="dataForm:NumRowsDialog" name="dataForm:NumRowsDialog" class="dialog_cont" style="height:200px;width:200px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:NumRowsIdOuter_dHId"><div class="pop_title -pdlg_dttc">Add multiple rows</div></td><td><div class="pop_close" id="dataForm:NumRowsIdOuter_cCId"><input type="button" tabindex="63"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:200px;width:200px;"><span id="dataForm:NumRowsA4J"><div id="NumRowsDivInner"><script language="JavaScript" src="/appointment/scripts/appointment.js"></script><span id="dataForm:numpop_script"></span>
	<br>
	<br>

	<div id="numOfRowsPanelPopupDiv"><div style="white-space:nowrap;vertical-align:inherit"><div id="numOfRowsPanelCap1" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><span id="dataForm:numOfRowsOut" class="captionData">Enter Rows</span></div><div style="white-space:nowrap;vertical-align:inherit"><div id="numOfRowsPanelCap2" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><input id="dataForm:numOfRows" type="text" name="dataForm:numOfRows" value="" maxlength="2" tabindex="64"></div></div>

	<br>

	<div id="numOfRowsPanelID" align="center"><input class="btn" id="dataForm:numOfRowsYes" name="dataForm:numOfRowsYes" onclick="addMultipleApptObjRow();return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:numOfRowsYes','parameters':{'dataForm:numOfRowsYes':'dataForm:numOfRowsYes'} } );return false;" value="Yes" alt="Yes" style="cursor:pointer" type="button" tabindex="65"><input class="btn" id="dataForm:numOfRowsNo" name="dataForm:numOfRowsNo" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:numOfRowsNo','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('NumRowsDialog');},'parameters':{'dataForm:numOfRowsNo':'dataForm:numOfRowsNo'} } );return false;" value="Cancel" alt="No" style="cursor:pointer" type="button" tabindex="66"></div></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("NumRowsDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:NumRowsDialog",dragHandleId:"dataForm:NumRowsIdOuter_dHId",onDialog:"",closeClientId:"dataForm:NumRowsIdOuter_cCId"});
</script><div id="dataForm:apptDockInnerDialog" name="dataForm:apptDockInnerDialog" class="dialog_cont" style="height:400px;width:720px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:apptdockIdInOuter_dHId"><div class="pop_title -pdlg_dttc">FindDock</div></td><td><div class="pop_close" id="dataForm:apptdockIdInOuter_cCId"><input type="button" tabindex="67"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg"><span id="dataForm:apptDockInnA4J"><div id="apptDockInDivInner"><script language="JavaScript" src="/appointment/lookup/js/ILMDockLookUpInner.js"></script><span id="dataForm:dockInnerFilter_script"></span>

		<input type="hidden" name="dockInnerTextName" id="dockInnerTextName" tabindex="68"><div class="pnltopdiv" id="PANEL_apptDockInnerFilter__Search_Panel1_top"><table id="PANEL_apptDockInnerFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_apptDockInnerFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:apptDockInnerFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="apptDockInnerFacCap"><span title="">Facility:</span><span class="required" id="apptDockInnerFacCap_cptnSpn">*</span><br><script language="JavaScript" src="/lps/resources/editControl/scripts/idLookup.js"></script><script language="JavaScript" src="/lps/resources/editControl/scripts/autocompleteinput.js"></script><script language="JavaScript">var isDemo=false</script><input type="hidden" id="dataForm:apptDockInnerFacilityecId" name="dataForm:apptDockInnerFacilityecId" value="" tabindex="69"><input type="hidden" id="dataForm_apptDockInnerFacility_enterKey" value="false" tabindex="70"><input type="hidden" id="triggerdataForm_apptDockInnerFacility_enterKey" value="false" tabindex="71"><input type="text" id="dataForm:apptDockInnerFacility" name="dataForm:apptDockInnerFacility" onfocus="javascript: focusOnTextBox('dataForm_apptDockInnerFacility_enterKey')" onblur="javascript: blurOnTextBox('dataForm_apptDockInnerFacility_enterKey')" onkeypress="if(enterPressed(event,'dataForm:apptDockInnerFacility') )return false;" value="" title="" alt="Find facility" tabindex="72">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcboFilterLookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=&amp;lookupType=Facility&amp;is3plEnabled=false&amp;returnId=dataForm:apptDockInnerFacility&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:apptDockInnerFacility" title="Find facility" align="absmiddle" id="trigger_dataForm:apptDockInnerFacility" name="trigger_dataForm:apptDockInnerFacility" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_apptDockInnerFacility_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_apptDockInnerFacility_enterKey')" tabindex="73"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="apptDockInnerCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:apptDockInnerSearch" type="text" name="dataForm:apptDockInnerSearch" value="*" tabindex="74"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:apptDockInnerFilter_locgetlist" name="dataForm:apptDockInnerFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptDockInnerFilter_locgetlist','parameters':{'dataForm:apptDockInnerFilter_locgetlist':'dataForm:apptDockInnerFilter_locgetlist'} } );return false;" value="Find " type="button" tabindex="75"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:apptDockInnerFilter_Detail"><div class="pnltopdiv" id="PANEL_apptDockInnerFilter_Find_Result_pannel_top"><div id="tr_apptDockInnerFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:apptDockInnerFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="apptDockInnerFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:apptDockInnerText" name="dataForm:apptDockInnerText" size="5" ondblclick="selectDockInner();return false;" style="width:50%" tabindex="76">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:apptDockInnerFilter_SelectButton" name="dataForm:apptDockInnerFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptDockInnerFilter_SelectButton','oncomplete':function(request,event,data){selectDockInner();return false;},'parameters':{'dataForm:apptDockInnerFilter_SelectButton':'dataForm:apptDockInnerFilter_SelectButton'} } );return false;" value="Select" type="button" tabindex="77"></div></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("apptDockInnerDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:apptDockInnerDialog",dragHandleId:"dataForm:apptdockIdInOuter_dHId",onDialog:"",closeClientId:"dataForm:apptdockIdInOuter_cCId"});
</script><div id="dataForm:RecurrenceWindow" name="dataForm:RecurrenceWindow" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:j_id146_dHId"><div class="pop_title -pdlg_dttc">Recurrence Pattern</div></td><td><div class="pop_close" id="dataForm:j_id146_cCId"><input type="button" tabindex="78"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:570px;"><div id="j_id147" style=" margin-left:20px; ">
				<br>
				<b>Daily <input type="checkbox" id="popup_daily" onchange="dailyChanged()" tabindex="79"></b>
				<br>every <input id="dataForm:popup_frequencyInDays" type="text" name="dataForm:popup_frequencyInDays" value="" tabindex="80"> days
				<br>
				<br>
				<b>Weekly <input type="checkbox" id="popup_weekly" onchange="weeklyChanged()" tabindex="81"></b>
				<br>Sunday <input type="checkbox" id="popup_weekly_sunday" tabindex="82">   Monday <input type="checkbox" id="popup_weekly_monday" tabindex="83">   Tuesday <input type="checkbox" id="popup_weekly_tuesday" tabindex="84">   Wednesday <input type="checkbox" id="popup_weekly_wednesday" tabindex="85">   Thursday <input type="checkbox" id="popup_weekly_thursday" tabindex="86">   Friday <input type="checkbox" id="popup_weekly_friday" tabindex="87">   Saturday <input type="checkbox" id="popup_weekly_saturday" tabindex="88">
				<br>
				<br>
				<b>Monthly </b> <input type="checkbox" id="popup_monthly" onchange="monthlyChanged()" tabindex="89">
				<br>
				<br>
				<b>Recurrence Range </b>
				<br>Start date<span style="color:red;">*  </span><script language="JavaScript" src="/lps/resources/calendar/scripts/calendar.js"></script><script language="JavaScript" src="/lps/resources/calendar/scripts/calendar-setup.js"></script><script language="JavaScript" src="/lps/resources/calendar/lang/calendar-en.js"></script><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="90"><input type="text" id="dataForm:popup_recurrenceStartTime" name="dataForm:popup_recurrenceStartTime" value="" alt="" size="" tabindex="91">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:popup_recurrenceStartTime" name="dataForm:popup_recurrenceStartTime_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="92" onclick="dataFormwpopup_recurrenceStartTimecalendarSetUp(this.name);return false">
<script type="text/javascript">
function dataFormwpopup_recurrenceStartTimecalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "false", 		dropDownIns  :  ''
	}
)
}
</script>
          End date<span style="color:red;">*  </span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="93"><input type="text" id="dataForm:popup_recurrenceEndTime" name="dataForm:popup_recurrenceEndTime" value="" alt="" size="" tabindex="94">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:popup_recurrenceEndTime" name="dataForm:popup_recurrenceEndTime_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="95" onclick="dataFormwpopup_recurrenceEndTimecalendarSetUp(this.name);return false">
<script type="text/javascript">
function dataFormwpopup_recurrenceEndTimecalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "false", 		dropDownIns  :  ''
	}
)
}
</script>

				<br>
				<br>No end date  <input type="checkbox" id="popup_noEndDate" onchange="noEndDateChange()" tabindex="96">
				<br>
				<br><input type="submit" name="dataForm:j_id193" value="Save" onclick="saveTemplate()" class="btn" tabindex="97">   <input type="button" name="dataForm:j_id195" value="Cancel" onclick="closeTemplate()" class="btn" tabindex="98">
				<br>   </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("RecurrenceWindow",{onClose:"",dialogClientId:"dataForm:RecurrenceWindow",dragHandleId:"dataForm:j_id146_dHId",onDialog:"",closeClientId:"dataForm:j_id146_cCId"});
</script>
	<script>
		function noEndDateChange()
		{
			if ( jQuery('input[id*="popup_noEndDate"]')[0].checked )
			{
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].value ="" ;
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].disabled = true;
				jQuery('input[id*="popup_recurrenceEndTime"]')[1].disabled = true;
			}
			else
			{
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].disabled = false;
				jQuery('input[id*="popup_recurrenceEndTime"]')[1].disabled = false;
			}
		}
		function weeklyChanged()
		{
			if ( jQuery('input[id*="popup_weekly"]')[0].checked )
			{
				enableWeeklyOptions();
				checkWeeklyOptions();

				jQuery('input[id*="popup_daily"]')[0].checked = false;
				disableFrequency();
				jQuery('input[id*="popup_monthly"]')[0].checked = false;
			}
			else
			{
				uncheckWeeklyOptions();
			}
		}

		function uncheckWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.checked = false;
			});
		}
		function disableWeeklyOptions()
		{
			uncheckWeeklyOptions();
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.disabled = true;
			});
		}
		function enableWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.disabled = false;
			});
		}
		function checkWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.checked = true;
			});
		}
		function dailyChanged()
		{
			if ( jQuery('input[id*="popup_daily"]')[0].checked )
			{
				jQuery('input[id*="popup_frequencyInDays"]')[0].disabled = false;

				jQuery('input[id*="popup_monthly"]')[0].checked = false;
				jQuery('input[id*="popup_weekly"]')[0].checked = false;
				disableWeeklyOptions();
			}
		}
		function disableFrequency()
		{
			jQuery('input[id*="popup_frequencyInDays"]')[0].disabled = true;
			jQuery('input[id*="popup_frequencyInDays"]')[0].value = "";
		}
		function monthlyChanged()
		{
			if ( jQuery('input[id*="popup_monthly"]')[0].checked )
			{
				jQuery('input[id*="popup_weekly"]')[0].checked = false;
				jQuery('input[id*="popup_daily"]')[0].checked = false;
				disableFrequency();
				disableWeeklyOptions();
			}
		}

		function copyBackValuesToAppointmentPage()
		{
			jQuery('input[id*="recurring_saveFromRecurringWindow"]')[0].value = true;

			jQuery('input[id*="recurring_daily"]')[0].value = jQuery('input[id*="popup_daily"]')[0].checked ;
			jQuery('input[id*="recurring_frequencyInDays"]')[0].value = jQuery('input[id*="popup_frequencyInDays"]')[0].value ;
			jQuery('input[id*="recurring_weekly"]')[0].value = jQuery('input[id*="popup_weekly"]')[0].checked;
			jQuery('input[id*="recurring_sunday"]')[0].value = jQuery('input[id*="popup_weekly_sunday"]')[0].checked;
			jQuery('input[id*="recurring_monday"]')[0].value = jQuery('input[id*="popup_weekly_monday"]')[0].checked;
			jQuery('input[id*="recurring_tuesday"]')[0].value = jQuery('input[id*="popup_weekly_tuesday"]')[0].checked;
			jQuery('input[id*="recurring_wednesday"]')[0].value = jQuery('input[id*="popup_weekly_wednesday"]')[0].checked ;
			jQuery('input[id*="recurring_thursday"]')[0].value = jQuery('input[id*="popup_weekly_thursday"]')[0].checked ;
			jQuery('input[id*="recurring_friday"]')[0].value = jQuery('input[id*="popup_weekly_friday"]')[0].checked ;
			jQuery('input[id*="recurring_saturday"]')[0].value = jQuery('input[id*="popup_weekly_saturday"]')[0].checked
			jQuery('input[id*="recurring_monthly"]')[0].value = jQuery('input[id*="popup_monthly"]')[0].checked ;
			jQuery('input[id*="recurring_StartDate"]')[0].value = jQuery('input[id*="popup_recurrenceStartTime"]')[0].value ;
			jQuery('input[id*="recurring_EndDate"]')[0].value = jQuery('input[id*="popup_recurrenceEndTime"]')[0].value ;
		}

		function saveTemplate()
		{
			copyBackValuesToAppointmentPage();
			UI8Layout.doDialogCloseById('RecurrenceWindow');
		}
		function closeTemplate()
		{
			UI8Layout.doDialogCloseById('RecurrenceWindow');
		}
	</script>


		<script language="JavaScript">
			var msgcheckDefaultFilter="Default filter already exist for this Object Type. Do you want to override?";
			var msgDeleteFilters="Are you sure that you want to delete selected filters?";
			var msgSelectFilterToDelete="Please select at least one filter to delete";
			var expandedTlTp="Expand";
			var collapseTlTp="Collapse";
		</script><div id="dataForm:filterList" name="dataForm:filterList" class="dialog_cont" style="height:460px;width:800px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_11_dHId"><div class="pop_title -pdlg_dttc">Saved Filters</div></td><td><div class="pop_close" id="dataForm:bdt_11_cCId"><input type="button" tabindex="99"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="width:800px;">


			<div class="popcon" id="filterListContainer">

			<div id="filterListImagecontainer" align="center">
					<br><br><br><br><br><img id="dataForm:animationGenId_start_filter" src="/lps/resources/menu/ribbon/images/loading_animation_liferay.gif">
					<br><br><br><br><br>
			</div>


				<div id="ajaxContainer"><span id="dataForm:filterDetailAJAXPanel"></span>

				</div>

			</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterList",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:filterList",dragHandleId:"dataForm:bdt_11_dHId",onDialog:"",closeClientId:"dataForm:bdt_11_cCId"});
</script><div id="dataForm:filterListFilter" name="dataForm:filterListFilter" class="dialog_cont" style="height:200px;width:350px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_flf_dHId"><div class="pop_title -pdlg_dttc">Filter List Filter</div></td><td><div class="pop_close" id="dataForm:bdt_flf_cCId"><input type="button" tabindex="100"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="width:340px;"><span id="dataForm:filterListFilterPanel">

		<script language="JavaScript">
			var filterListErrMsg1="Filter condition cannot be blank";
			var filterListErrMsg2="Wild card (*) not supported. Please enter different filter criteria";
			var filterListErrMsg3="Filter Criteria cannot be blank - please enter the search criteria";
		</script>&nbsp;&nbsp;<span id="dataForm:filterTitle" class="boldFont">Criteria: </span>
		<br>
		<br><div id="fltrDivId"><input id="dataForm:fltrListFltrId:fieldName" type="hidden" name="dataForm:fltrListFltrId:fieldName" tabindex="101"><input id="dataForm:fltrListFltrId:filterName" type="hidden" name="dataForm:fltrListFltrId:filterName" value="FL_PREPAID" tabindex="102"><input id="dataForm:fltrListFltrId:owner" type="hidden" name="dataForm:fltrListFltrId:owner" value="PREPAID" tabindex="103"><input id="dataForm:fltrListFltrId:objectType" type="hidden" name="dataForm:fltrListFltrId:objectType" value="FL_FILTER" tabindex="104"><input id="dataForm:fltrListFltrId:filterObjectType" type="hidden" name="dataForm:fltrListFltrId:filterObjectType" tabindex="105"><input id="dataForm:fltrListFltrId:field0value1" type="hidden" name="dataForm:fltrListFltrId:field0value1" value="" tabindex="106"><input id="dataForm:fltrListFltrId:field0" type="hidden" name="dataForm:fltrListFltrId:field0" value="FILTER.FILTER_NAME" tabindex="107"><input id="dataForm:fltrListFltrId:field0operator" type="hidden" name="dataForm:fltrListFltrId:field0operator" value="" tabindex="108"><input id="dataForm:fltrListFltrId:field1value1" type="hidden" name="dataForm:fltrListFltrId:field1value1" value="" tabindex="109"><input id="dataForm:fltrListFltrId:field1" type="hidden" name="dataForm:fltrListFltrId:field1" value="FILTER.IS_DEFAULT" tabindex="110"><input id="dataForm:fltrListFltrId:field1operator" type="hidden" name="dataForm:fltrListFltrId:field1operator" value="" tabindex="111"><input id="dataForm:fltrListFltrId:field2value1" type="hidden" name="dataForm:fltrListFltrId:field2value1" value="" tabindex="112"><input id="dataForm:fltrListFltrId:field2" type="hidden" name="dataForm:fltrListFltrId:field2" value="FILTER.IS_PRIVATE" tabindex="113"><input id="dataForm:fltrListFltrId:field2operator" type="hidden" name="dataForm:fltrListFltrId:field2operator" value="" tabindex="114"><input id="dataForm:fltrListFltrId:field3value1" type="hidden" name="dataForm:fltrListFltrId:field3value1" value="" tabindex="115"><input id="dataForm:fltrListFltrId:field3" type="hidden" name="dataForm:fltrListFltrId:field3" value="FILTER.OWNER" tabindex="116"><input id="dataForm:fltrListFltrId:field3operator" type="hidden" name="dataForm:fltrListFltrId:field3operator" value="" tabindex="117"><input id="dataForm:fltrListFltrId:field4value1" type="hidden" name="dataForm:fltrListFltrId:field4value1" value="" tabindex="118"><input id="dataForm:fltrListFltrId:field4" type="hidden" name="dataForm:fltrListFltrId:field4" value="FILTER.IS_DELETED" tabindex="119"><input id="dataForm:fltrListFltrId:field4operator" type="hidden" name="dataForm:fltrListFltrId:field4operator" value="" tabindex="120"><table id="dataForm:fltrListFltrId:j_id4363" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Condition:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:fltrListFltrId:fltrCondition" name="dataForm:fltrListFltrId:fltrCondition" class="fltrSelMenuStyle" size="1" tabindex="121">	<option value="" selected="selected"></option>
	<option value="Equals">Equals</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Filter:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:fltrListFltrId:fltrCrtSel" name="dataForm:fltrListFltrId:fltrCrtSel" class="fltrSelMenuStyle" size="1" tabindex="122">	<option value="" selected="selected"></option>
	<option value="1">Yes </option>
	<option value="0">No</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:fltrListFltrId:j_id4363","_u8_gl_1",{layout:"v"});
</script></div>

		<br><input class="btn" id="dataForm:clearButton" name="dataForm:clearButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:clearButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:clearButton':'dataForm:clearButton','reRenderParent':''} } );return false;" value="Clear Filter" type="button" tabindex="123">&nbsp;&nbsp;<input class="btn" id="dataForm:applyAndSaveButton" name="dataForm:applyAndSaveButton" onclick="if(!doValTrimming()){ return false;};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:applyAndSaveButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'filterScreenType':'ON_SCREEN','dataForm:fltrListFltrId:filterId':'','fltrClientId':'dataForm:fltrListFltrId','fltrListFltr':'true','defaultChanged':'1','isJSF':'true','reRenderParent':'','dataForm:applyAndSaveButton':'dataForm:applyAndSaveButton'} } );return false;" value="Save and Apply" type="button" tabindex="124">&nbsp;&nbsp;<input class="btn" id="dataForm:applyButton" name="dataForm:applyButton" onclick="if(!doValTrimming()){ return false;};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:applyButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:applyButton':'dataForm:applyButton','fltrClientId':'dataForm:fltrListFltrId','fltrListFltr':'true','isJSF':'true','reRenderParent':''} } );return false;" value="Apply" type="button" tabindex="125">&nbsp;&nbsp;<input class="btn" id="dataForm:cancelButton" name="dataForm:cancelButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:cancelButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:cancelButton':'dataForm:cancelButton','reRenderParent':''} } );return false;" value="Cancel" type="button" tabindex="126">

		<script>
    	//<![CDATA[

    		function filterListOnClick(event){
				var event = ( event ) ? event : window.event;
				if(window.event)
				{
					window.event.cancelBubble = true;
				}
				if(event.cancelBubble)
				{
					event.cancelBubble = true;
				}
				if(event.stopPropagation)
				{
					event.stopPropagation();
				}
			}

    		function onComplete(){
    			UI8Layout.doDialogCloseById('filterListFilter');
    			resizeWidth('filterListTable');
    		}

			function doValTrimming(){

    			var fldName = eByID('dataForm:fltrListFltrId:fieldName');
    			var selMenu= eByID('dataForm:fltrListFltrId:fltrCondition');

    			if(fldName && fldName.value=='FILTER.FILTER_NAME')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field0value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field0operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtText');
    			}
    			else if(fldName && fldName.value=='FILTER.OWNER')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field3value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field3operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtText');
    			}
    			else if(fldName && fldName.value=='FILTER.IS_DEFAULT')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field1value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field1operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
	    		}
    			else if(fldName && fldName.value=='FILTER.IS_PRIVATE')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field2value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field2operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
	    		}
    			else
    			{
    				var fld= eByID('dataForm:fltrListFltrId:field4value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field4operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
    			}

    			if(selMenu && selMenu.value=='')
    			{
    				alert(filterListErrMsg1);
    				return false;
    			}
    			if(crtText && crtText.value.indexOf("*")!=-1)
    			{
    				alert(filterListErrMsg2);
     				return false;
     			}

     			if(selMenu && selMenu.value=='Contains' && crtText && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0)
    					{
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Begins with' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Ends with' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" +trim(splitArr[i]);
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Does Not Contains' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "!=";
    			}
    			else if(selMenu && crtText && fldOper && selMenu.value=='Equals' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr  + trim(splitArr[i]) ;
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && fldOper && selMenu.value=='Does Not Equals' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr  + trim(splitArr[i]) ;
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "!=";
    			}
	   			if(crtText!= null && trim(crtText.value)!='')
	   			{
	   				return true;
	   			}
	   			else
	   			{
	   				alert(filterListErrMsg3);
	   				return false;
	   			}
    		}


   		function trim(str, chars) {
		return ltrim(rtrim(str, chars), chars);
		}

		function ltrim(str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("^[" + chars + "]+", "g"), "");
		}

		function rtrim(str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("[" + chars + "]+$", "g"), "");
		}

    	function checkForEnter() {
		     if(window.event.keyCode == 13){
		     	  window.location.reload();
		     }
		}


    	//]]>
	   </script></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterListFilter",{onClose:"",dialogClientId:"dataForm:filterListFilter",dragHandleId:"dataForm:bdt_flf_dHId",onDialog:"",closeClientId:"dataForm:bdt_flf_cCId"});
</script><div id="dataForm:advanced_Filter_Dialog" name="dataForm:advanced_Filter_Dialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:advanced_Filter_BasicDialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Advance Filter</div></td><td><div class="pop_close" id="dataForm:advanced_Filter_BasicDialogTemplate_cCId"><input type="button" tabindex="127"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg">
				<div class="popcon">
					<iframe id="advanced_Filter_iframeId" frameborder="0" scrolling="no" width="800px" height="700px">
					</iframe>
				</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("advanced_Filter_Dialog",{onClose:"",dialogClientId:"dataForm:advanced_Filter_Dialog",dragHandleId:"dataForm:advanced_Filter_BasicDialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:advanced_Filter_BasicDialogTemplate_cCId"});
</script>

		<script language="JavaScript">
			var alertMsg1="Filter Name is required";
			var filterDetailHeight = document.documentElement.clientHeight;
			var expandedTlTp="Expand";
			var collapseTlTp="Collapse";
		</script><div id="dataForm:filterDetail" name="dataForm:filterDetail" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_222_dHId"><div class="pop_title -pdlg_dttc">Filter Details</div></td><td><div class="pop_close" id="dataForm:bdt_222_cCId"><input type="button" tabindex="128"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg"><span id="dataForm:editfilterDetailAJAXPanel">

				<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy3_clear" onclick="return false;" tabindex="129">
					<script>
						if(document.getElementById("filterDetailOutDiv")){
							if((filterDetailHeight-150)>0 )
								document.getElementById("filterDetailOutDiv").style.maxHeight=(filterDetailHeight-150)+"px";
								document.getElementById("filterDetailOutDiv").style.padding=10+"px";
						}
					</script></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterDetail",{onClose:"UIFilterJS.onFilterDtlClose",dialogClientId:"dataForm:filterDetail",dragHandleId:"dataForm:bdt_222_dHId",onDialog:"",closeClientId:"dataForm:bdt_222_cCId"});
</script><span id="dataForm:editfilterDetailAJAXPanelScript"></span><div id="dataForm:editQuickFilterDialog" name="dataForm:editQuickFilterDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:editQuickFilterTemplate_dHId"><div class="pop_title -pdlg_dttc">Edit Quick Filter</div></td><td><div class="pop_close" id="dataForm:editQuickFilterTemplate_cCId"><input type="button" tabindex="130"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg"><span id="dataForm:editQuickFilterAjaxPopup"></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("editQuickFilterDialog",{onClose:"",dialogClientId:"dataForm:editQuickFilterDialog",dragHandleId:"dataForm:editQuickFilterTemplate_dHId",onDialog:"",closeClientId:"dataForm:editQuickFilterTemplate_cCId"});
</script>

    </div>
    <script>
    //<![CDATA[
        jQuery('#drag_1').css('z-index', UI8Layout.dialogZ);
    //]]>
    </script><div id="dataForm:printDialog" name="dataForm:printDialog" class="dialog_cont" style="height:460px;width:800px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/images/foPrint.gif"></div></td><td class="pop_dragHandler" id="dataForm:printTemp_dHId"><div class="pop_title -pdlg_dttc">Print Dialog</div></td><td><div class="pop_close" id="dataForm:printTemp_cCId"><input type="button" tabindex="131"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:420px;width:780px;">
			<iframe name="printFrame" id="printFrame" style="visibility:hidden;display:none;width:100%;height:100%">
			</iframe></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("printDialog",{onClose:"",dialogClientId:"dataForm:printDialog",dragHandleId:"dataForm:printTemp_dHId",onDialog:"",closeClientId:"dataForm:printTemp_cCId"});
</script>
    <div style="position: absolute; background-color: rgb(0, 0, 0); opacity: 0; z-index: 9999999; height: 0px; width: 0px; left: 0px; top: 0px;" id="overlapLayer">
    </div>
    <div id="main">
    <table border="0" width="100%" cellpadding="0" cellspacing="0" id="maintable">
        <tbody><tr>
            <td>
		         <div id="popmenu" class="menuskin_UI08"></div>
		         <script language="javascript">;
		         //<![CDATA[
		             var oPopup = document.getElementById( "popmenu" );
		         //]]>
		         </script><div id="j_id256" style="padding:0px; margin:0px; border:0px;">
		         <div id="toolbar" style="width: auto; overflow: hidden;">
     <script type="text/javascript">
           //<![CDATA[

        function suspendMenuClose(evt)
        {
            if(evt.button == 2 || evt.which == 3)
                UI8Layout.setSuspendClose(false);
            return true;
        }
        function showFilter() {
            var filterid = getFilterId();
            if (filterid) {
                var fltricon = document.getElementById("mpsFltrImg");
                var fltrlink = document.getElementById("mpsFltrLink");
                var filter = document.getElementById(filterid);
                var cookie = getCookieValue('filterExpandState');
                if (fltricon && fltrlink) {
                    fltricon.style.display = 'block';
                    fltrlink.style.display = 'block';
                }
                if (cookie) {
                    if (cookie == "true") {
                        filter.style.display = "block";
                    } else {
                        filter.style.display = "none";
                    }
                } else {
                    filter.style.display = "block";
                    if (top != self) {
                        parent.document.cookie = "filterExpandState=true";
                    } else {
                        document.cookie = "filterExpandState=true";
                    }
                }
            }
        }

        function toggleFilter() {
            var filterid = getFilterId();
            var filter = document.getElementById(filterid);
            var cookie = getCookieValue('filterExpandState');
            if (filter.style.display == "none") {
                filter.style.display = "block";
                if (top != self) {
                    parent.document.cookie = "filterExpandState=true";
                } else {
                    document.cookie = "filterExpandState=true";
                }
            } else {
                filter.style.display = "none";
                if (top != self) {
                    parent.document.cookie = "filterExpandState=false";
                } else {
                    document.cookie = "filterExpandState=false";
                }
            }

        }

        function getCookieValue(key) {
            var currentcookie;
            if (top != self) {
                currentcookie = parent.document.cookie;
            } else {
                currentcookie = document.cookie;
            }
            if (currentcookie.length > 0) {
                firstidx = currentcookie.indexOf(key + "=");
                if (firstidx != -1) {
                    firstidx = firstidx + key.length + 1;
                    lastidx = currentcookie.indexOf(";", firstidx);
                    if (lastidx == -1) {
                        lastidx = currentcookie.length;
                    }
                    return currentcookie.substring(firstidx, lastidx);
                }
            }
            return "";
        }

        function getFilterId() {
            var ids = [], i = 0;
            jQuery("table.fltr_tbl").each(
                    function(idx, el) {
                        if (jQuery(el).parents("div.tabpnlout").length === 0) {
                            ids[i++] = el.parentNode.id;
                        }
                    });
            if (ids.length == 1) {
                return ids[0];
            }
            return null;
        }


    //]]>
    </script>


    <table class="pghdrmps -hft_tis" id="pghdr" cellspacing="0" cellpadding="0">
        <tbody><tr>
            <td class="pghdrrow">
            <table class="hdrt" cellspacing="0">
                <tbody><tr>
                      <td class="hdrto"><img id="backImage" src="/lps/resources/layout/images/backdnew.png">
                      </td>

                    <td class="hdrto" id="m_act_td"><a hidefocus="true" id="Actions" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" accesskey="A" class="mo -gbl_hhc -md_mhbc">
                    <span class="ul menulbl -hft_pht">A</span><span class="menulbl -hft_pht">ctions</span><span class="moimg -icons_mopt">&nbsp;</span> </a>


                    </td>
                    <td class="hdrto" id="m_tools_td"><a hidefocus="true" class="mo -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="Tools" accesskey="T">
                    <span class="ul menulbl -hft_pht">T</span><span class="menulbl -hft_pht">ools</span><span class="moimg -icons_mopt">&nbsp;</span></a>

                    </td>

                    <td class="hdrto" id="m_roles_td" style="display:none"><a hidefocus="true" class="mo -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="Roles">
                    <span class="menulbl -hft_pht">Role:</span><span class="name -hft_pht"></span><span class="moimg -icons_mopt">&nbsp;</span></a>

                    </td>

                    <td class="hdrtw" id="m_tools_welcome"></td>


                  <td class="phtopicon2"><a hidefocus="true" id="mpsFltrLink" class="btn" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" style="display: block;" onclick="javascript:toggleFilter();"><img id="mpsFltrImg" style="display: block;" src="/manh/mps/resources/icons/funnel.png" alt="Filter"></a> </td>


                    <td style="display:none; border:0px;"> <span id="titlePage"><div class="header-left"><span id="dataForm:hLabel" class="sectionheader">Appointments</span></div></span>
                    </td>

                    <td></td>

                </tr>
            </tbody></table>

            </td>
        </tr>
    </tbody></table>

    <div id="phMenu_child" class="menuskin_UI08" onmousedown="suspendMenuClose(event); return true;">
        <div class="mpop -md_obg -md_obc" id="mainMenu">
            <div class="mpopsdw -pdlg_sdw"></div>
            <div class="mpopfnd1 -md_obg" id="mnfind_1"></div>
            <div class="mpopclose"><img src="/lps/resources/dialogControl/images/close.png" title="Close" alt="Close" onclick="menuClose();"></div>
            <div class="mpophdr -md_obg"></div>
            <div class="mpopbdr -md_obg">
                <div class="mpopbody -tbs_tbc -tbs_tbgc">
                </div>
            </div>
        </div>
    </div>
    <div id="Actions_child" class="menuskin_UI08" style="top: 25px; left: 32px; display: none;"><div id="foACTIONS_MENU" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_1_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_1_menuItemBtn');}" title="Add"><img src="/lps/resources/menu/images/clear.gif">Add</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_2_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_2_menuItemBtn');}" title="Edit"><img src="/lps/resources/menu/images/clear.gif">Edit</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_5_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_5_menuItemBtn');}" title="Approve"><img src="/lps/resources/menu/images/clear.gif">Approve</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif">Validate</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif">Recommend Time Slots</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif">Save</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif">Cancel</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_18_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_18_menuItemBtn');}" title="Email"><img src="/lps/resources/menu/images/clear.gif">Email</a></li></ul></div></div></div>
    <div id="Tools_child" class="menuskin_UI08" style="top: 25px; left: 103.633px; display: none;"><div id="foTools" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="ASNList_Menu_Item_Reports_toolsItem" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('ASNList_Menu_Item_Reports_toolsItemBtn');}" title="Report"><img src="/lps/resources/themes/icons/mablue/report.gif">Report</a></li></ul><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="printOrd_toolsItem" onclick="printPage()" title="Print"><img src="/lps/resources/themes/icons/mablue/foPrint.gif">Print</a></li></ul></div></div></div>
    <div id="Roles_child" class="menuskin_UI08" style="top: -1px; left: 0px; display: none;"></div>
    <div id="MA_child" class="menuskin_UI08">
            <div class="mpop -md_obg" id="MA_child_cont" style="position:static">
            <div class="mpopsdw -pdlg_sdw"></div>

        <div id="favappmenuappcontid" class="favappmenuappcont">

        </div>
        <div class="mpopclose"><img src="/lps/resources/themes/icons/mablue/pinfavapp.png" onclick="UI8Layout.setSuspendClose(true); menuClose();UI8Layout.Menu.AppMgr.SideNavHandler.start();"></div>

            </div>
    </div>



    <script type="text/javascript">
           //<![CDATA[
               (function () {
                   var Mb = UI8Layout.MenuBar;

                   Mb.addItem('Actions',
                      {popupId: 'Actions_child', postOpen: 'AXNPostOpen', preOpen: null});

                   Mb.addItem('Tools',
                      {popupId: 'Tools_child', postOpen: 'ToolsPostOpen', preOpen: null});

                   Mb.addItem('Roles',
                           {popupId: 'Roles_child', postOpen: 'RolesPostOpen', preOpen: null});


                           })();

           //]]>
    </script>
		         </div></div><input id="windowId" name="windowId" type="hidden" value="wt-c133-ad04-84d0" tabindex="132">
            </td>
        </tr>
        <tr>
            <td valign="top">
            <table width="100%" border="0" cellpadding="0" cellspacing="0">
                <tbody><tr id="crowid" style="height: 525px;">
                    <td id="navaredtd" width="1px" valign="top" style="display: none;">
                    <div id="navarea" class="navarea" style="display: none;">

                    </div>
                    </td>
                    <td id="workareatd" valign="top" class="workareatd -gbl_bbg">
		<table width="100%" cellpadding="0" cellspacing="0">
			<tbody><tr>
				<td id="workareahdrtd" class="workareahdrtd">
					<div id="workareaheader" class="workareaheader" style="display: none;">

						<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy1_clear" onclick="return false;" tabindex="133">
							<script>
								//<![CDATA[
								UI8Layout.hideheader = true;
								eByID(UI8Layout.HEADER_DIV_ID).style.display = 'none';
								//]]>
							</script>
					</div>
				</td>
			</tr>
			<tr>
				<td id="workareanavtd" class="workareanavtd">
					<div id="workareanavigation" class="workareanav" style="display: none;">
							<script>
								//<![CDATA[
								UI8Layout.hidenavigation = true;
								eByID(UI8Layout.NAVIGATION_DIV_ID).style.display = 'none';
								//]]>
							</script>
					</div>
				</td>
			</tr>
			<tr>
				<td id="workareacontenttd" class="workareacontenttd" style="height: 463px;">
					<div id="workarea" class="workareamain" style="height: 463px; width: 990px;">

						<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy2_clear" onclick="return false;" tabindex="134">

		<script type="text/javascript">
                        var messageArray = new Array(10);
                        messageArray[0] = 'Please\x20select\x20a\x20row';
                        messageArray[1] = 'Invalid\x20Appointment\x20Status\x20to\x20approve\x20the\x20Appointment.';
                        messageArray[2] = 'Invalid\x20Appointment\x20Status\x20to\x20check\x2Din\x20the\x20Appointment.';
                        messageArray[3] = 'Multiple\x20selection\x20not\x20allowed\x20for\x20this\x20action.';
                        messageArray[4] = 'Do\x20you\x20want\x20to\x20delete\x20this\x20row\x3F';
                        messageArray[5] = 'No\x20PO\x2FShipment\x2FASN\x20on\x20Appointment.Do\x20you\x20want\x20to\x20save\x20it\x20as\x20Blind\x20Appointment\x3F';
                        messageArray[6] = 'Appointment\x20Status\x20should\x20be\x20Scheduled\x20or\x20Checked\x20In\x20for\x20Appointment\x20Leveling\x20Analysis';
                        messageArray[7] = 'Appointment\x20Should\x20Be\x20of\x20Unload\x20Type';
                        messageArray[8] = 'Reject\x20is\x20applicable\x20for\x20checked\x2Din\x20appointments.\x20Invalid\x20Appointment\x20status\x20to\x20Reject';
                  </script><script language="JavaScript" src="/appointment/scripts/appointment.js"></script><span id="dataForm:src123"></span><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:ribbonMenuPane1"><input type="hidden" name="targetLink" id="targetLink" value=""><div id="Actions_div" style="display:none"><div id="foACTIONS_MENU" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_1_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_1_menuItemBtn');}" title="Add"><img src="/lps/resources/menu/images/clear.gif">Add</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_2_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_2_menuItemBtn');}" title="Edit"><img src="/lps/resources/menu/images/clear.gif">Edit</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_5_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_5_menuItemBtn');}" title="Approve"><img src="/lps/resources/menu/images/clear.gif">Approve</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif">Validate</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif">Recommend Time Slots</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif">Save</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif">Cancel</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_18_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_18_menuItemBtn');}" title="Email"><img src="/lps/resources/menu/images/clear.gif">Email</a></li></ul></div></div></div><div id="Tools_div" style="display:none"><div id="foTools" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="ASNList_Menu_Item_Reports_toolsItem" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('ASNList_Menu_Item_Reports_toolsItemBtn');}" title="Report"><img src="/lps/resources/themes/icons/mablue/report.gif">Report</a></li></ul><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="printOrd_toolsItem" onclick="printPage()" title="Print"><img src="/lps/resources/themes/icons/mablue/foPrint.gif">Print</a></li></ul></div></div></div><div id="mBtnContainer"><input id="dataForm:apptList_menu_1_menuItemBtn" type="submit" name="dataForm:apptList_menu_1_menuItemBtn" value="" onclick="return ;" style="display:none"><input id="dataForm:apptList_menu_2_menuItemBtn" name="dataForm:apptList_menu_2_menuItemBtn" onclick="if( !editChecks() ) { return false};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_2_menuItemBtn','oncomplete':function(request,event,data){editComplete()},'parameters':{'dataForm:apptList_menu_2_menuItemBtn':'dataForm:apptList_menu_2_menuItemBtn'} } );return false;" style="display:none" type="button"><input id="dataForm:apptList_menu_5_menuItemBtn" type="submit" name="dataForm:apptList_menu_5_menuItemBtn" value="" onclick="approve();" style="display:none"><input id="dataForm:apptList_menu_9_menuItemBtn" name="dataForm:apptList_menu_9_menuItemBtn" onclick=";A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_9_menuItemBtn','oncomplete':function(request,event,data){},'parameters':{'dataForm:apptList_menu_9_menuItemBtn':'dataForm:apptList_menu_9_menuItemBtn'} } );return false;" style="display:none" type="button"><input id="dataForm:apptList_menu_10_menuItemBtn" name="dataForm:apptList_menu_10_menuItemBtn" onclick="dummy();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_10_menuItemBtn','oncomplete':function(request,event,data){openDialogwithId('Recommendations_Dialog')},'parameters':{'dataForm:apptList_menu_10_menuItemBtn':'dataForm:apptList_menu_10_menuItemBtn'} } );return false;" style="display:none" type="button"><input id="dataForm:apptList_menu_12_menuItemBtn" type="submit" name="dataForm:apptList_menu_12_menuItemBtn" value="" onclick="return ;" style="display:none"><input id="dataForm:apptList_menu_14_menuItemBtn" type="submit" name="dataForm:apptList_menu_14_menuItemBtn" value="" onclick="return ;" style="display:none"><input id="dataForm:apptList_menu_18_menuItemBtn" name="dataForm:apptList_menu_18_menuItemBtn" onclick="if(!emailClick()) {return false};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_18_menuItemBtn','oncomplete':function(request,event,data){openDialogwithId('EmailDialog');},'parameters':{'dataForm:apptList_menu_18_menuItemBtn':'dataForm:apptList_menu_18_menuItemBtn'} } );return false;" style="display:none" type="button"><input id="dataForm:ASNList_Menu_Item_Reports_toolsItemBtn" name="dataForm:ASNList_Menu_Item_Reports_toolsItemBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:ASNList_Menu_Item_Reports_toolsItemBtn','oncomplete':function(request,event,data){UI8Layout.doDialogById('reportDialog',true)},'parameters':{'reportEnabled':'true','dataForm:ASNList_Menu_Item_Reports_toolsItemBtn':'dataForm:ASNList_Menu_Item_Reports_toolsItemBtn'} } );return false;" style="display:none" type="button"><input id="dataForm:print_toolsItemBtn" type="submit" name="dataForm:print_toolsItemBtn" value="" style="display:none"></div><script type="text/javascript" language="JavaScript" src="/lps/resources/rightclickmenu/scripts/rightclickmenu.js">
</script><div id="rmenu" class="menuUI8"><div id="fo" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_1_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_1_menuItemBtn');}" title="Add"><img src="/lps/resources/menu/images/clear.gif"><span>Add</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_2_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_2_menuItemBtn');}" title="Edit"><img src="/lps/resources/menu/images/clear.gif"><span>Edit</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_5_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_5_menuItemBtn');}" title="Approve"><img src="/lps/resources/menu/images/clear.gif"><span>Approve</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif"><span>Validate</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif"><span>Recommend Time Slots</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif"><span>Save</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif"><span>Cancel</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_18_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_18_menuItemBtn');}" title="Email"><img src="/lps/resources/menu/images/clear.gif"><span>Email</span></a></li></ul></div></div></div><script type="text/javascript" language="JavaScript">//<![CDATA[
loadTnA();
//]]>
</script><script type="text/javascript" language="JavaScript">//<![CDATA[
 var printMultiTab='false';
//]]>
</script></span><span id="dataForm:errors"><script type="text/javascript">
_u8_ely_1({isAjax:false,list:[],ortext:"Override",name:"error_OverLay"});
</script></span><div id="dataForm:SoftCheckErrorOverride_Dialog" name="dataForm:SoftCheckErrorOverride_Dialog" class="dialog_cont" style="height:420px;width:750px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/ribbon/images/about.gif"></div></td><td class="pop_dragHandler" id="dataForm:SoftCheckErrorOverride_DialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Warnings</div></td><td><div class="pop_close" id="dataForm:SoftCheckErrorOverride_DialogTemplate_cCId"><input type="button" tabindex="142"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:420px;width:750px;">
	                <div class="popcon"><span id="dataForm:SoftCheckErrorOverride_Dialog_DetailAJAXPanel_1"><span id="dataForm:softcheckerrorlistpanel" border="false"><input type="hidden" name="dataForm:softcheckerrorsTable_deleteHidden" value="" id="dataForm:softcheckerrorsTable_deleteHidden" tabindex="143"><input type="hidden" name="dataForm:softcheckerrorsTable_selectedRows" value="#:#" id="dataForm:softcheckerrorsTable_selectedRows" tabindex="144"><div class="advtbl_contr" id="dataForm:softcheckerrorsTable_container" style=""><div id="dataForm:softcheckerrorsTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:softcheckerrorsTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:softcheckerrorsTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:softcheckerrorsTable:isSortButtonClick" id="dataForm:softcheckerrorsTable:isSortButtonClick" value="" tabindex="145"><input type="hidden" name="dataForm:softcheckerrorsTable:sortDir" id="dataForm:softcheckerrorsTable:sortDir" value="desc" tabindex="146"><input type="hidden" name="dataForm:softcheckerrorsTable:colCount" id="dataForm:softcheckerrorsTable:colCount" value="" tabindex="147"><input type="hidden" name="dataForm:softcheckerrorsTable:tableClicked" id="dataForm:softcheckerrorsTable:tableClicked" value="" tabindex="148"><input type="hidden" name="dataForm:softcheckerrorsTable:tableResized" id="dataForm:softcheckerrorsTable:tableResized" value="false" tabindex="149">
<div class="advtbl_contr_head" style="position:relative;" id="dataForm:softcheckerrorsTable_headDiv"><table style="table-layout:auto;" id="dataForm:softcheckerrorsTable" cellspacing="0"><colgroup>
<col>
<col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top"><input type="checkbox" name="dataForm:softcheckerrorsTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'softcheckerrorsTable');" disabled="" tabindex="150"></td><td align="right" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:softcheckerrorsTable:rc1" class="titleCase">Warning</span></td>
<td align="right" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:softcheckerrorsTable:rc2" class="titleCase">Override Code</span></td>
</tr></thead></table></div><div id="dataForm:softcheckerrorsTable_bodyDiv" class="advtbl_contr_body"><table style="table-layout:auto;" id="dataForm:softcheckerrorsTable_body" cellspacing="0"><colgroup>
<col>
<col><col></colgroup><tbody>
<tr style="visibility:hidden;display:none" id="dummyRowIpsTableId" class="advtbl_row"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:softcheckerrorsTable" id="checkAll_c0_dataForm:softcheckerrorsTable" value="0" tabindex="151"><input type="hidden" value="DUMMYROW" id="dataForm:softcheckerrorsTable:0:PK_0" name="dataForm:softcheckerrorsTable:0:PK_0" tabindex="152"></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="right"><div id="dataForm:softcheckerrorsTable_body_tr0_td0_view" class="dshow">&nbsp;<span id="dataForm:softcheckerrorsTable:0:softcheckerror"></span></div><div id="dataForm:softcheckerrorsTable_body_tr0_td0_edit" class="dhide">&nbsp;<span id="dataForm:softcheckerrorsTable:0:softcheckerrorEdit"></span><input type="hidden" name="dataForm:softcheckerrorsTable:0:markForDelete" id="dataForm:softcheckerrorsTable:0:markForDelete" value="" tabindex="153"></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="right"><div id="dataForm:softcheckerrorsTable_body_tr0_td1_view" class="dshow">&nbsp;<select id="dataForm:softcheckerrorsTable:0:reasoncode" name="dataForm:softcheckerrorsTable:0:reasoncode" size="1" tabindex="154">	<option value="">Select one</option>
	<option value="937">Carrier Delay</option>
	<option value="316">Receiving Capacity</option>
	<option value="935">Vendor Delay</option>
</select></div><span style="display:none">&nbsp;</span></td></tr><tr id="dataForm:softcheckerrorsTable:nodataRow" class="advtbl_row trshow" style="visibility: inherit; display: table-row;"><td class="advtbl_col advtbl_body_col tdshow" colspan="3" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="softcheckerrorsTable_hdnMaxIndexHldr" name="softcheckerrorsTable_hdnMaxIndexHldr" value="0" tabindex="155"></table></div><div class="emptyHoriScrollDiv"></div></div>
<input type="hidden" id="dataForm:softcheckerrorsTable_trs_pageallrowskey" name="dataForm:softcheckerrorsTable_trs_pageallrowskey" value="" tabindex="156"><input type="hidden" id="dataForm:softcheckerrorsTable_selectedRows" name="dataForm:softcheckerrorsTable_selectedRows" value="" tabindex="157"><input type="hidden" id="dataForm:softcheckerrorsTable_selectedIdList" name="dataForm:softcheckerrorsTable_selectedIdList" value="" tabindex="158"><input type="hidden" id="dataForm:softcheckerrorsTable_trs_allselectedrowskey" name="dataForm:softcheckerrorsTable_trs_allselectedrowskey" value="softcheckerrorsTable$:$1752205181860" tabindex="159"><script type="text/javascript">var  dataFormsoftcheckerrorsTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='softcheckerrorsTable';
tableObjectArray[count]=dataFormsoftcheckerrorsTable_tableObj;dataFormsoftcheckerrorsTable_tableObj.bind(document.getElementById('dataForm:softcheckerrorsTable_container'),document.getElementById('dataForm:softcheckerrorsTable_headDiv'), document.getElementById('dataForm:softcheckerrorsTable_bodyDiv'), document.getElementById('dataForm:softcheckerrorsTable_scrollDiv'),document.getElementById('dataForm:softcheckerrorsTable_scrollDivBody'), document.getElementById('dataForm:softcheckerrorsTable_button'),true,1,2,'dataForm:softcheckerrorsTable','edit','yes','no','10','bottom','view',1,2147483647,'yes','no','even','odd','Invalid Table','dataForm:softcheckerrorsTable_selectedIdList','true','softcheckerrorsTable','false' ,0,8,'false','Synced','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
</script><script>var msgToActivateTotal;msgToActivateTotal = "To activate this feature, click the Total button";</script>
<script type="text/javascript">
UI8Layout.ondialogTableLoad("softcheckerrorsTable");
</script><input id="dataForm:saveSoftErrorBtn" type="submit" name="dataForm:saveSoftErrorBtn" value="Save" alt="Save" onmousedown="overrideApptSCE();" class="btn" tabindex="160"><input class="btn" id="dataForm:softErrorcancelBtn" name="dataForm:softErrorcancelBtn" onclick="UI8Layout.doDialogCloseById('SoftCheckErrorOverride_Dialog');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:softErrorcancelBtn','parameters':{'dataForm:softErrorcancelBtn':'dataForm:softErrorcancelBtn'} } );return false;" value="Cancel" alt="Cancel" type="button" tabindex="161"><span class="groupBtnSpace">&nbsp;</span></span>
</span>
	                </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("SoftCheckErrorOverride_Dialog",{onClose:"",dialogClientId:"dataForm:SoftCheckErrorOverride_Dialog",dragHandleId:"dataForm:SoftCheckErrorOverride_DialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:SoftCheckErrorOverride_DialogTemplate_cCId"});
</script><input type="hidden" id="dataForm:synccomp1_syncActionHid" name="dataForm:synccomp1_syncActionHid" value="" tabindex="162"><div class="synclstmn" id="dataForm:synccomp1"><script type="text/javascript">
UI8Layout.Sync.initSL("dataForm:synccomp1",{listId:"dataTable",detailId:"syncTab",synconload:true,alignment:"vertical",serverId:"synccomp1"});
</script><table class="synctable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="synclsttdV"><div class="synccontout"><span id="dataForm:listPanel"><div id="ListView_listView"><div id="filterId1" class="fltr_div" style="display: block;"><table class="fltr_tbl" cellspacing="0px" width="100%"><tbody><tr><td width="2%" class="fltr_tbl_tdmodeTop"><input type="image" style="padding-top: 6px; visibility: visible;" src="/lps/resources/themes/icons/mablue/arrow_collapse.gif" title="Expand" id="filterId1_fltrExpCol" class="fltrHidden" tabindex="163"></td><td width="6%" class="fltr_tbl_tdmode"><div class="fltr_qfDiv -gbl_hhc  -md_mhbc"><span id="dataForm:listView:filterId1:QFCap">Quick filter</span><span id="dataForm:listView:filterId1:SFCap" class="fltr_capHide">Saved filters</span><span class="fltr_downArrowImg" id="filterId1_fltrDownArrow"><br><div class="menuskin_UI08" id="filterId1_filter_drop_down" style="visibility: hidden; display: none;"><div class="mo" id="filterActions"><div class="fosdw"></div><div class="fobody" id="fltr_drop_body"><ul id="filterId1_ul" class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="filterId1_li1" onclick="UI8Layout.FilterMap.dropDownOnChange('filterId1','saved');return false;">Saved filters</a></li><li><a class="foopt -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="filterId1_li2" onclick="UI8Layout.FilterMap.dropDownOnChange('filterId1','quick');return false;">Quick filter</a></li></ul></div></div></div><input id="dataForm:listView:filterId1:radioSelect" type="hidden" name="dataForm:listView:filterId1:radioSelect" value="quick" tabindex="164"><input id="dataForm:listView:filterId1:_fltrExpColTxt" type="hidden" name="dataForm:listView:filterId1:_fltrExpColTxt" value="DONE" tabindex="165"><input id="dataForm:listView:filterId1:_filtrExpColState" type="hidden" name="dataForm:listView:filterId1:_filtrExpColState" value="collapsed" tabindex="166"><input id="dataForm:listView:filterId1:_filtrExpIconSrc" type="hidden" name="dataForm:listView:filterId1:_filtrExpIconSrc" value="/lps/resources/themes/icons/mablue/arrow_expand.gif" tabindex="167"><input id="dataForm:listView:filterId1:_filtrColIconSrc" type="hidden" name="dataForm:listView:filterId1:_filtrColIconSrc" value="/lps/resources/themes/icons/mablue/arrow_collapse.gif" tabindex="168"><input id="dataForm:listView:filterId1:_filtrdropDownSrc" type="hidden" name="dataForm:listView:filterId1:_filtrdropDownSrc" value="/lps/resources/themes/icons/mablue/arrowDown.gif" tabindex="169"></span></div></td><td class="fltr_tbl_tdmode" id="filterId1_tdfldCont" style="width: 70%;"><div id="filterId1_filterPanel"><table cellspacing="2px" cellpadding="0px" width="100%" id="filterId1_quckFltrTable" class="fltr_capShow"><tbody><tr><td><div id="filterId1_quickFilterDiv" class="fltr_capShow"><div class="fltr_rightBdr"><div style="white-space:nowrap;vertical-align:inherit"><div id="_otid5517" class="captionLeftNoWrap" style="width:2%;"><span title="">Appointment:</span><span class="required" id="_otid5517_cptnSpn">*</span>&nbsp;</div><input id="dataForm:listView:filterId1:field10" type="hidden" name="dataForm:listView:filterId1:field10" value="Appointment" tabindex="170"><input id="dataForm:listView:filterId1:field10operator" type="hidden" name="dataForm:listView:filterId1:field10operator" value="=" tabindex="171"><input id="dataForm:listView:filterId1:subObject10" type="hidden" name="dataForm:listView:filterId1:subObject10" value="" tabindex="172"><script language="JavaScript">var isDemo=false</script><input type="hidden" id="dataForm:listView:filterId1:field10value1ecId" name="dataForm:listView:filterId1:field10value1ecId" value="" tabindex="173"><input type="hidden" id="dataForm_listView_filterId1_field10value1_enterKey" value="false" tabindex="174"><input type="hidden" id="triggerdataForm_listView_filterId1_field10value1_enterKey" value="false" tabindex="175"><input type="text" id="dataForm:listView:filterId1:field10value1" name="dataForm:listView:filterId1:field10value1" onfocus="javascript: focusOnTextBox('dataForm_listView_filterId1_field10value1_enterKey')" onblur="javascript: blurOnTextBox('dataForm_listView_filterId1_field10value1_enterKey')" onkeypress="if(enterPressed(event,'dataForm:listView:filterId1:field10value1') )return false;" value="*********" title="*********" alt="Find Appointment" tabindex="176">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BymslookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BymslookupBackingBean.getBUMap%7D&amp;lookupType=Appointment&amp;is3plEnabled=true&amp;returnId=dataForm:listView:filterId1:field10value1&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code=VAPT'; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:listView:filterId1:field10value1" title="Find Appointment" align="absmiddle" id="trigger_dataForm:listView:filterId1:field10value1" name="trigger_dataForm:listView:filterId1:field10value1" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_listView_filterId1_field10value1_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_listView_filterId1_field10value1_enterKey')" tabindex="177"></div></div><div class="fltr_rightBdr fltr_capHide"><div style="white-space:nowrap;vertical-align:inherit"><div id="_otid5518" class="captionLeftNoWrap" style="width:2%;"><span title="">Appointment requested date/time:</span><span class="required" id="_otid5518_cptnSpn">*</span>&nbsp;</div><input id="dataForm:listView:filterId1:field30" type="hidden" name="dataForm:listView:filterId1:field30" value="Appointment requested date/time" tabindex="178"><input id="dataForm:listView:filterId1:field30operator" type="hidden" name="dataForm:listView:filterId1:field30operator" value="BT" tabindex="179"><input id="dataForm:listView:filterId1:subObject30" type="hidden" name="dataForm:listView:filterId1:subObject30" value="" tabindex="180"><span id="dataForm:listView:filterId1:_otid5519" class="captionData">From</span>&nbsp;<span id="dataForm:listView:filterId1:field30value1_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas1_maintab"><tbody><tr><td><div class="asbasout" id="as_bas1"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas1_in" type="text" value="" size="15" tabindex="181"></td><td class="asbinptd"><div id="as_bas1_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="182"><input type="hidden" id="dataForm:listView:filterId1:field30value1" name="dataForm:listView:filterId1:field30value1" value="" alt="" size="" tabindex="183">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field30value1" name="dataForm:listView:filterId1:field30value1_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="184" onclick="dataFormwlistViewwfilterId1wfield30value1calendarSetUp(this.name);return false">
<script type="text/javascript"> var field30value1_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field30value1',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field30value1',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field30value1_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field30value1_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield30value1calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field30value1_combo
	}
)
}
</script>
&nbsp;&nbsp;&nbsp;<span id="dataForm:listView:filterId1:_otid5521" class="captionData">To</span>&nbsp;<span id="dataForm:listView:filterId1:field30value2_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas2_maintab"><tbody><tr><td><div class="asbasout" id="as_bas2"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas2_in" type="text" value="" size="15" tabindex="185"></td><td class="asbinptd"><div id="as_bas2_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="186"><input type="hidden" id="dataForm:listView:filterId1:field30value2" name="dataForm:listView:filterId1:field30value2" value="" alt="" size="" tabindex="187">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field30value2" name="dataForm:listView:filterId1:field30value2_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="188" onclick="dataFormwlistViewwfilterId1wfield30value2calendarSetUp(this.name);return false">
<script type="text/javascript"> var field30value2_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field30value2',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field30value2',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field30value2_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field30value2_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield30value2calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field30value2_combo
	}
)
}
</script>
</div></div><div class="fltr_rightBdr fltr_capHide"><div style="white-space:nowrap;vertical-align:inherit"><div id="_otid5523" class="captionLeftNoWrap" style="width:2%;"><span title="">Estimated departure date/time:</span><span class="required" id="_otid5523_cptnSpn">*</span>&nbsp;</div><input id="dataForm:listView:filterId1:field40" type="hidden" name="dataForm:listView:filterId1:field40" value="Estimated departure date/time" tabindex="189"><input id="dataForm:listView:filterId1:field40operator" type="hidden" name="dataForm:listView:filterId1:field40operator" value="BT" tabindex="190"><input id="dataForm:listView:filterId1:subObject40" type="hidden" name="dataForm:listView:filterId1:subObject40" value="" tabindex="191"><span id="dataForm:listView:filterId1:_otid5524" class="captionData">From</span>&nbsp;<span id="dataForm:listView:filterId1:field40value1_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas3_maintab"><tbody><tr><td><div class="asbasout" id="as_bas3"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas3_in" type="text" value="" size="15" tabindex="192"></td><td class="asbinptd"><div id="as_bas3_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="193"><input type="hidden" id="dataForm:listView:filterId1:field40value1" name="dataForm:listView:filterId1:field40value1" value="" alt="" size="" tabindex="194">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field40value1" name="dataForm:listView:filterId1:field40value1_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="195" onclick="dataFormwlistViewwfilterId1wfield40value1calendarSetUp(this.name);return false">
<script type="text/javascript"> var field40value1_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field40value1',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field40value1',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field40value1_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field40value1_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield40value1calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field40value1_combo
	}
)
}
</script>
&nbsp;&nbsp;&nbsp;<span id="dataForm:listView:filterId1:_otid5526" class="captionData">To</span>&nbsp;<span id="dataForm:listView:filterId1:field40value2_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas4_maintab"><tbody><tr><td><div class="asbasout" id="as_bas4"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas4_in" type="text" value="" size="15" tabindex="196"></td><td class="asbinptd"><div id="as_bas4_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="197"><input type="hidden" id="dataForm:listView:filterId1:field40value2" name="dataForm:listView:filterId1:field40value2" value="" alt="" size="" tabindex="198">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field40value2" name="dataForm:listView:filterId1:field40value2_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="199" onclick="dataFormwlistViewwfilterId1wfield40value2calendarSetUp(this.name);return false">
<script type="text/javascript"> var field40value2_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field40value2',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field40value2',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field40value2_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field40value2_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield40value2calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field40value2_combo
	}
)
}
</script>
</div></div><div class="fltr_rightBdr fltr_capHide"><div style="white-space:nowrap;vertical-align:inherit"><div id="_otid5528" class="captionLeftNoWrap" style="width:2%;"><span title="">Suggested start date/time:</span><span class="required" id="_otid5528_cptnSpn">*</span>&nbsp;</div><input id="dataForm:listView:filterId1:field20" type="hidden" name="dataForm:listView:filterId1:field20" value="Suggested start date/time" tabindex="200"><input id="dataForm:listView:filterId1:field20operator" type="hidden" name="dataForm:listView:filterId1:field20operator" value="BT" tabindex="201"><input id="dataForm:listView:filterId1:subObject20" type="hidden" name="dataForm:listView:filterId1:subObject20" value="" tabindex="202"><span id="dataForm:listView:filterId1:_otid5529" class="captionData">From</span>&nbsp;<span id="dataForm:listView:filterId1:field20value1_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas5_maintab"><tbody><tr><td><div class="asbasout" id="as_bas5"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas5_in" type="text" value="" size="15" tabindex="203"></td><td class="asbinptd"><div id="as_bas5_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="204"><input type="hidden" id="dataForm:listView:filterId1:field20value1" name="dataForm:listView:filterId1:field20value1" value="" alt="" size="" tabindex="205">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field20value1" name="dataForm:listView:filterId1:field20value1_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="206" onclick="dataFormwlistViewwfilterId1wfield20value1calendarSetUp(this.name);return false">
<script type="text/javascript"> var field20value1_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field20value1',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field20value1',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field20value1_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field20value1_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield20value1calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field20value1_combo
	}
)
}
</script>
&nbsp;&nbsp;&nbsp;<span id="dataForm:listView:filterId1:_otid5531" class="captionData">To</span>&nbsp;<span id="dataForm:listView:filterId1:field20value2_Cont" class="dtddropdown"><table style="display:inline" cellpadding="0" cellspacing="0" border="0" id="as_bas6_maintab"><tbody><tr><td><div class="asbasout" id="as_bas6"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td class="asbinptd"><input class="asbasinp" id="as_bas6_in" type="text" value="" size="15" tabindex="207"></td><td class="asbinptd"><div id="as_bas6_img" class="asbasdwndiv"><img src="/lps/resources/common/images/dropdown.png"></div></td></tr></tbody></table></div></td></tr></tbody></table></span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="208"><input type="hidden" id="dataForm:listView:filterId1:field20value2" name="dataForm:listView:filterId1:field20value2" value="" alt="" size="" tabindex="209">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:listView:filterId1:field20value2" name="dataForm:listView:filterId1:field20value2_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="210" onclick="dataFormwlistViewwfilterId1wfield20value2calendarSetUp(this.name);return false">
<script type="text/javascript"> var field20value2_combo = new SimpleAutoSuggest ({  ontype     : function (ecb, str) {  var r = [];   r.push ({name : "", value : ""});  r.push ({name : "Today", value : "Today"});  r.push ({name : "Tomorrow", value : "Tomorrow"});  r.push ({name : "Next Week", value : "Next Week"});  r.push ({name : "Next Two Weeks", value : "Next Two Weeks"});  r.push ({name : "Next Month", value : "Next Month"});  r.push ({name : "Next Two Months", value : "Next Two Months"});  r.push ({name : "Yesterday", value : "Yesterday"});  r.push ({name : "Last Week", value : "Last Week"});  r.push ({name : "Last Two Weeks", value : "Last Two Weeks"});  r.push ({name : "Last Month", value : "Last Month"});  r.push ({name : "Last Two Months", value : "Last Two Months"});  r.push ({name : "Last Three Months", value : "Last Three Months"});  CalendarTimer.changeCalVal('dataForm:listView:filterId1:field20value2',ecb,str);  return r;; },  onselect   : function (ecb, obj ) { CalendarTimer.changeCalVal('dataForm:listView:filterId1:field20value2',ecb,obj);},  parentid   : 'dataForm:listView:filterId1:field20value2_Cont',  parent     : null,  inputsize  : 15,  maxWidth   : 0,  minWidth   : 0,  maxHeight  : 80,  posopt     : false });field20value2_combo.start(); </script><script type="text/javascript">
function dataFormwlistViewwfilterId1wfield20value2calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "true", 		dropDownIns  :  field20value2_combo
	}
)
}
</script>
</div></div><div class="fltr_rightBdr fltr_capHide"><div style="white-space:nowrap;vertical-align:inherit"><div id="_otid5533" class="captionLeftNoWrap" style="width:2%;"><span title="">Appointment status:</span><span class="notRequired">p</span>&nbsp;</div><input id="dataForm:listView:filterId1:field80" type="hidden" name="dataForm:listView:filterId1:field80" value="Appointment status" tabindex="211"><select id="dataForm:listView:filterId1:field80operator" name="dataForm:listView:filterId1:field80operator" class="multiselectFilterOperator" size="1" tabindex="212">	<option value="=">=</option>
	<option value="!=">≠</option>
</select>&nbsp;&nbsp;<input id="dataForm:listView:filterId1:subObject80" type="hidden" name="dataForm:listView:filterId1:subObject80" value="" tabindex="213"><input type="hidden" name="dataForm:listView:filterId1:field80value1" id="dataForm:listView:filterId1:field80value1" value="[]" tabindex="214"><table border="0" cellpadding="0" cellspacing="0" id="dataForm:listView:filterId1:field80value1_stTab" class="fltrsmanycont"><tbody><tr><td id="dataForm:listView:filterId1:field80value1_cont"><table border="0" cellpadding="0" cellspacing="0" class="msdstattab" id="ms_sttab1"><tbody><tr><td><div class="msdstattxtdiv" style="width:70px"><span class="msdstattext" id="ms_stspan1"></span></div></td><td class="msdstattabtd2"><div id="ms_stimgdiv1" class="msdstatimg" <="" div=""></div></td></tr></tbody></table></td></tr></tbody></table><script type="text/javascript">
_u8_ms_1({minHeight:10,maxHeight:150,cancelText:"Cancel",stwidth:70,list:[{name:"",value:"",isSel:false},{name:"Scheduled",value:"3",isSel:false},{name:"Checked In",value:"5",isSel:false},{name:"Complete",value:"9",isSel:false},{name:"Canceled",value:"10",isSel:false},{name:"Countered",value:"15",isSel:false},{name:"Requested",value:"17",isSel:false},{name:"In Progress",value:"18",isSel:false},{name:"Suspended",value:"20",isSel:false}],okText:"OK",parentid:"dataForm:listView:filterId1:field80value1_cont",cId:"dataForm:listView:filterId1:field80value1",allText:"All"});
</script></div></div></div></td></tr></tbody></table><div id="filterId1_savedFilterDiv" class="fltr_capHide"><div class="fltr_rightBdr"><div style="white-space:nowrap;vertical-align:inherit"><div id="null" class="captionLeftNoWrap" style="width:2%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><span id="dataForm:listView:filterId1:filterId1categoryDropDownPanel"><select id="dataForm:listView:filterId1:currentAppliedFilterId" name="dataForm:listView:filterId1:currentAppliedFilterId" onkeydown="UIFilterJS.applyFilterOnEnter(e,this)" tabindex="215"><option value="-1">Add Filter</option><optgroup label="Recent"><option value="1999025866">Appt Search2 √</option><option value="1999025194">Search PO #</option><option value="1999024492">PO NUMBER</option><option value="1999025414">Del appts</option></optgroup><optgroup label="Favorites"><option value="1999023416">1177201</option><option value="1999023415">11772015</option><option value="1999023419">117720155</option><option value="1999025093">12485797</option><option value="1999021673">44</option><option value="1999025684">AtlAppts</option><option value="1999021747">Butner</option><option value="1999024393">JON</option><option value="1999025679">Land O lakes 5/2/25</option><option value="1999023908">None</option><option value="1999024492">PO NUMBER</option><option value="1999024185">PO SEARCH-1</option><option value="1999024203">SM Appts</option><option value="1999021609">Search Bar</option><option value="1999024321">Soag</option><option value="1999023766">cp</option><option value="1999025424">giant appt 3/4</option><option value="1999024306">hahaha</option><option value="1999025332">latasha dunn nc</option><option value="1999025936">this sit SUCKS</option></optgroup><optgroup label="My Filters"><option value="1999025087">013748494</option><option value="1999024141">08/19</option><option value="1999024685">233941ma</option><option value="1999023094">Appointment Needed</option><option value="1999021593">BY CONFIRMATION NUMBER</option><option value="1999021529">Bob</option><option value="1999025414">Del appts</option><option value="1999025937">Jessup 6/14</option><option value="1999021071">New</option><option value="1999021247">SEARCH APPOINTMENT</option><option value="1999025194">Search PO #</option><option value="1999021528">South Portland</option><option value="1999025195">appt number</option><option value="1999020828">appt.</option></optgroup></select></span></div></div></div></div></td><td width="150px" style="width:150px"><div id="filterId1_quickFilterButton" class="fltr_capShow"><span id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton" class="maingrpdiv"><input type="hidden" name="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_mainButtonCategory" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_mainButtonCategory" value="-1" tabindex="216"><input type="hidden" name="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_mainButtonIndex" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_mainButtonIndex" value="-1" tabindex="217"><input type="hidden" name="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_changeDefault" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_changeDefault" value="false" tabindex="218"><table cellspacing="0px" cellpadding="0px"><tbody><tr><td style="padding: 2px;display: inline-flex;"><div class="mainbuttondiv" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_mainButton"><input class="btn  groupBtn" id="dataForm:listView:filterId1:filterId1apply" name="dataForm:listView:filterId1:filterId1apply" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:filterId1apply','oncomplete':function(request,event,data){UI8Layout.doRecalc()},'parameters':{'fltrApplyFromQF':'true','fltrClientId':'dataForm:listView:filterId1','reRenderParent':'synccomp1','dataForm:listView:filterId1:filterId1apply':'dataForm:listView:filterId1:filterId1apply'} } );return false;" value="Apply" style="cursor:pointer" title="Apply" type="button" tabindex="219"></div><span class="btn groupBtn-arrow" style="padding-left : 0px; margin-left: 0px;" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_apllyImgDiv" onclick="UI8Layout.GroupButtonMap.toggleGroupOptions('dataForm:listView:filterId1:filterId1_quickFilterGroupButton')"> ▼</span></td></tr><tr><td style="padding: 0px 2px 2px 2px; border: 0px;"><div class="menuskin_UI08" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_dropDwn" style="display: none;"><div class="grptop"><div class="fosdw -pdlg_sdw"></div><div class="grpbody"><ul class="grpul" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_ul"><li id="filterButtons" class="grpli" style="border-bottom: none;"><input id="dataForm:listView:filterId1:filterId1clearAll" name="dataForm:listView:filterId1:filterId1clearAll" onclick="collapseQF('dataForm:listView:filterId1');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:filterId1clearAll','oncomplete':function(request,event,data){UI8Layout.doRecalc()},'parameters':{'dataForm:listView:filterId1:filterId1clearAll':'dataForm:listView:filterId1:filterId1clearAll','clrFilterActionQF':'true','fltrClientId':'dataForm:listView:filterId1','reRenderParent':'synccomp1'} } );return false;" value="Default" title="Apply Default Filter" type="button" tabindex="220"><input id="dataForm:listView:filterId1:filterId1editQuickFilter" name="dataForm:listView:filterId1:filterId1editQuickFilter" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:filterId1editQuickFilter','oncomplete':function(request,event,data){UI8Layout.doRecalc()},'parameters':{'dataForm:listView:filterId1:filterId1editQuickFilter':'dataForm:listView:filterId1:filterId1editQuickFilter','objectType':'UI_APPOINTMENT'} } );return false;" value="Configure" style="cursor:pointer" title="Edit quick filter" type="button" tabindex="221"></li></ul></div></div></div></td></tr></tbody></table><input type="hidden" value="dataForm:listView:filterId1:filterId1apply#0#0|dataForm:listView:filterId1:filterId1clearAll#0#1|dataForm:listView:filterId1:filterId1editQuickFilter#0#2|" id="dataForm:listView:filterId1:filterId1_quickFilterGroupButton_info" tabindex="222"><script> UI8Layout.GroupButtonMap.init('dataForm:listView:filterId1:filterId1_quickFilterGroupButton');</script></span></div><div id="filterId1_savedFilterButton" class="fltr_capHide"><span id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton" class="maingrpdiv"><input type="hidden" name="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_mainButtonCategory" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_mainButtonCategory" value="-1" tabindex="223"><input type="hidden" name="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_mainButtonIndex" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_mainButtonIndex" value="-1" tabindex="224"><input type="hidden" name="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_changeDefault" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_changeDefault" value="false" tabindex="225"><table cellspacing="0px" cellpadding="0px"><tbody><tr><td style="padding: 2px;display: inline-flex;"><div class="mainbuttondiv" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_mainButton"><input class="btn groupBtn" id="dataForm:listView:filterId1:savedapply" name="dataForm:listView:filterId1:savedapply" onclick="if(!UIFilterJS.savedFltrApplyClick(this)) return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:savedapply','oncomplete':function(request,event,data){UI8Layout.doRecalc()},'parameters':{'dataForm:listView:filterId1:savedapply':'dataForm:listView:filterId1:savedapply'} } );return false;" value="Apply" title="Apply" type="button" tabindex="226"></div><span class="btn groupBtn-arrow" style="padding-left : 0px; margin-left: 0px;" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_apllyImgDiv" onclick="UI8Layout.GroupButtonMap.toggleGroupOptions('dataForm:listView:filterId1:filterId1_savedFilterGroupButton')"> ▼</span></td></tr><tr><td style="padding: 0px 2px 2px 2px; border: 0px;"><div class="menuskin_UI08" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_dropDwn" style="display: none;"><div class="grptop"><div class="fosdw -pdlg_sdw"></div><div class="grpbody"><ul class="grpul" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_ul"><li id="saveFilterButtons" class="grpli" style="border-bottom: none;"><input id="dataForm:listView:filterId1:filterId1savedEditFilter" name="dataForm:listView:filterId1:filterId1savedEditFilter" onclick="UIFilterJS.showModalDialg();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:filterId1savedEditFilter','parameters':{'filterComponentId':'filterId1','dataForm:listView:filterId1:filterId1savedEditFilter':'dataForm:listView:filterId1:filterId1savedEditFilter','fltrClientId':'dataForm:listView:filterId1','reRenderParent':'synccomp1'} } );return false;" value="Configure" style="cursor:pointer" title="Edit saved filters" type="button" tabindex="227"><input id="dataForm:listView:filterId1:newFilterCmd" name="dataForm:listView:filterId1:newFilterCmd" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:filterId1:newFilterCmd','parameters':{'dataForm:listView:filterId1:newFilterCmd':'dataForm:listView:filterId1:newFilterCmd','fltrClientId':'dataForm:listView:filterId1','newFilter':'yes','reRenderParent':'synccomp1'} } );return false;" value="New" style="visibility:hidden;display:none" title="Edit saved filters" type="button" tabindex="228"></li></ul></div></div></div></td></tr></tbody></table><input type="hidden" value="dataForm:listView:filterId1:savedapply#0#0|dataForm:listView:filterId1:filterId1savedEditFilter#0#1|dataForm:listView:filterId1:newFilterCmd#0#2|" id="dataForm:listView:filterId1:filterId1_savedFilterGroupButton_info" tabindex="229"><script> UI8Layout.GroupButtonMap.init('dataForm:listView:filterId1:filterId1_savedFilterGroupButton');</script></span></div></td></tr></tbody></table></div> <script> UI8Layout.FilterMap.init('filterId1','dataForm:listView:filterId1'); </script><input id="dataForm:listView:filterId1:dummyToGetPrefix" type="hidden" name="dataForm:listView:filterId1:dummyToGetPrefix" value="" tabindex="230"><input id="dataForm:listView:filterId1:filterId" type="hidden" name="dataForm:listView:filterId1:filterId" value="2147483646" tabindex="231"><input id="dataForm:listView:filterId1:owner" type="hidden" name="dataForm:listView:filterId1:owner" value="PREPAID" tabindex="232"><input id="customParams" name="customParams " type="hidden" value="&amp;&amp;&amp;" tabindex="233"><input type="hidden" name="queryPersistParameter" value="" tabindex="234"><input type="hidden" name="dataForm:listView:filterId1:objectType" id="dataForm:listView:filterId1:objectType" value="UI_APPOINTMENT" tabindex="235"><input type="hidden" name="isJSF" id="isJSF" value="true" tabindex="236"><input type="hidden" name="filterScreenType" id="filterScreenType" value="ON_SCREEN" tabindex="237"><div class="pnltopdiv" id="PANEL_custom_listpanel_top"><div id="tr_custom_listpanel" class="pnlcondivhdr"><script type="text/javascript">
UI8Layout.Sync.initList("dataForm:synccomp1","dataForm:listView:dataTable",{initialIdx:0});
</script><div class="pager" id="dataForm:listView:dataTable::pager"><span class="pagerNoWrap">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:listView:dataTable:pager:first" name="dataForm:listView:dataTable:pager:first" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:pager:first','parameters':{'dataForm:listView:dataTable:pager:first':'dataForm:listView:dataTable:pager:first'} } );return false;" alt="First" type="image" src="/lps/resources/themes/icons/mablue/arrow_first_disabled.gif" tabindex="238">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:listView:dataTable:pager:previous" name="dataForm:listView:dataTable:pager:previous" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:pager:previous','parameters':{'dataForm:listView:dataTable:pager:previous':'dataForm:listView:dataTable:pager:previous'} } );return false;" alt="Previous" type="image" src="/lps/resources/themes/icons/mablue/arrow_left_disabled.gif" tabindex="239"><script language="JavaScript" src="/lps/resources/inputmask/scripts/mask.js"></script><input id="dataForm:listView:dataTable:pager:pageInput" name="dataForm:listView:dataTable:pager:pageInput" type="text" mask="N" decseparator="." onkeypress="return( (checkKey(this,event))? checkPageCount(event,1,'dataForm:listView:dataTable'):false);" onchange="" onpaste="return checkCopyValue(this); " value="" size="1" style="border-style: solid; border-color:darkgray; color:darkgray; padding: .1em; margin: .1em; border-width: 1px; text-align: left;" class="PageNumberText" onfocus="this.blur();" disabled="disabled" tabindex="240">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:listView:dataTable:pager:next" name="dataForm:listView:dataTable:pager:next" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:pager:next','parameters':{'dataForm:listView:dataTable:pager:next':'dataForm:listView:dataTable:pager:next'} } );return false;" alt="Next" type="image" src="/lps/resources/themes/icons/mablue/arrow_right_disabled.gif" tabindex="241">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:listView:dataTable:pager:last" name="dataForm:listView:dataTable:pager:last" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:pager:last','parameters':{'dataForm:listView:dataTable:pager:last':'dataForm:listView:dataTable:pager:last'} } );return false;" alt="Last" type="image" src="/lps/resources/themes/icons/mablue/arrow_last_disabled.gif" tabindex="242"></span>&nbsp;&nbsp;<span class="pagerNoWrap pagersep"><input class="paginationCtrlCls" id="dataForm:listView:dataTable:pager:dataTable_rfsh_but" name="dataForm:listView:dataTable:pager:dataTable_rfsh_but" onclick="changeCursor('wait');resetFilterIfChanged('dataForm:listView:filterId1');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:pager:dataTable_rfsh_but','oncomplete':function(request,event,data){changeCursor('default')},'parameters':{'dataForm:listView:dataTable:pager:dataTable_rfsh_but':'dataForm:listView:dataTable:pager:dataTable_rfsh_but'} } );return false;" alt="Refresh" type="image" src="/lps/resources/themes/icons/mablue/refresh.gif" tabindex="243"></span><span class="pagerNoWrap">&nbsp;Displaying 1 - 1 of 1&nbsp;</span><span class="pagerNoWrap">(<span id="dataForm:listView:dataTable_TotalSelectedRow">1</span> selected)</span></div><input type="hidden" id="dataForm:listView:dataTable:pagerBoxValue" name="dataForm:listView:dataTable:pagerBoxValue" value="" tabindex="244"><input type="hidden" id="dataForm:listView:dataTable:isPaginationEvent" name="dataForm:listView:dataTable:isPaginationEvent" value="" tabindex="245"><input type="hidden" id="dataForm:listView:dataTable:pagerAction" name="dataForm:listView:dataTable:pagerAction" value="" tabindex="246"><input id="dataForm:listView:dataTable:tableAjaxSubmitBtn" name="dataForm:listView:dataTable:tableAjaxSubmitBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:tableAjaxSubmitBtn','parameters':{'dataForm:listView:dataTable:tableAjaxSubmitBtn':'dataForm:listView:dataTable:tableAjaxSubmitBtn'} } );return false;" style="display:none" type="button" tabindex="247"><input type="hidden" name="dataForm:listView:dataTable_deleteHidden" value="" id="dataForm:listView:dataTable_deleteHidden" tabindex="248"><input type="hidden" name="dataForm:listView:dataTable_selectedRows" value="#:#22312892#:#" id="dataForm:listView:dataTable_selectedRows" tabindex="249"><div class="datatbl_contr" id="dataForm:listView:dataTable_container" style="overflow-y: hidden; width: 982px; height: 75px;"><div id="dataForm:listView:dataTable_scrollDiv" class="advtbl_scrollDiv" style="width: 980px; height: 73px;"><div id="dataForm:listView:dataTable_scrollDivBody" style="width: 1166px; height: 61px;"></div></div><div id="dataForm:listView:dataTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:listView:dataTable:isSortButtonClick" id="dataForm:listView:dataTable:isSortButtonClick" value="ILMApptData.startTime,ILMApptData.status" tabindex="250"><input type="hidden" name="dataForm:listView:dataTable:sortDir" id="dataForm:listView:dataTable:sortDir" value="asc" tabindex="251"><input type="hidden" name="dataForm:listView:dataTable:colCount" id="dataForm:listView:dataTable:colCount" value="" tabindex="252"><input type="hidden" name="dataForm:listView:dataTable:tableClicked" id="dataForm:listView:dataTable:tableClicked" value="" tabindex="253"><input type="hidden" name="dataForm:listView:dataTable:tableResized" id="dataForm:listView:dataTable:tableResized" value="false" tabindex="254"><div class="advtbl_contr_head" id="dataForm:listView:dataTable_headDiv" style="width: 980px;"><table id="dataForm:listView:dataTable" cellspacing="0" style="table-layout: fixed; width: 1012px;">
<colgroup>
<col>
<col><col><col><col><col><col><col><col><col><col><col><col><col><col class="tbl_colHidden"></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top" style="width: 25px;"><input type="checkbox" name="dataForm:listView:dataTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'dataTable');" tabindex="255"></td><td onclick="setSCVal('ILMApptData.hasSoftCheckErrors','0','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header " title="Click to sort" style="width: 29px; cursor: pointer;"><img id="dataForm:listView:dataTable:image02" src="/lcom/common/image/softcheck.gif" alt="Warnings"></td>
<td onclick="setSCVal('ILMApptData.hasHardCheckErrors','1','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header " title="Click to sort" style="width: 29px;"><img id="dataForm:listView:dataTable:AppList_Error_img" src="/lcom/common/image/hardcheck.gif" alt="Errors" style="border:0"></td>
<td onclick="setSCVal('ILMApptData.tcAppointmentId','2','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 95px;"><span id="dataForm:listView:dataTable:cc1" class="titleCase">Appointment ID</span></td>
<td onclick="setSCVal('ILMApptData.startTime','3','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 128px; cursor: pointer;"><span id="dataForm:listView:dataTable:cc2" class="titleCase">Suggested start date/time</span></td>
<td onclick="setSCVal('ILMApptData.type','4','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 104px;"><span id="dataForm:listView:dataTable:cc4" class="titleCase">Appointment type</span></td>
<td onclick="setSCVal('ILMApptData.status','5','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 109px; cursor: pointer;"><span id="dataForm:listView:dataTable:cc5" class="titleCase">Appointment status</span></td>
<td onclick="setSCVal('4','6','dataForm:listView:dataTable');" align="left" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 68px;"><span id="dataForm:listView:dataTable:hout4">Shipment</span></td>
<td onclick="setSCVal('5','7','dataForm:listView:dataTable');" align="left" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 89px;"><span id="dataForm:listView:dataTable:hout5">Purchase Order</span></td>
<td onclick="setSCVal('3','8','dataForm:listView:dataTable');" align="left" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 40px;"><span id="dataForm:listView:dataTable:hout6">ASN</span></td>
<td onclick="setSCVal('ILMApptData.facility','9','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 73px;"><span id="dataForm:listView:dataTable:cc8" class="titleCase">Facility</span></td>
<td onclick="setSCVal('ILMApptData.trailer','10','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 49px;"><span id="dataForm:listView:dataTable:cc12" class="titleCase">Trailer</span></td>
<td align="right" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 76px;"><span id="dataForm:listView:dataTable:cc13" class="titleCase">BOL number</span></td>
<td onclick="setSCVal('ILMApptData.loadPosition','12','dataForm:listView:dataTable');" align="right" class="sortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " title="Click to sort" style="width: 78px;"><span id="dataForm:listView:dataTable:cc15" class="titleCase">Load position</span></td>
<td class="tbl_colHidden advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " align="left" style="width: 20px;"><span style="display:none">&nbsp;</span></td>
</tr></thead></table></div><div id="dataForm:listView:dataTable_bodyDiv" class="advtbl_contr_body" style="width: 980px; height: 23px;"><table id="dataForm:listView:dataTable_body" cellspacing="0" style="table-layout: fixed; width: 980px;"><colgroup>
<col>
<col><col><col><col><col><col><col><col><col><col><col><col><col><col class="tbl_colHidden"></colgroup><tbody>
<tr class="advtbl_row -dg_tr -dg_tsr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col" style="width: 25px;"><input type="checkbox" name="checkAll_c0_dataForm:listView:dataTable" id="checkAll_c0_dataForm:listView:dataTable" value="0" tabindex="256"><input type="hidden" value="22312892" id="dataForm:listView:dataTable:0:PK_0" name="dataForm:listView:dataTable:0:PK_0" tabindex="257"></td><td style="white-space: nowrap; width: 29px;" align="right" class="advtbl_col advtbl_body_col"><a id="dataForm:listView:dataTable:0:HSEListPO_link_WarningImage1" name="dataForm:listView:dataTable:0:HSEListPO_link_WarningImage1" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentWarnings.xhtml?appointmentId=22312892&amp;errorType=2&amp;windowId=wt-c133-ad04-84d0"></a><input id="dataForm:listView:dataTable:0:apptList_WarnInd_11" type="hidden" name="dataForm:listView:dataTable:0:apptList_WarnInd_11" value="false" tabindex="258"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 29px;" align="right" class="advtbl_col advtbl_body_col"><a id="dataForm:listView:dataTable:0:HSEListPO_link_ErrorImage1" name="dataForm:listView:dataTable:0:HSEListPO_link_ErrorImage1" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentWarnings.xhtml?appointmentId=22312892&amp;errorType=1&amp;windowId=wt-c133-ad04-84d0"></a><input id="dataForm:listView:dataTable:0:apptList_ErrorInd_11" type="hidden" name="dataForm:listView:dataTable:0:apptList_ErrorInd_11" value="false" tabindex="259"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 95px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:apptId">*********</span><input id="dataForm:listView:dataTable:0:apptHid" type="hidden" name="dataForm:listView:dataTable:0:apptHid" value="*********" tabindex="260"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 128px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:startTime">7/17/25 18:30</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 104px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:type">Live Unload</span><input id="dataForm:listView:dataTable:0:appointmentTypeHid" type="hidden" name="dataForm:listView:dataTable:0:appointmentTypeHid" value="Live Unload" tabindex="261"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 109px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:apptType">Scheduled</span><input id="dataForm:listView:dataTable:0:apptTypeHid" type="hidden" name="dataForm:listView:dataTable:0:apptTypeHid" value="Scheduled" tabindex="262"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 68px;" class="advtbl_col advtbl_body_col"><a id="dataForm:listView:dataTable:0:ApptList_ShipmentId_output_Link_id_carr" name="dataForm:listView:dataTable:0:ApptList_ShipmentId_output_Link_id_carr" href="https://ahold-tlm.logistics.com/ofr/ra/jsp/ShipmentDetails.jsp?shipmentId=9021039&amp;shipperId=&amp;previousPage=webTenders&amp;windowId=wt-c133-ad04-84d0"><span id="dataForm:listView:dataTable:0:out4_carr"></span></a> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 89px;" class="advtbl_col advtbl_body_col"><a id="dataForm:listView:dataTable:0:Apptlist_PONbr_output_Link_id" name="dataForm:listView:dataTable:0:Apptlist_PONbr_output_Link_id" href="https://ahold-tlm.logistics.com/cbo/transactional/purchaseorder/view/PODetail.xhtml?poId=9021039&amp;shipperId=&amp;windowId=wt-c133-ad04-84d0"><span id="dataForm:listView:dataTable:0:out5">F-12926701</span></a> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 40px;" class="advtbl_col advtbl_body_col"><a id="dataForm:listView:dataTable:0:ApptList_output_Link_id" name="dataForm:listView:dataTable:0:ApptList_output_Link_id" href="https://ahold-tlm.logistics.com/cbo/transactional/asn/ASNDetails.xhtml?asnId=0&amp;tcCompanyId=&amp;windowId=wt-c133-ad04-84d0"><span id="dataForm:listView:dataTable:0:out6"></span></a> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 73px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:facility">DCDA09P</span><input id="dataForm:listView:dataTable:0:facilityHid" type="hidden" name="dataForm:listView:dataTable:0:facilityHid" value="DCDA09P" tabindex="263"> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 49px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:trailer"></span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 76px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:bolNumber"></span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 78px;" align="right" class="advtbl_col advtbl_body_col"><span id="dataForm:listView:dataTable:0:loadPosition"></span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap; width: 20px;" class="tbl_colHidden"><div id="dataForm:listView:dataTable:0:sdyncaxbt"><a href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml#?windowId=wt-c133-ad04-84d0" id="dataForm:listView:dataTable:0:tgeLink" name="dataForm:listView:dataTable:0:tgeLink" onclick="hideSynchListDetails() ;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:0:tgeLink','oncomplete':function(request,event,data){UI8Layout.Sync.onEnd();},'parameters':{'dataForm:listView:dataTable:0:tgeLink':'dataForm:listView:dataTable:0:tgeLink','editFlag':'true','noCustomizationFromSync':'true','linkClicked':'true','primaryKey':22312892} } );return false;">XdDummy</a><script type="text/javascript">
_u8_sy_1("dataForm:synccomp1","dataForm:listView:dataTable:0:tgeLink");
</script></div><span style="display:none">&nbsp;</span></td>
</tr>
<tr id="dataForm:listView:dataTable:nodataRow" class="advtbl_row trhide"><td class="advtbl_col advtbl_body_col tdhide" colspan="15" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="dataTable_hdnMaxIndexHldr" name="dataTable_hdnMaxIndexHldr" value="1" tabindex="264"></table></div><div class="emptyHoriScrollDiv"></div><div id="sortButton" style="display:none;"><input id="dataForm:listView:dataTable:sortButton" name="dataForm:listView:dataTable:sortButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:dataTable:sortButton','parameters':{'dataForm:listView:dataTable:sortButton':'dataForm:listView:dataTable:sortButton'} } );return false;" value="|ILMApptData.startTime,ILMApptData.status" type="button" tabindex="265"></div></div>
<input type="hidden" id="dataForm:listView:dataTable_trs_pageallrowskey" name="dataForm:listView:dataTable_trs_pageallrowskey" value="22312892#:#" tabindex="266"><input type="hidden" id="dataForm:listView:dataTable_selectedRows" name="dataForm:listView:dataTable_selectedRows" value="" tabindex="267"><input type="hidden" id="dataForm:listView:dataTable_selectedIdList" name="dataForm:listView:dataTable_selectedIdList" value="" tabindex="268"><input type="hidden" id="dataForm:listView:dataTable_trs_allselectedrowskey" name="dataForm:listView:dataTable_trs_allselectedrowskey" value="dataTable$:$1752205219990" tabindex="269"><script type="text/javascript">var  dataFormlistViewdataTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='dataTable';
tableObjectArray[count]=dataFormlistViewdataTable_tableObj;dataFormlistViewdataTable_tableObj.bind(document.getElementById('dataForm:listView:dataTable_container'), document.getElementById('dataForm:listView:dataTable_headDiv'), document.getElementById('dataForm:listView:dataTable_bodyDiv'), document.getElementById('dataForm:listView:dataTable_scrollDiv'),document.getElementById('dataForm:listView:dataTable_scrollDivBody'),document.getElementById('dataForm:listView:dataTable_button'),false,1,2,'dataForm:listView:dataTable','edit','yes','no','0','bottom','view',0,10,'yes','no','even','odd','Invalid Table','dataForm:listView:dataTable_selectedIdList','true','dataTable','false' ,0,8,'false','Synced','dataForm:synccomp1','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
var dataForm_listView_dataTable_sce2="true";var dataForm_listView_dataTable_custIderrors="true";var dataForm_listView_dataTable_c1="true";var dataForm_listView_dataTable_c2="true";var dataForm_listView_dataTable_c4="true";var dataForm_listView_dataTable_c5="true";var dataForm_listView_dataTable_custId4="true";var dataForm_listView_dataTable_custId5="true";var dataForm_listView_dataTable_custId6="true";var dataForm_listView_dataTable_c8="true";var dataForm_listView_dataTable_c12="true";var dataForm_listView_dataTable_c13="true";var dataForm_listView_dataTable_c15="true";var dataForm_listView_dataTable_isRangeTasks="true";</script>
<script type="text/javascript">
UI8Layout.Sync.postList("dataForm:synccomp1","dataForm:listView:dataTable",{syncajaxed:null,isA4Jajax:false});
</script><script language="JavaScript" src="/lps/resources/actionpanel/moreButton/scripts/moreButton.js"></script><div id="soheaderbuttons" class="actionPanelLeft"><input id="dataForm:listView:apptList_btn_1" type="submit" name="dataForm:listView:apptList_btn_1" value="Add" alt="Add" class="btn" tabindex="270"><input class="btn" id="dataForm:listView:apptList_btn_5" name="dataForm:listView:apptList_btn_5" onclick="approve();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:apptList_btn_5','parameters':{'dataForm:listView:apptList_btn_5':'dataForm:listView:apptList_btn_5'} } );return false;" value="Approve" alt="Approve" type="button" tabindex="271"><input class="btn" id="dataForm:listView:apptList_btn_2" name="dataForm:listView:apptList_btn_2" onclick="if( !editChecks() ) { return false};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:apptList_btn_2','oncomplete':function(request,event,data){editComplete()},'parameters':{'dataForm:listView:apptList_btn_2':'dataForm:listView:apptList_btn_2'} } );return false;" value="Edit" alt="Edit" type="button" tabindex="272"><input class="btn" id="dataForm:listView:apptList_btn_Mail" name="dataForm:listView:apptList_btn_Mail" onclick="if(!emailClick()) {return false};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:listView:apptList_btn_Mail','oncomplete':function(request,event,data){openDialogwithId('EmailDialog');},'parameters':{'dataForm:listView:apptList_btn_Mail':'dataForm:listView:apptList_btn_Mail'} } );return false;" value="Email" alt="Email" onmousedown="dummy();" type="button" tabindex="273"><span class="groupBtnSpace">&nbsp;</span><input type="hidden" id="moreActionTargetLinksoheaderbuttons" name="moreActionTargetLinksoheaderbuttons" value="" tabindex="274"><input type="hidden" id="moreActionButtonPressedsoheaderbuttons" name="moreActionButtonPressedsoheaderbuttons" value="" tabindex="275"></div></div></div></div></span></div></td></tr><tr><td class="syncsepV"><div class="syncsephld">&nbsp;</div><div class="syncsepdiv">&nbsp;</div></td></tr><tr><td class="syncdettdV"><div class="synccontout" id="dataForm:synccomp1_detout"><div class="syncmultisel" style="display:none" id="dataForm:synccomp1_detout_multisel"><div class="syncmultiseldiv -sl_sr">Multiple rows have been selected. Select single row to view results</div></div><div id="dataForm:synccomp1_detout_main" style="display: block;"><input id="dataForm:fromApptList" type="hidden" name="dataForm:fromApptList" value="true" tabindex="276"><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:syncTabjx"><input id="dataForm:prevPage" type="hidden" name="dataForm:prevPage" value="/appointment/ui/jsf/appointmentList.xhtml"><div id="dataForm:SlotGrpDialog" name="dataForm:SlotGrpDialog" class="dialog_cont" style="height:400px;width:720px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:slotGrpIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Slot Group</div></td><td><div class="pop_close" id="dataForm:slotGrpIdOuter_cCId"><input type="button"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;"><span id="dataForm:slotGrpA4J"><div id="slotGrpDivInner"><script type="text/javascript" language="JavaScript" src="/appointment/lookup/js/ILMSlotLookUp.js">
</script><span id="dataForm:SlotGrpFilter_script"></span> <input type="hidden" name="slotGrpTextName" id="slotGrpTextName"><script type="text/javascript" language="JavaScript" src="/lps/resources/panel/scripts/panel.js">
</script><div class="pnltopdiv" id="PANEL_SlotGrpFilter__Search_Panel1_top"><table id="PANEL_SlotGrpFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_SlotGrpFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:SlotGrpFilter_pg_1"><tbody><tr><td><script type="text/javascript" language="JavaScript" src="/lps/resources/caption/scripts/caption.js">
</script><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="slotGrpCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:slotGrpNameSearch" type="text" name="dataForm:slotGrpNameSearch" value=""></div></div></td></tr><tr><td><input class="btn" id="dataForm:slotGrpFilter_locgetlist" name="dataForm:slotGrpFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotGrpFilter_locgetlist','parameters':{'dataForm:slotGrpFilter_locgetlist':'dataForm:slotGrpFilter_locgetlist'} } );return false;" value="Find " type="button"></td></tr></tbody></table></div></div><span id="dataForm:slotGrpFilter_Detail"><div class="pnltopdiv" id="PANEL_slotGrpFilter_Find_Result_pannel_top"><div id="tr_slotGrpFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:slotGrpFilter__spg_1" width="100%"><tbody><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div id="slotGrpFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:slotGrpText" name="dataForm:slotGrpText" size="5" ondblclick="selectSlotGrp();return false;" style="width:90%"><option value="No matching Records">No Matching Records Found</option></select></div></td></tr><tr><td><input class="btn" id="dataForm:slotGrpFilter_SelectButton" name="dataForm:slotGrpFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotGrpFilter_SelectButton','oncomplete':function(request,event,data){selectSlotGrp();return false;},'parameters':{'dataForm:slotGrpFilter_SelectButton':'dataForm:slotGrpFilter_SelectButton'} } );return false;" value="Select" type="button"></td></tr></tbody></table></div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">//<![CDATA[
UI8Layout.data.put("SlotGrpDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:SlotGrpDialog",dragHandleId:"dataForm:slotGrpIdOuter_dHId",onDialog:"",closeClientId:"dataForm:slotGrpIdOuter_cCId"});
//]]>
</script><div id="dataForm:SlotDialog" name="dataForm:SlotDialog" class="dialog_cont" style="height:400px;width:720px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:slotIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Slot</div></td><td><div class="pop_close" id="dataForm:slotIdOuter_cCId"><input type="button"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;"><span id="dataForm:slotA4J"><div id="slotDivInner"><script type="text/javascript" language="JavaScript" src="/appointment/lookup/js/ILMSlotLookUp.js">
</script><span id="dataForm:SlotFilter_script"></span> <input type="hidden" name="slotTextName" id="slotTextName"><div class="pnltopdiv" id="PANEL_SlotFilter__Search_Panel1_top"><table id="PANEL_SlotFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_SlotFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:SlotFilter_pg_1"><tbody><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="slotCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:slotNameSearch" type="text" name="dataForm:slotNameSearch" value=""></div></div></td></tr><tr><td><input class="btn" id="dataForm:slotFilter_locgetlist" name="dataForm:slotFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotFilter_locgetlist','parameters':{'dataForm:slotFilter_locgetlist':'dataForm:slotFilter_locgetlist'} } );return false;" value="Find " type="button"></td></tr></tbody></table></div></div><span id="dataForm:slotFilter_Detail"><div class="pnltopdiv" id="PANEL_slotFilter_Find_Result_pannel_top"><div id="tr_slotFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:slotFilter__spg_1" width="100%"><tbody><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div id="slotFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:slotText" name="dataForm:slotText" size="5" ondblclick="selectSlot();return false;" style="width:90%"><option value="No matching Records">No Matching Records Found</option></select></div></td></tr><tr><td><input class="btn" id="dataForm:slotFilter_SelectButton" name="dataForm:slotFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotFilter_SelectButton','oncomplete':function(request,event,data){selectSlot();return false;},'parameters':{'dataForm:slotFilter_SelectButton':'dataForm:slotFilter_SelectButton'} } );return false;" value="Select" type="button"></td></tr></tbody></table></div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">//<![CDATA[
UI8Layout.data.put("SlotDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:SlotDialog",dragHandleId:"dataForm:slotIdOuter_dHId",onDialog:"",closeClientId:"dataForm:slotIdOuter_cCId"});
//]]>
</script><script type="text/javascript" language="JavaScript" src="/appointment/lookup/js/FacilityLookup.js">
</script><span id="dataForm:facLookupScript"></span><div id="dataForm:YMFacilityDialog" name="dataForm:YMFacilityDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:facilityDialog_dHId"><div class="pop_title -pdlg_dttc">Find Trailer</div></td><td><div class="pop_close" id="dataForm:facilityDialog_cCId"><input type="button"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:400px;width:500px;"><span id="dataForm:facLookupA4J"><iframe id="YMFacilityiframeId" frameborder="0" scrolling="no" width="500px" height="280px"></iframe></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">//<![CDATA[
UI8Layout.data.put("YMFacilityDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:YMFacilityDialog",dragHandleId:"dataForm:facilityDialog_dHId",onDialog:"",closeClientId:"dataForm:facilityDialog_cCId"});
//]]>
</script><div id="dataForm:DriverDialog" name="dataForm:DriverDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:driverIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Driver</div></td><td><div class="pop_close" id="dataForm:driverIdOuter_cCId"><input type="button"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:450px;width:450px;"><span id="dataForm:ddriverA4J"><div id="driverDivInner"><script type="text/javascript" language="JavaScript" src="/appointment/lookup/js/ILMDriverLookUp.js">
</script><span id="dataForm:check_locn_script"></span> <input type="hidden" name="driverText" id="driverText"> <input type="hidden" name="driverTextName"> <input type="hidden" name="driverStateName"> <input type="hidden" name="driverCountryName"> <input type="hidden" name="driverLicense"> <input type="hidden" name="drvLicenseHid"> <input type="hidden" name="drvLicExpHid"> <input type="hidden" name="drvrLicStateHid"> <input type="hidden" name="drvrLicCountryHid"> <input type="hidden" name="drvCntNumHid"> <input type="hidden" name="driverCode"> <input type="hidden" name="carrierCode"> <input type="hidden" name="carrierCodeHid"> <input type="hidden" name="driverContact"> <input type="hidden" name="driverLicExpiry"> <input type="hidden" name="driverFound" value="false"> <input type="hidden" name="selCountry" value="[US]"><div class="pnltopdiv" id="PANEL_ydriverLocn33__Search_Panel1_top"><table id="PANEL_ydriverLocn33__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ydriverLocn33__Search_Panel1" class="pnlcondiv"><table id="dataForm:driver22_pg_1"><tbody><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driveNameSrch"><span title="">First name:</span><span class="notRequired">p</span><br><input id="dataForm:driverName" type="text" name="dataForm:driverName" value=""></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLstNmSrch"><span title="">Last name:</span><span class="notRequired">p</span><br><input id="dataForm:driverLastName" type="text" name="dataForm:driverLastName" value=""></div></div></td></tr><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLcSrch"><span title="">License number:</span><span class="notRequired">p</span><br><input id="dataForm:driverLicNo" type="text" name="dataForm:driverLicNo" value=""></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverCrSrch"><span title="">Carrier:</span><span class="notRequired">p</span><br><script type="text/javascript" language="JavaScript" src="/lps/resources/editControl/scripts/idLookup.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/editControl/scripts/autocompleteinput.js">
</script><script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:driverCRCodeecId" name="dataForm:driverCRCodeecId" value=""><input type="hidden" id="dataForm_driverCRCode_enterKey" value="false"><input type="hidden" id="triggerdataForm_driverCRCode_enterKey" value="false"><input type="text" id="dataForm:driverCRCode" name="dataForm:driverCRCode" onfocus="javascript: focusOnTextBox('dataForm_driverCRCode_enterKey')" onblur="javascript: blurOnTextBox('dataForm_driverCRCode_enterKey')" onkeypress="if(enterPressed(event,'dataForm:driverCRCode') )return false;" value="" title="" alt="Find Carrier">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcbolookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcbolookupBackingBean.getBUMap%7D&amp;lookupType=Carrier&amp;is3plEnabled=true&amp;returnId=dataForm:driverCRCode&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:driverCRCode" title="Find Carrier" align="absmiddle" id="trigger_dataForm:driverCRCode" name="trigger_dataForm:driverCRCode" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_driverCRCode_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_driverCRCode_enterKey')"></div></div></td></tr><tr><td><input class="btn" id="dataForm:driver_locgetlist22" name="dataForm:driver_locgetlist22" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver_locgetlist22','parameters':{'dataForm:driver_locgetlist22':'dataForm:driver_locgetlist22'} } );return false;" value="Find " type="button"></td></tr></tbody></table></div></div><span id="dataForm:DRVlistPanel"><span id="dataForm:custIdrecordmsg"></span><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/sortable.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/datatable.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/tableCommon.js">
</script><input type="hidden" name="dataForm:DrvdataTable_deleteHidden" value="" id="dataForm:DrvdataTable_deleteHidden"><input type="hidden" name="dataForm:DrvdataTable_selectedRows" value="#:#" id="dataForm:DrvdataTable_selectedRows"><div class="datatbl_contr" id="dataForm:DrvdataTable_container" style="overflow-y: hidden;"><div id="dataForm:DrvdataTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:DrvdataTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:DrvdataTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:DrvdataTable:isSortButtonClick" id="dataForm:DrvdataTable:isSortButtonClick" value=""><input type="hidden" name="dataForm:DrvdataTable:sortDir" id="dataForm:DrvdataTable:sortDir" value="desc"><input type="hidden" name="dataForm:DrvdataTable:colCount" id="dataForm:DrvdataTable:colCount" value=""><input type="hidden" name="dataForm:DrvdataTable:tableClicked" id="dataForm:DrvdataTable:tableClicked" value=""><input type="hidden" name="dataForm:DrvdataTable:tableResized" id="dataForm:DrvdataTable:tableResized" value="false"><div class="advtbl_contr_head" id="dataForm:DrvdataTable_headDiv"><table id="dataForm:DrvdataTable" cellspacing="0"><colgroup><col><col><col><col><col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row"><td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top"><input type="checkbox" name="dataForm:DrvdataTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'DrvdataTable');"></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:DrvdataTable:custId21">Driver name</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId31">License number</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId41">Carrier code</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId61">State</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId51">Appointment status</span></td></tr></thead></table></div><div id="dataForm:DrvdataTable_bodyDiv" class="advtbl_contr_body"><input type="hidden" id="DrvdataTable_hdnMaxIndexHldr" name="DrvdataTable_hdnMaxIndexHldr" value="9"><table id="dataForm:DrvdataTable_body" cellspacing="0"><colgroup><col><col><col><col><col><col></colgroup><tbody><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:DrvdataTable" id="checkAll_c0_dataForm:DrvdataTable" value="0"><input type="hidden" value="102" id="dataForm:DrvdataTable:0:PK_0" name="dataForm:DrvdataTable:0:PK_0"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId22">Catrena Bishop</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId32">1617121</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId62">ME</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c1_dataForm:DrvdataTable" id="checkAll_c1_dataForm:DrvdataTable" value="1"><input type="hidden" value="82" id="dataForm:DrvdataTable:1:PK_1" name="dataForm:DrvdataTable:1:PK_1"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId22">Dana McQuesten</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId32">3522220</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId62">ME</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c2_dataForm:DrvdataTable" id="checkAll_c2_dataForm:DrvdataTable" value="2"><input type="hidden" value="42" id="dataForm:DrvdataTable:2:PK_2" name="dataForm:DrvdataTable:2:PK_2"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId22">Daniel Greenlee</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId32">G654135234467</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId62">MD</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c3_dataForm:DrvdataTable" id="checkAll_c3_dataForm:DrvdataTable" value="3"><input type="hidden" value="142" id="dataForm:DrvdataTable:3:PK_3" name="dataForm:DrvdataTable:3:PK_3"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId22">Eric Fitzpatrick</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId32">32681535</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId62">PA</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c4_dataForm:DrvdataTable" id="checkAll_c4_dataForm:DrvdataTable" value="4"><input type="hidden" value="122" id="dataForm:DrvdataTable:4:PK_4" name="dataForm:DrvdataTable:4:PK_4"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId22">Gardner Flemming</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId32">4437148</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId62">ME</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c5_dataForm:DrvdataTable" id="checkAll_c5_dataForm:DrvdataTable" value="5"><input type="hidden" value="202" id="dataForm:DrvdataTable:5:PK_5" name="dataForm:DrvdataTable:5:PK_5"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId22">Jeffrey Weymouth</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId32">7484099</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId62">ME</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c6_dataForm:DrvdataTable" id="checkAll_c6_dataForm:DrvdataTable" value="6"><input type="hidden" value="2" id="dataForm:DrvdataTable:6:PK_6" name="dataForm:DrvdataTable:6:PK_6"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId22">Joe Shmo</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId32">12345678</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId42">ADTEST1</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId62">MA</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId52">On Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c7_dataForm:DrvdataTable" id="checkAll_c7_dataForm:DrvdataTable" value="7"><input type="hidden" value="23" id="dataForm:DrvdataTable:7:PK_7" name="dataForm:DrvdataTable:7:PK_7"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId22">Joe Shmo</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId32">12345678</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId62">MA</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c8_dataForm:DrvdataTable" id="checkAll_c8_dataForm:DrvdataTable" value="8"><input type="hidden" value="163" id="dataForm:DrvdataTable:8:PK_8" name="dataForm:DrvdataTable:8:PK_8"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId22">Montie Thompson</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId32">T51254551294</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId62">IL</span> <span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td></tr><tr id="dataForm:DrvdataTable:nodataRow" class="advtbl_row trhide"><td class="advtbl_col advtbl_body_col tdhide" colspan="6" align="left">No data found</td></tr></tbody></table></div><div class="emptyHoriScrollDiv"></div><div id="sortButton" style="display:none;"><input id="dataForm:DrvdataTable:sortButton" name="dataForm:DrvdataTable:sortButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:DrvdataTable:sortButton','parameters':{'dataForm:DrvdataTable:sortButton':'dataForm:DrvdataTable:sortButton'} } );return false;" value="|" type="button"></div></div> <input type="hidden" id="dataForm:DrvdataTable_trs_pageallrowskey" name="dataForm:DrvdataTable_trs_pageallrowskey" value="102#:#82#:#42#:#142#:#122#:#202#:#2#:#23#:#163#:#"><input type="hidden" id="dataForm:DrvdataTable_selectedRows" name="dataForm:DrvdataTable_selectedRows" value=""><input type="hidden" id="dataForm:DrvdataTable_selectedIdList" name="dataForm:DrvdataTable_selectedIdList" value=""><input type="hidden" id="dataForm:DrvdataTable_trs_allselectedrowskey" name="dataForm:DrvdataTable_trs_allselectedrowskey" value="DrvdataTable$:$1752205181860"><script type="text/javascript">//<![CDATA[
var  dataFormDrvdataTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='DrvdataTable';
tableObjectArray[count]=dataFormDrvdataTable_tableObj;dataFormDrvdataTable_tableObj.bind(document.getElementById('dataForm:DrvdataTable_container'), document.getElementById('dataForm:DrvdataTable_headDiv'), document.getElementById('dataForm:DrvdataTable_bodyDiv'), document.getElementById('dataForm:DrvdataTable_scrollDiv'),document.getElementById('dataForm:DrvdataTable_scrollDivBody'),document.getElementById('dataForm:DrvdataTable_button'),true,1,2,'dataForm:DrvdataTable','edit','yes','no','0','bottom','view',0,2147483647,'yes','no','even','odd','Invalid Table','dataForm:DrvdataTable_selectedIdList','true','DrvdataTable','false' ,0,8,'false','Synced','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',1);
var dataForm_DrvdataTable_custId2="true";var dataForm_DrvdataTable_custId3="true";var dataForm_DrvdataTable_custId4="true";var dataForm_DrvdataTable_custId6="true";var dataForm_DrvdataTable_custId5="true";
//]]>
</script> <script type="text/javascript">//<![CDATA[
UI8Layout.ondialogTableLoad("DrvdataTable");
//]]>
</script><input id="dataForm:allDrivers" type="hidden" name="dataForm:allDrivers" value="Catrena Bishop;1617121;ME;PREPAID;US;9/22/21#Dana McQuesten;3522220;ME;PREPAID;US;5/4/24#Daniel Greenlee;G654135234467;MD;PREPAID;US;6/18/22#Eric Fitzpatrick;32681535;PA;PREPAID;US;11/11/23#Gardner Flemming;4437148;ME;PREPAID;US;6/16/26#Jeffrey Weymouth;7484099;ME;PREPAID;US;5/18/27#Joe Shmo;12345678;MA;ADTEST1;US;12/31/20#Joe Shmo;12345678;MA;PREPAID;US;12/31/20#Montie Thompson;T51254551294;IL;PREPAID;US;10/16/26#"><input class="btn" id="dataForm:driver22Filter_SelectButton" name="dataForm:driver22Filter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver22Filter_SelectButton','oncomplete':function(request,event,data){selectDriver();return false;},'parameters':{'dataForm:driver22Filter_SelectButton':'dataForm:driver22Filter_SelectButton'} } );return false;" value="Select" type="button"></span><span id="dataForm:driverFndJSPanel"><input id="dataForm:drvFound" type="hidden" name="dataForm:drvFound" value="false"></span><div class="pnltopdiv" id="PANEL_ydriverLocn33__Create_Panel1_top"><table id="PANEL_ydriverLocn33__Create_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><img src="/lps/resources/panel/images/expand.gif" id="tr_ydriverLocn33__Create_Panel1img" onclick="return ExpandCollapse('tr_ydriverLocn33__Create_Panel1','/lps/resources/panel/images/expand.gif','/lps/resources/panel/images/collapse.gif')">New Driver</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ydriverLocn33__Create_Panel1" class="pnlcondiv"><table id="dataForm:driver22_pg_2"><tbody><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driveName"><span title="">First Name:</span><span class="required" id="driveName_cptnSpn">*</span><br><input id="dataForm:driverNameNew1" type="text" name="dataForm:driverNameNew1" value="" maxlength="25"></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLstNm"><span title="">Last Name:</span><span class="required" id="driverLstNm_cptnSpn">*</span><br><input id="dataForm:driverLastNameNew1" type="text" name="dataForm:driverLastNameNew1" value="" maxlength="25"></div></div></td></tr><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLc"><span title="">Driver license number:</span><span class="required" id="driverLc_cptnSpn">*</span><br><input id="dataForm:driverLicNoNew1" type="text" name="dataForm:driverLicNoNew1" value="" maxlength="25"></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverCntctNm"><span title="">Contact number:</span><span class="notRequired">p</span><br><input id="dataForm:driverContactNum" type="text" name="dataForm:driverContactNum" value="" maxlength="25"></div></div></td></tr><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="carrier"><span title="">Carrier:</span><span class="required" id="carrier_cptnSpn">*</span><br><script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:driverCRCode1ecId" name="dataForm:driverCRCode1ecId" value=""><input type="hidden" id="dataForm_driverCRCode1_enterKey" value="false"><input type="hidden" id="triggerdataForm_driverCRCode1_enterKey" value="false"><input type="text" id="dataForm:driverCRCode1" name="dataForm:driverCRCode1" onfocus="javascript: focusOnTextBox('dataForm_driverCRCode1_enterKey')" onblur="javascript: blurOnTextBox('dataForm_driverCRCode1_enterKey')" onkeypress="if(enterPressed(event,'dataForm:driverCRCode1') )return false;" value="" title="" alt="Find Carrier" maxlength="25">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcbolookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcbolookupBackingBean.getBUMap%7D&amp;lookupType=Carrier&amp;is3plEnabled=true&amp;returnId=dataForm:driverCRCode1&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:driverCRCode1" title="Find Carrier" align="absmiddle" id="trigger_dataForm:driverCRCode1" name="trigger_dataForm:driverCRCode1" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_driverCRCode1_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_driverCRCode1_enterKey')"></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="DriverEdit_capCountry"><span title="">License Country:</span><span class="required" id="DriverEdit_capCountry_cptnSpn">*</span><br><script type="text/javascript" language="JavaScript" src="/lps/resources/common/scripts/dependentList.js">
</script><script type="text/javascript">//<![CDATA[

  var State_ListArray = [ [ "",[[ " ","(select one)"]]],[ "US",[[ " ","(none)"],[ "AA","AA"],[ "AE","AE "],[ "AK","AK"],[ "AL","AL"],[ "AP","AP"],[ "AR","AR"],[ "AS","AS"],[ "AZ","AZ"],[ "CA","CA"],[ "CO","CO"],[ "CT","CT"],[ "DC","DC"],[ "DE","DE"],[ "FL","FL"],[ "FM","FM"],[ "GA","GA"],[ "GU","GU"],[ "HI","HI"],[ "IA","IA"],[ "ID","ID"],[ "IL","IL"],[ "IN","IN"],[ "KS","KS"],[ "KY","KY"],[ "LA","LA"],[ "MA","MA"],[ "MD","MD"],[ "ME","ME"],[ "MH","MH"],[ "MI","MI"],[ "MN","MN"],[ "MO","MO"],[ "MP","MP"],[ "MS","MS"],[ "MT","MT"],[ "NC","NC"],[ "ND","ND"],[ "NE","NE"],[ "NH","NH"],[ "NJ","NJ"],[ "NM","NM"],[ "NV","NV"],[ "NY","NY"],[ "OH","OH"],[ "OK","OK"],[ "OR","OR"],[ "PA","PA"],[ "PR","PR"],[ "PW","PW"],[ "RI","RI"],[ "SC","SC"],[ "SD","SD"],[ "TN","TN"],[ "TX","TX"],[ "UT","UT"],[ "VA","VA"],[ "VI","VI"],[ "VT","VT"],[ "WA","WA"],[ "WI","WI"],[ "WV","WV"],[ "WY","WY"]]],[ "AF",[[ " ","(none)"],[ "AF","AF"]]],[ "AL",[[ " ","(none)"],[ "AL","AL"]]],[ "DZ",[[ " ","(none)"],[ "DZ","DZ"]]],[ "AS",[[ " ","(none)"],[ "AS","AS"]]],[ "AD",[[ " ","(none)"],[ "AD","AD"]]],[ "AO",[[ " ","(none)"],[ "AO","AO"]]],[ "AI",[[ " ","(none)"],[ "AI","AI "]]],[ "AQ",[[ " ","(none)"],[ "AQ","AQ"]]],[ "AG",[[ " ","(none)"],[ "AG","AG"]]],[ "AR",[[ " ","(none)"],[ "BA","BA"],[ "CA","CA"],[ "CB","CB"],[ "CD","CD"],[ "CH","CH"],[ "CR","CR"],[ "ER","ER"],[ "FO","FO"],[ "LP","LP"],[ "LR","LR"],[ "MD","MD"],[ "MI","MI"],[ "NE","NE"],[ "PJ","PJ"],[ "RN","RN"],[ "SA","SA"],[ "SC","SC"],[ "SE","SE"],[ "SF","SF"],[ "SJ","SJ"],[ "SL","SL"],[ "TF","TF"],[ "TU","TU"]]],[ "AM",[[ " ","(none)"],[ "AM","AM"]]],[ "AW",[[ " ","(none)"],[ "AW","AW"]]],[ "AU",[[ " ","(none)"],[ "NS","NS"],[ "NT","NT"],[ "QL","QL"],[ "SA","SA"],[ "TS","TS"],[ "VI","VI"],[ "WA","WA"]]],[ "AT",[[ " ","(none)"],[ "AT","AT"]]],[ "AZ",[[ " ","(none)"],[ "AZ","AZ"]]],[ "BS",[[ " ","(none)"],[ "BS","BS"]]],[ "BH",[[ " ","(none)"],[ "BH","BH"]]],[ "BD",[[ " ","(none)"],[ "BD","BD"]]],[ "BB",[[ " ","(none)"],[ "BB","BB"]]],[ "BY",[[ " ","(none)"],[ "BY","BY "]]],[ "BE",[[ " ","(none)"],[ "BE","BE"]]],[ "BZ",[[ " ","(none)"],[ "BZ","BZ"]]],[ "BJ",[[ " ","(none)"],[ "BJ","BJ"]]],[ "BM",[[ " ","(none)"],[ "BM","BM"]]],[ "BT",[[ " ","(none)"],[ "BT","BT"]]],[ "BO",[[ " ","(none)"],[ "BO","BO"]]],[ "BA",[[ " ","(none)"],[ "BA","BA"]]],[ "BW",[[ " ","(none)"],[ "BW","BW "]]],[ "BV",[[ " ","(none)"],[ "BV","BV"]]],[ "BR",[[ " ","(none)"],[ "AC","AC"],[ "AL","AL"],[ "AM","AM"],[ "AP","AP"],[ "BA","BA"],[ "CE","CE"],[ "DF","DF"],[ "ES","ES"],[ "FN","FN"],[ "GO","GO"],[ "MA","MA"],[ "MG","MG"],[ "MS","MS"],[ "MT","MT"],[ "PA","PA"],[ "PB","PB"],[ "PE","PE"],[ "PI","PI"],[ "PR","PR"],[ "RJ","RJ"],[ "RN","RN"],[ "RO","RO"],[ "RR","RR"],[ "RS","RS"],[ "SC","SC"],[ "SE","SE"],[ "SP","SP"],[ "TO","TO"]]],[ "IO",[[ " ","(none)"],[ "IO","IO"]]],[ "BN",[[ " ","(none)"],[ "BN","BN"]]],[ "BG",[[ " ","(none)"],[ "BG","BG"]]],[ "BF",[[ " ","(none)"],[ "BF","BF"]]],[ "BI",[[ " ","(none)"],[ "BI","BI"]]],[ "KH",[[ " ","(none)"],[ "KH","KH"]]],[ "CM",[[ " ","(none)"],[ "CM","CM"]]],[ "CA",[[ " ","(none)"],[ "AB","AB"],[ "BC","BC"],[ "MB","MB"],[ "NB","NB"],[ "NF","NF"],[ "NS","NS"],[ "NT","NT"],[ "NU","NU"],[ "ON","ON"],[ "PE","PE"],[ "QC","QC"],[ "SK","SK"],[ "YT","YT"]]],[ "CV",[[ " ","(none)"],[ "CV","CV"]]],[ "KY",[[ " ","(none)"],[ "KY","KY"]]],[ "CF",[[ " ","(none)"],[ "CF","CF"]]],[ "TD",[[ " ","(none)"],[ "TD","TD"]]],[ "CL",[[ " ","(none)"],[ "CL","CL"]]],[ "CN",[[ " ","(none)"],[ "CN","CN"]]],[ "CX",[[ " ","(none)"],[ "CX","CX "]]],[ "CC",[[ " ","(none)"],[ "CC","CC"]]],[ "CO",[[ " ","(none)"],[ "CO","CO"]]],[ "KM",[[ " ","(none)"],[ "KM","KM"]]],[ "CG",[[ " ","(none)"],[ "CG","CG"]]],[ "CD",[[ " ","(none)"],[ "CD","CD"]]],[ "CK",[[ " ","(none)"],[ "CK","CK"]]],[ "CR",[[ " ","(none)"],[ "CR","CR"]]],[ "HR",[[ " ","(none)"],[ "HR","HR"]]],[ "CU",[[ " ","(none)"],[ "CU","CU"]]],[ "CY",[[ " ","(none)"],[ "CY","CY"]]],[ "CZ",[[ " ","(none)"],[ "CZ","CZ"]]],[ "DK",[[ " ","(none)"],[ "DK","DK"]]],[ "DJ",[[ " ","(none)"],[ "DJ","DJ"]]],[ "DM",[[ " ","(none)"],[ "DM","DM"]]],[ "DO",[[ " ","(none)"],[ "DO","DO"]]],[ "TP",[[ " ","(none)"],[ "TP","TP"]]],[ "EC",[[ " ","(none)"],[ "EC","EC"]]],[ "ED",[[ " ","(none)"],[ "ED","ED"]]],[ "EG",[[ " ","(none)"],[ "EG","EG"]]],[ "SV",[[ " ","(none)"],[ "SV","SV"]]],[ "GQ",[[ " ","(none)"],[ "GQ","GQ"]]],[ "ER",[[ " ","(none)"],[ "ER","ER"]]],[ "EE",[[ " ","(none)"],[ "EE","EE"]]],[ "ET",[[ " ","(none)"],[ "ET","ET"]]],[ "FK",[[ " ","(none)"],[ "FK","FK"]]],[ "FO",[[ " ","(none)"],[ "FO","FO"]]],[ "FJ",[[ " ","(none)"],[ "FJ","FJ"]]],[ "FI",[[ " ","(none)"],[ "FI","FI"]]],[ "CS",[[ " ","(none)"],[ "CS","CS"]]],[ "SU",[[ " ","(none)"],[ "SU","SU"]]],[ "FR",[[ " ","(none)"],[ "FR","FR"]]],[ "FX",[[ " ","(none)"],[ "FX","FX"]]],[ "GF",[[ " ","(none)"],[ "GF","GF"]]],[ "TF",[[ " ","(none)"],[ "TF","TF"]]],[ "GA",[[ " ","(none)"],[ "GA","GA"]]],[ "GM",[[ " ","(none)"],[ "GM","GM"]]],[ "GE",[[ " ","(none)"],[ "GE","GE"]]],[ "DE",[[ " ","(none)"],[ "DE","DE"]]],[ "GH",[[ " ","(none)"],[ "GH","GH"]]],[ "GI",[[ " ","(none)"],[ "GI","GI"]]],[ "GB",[[ " ","(none)"],[ "GB","GB"]]],[ "GR",[[ " ","(none)"],[ "GR","GR"]]],[ "GL",[[ " ","(none)"],[ "GL","GL"]]],[ "GD",[[ " ","(none)"],[ "GD","GD"]]],[ "GP",[[ " ","(none)"],[ "GP","GP"]]],[ "GU",[[ " ","(none)"],[ "GU","GU"]]],[ "GT",[[ " ","(none)"],[ "GT","GT"]]],[ "GN",[[ " ","(none)"],[ "GN","GN"]]],[ "GW",[[ " ","(none)"],[ "GW","GW"]]],[ "GY",[[ " ","(none)"],[ "GY","GY"]]],[ "HT",[[ " ","(none)"],[ "HT","HT"]]],[ "HM",[[ " ","(none)"],[ "HM","HM"]]],[ "VA",[[ " ","(none)"],[ "VA","VA"]]],[ "HN",[[ " ","(none)"],[ "HN","HN"]]],[ "HK",[[ " ","(none)"],[ "HK","HK"]]],[ "HU",[[ " ","(none)"],[ "HU","HU"]]],[ "IS",[[ " ","(none)"],[ "IS","IS"]]],[ "IN",[[ " ","(none)"],[ "IN","IN"]]],[ "ID",[[ " ","(none)"],[ "ID","ID"]]],[ "IR",[[ " ","(none)"],[ "IR","IR"]]],[ "IQ",[[ " ","(none)"],[ "IQ","IQ"]]],[ "IE",[[ " ","(none)"],[ "IE","IE"]]],[ "IL",[[ " ","(none)"],[ "IL","IL"]]],[ "IT",[[ " ","(none)"],[ "IT","IT"]]],[ "CI",[[ " ","(none)"],[ "CI","CI"]]],[ "JM",[[ " ","(none)"],[ "JM","JM"]]],[ "JP",[[ " ","(none)"],[ "JP","JP"]]],[ "JO",[[ " ","(none)"],[ "JO","JO"]]],[ "KZ",[[ " ","(none)"],[ "KZ","KZ"]]],[ "KE",[[ " ","(none)"],[ "KE","KE"]]],[ "KI",[[ " ","(none)"],[ "KI","KI"]]],[ "KV",[[ " ","(none)"]]],[ "KW",[[ " ","(none)"],[ "KW","KW"]]],[ "KG",[[ " ","(none)"],[ "KG","KG"]]],[ "LA",[[ " ","(none)"],[ "LA","LA"]]],[ "LV",[[ " ","(none)"],[ "LV","LV"]]],[ "LB",[[ " ","(none)"],[ "LB","LB"]]],[ "LS",[[ " ","(none)"],[ "LS","LS"]]],[ "LR",[[ " ","(none)"],[ "LR","LR"]]],[ "LY",[[ " ","(none)"],[ "LY","LY"]]],[ "LI",[[ " ","(none)"],[ "LI","LI"]]],[ "LT",[[ " ","(none)"],[ "LT","LT"]]],[ "LU",[[ " ","(none)"],[ "LU","LU"]]],[ "MO",[[ " ","(none)"],[ "MO","MO"]]],[ "MK",[[ " ","(none)"],[ "MK","MK"]]],[ "MG",[[ " ","(none)"],[ "MG","MG"]]],[ "MW",[[ " ","(none)"],[ "MW","MW"]]],[ "MY",[[ " ","(none)"],[ "MY","MY"]]],[ "MV",[[ " ","(none)"],[ "MV","MV"]]],[ "ML",[[ " ","(none)"],[ "ML","ML"]]],[ "MT",[[ " ","(none)"],[ "MT","MT"]]],[ "MH",[[ " ","(none)"],[ "MH","MH"]]],[ "MQ",[[ " ","(none)"],[ "MQ","MQ"]]],[ "MR",[[ " ","(none)"],[ "MR","MR"]]],[ "MU",[[ " ","(none)"],[ "MU","MU"]]],[ "YT",[[ " ","(none)"],[ "YT","YT"]]],[ "MX",[[ " ","(none)"],[ "AG","AG"],[ "BC","BC"],[ "BJ","BJ"],[ "BS","BS"],[ "CH","CH"],[ "CI","CI"],[ "CL","CL"],[ "CP","CP"],[ "CU","CU"],[ "DF","DF"],[ "DG","DG"],[ "EM","EM"],[ "GJ","GJ"],[ "GR","GR"],[ "HG","HG"],[ "JA","JA"],[ "MH","MH"],[ "MR","MR"],[ "MX","MX.CODE"],[ "NA","NA"],[ "NL","NL"],[ "OA","OA"],[ "PU","PU"],[ "QA","QA"],[ "QR","QR"],[ "SI","SI"],[ "SL","SL"],[ "SO","SO"],[ "TA","TA"],[ "TL","TL"],[ "TM","TM"],[ "VL","VL"],[ "YC","YC"],[ "ZT","ZT"]]],[ "FM",[[ " ","(none)"],[ "FM","FM"]]],[ "MD",[[ " ","(none)"],[ "MD","MD"]]],[ "MC",[[ " ","(none)"],[ "MC","MC"]]],[ "MN",[[ " ","(none)"],[ "MN","MN"]]],[ "MS",[[ " ","(none)"],[ "MS","MS"]]],[ "MA",[[ " ","(none)"],[ "MA","MA"]]],[ "MZ",[[ " ","(none)"],[ "MZ","MZ"]]],[ "MM",[[ " ","(none)"],[ "MM","MM"]]],[ "NA",[[ " ","(none)"],[ "NA","NA"]]],[ "NR",[[ " ","(none)"],[ "NR","NR"]]],[ "NP",[[ " ","(none)"],[ "NP","NP"]]],[ "NL",[[ " ","(none)"],[ "NL","NL"]]],[ "AN",[[ " ","(none)"],[ "AN","AN"]]],[ "NT",[[ " ","(none)"],[ "NT","NT"]]],[ "NC",[[ " ","(none)"],[ "NC","NC"]]],[ "NZ",[[ " ","(none)"],[ "NZ","NZ"]]],[ "NI",[[ " ","(none)"],[ "NI","NI"]]],[ "NE",[[ " ","(none)"],[ "NE","NE"]]],[ "NG",[[ " ","(none)"],[ "NG","NG"]]],[ "NU",[[ " ","(none)"],[ "NU","NU"]]],[ "NF",[[ " ","(none)"],[ "NF","NF"]]],[ "KP",[[ " ","(none)"],[ "KP","KP"]]],[ "MP",[[ " ","(none)"],[ "MP","MP"]]],[ "NO",[[ " ","(none)"],[ "NO","NO"]]],[ "OM",[[ " ","(none)"],[ "OM","OM"]]],[ "PK",[[ " ","(none)"],[ "PK","PK"]]],[ "PW",[[ " ","(none)"],[ "PW","PW"]]],[ "PA",[[ " ","(none)"],[ "PA","PA"]]],[ "PG",[[ " ","(none)"],[ "PG","PG"]]],[ "PY",[[ " ","(none)"],[ "PY","PY"]]],[ "PE",[[ " ","(none)"],[ "PE","PE"]]],[ "PH",[[ " ","(none)"],[ "PH","PH"]]],[ "PN",[[ " ","(none)"],[ "PN","PN"]]],[ "PL",[[ " ","(none)"],[ "PL","PL"]]],[ "PF",[[ " ","(none)"],[ "PF","PF"]]],[ "PT",[[ " ","(none)"],[ "PT","PT"]]],[ "PR",[[ " ","(none)"],[ "PR","PR"]]],[ "QA",[[ " ","(none)"],[ "QA","QA"]]],[ "RE",[[ " ","(none)"],[ "RE","RE"]]],[ "RO",[[ " ","(none)"],[ "RO","RO"]]],[ "RU",[[ " ","(none)"],[ "RU","RU"]]],[ "RW",[[ " ","(none)"],[ "RW","RW"]]],[ "GS",[[ " ","(none)"],[ "GS","GS"]]],[ "SH",[[ " ","(none)"],[ "SH","SH"]]],[ "KN",[[ " ","(none)"],[ "KN","KN"]]],[ "LC",[[ " ","(none)"],[ "LC","LC"]]],[ "PM",[[ " ","(none)"],[ "PM","PM"]]],[ "ST",[[ " ","(none)"],[ "ST","ST"]]],[ "VC",[[ " ","(none)"],[ "VC","VC"]]],[ "WS",[[ " ","(none)"],[ "WS","WS"]]],[ "SM",[[ " ","(none)"],[ "SM","SM"]]],[ "SA",[[ " ","(none)"],[ "SA","SA"]]],[ "SN",[[ " ","(none)"],[ "SN","SN"]]],[ "SC",[[ " ","(none)"],[ "SC","SC"]]],[ "SL",[[ " ","(none)"],[ "SL","SL"]]],[ "SG",[[ " ","(none)"],[ "SG","SG"]]],[ "SK",[[ " ","(none)"],[ "SK","SK"]]],[ "SI",[[ " ","(none)"],[ "SI","SI"]]],[ "SB",[[ " ","(none)"],[ "SB","SB"]]],[ "SO",[[ " ","(none)"],[ "SO","SO"]]],[ "ZA",[[ " ","(none)"],[ "ZA","ZA"]]],[ "KR",[[ " ","(none)"],[ "KR","KR"]]],[ "ES",[[ " ","(none)"],[ "ES","ES"]]],[ "LK",[[ " ","(none)"],[ "LK","LK"]]],[ "SD",[[ " ","(none)"],[ "SD","SD"]]],[ "SR",[[ " ","(none)"],[ "SR","SR"]]],[ "SJ",[[ " ","(none)"],[ "SJ","SJ"]]],[ "SZ",[[ " ","(none)"],[ "SZ","SZ"]]],[ "SE",[[ " ","(none)"],[ "SE","SE"]]],[ "CH",[[ " ","(none)"],[ "CH","CH"]]],[ "SY",[[ " ","(none)"],[ "SY","SY"]]],[ "TJ",[[ " ","(none)"],[ "TJ","TJ"]]],[ "TW",[[ " ","(none)"],[ "TW","TW"]]],[ "TZ",[[ " ","(none)"],[ "TZ","TZ"]]],[ "TH",[[ " ","(none)"],[ "TH","TH"]]],[ "TG",[[ " ","(none)"],[ "TG","TG"]]],[ "TK",[[ " ","(none)"],[ "TK","TK"]]],[ "TO",[[ " ","(none)"],[ "TO","TO"]]],[ "TT",[[ " ","(none)"],[ "TT","TT"]]],[ "TN",[[ " ","(none)"],[ "TN","TN"]]],[ "TR",[[ " ","(none)"],[ "TR","TR"]]],[ "TM",[[ " ","(none)"],[ "TM","TM"]]],[ "TC",[[ " ","(none)"],[ "TC","TC"]]],[ "TV",[[ " ","(none)"],[ "TV","TV"]]],[ "UM",[[ " ","(none)"],[ "UM","UM"]]],[ "UG",[[ " ","(none)"],[ "UG","UG"]]],[ "UA",[[ " ","(none)"],[ "UA","UA"]]],[ "AE",[[ " ","(none)"],[ "AE","AE "]]],[ "UK",[[ " ","(none)"],[ "UK","UK"]]],[ "UY",[[ " ","(none)"],[ "UY","UY"]]],[ "UZ",[[ " ","(none)"],[ "UZ","UZ"]]],[ "VU",[[ " ","(none)"],[ "VU","VU"]]],[ "VE",[[ " ","(none)"],[ "VE","VE"]]],[ "VN",[[ " ","(none)"],[ "VN","VN"]]],[ "VG",[[ " ","(none)"],[ "VG","VG"]]],[ "VI",[[ " ","(none)"],[ "VI","VI"]]],[ "WF",[[ " ","(none)"],[ "WF","WF"]]],[ "EH",[[ " ","(none)"],[ "EH","EH"]]],[ "YE",[[ " ","(none)"],[ "YE","YE"]]],[ "YU",[[ " ","(none)"],[ "YU","YU"]]],[ "ZR",[[ " ","(none)"],[ "ZR","ZR"]]],[ "ZM",[[ " ","(none)"],[ "ZM","ZM"]]],[ "ZW",[[ " ","(none)"],[ "ZW","ZW"]]] ]
//]]>
</script><input type="hidden" name="primaryDependentListId" value="dataForm:Country_List"><select size="1" name="dataForm:Country_List" id="dataForm:Country_List" onchange="delegateMethod(this,'State_List','null','null','null')"><option value="">(select one)</option><option value="US" selected="selected">United States</option><option value="AF">Afghanistan</option><option value="AL">Albania</option><option value="DZ">Algeria</option><option value="AS">American Samoa</option><option value="AD">Andorra</option><option value="AO">Angola</option><option value="AI">Anguilla</option><option value="AQ">Antarctica</option><option value="AG">Antigua &amp; Barbuda</option><option value="AR">Argentina</option><option value="AM">Armenia</option><option value="AW">Aruba</option><option value="AU">Australia</option><option value="AT">Austria</option><option value="AZ">Azerbaijan</option><option value="BS">Bahamas</option><option value="BH">Bahrain</option><option value="BD">Bangladesh</option><option value="BB">Barbados</option><option value="BY">Belarus</option><option value="BE">Belgium</option><option value="BZ">Belize</option><option value="BJ">Benin</option><option value="BM">Bermuda</option><option value="BT">Bhutan</option><option value="BO">Bolivia</option><option value="BA">Bosnia-Herzegovina</option><option value="BW">Botswana</option><option value="BV">Bouvet Island</option><option value="BR">Brazil</option><option value="IO">British Indian Ocean Terr.</option><option value="BN">Brunei Darussalam</option><option value="BG">Bulgaria</option><option value="BF">Burkina Faso</option><option value="BI">Burundi</option><option value="KH">Cambodia</option><option value="CM">Cameroon</option><option value="CA">Canada</option><option value="CV">Cape Verde</option><option value="KY">Cayman Islands</option><option value="CF">Central African Republic</option><option value="TD">Chad</option><option value="CL">Chile</option><option value="CN">China</option><option value="CX">Christmas Island</option><option value="CC">Cocos (Keeling) Isls.</option><option value="CO">Colombia</option><option value="KM">Comoros</option><option value="CG">Congo</option><option value="CD">Congo, Democratic Rep.</option><option value="CK">Cook Islands</option><option value="CR">Costa Rica</option><option value="HR">Croatia</option><option value="CU">Cuba</option><option value="CY">Cyprus</option><option value="CZ">Czech Republic</option><option value="DK">Denmark</option><option value="DJ">Djibouti</option><option value="DM">Dominica</option><option value="DO">Dominican Republic</option><option value="TP">East Timor</option><option value="EC">Ecuador</option><option value="ED">Educational</option><option value="EG">Egypt</option><option value="SV">El Salvador</option><option value="GQ">Equatorial Guinea</option><option value="ER">Eritrea</option><option value="EE">Estonia</option><option value="ET">Ethiopia</option><option value="FK">Falkland Islands</option><option value="FO">Faroe Islands</option><option value="FJ">Fiji</option><option value="FI">Finland</option><option value="CS">Former Czechoslovakia</option><option value="SU">Former USSR</option><option value="FR">France</option><option value="FX">France, European Terr.</option><option value="GF">French Guyana</option><option value="TF">French S. Territories</option><option value="GA">Gabon</option><option value="GM">Gambia</option><option value="GE">Georgia</option><option value="DE">Germany</option><option value="GH">Ghana</option><option value="GI">Gibraltar</option><option value="GB">Great Britain</option><option value="GR">Greece</option><option value="GL">Greenland</option><option value="GD">Grenada</option><option value="GP">Guadeloupe</option><option value="GU">Guam</option><option value="GT">Guatemala</option><option value="GN">Guinea</option><option value="GW">Guinea Bissau</option><option value="GY">Guyana</option><option value="HT">Haiti</option><option value="HM">Heard &amp; McDonald Isls.</option><option value="VA">Holy See, Vatican City</option><option value="HN">Honduras</option><option value="HK">Hong Kong</option><option value="HU">Hungary</option><option value="IS">Iceland</option><option value="IN">India</option><option value="ID">Indonesia</option><option value="IR">Iran</option><option value="IQ">Iraq</option><option value="IE">Ireland</option><option value="IL">Israel</option><option value="IT">Italy</option><option value="CI">Ivory Coast</option><option value="JM">Jamaica</option><option value="JP">Japan</option><option value="JO">Jordan</option><option value="KZ">Kazakhstan</option><option value="KE">Kenya</option><option value="KI">Kiribati</option><option value="KV">Kosovo</option><option value="KW">Kuwait</option><option value="KG">Kyrgyzstan</option><option value="LA">Laos</option><option value="LV">Latvia</option><option value="LB">Lebanon</option><option value="LS">Lesotho</option><option value="LR">Liberia</option><option value="LY">Libya</option><option value="LI">Liechtenstein</option><option value="LT">Lithuania</option><option value="LU">Luxembourg</option><option value="MO">Macau</option><option value="MK">Macedonia</option><option value="MG">Madagascar</option><option value="MW">Malawi</option><option value="MY">Malaysia</option><option value="MV">Maldives</option><option value="ML">Mali</option><option value="MT">Malta</option><option value="MH">Marshall Islands</option><option value="MQ">Martinique</option><option value="MR">Mauritania</option><option value="MU">Mauritius</option><option value="YT">Mayotte</option><option value="MX">Mexico</option><option value="FM">Micronesia</option><option value="MD">Moldavia</option><option value="MC">Monaco</option><option value="MN">Mongolia</option><option value="MS">Montserrat</option><option value="MA">Morocco</option><option value="MZ">Mozambique</option><option value="MM">Myanmar</option><option value="NA">Namibia</option><option value="NR">Nauru</option><option value="NP">Nepal</option><option value="NL">Netherlands</option><option value="AN">Netherlands Antilles</option><option value="NT">Neutral Zone</option><option value="NC">New Caledonia</option><option value="NZ">New Zealand</option><option value="NI">Nicaragua</option><option value="NE">Niger</option><option value="NG">Nigeria</option><option value="NU">Niue</option><option value="NF">Norfolk Island</option><option value="KP">North Korea</option><option value="MP">Northern Mariana Isls.</option><option value="NO">Norway</option><option value="OM">Oman</option><option value="PK">Pakistan</option><option value="PW">Palau</option><option value="PA">Panama</option><option value="PG">Papua New Guinea</option><option value="PY">Paraguay</option><option value="PE">Peru</option><option value="PH">Philippines</option><option value="PN">Pitcairn Island</option><option value="PL">Poland</option><option value="PF">Polynesia, French</option><option value="PT">Portugal</option><option value="PR">Puerto Rico</option><option value="QA">Qatar</option><option value="RE">Reunion</option><option value="RO">Romania</option><option value="RU">Russian Federation</option><option value="RW">Rwanda</option><option value="GS">S. Georgia &amp; S. Sandwich Isls.</option><option value="SH">Saint Helena</option><option value="KN">Saint Kitts &amp; Nevis</option><option value="LC">Saint Lucia</option><option value="PM">Saint Pierre &amp; Miquelon</option><option value="ST">Sao Tome &amp; Principe</option><option value="VC">Saint Vincent &amp; Grenadines</option><option value="WS">Samoa</option><option value="SM">San Marino</option><option value="SA">Saudi Arabia</option><option value="SN">Senegal</option><option value="SC">Seychelles</option><option value="SL">Sierra Leone</option><option value="SG">Singapore</option><option value="SK">Slovakia</option><option value="SI">Slovenia</option><option value="SB">Solomon Islands</option><option value="SO">Somalia</option><option value="ZA">South Africa</option><option value="KR">South Korea</option><option value="ES">Spain</option><option value="LK">Sri Lanka</option><option value="SD">Sudan</option><option value="SR">Suriname</option><option value="SJ">Svalbard &amp; Jan Mayen</option><option value="SZ">Swaziland</option><option value="SE">Sweden</option><option value="CH">Switzerland</option><option value="SY">Syria</option><option value="TJ">Tadjikistan</option><option value="TW">Taiwan</option><option value="TZ">Tanzania</option><option value="TH">Thailand</option><option value="TG">Togo</option><option value="TK">Tokelau</option><option value="TO">Tonga</option><option value="TT">Trinidad &amp; Tobago</option><option value="TN">Tunisia</option><option value="TR">Turkey</option><option value="TM">Turkmenistan</option><option value="TC">Turks &amp; Caicos Isls.</option><option value="TV">Tuvalu</option><option value="UM">US Minor Outlying Isls.</option><option value="UG">Uganda</option><option value="UA">Ukraine</option><option value="AE">United Arab Emirates</option><option value="UK">United Kingdom</option><option value="UY">Uruguay</option><option value="UZ">Uzbekistan</option><option value="VU">Vanuatu</option><option value="VE">Venezuela</option><option value="VN">Vietnam</option><option value="VG">Virgin Islands, British</option><option value="VI">Virgin Islands, US</option><option value="WF">Wallis &amp; Futuna Isls.</option><option value="EH">Western Sahara</option><option value="YE">Yemen</option><option value="YU">Yugoslavia</option><option value="ZR">Zaire</option><option value="ZM">Zambia</option><option value="ZW">Zimbabwe</option></select><script type="text/javascript">//<![CDATA[
 var panel_visibility_Array;
if(panel_visibility_Array==undefined)
{
        panel_visibility_Array=new Array();
}
var count=panel_visibility_Array.length;panel_visibility_Array[count]='Country_List_panel_visibility_Array';
var Country_List_panel_visibility_Array= []; var input_object_selection_Array;
if(input_object_selection_Array==undefined)
{
        input_object_selection_Array=new Array();
}
var count=input_object_selection_Array.length;input_object_selection_Array[count]='Country_List_input_object_selection_Array';
var Country_List_input_object_selection_Array= [["US"],"State_List"]; var input_Element_selection_Array;
if(input_Element_selection_Array==undefined)
{
        input_Element_selection_Array=new Array();
}
var count=input_Element_selection_Array.length;input_Element_selection_Array[count]='Country_Listinput_Element_selection_Array';
var Country_Listinput_Element_selection_Array= []; var input_object_disable_Array;
if(input_object_disable_Array==undefined)
{
        input_object_disable_Array=new Array();
}
var count=input_object_disable_Array.length;input_object_disable_Array[count]='Country_List_input_disable_selection_Array';
var Country_List_input_disable_selection_Array= [];
//]]>
</script></div></div></td></tr><tr><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="DriverEdit_capState"><span title="">Driver license state:</span><span class="required" id="DriverEdit_capState_cptnSpn">*</span><br><select name="dataForm:State_List" id="dataForm:State_List" size="1" onchange=""><option value=" " selected="selected">(none)</option><option value="AA">AA</option><option value="AE">AE</option><option value="AK">AK</option><option value="AL">AL</option><option value="AP">AP</option><option value="AR">AR</option><option value="AS">AS</option><option value="AZ">AZ</option><option value="CA">CA</option><option value="CO">CO</option><option value="CT">CT</option><option value="DC">DC</option><option value="DE">DE</option><option value="FL">FL</option><option value="FM">FM</option><option value="GA">GA</option><option value="GU">GU</option><option value="HI">HI</option><option value="IA">IA</option><option value="ID">ID</option><option value="IL">IL</option><option value="IN">IN</option><option value="KS">KS</option><option value="KY">KY</option><option value="LA">LA</option><option value="MA">MA</option><option value="MD">MD</option><option value="ME">ME</option><option value="MH">MH</option><option value="MI">MI</option><option value="MN">MN</option><option value="MO">MO</option><option value="MP">MP</option><option value="MS">MS</option><option value="MT">MT</option><option value="NC">NC</option><option value="ND">ND</option><option value="NE">NE</option><option value="NH">NH</option><option value="NJ">NJ</option><option value="NM">NM</option><option value="NV">NV</option><option value="NY">NY</option><option value="OH">OH</option><option value="OK">OK</option><option value="OR">OR</option><option value="PA">PA</option><option value="PR">PR</option><option value="PW">PW</option><option value="RI">RI</option><option value="SC">SC</option><option value="SD">SD</option><option value="TN">TN</option><option value="TX">TX</option><option value="UT">UT</option><option value="VA">VA</option><option value="VI">VI</option><option value="VT">VT</option><option value="WA">WA</option><option value="WI">WI</option><option value="WV">WV</option><option value="WY">WY</option></select></div></div></td><td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="lkpdrvexpdaid_1"><span title="">Driver license expiry:</span><span class="notRequired">p</span><br><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/scripts/calendar.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/scripts/calendar-setup.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/lang/calendar-en.js">
</script><input type="hidden" id="UsrZnOfset" value="-14400000"><input type="text" id="dataForm:driverLicExp1" name="dataForm:driverLicExp1" value="7/11/26" alt="" size="">&nbsp;<input type="image" title="Select date" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:driverLicExp1" name="dataForm:driverLicExp1_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="" onclick="dataFormwdriverLicExp1calendarSetUp(this.name);return false"> <script type="text/javascript">//<![CDATA[
function dataFormwdriverLicExp1calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
        {
                ifFormat    : "%m/%d/%y %H:%M",
                imgName      : name,            showsTime   :  false,           timer  :  false,                splitDate   :  false,           showsTimeZone  :  "",           edDropDown  :  "false",                 dropDownIns  :  ''
        }
)
}
//]]>
</script></div></div></td></tr><tr><td><input class="btn" id="dataForm:driver_locgetlist23" name="dataForm:driver_locgetlist23" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver_locgetlist23','oncomplete':function(request,event,data){createDriver();return false;},'parameters':{'dataForm:driver_locgetlist23':'dataForm:driver_locgetlist23'} } );return false;" value="Create" type="button"></td></tr></tbody></table></div></div></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">//<![CDATA[
UI8Layout.data.put("DriverDialog",{onClose:"",dialogClientId:"dataForm:DriverDialog",dragHandleId:"dataForm:driverIdOuter_dHId",onDialog:"",closeClientId:"dataForm:driverIdOuter_cCId"});
//]]>
</script><span id="dataForm:apptDtlPanel22" border="true"><div class="mtabsyncplnbdr" id="syncTab"><script type="text/javascript">//<![CDATA[
_u8_tnl_1("syncTab",{ajaxEnabled:false,width:null,height:null,forSyncedList:true,mainContainerId:"syncTab",submitOnTabClick:false});
//]]>
</script><input type="hidden" id="ajaxTabClicked" name="ajaxTabClicked" value=""><input type="hidden" name="syncTab_SubmitOnTabClick" value="false" id="syncTab_SubmitOnTabClick"><input type="hidden" name="syncTab_selectedTab" value="tab1" id="syncTab_selectedTab"><div id="syncTab_tabsout" class="tbpnltabsout"><table id="syncTab_tabstable" class="mtabs" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div id="TABH_syncTab_SYNCNAV" class="synctabpnnav"><table cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><input type="hidden" id="dataForm:synccomp1_syncClick" name="syncClicked" value="false"><div id="dataForm:synccomp1_upEn" class="sytpnavup -icons_sp" onclick="_u8_sy_2('dataForm:synccomp1', true)" title="Previous Record" style="display: none;"></div><div id="dataForm:synccomp1_upDis" class="sytpnavupd -icons_sp" style="display: block;"></div></td><td><div id="dataForm:synccomp1_dnEn" class="sytpnavdn -icons_sn" onclick="_u8_sy_2('dataForm:synccomp1')" title="Next Record" style="display: none;"></div><div id="dataForm:synccomp1_dnDis" class="sytpnavdnd -icons_sn" style="display: block;"></div></td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tnl_8("syncTab",{DnIdDisabled:"dataForm:synccomp1_dnDis",DnIdEnabled:"dataForm:synccomp1_dnEn",UpIdDisabled:"dataForm:synccomp1_upDis",SyncClicked:"dataForm:synccomp1_syncClick",UpIdEnabled:"dataForm:synccomp1_upEn"},"dataForm:synccomp1");
//]]>
</script></div></td><td><div id="TABH_dataForm:tab1" onclick="return _u8_tnl_6('tab1','syncTab','');" class="tab" title="Appointment Details"><span class="tab_span tab_sel_span tab_sel_top"><a hidefocus="true" id="tab1_lnk" name="tab1" class="tab_link tab_sel_link" href="javascript:{}">Appointment Details</a></span></div></td><td><div id="TABH_dataForm:tab2" onclick="return _u8_tnl_6('tab2','syncTab','');" class="tab" title="Appointment Objects"><span class="tab_span tab_top"><a hidefocus="true" id="tab2_lnk" name="tab2" class="tab_link" href="javascript:{}">Appointment Objects</a></span></div></td><td><div id="TABH_dataForm:tab4" onclick="return _u8_tnl_6('tab4','syncTab','');" class="tab" title="Additional Details"><span class="tab_span tab_top"><a hidefocus="true" id="tab4_lnk" name="tab4" class="tab_link" href="javascript:{}">Additional Details</a></span></div></td></tr></tbody></table></div><div class="tabbarstrip"></div><script type="text/javascript">//<![CDATA[
_u8_tnl_7("syncTab",{TabToHeadingMap:{tab4:"TABH_dataForm:tab4",tab1:"TABH_dataForm:tab1",tab2:"TABH_dataForm:tab2"},TabsOuterId:"syncTab_tabsout",TabsTableId:"syncTab_tabstable",TabIdList:["tab1","tab2","tab4"]});
//]]>
</script><div class="tabpnlout" id="CONT_dataForm:tab1" style="display: block; min-width: 740px;"><div class="mtabsyncpnlbody -tbs_tbc -tbs_tbgc " style="min-width: 740px;"><div class="mtabpnlscroll"><table class="tabpnlconttable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><span id="dataForm:apptDtlPanel" border="true"><div class="pnltopdiv" id="PANEL_lSizePanel_top"><div id="tr_lSizePanel" class="pnlcondivhdr"><input id="dataForm:addFlag" type="hidden" name="dataForm:addFlag" value="false"><input id="dataForm:flag" type="hidden" name="dataForm:flag" value="true"><input id="dataForm:primaryKey" type="hidden" name="dataForm:primaryKey" value="22312892"><input id="dataForm:apptIdHid" type="hidden" name="dataForm:apptIdHid" value="22312892"><input id="dataForm:tcApptIdHid" type="hidden" name="dataForm:tcApptIdHid" value="*********"><input id="dataForm:editClicked" type="hidden" name="dataForm:editClicked" value="true"><input id="dataForm:shipperCompany" type="hidden" name="dataForm:shipperCompany" value="100"><input id="dataForm:isCarrier" type="hidden" name="dataForm:isCarrier" value="true"><input id="dataForm:isShipper" type="hidden" name="dataForm:isShipper" value="false"><input id="dataForm:templateFlag" type="hidden" name="dataForm:templateFlag" value="N"><input id="dataForm:templateId" type="hidden" name="dataForm:templateId" value="0"><input id="dataForm:actCheckDttm" type="hidden" name="dataForm:actCheckDttm" value=""><input id="dataForm:prevApptType" type="hidden" name="dataForm:prevApptType" value="Live Unload"><input id="dataForm:selectedRowPOId" type="hidden" name="dataForm:selectedRowPOId" value=""><input id="dataForm:userEnteredPOId" type="hidden" name="dataForm:userEnteredPOId" value=""><table id="dataForm:gl2" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Facility:</span><span class="required" id="caseSizeType1_cptnSpn">*</span></td><td><div style="white-space:nowrap"><input id="dataForm:facility_det" type="text" name="dataForm:facility_det" value="DCDA09P">&nbsp;<a id="dataForm:facility_det_id" name="dataForm:facility_det_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-c133-ad04-84d0" onclick="return false;"><img id="dataForm:facility_det_lookupImage" src="/lcom/common/image/find.gif" alt="Find Facility" onclick="populateFromDCFacilityLookupPopup('facility_det');" title="Find Facility"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated trailer duration (min):</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><script type="text/javascript" language="JavaScript" src="/lps/resources/inputmask/scripts/mask.js">
</script><input id="dataForm:estTrailerDuration_det0" name="dataForm:estTrailerDuration_det0" type="text" mask="NNNNNNNNN" decseparator="." onkeypress="return checkKey(this,event); " onchange="" onpaste="return checkCopyValue(this); " value="60"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Suggested start date/time:</span><span class="required" id="caseSizeType2_cptnSpn">*</span></td><td><div style="white-space:nowrap"><input type="hidden" id="UsrZnOfset" value="-14400000"><input type="text" id="dataForm:startTime_det" name="dataForm:startTime_det" value="7/17/25 18:30" alt="" size="">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:startTime_det" name="dataForm:startTime_det_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="" onclick="dataFormwstartTime_detcalendarSetUp(this.name);return false"> <script type="text/javascript">//<![CDATA[
function dataFormwstartTime_detcalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
        {
                ifFormat    : "%m/%d/%y %H:%M",
                imgName      : name,            showsTime   :  true,            timer  :  false,                splitDate   :  false,           showsTimeZone  :  "",           edDropDown  :  "false",                 dropDownIns  :  ''
        }
)
}
//]]>
</script></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated tractor duration (min):</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:estTractorDuration_det" name="dataForm:estTractorDuration_det" type="text" mask="NNNNNNNNN" decseparator="." onkeypress="return checkKey(this,event); " onchange="" onpaste="return checkCopyValue(this); " value="60"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Appointment type:</span><span class="required" id="at0_cptnSpn">*</span></td><td><div style="white-space:nowrap"><select id="dataForm:cd10" name="dataForm:cd10" size="1"><option value="">Select one</option><option value="30">Drop Empty</option><option value="20">Drop Unload</option><option value="40">Live Load</option><option value="10" selected="selected">Live Unload</option><option value="60">Pickup Empty</option><option value="50">Pickup Load</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated departure date/time:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:estDepartureDateTime_det" type="text" name="dataForm:estDepartureDateTime_det" value="7/17/25 19:30" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Appointment ID:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><span id="dataForm:appointmentId_d" class="captionData">*********</span></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment requested date/time:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:requestedDateTime_det" type="text" name="dataForm:requestedDateTime_det" value="7/17/25 17:00" disabled="disabled"><input id="dataForm:requestedDateTime_dethid" type="hidden" name="dataForm:requestedDateTime_dethid" value="7/17/25 17:00"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Equipment code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:equipment_detecId" name="dataForm:equipment_detecId" value=""><input type="hidden" id="dataForm_equipment_det_enterKey" value="false"><input type="hidden" id="triggerdataForm_equipment_det_enterKey" value="false"><input type="text" id="dataForm:equipment_det" name="dataForm:equipment_det" onfocus="javascript: focusOnTextBox('dataForm_equipment_det_enterKey')" onblur="javascript: blurOnTextBox('dataForm_equipment_det_enterKey')" onkeypress="if(enterPressed(event,'dataForm:equipment_det') )return false;" value="" alt="Find Equipment code">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BymslookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BymslookupBackingBean.getBUMap%7D&amp;lookupType=EquipmentType&amp;is3plEnabled=true&amp;returnId=dataForm:equipment_det&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:equipment_det" title="Find Equipment code" align="absmiddle" id="trigger_dataForm:equipment_det" name="trigger_dataForm:equipment_det" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_equipment_det_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_equipment_det_enterKey')"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Actual checkin date/time:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><span id="dataForm:actCDttm1" class="captionData"></span></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Load configuration:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:loadConfigurationListSel" name="dataForm:loadConfigurationListSel" multiple="multiple" size="3" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id310','parameters':{'dataForm:j_id310':'dataForm:j_id310'} } )"><option value="">None</option><option value="WPLT" selected="selected">WPLT</option></select><input id="dataForm:appointmentLC_det4" type="hidden" name="dataForm:appointmentLC_det4" value="WPLT"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl2","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span></td></tr></tbody></table></div></div></div><div class="tabpnlout" id="CONT_dataForm:tab2" style="display:none"><div class="mtabsyncpnlbody -tbs_tbc -tbs_tbgc "><div class="mtabpnlscroll"><table class="tabpnlconttable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div class="pnltopdiv" id="PANEL_apptObj_listpanel_top"><div id="tr_apptObj_listpanel" class="pnlcondivhdr"><script type="text/javascript">//<![CDATA[
                var messageArr = new Array(5);
                messageArr[0] = 'Please select a row';
        //]]>
</script><input id="dataForm:poDueDate_cache" type="hidden" name="dataForm:poDueDate_cache" value=""><input id="dataForm:billingMethod_cache" type="hidden" name="dataForm:billingMethod_cache" value=""><input id="dataForm:originFacility_cache" type="hidden" name="dataForm:originFacility_cache" value=""><input id="dataForm:vendorId_cache" type="hidden" name="dataForm:vendorId_cache" value=""><input id="dataForm:vendorName_cache" type="hidden" name="dataForm:vendorName_cache" value=""><input id="dataForm:bolNumber_cache" type="hidden" name="dataForm:bolNumber_cache" value=""><input id="dataForm:proNumber_cache" type="hidden" name="dataForm:proNumber_cache" value=""><span id="dataForm:apptObjlistPanel"><div class="pager" id="dataForm:apptObjTable::pager"><span class="pagerNoWrap">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:first" name="dataForm:apptObjTable:pager:first" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:first','parameters':{'dataForm:apptObjTable:pager:first':'dataForm:apptObjTable:pager:first'} } );return false;" alt="First" type="image" src="/lps/resources/themes/icons/mablue/arrow_first_disabled.gif">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:previous" name="dataForm:apptObjTable:pager:previous" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:previous','parameters':{'dataForm:apptObjTable:pager:previous':'dataForm:apptObjTable:pager:previous'} } );return false;" alt="Previous" type="image" src="/lps/resources/themes/icons/mablue/arrow_left_disabled.gif"><input id="dataForm:apptObjTable:pager:pageInput" name="dataForm:apptObjTable:pager:pageInput" type="text" mask="N" decseparator="." onkeypress="return( (checkKey(this,event))? checkPageCount(event,1,'dataForm:apptObjTable'):false);" onchange="" onpaste="return checkCopyValue(this); " value="" size="1" style="border-style: solid; border-color:darkgray; color:darkgray; padding: .1em; margin: .1em; border-width: 1px; text-align: left;" class="PageNumberText" onfocus="this.blur();" disabled="disabled">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:next" name="dataForm:apptObjTable:pager:next" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:next','parameters':{'dataForm:apptObjTable:pager:next':'dataForm:apptObjTable:pager:next'} } );return false;" alt="Next" type="image" src="/lps/resources/themes/icons/mablue/arrow_right_disabled.gif">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:last" name="dataForm:apptObjTable:pager:last" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:last','parameters':{'dataForm:apptObjTable:pager:last':'dataForm:apptObjTable:pager:last'} } );return false;" alt="Last" type="image" src="/lps/resources/themes/icons/mablue/arrow_last_disabled.gif"></span>&nbsp;&nbsp;<span class="pagerNoWrap pagersep"><input class="paginationCtrlCls" id="dataForm:apptObjTable:pager:apptObjTable_rfsh_but" name="dataForm:apptObjTable:pager:apptObjTable_rfsh_but" onclick="changeCursor('wait');resetFilterIfChanged('');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:apptObjTable_rfsh_but','oncomplete':function(request,event,data){changeCursor('default')},'parameters':{'dataForm:apptObjTable:pager:apptObjTable_rfsh_but':'dataForm:apptObjTable:pager:apptObjTable_rfsh_but'} } );return false;" alt="Refresh" type="image" src="/lps/resources/themes/icons/mablue/refresh.gif"></span><span class="pagerNoWrap">&nbsp;Displaying 1 - 1 of 1&nbsp;</span><span class="pagerNoWrap">(<span id="dataForm:apptObjTable_TotalSelectedRow">0</span> selected)</span></div><input type="hidden" id="dataForm:apptObjTable:pagerBoxValue" name="dataForm:apptObjTable:pagerBoxValue" value=""><input type="hidden" id="dataForm:apptObjTable:isPaginationEvent" name="dataForm:apptObjTable:isPaginationEvent" value=""><input type="hidden" id="dataForm:apptObjTable:pagerAction" name="dataForm:apptObjTable:pagerAction" value=""><input id="dataForm:apptObjTable:tableAjaxSubmitBtn" name="dataForm:apptObjTable:tableAjaxSubmitBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:tableAjaxSubmitBtn','parameters':{'dataForm:apptObjTable:tableAjaxSubmitBtn':'dataForm:apptObjTable:tableAjaxSubmitBtn'} } );return false;" style="display:none" type="button"><input type="hidden" name="dataForm:apptObjTable_deleteHidden" value="" id="dataForm:apptObjTable_deleteHidden"><input type="hidden" name="dataForm:apptObjTable_selectedRows" value="#:#" id="dataForm:apptObjTable_selectedRows"><div class="advtbl_contr" id="dataForm:apptObjTable_container"><div id="dataForm:apptObjTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:apptObjTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:apptObjTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:apptObjTable:isSortButtonClick" id="dataForm:apptObjTable:isSortButtonClick" value=""><input type="hidden" name="dataForm:apptObjTable:sortDir" id="dataForm:apptObjTable:sortDir" value="desc"><input type="hidden" name="dataForm:apptObjTable:colCount" id="dataForm:apptObjTable:colCount" value=""><input type="hidden" name="dataForm:apptObjTable:tableClicked" id="dataForm:apptObjTable:tableClicked" value=""><input type="hidden" name="dataForm:apptObjTable:tableResized" id="dataForm:apptObjTable:tableResized" value="false"><div class="advtbl_contr_head" style="position:relative;" id="dataForm:apptObjTable_headDiv"><table style="table-layout:auto;" id="dataForm:apptObjTable" cellspacing="0"><colgroup><col><col><col><col><col><col><col><col><col><col><col><col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row"><td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top"><input type="checkbox" name="dataForm:apptObjTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'apptObjTable');"></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:apptObjTable:cc1" class="titleCase">Purchase Order</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc2" class="titleCase">Shipment</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc3" class="titleCase">Stop</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc4" class="titleCase">ASN</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc5" class="titleCase">PO due date</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc6" class="titleCase">Billing Method</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc7" class="titleCase">Origin Facility</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc8" class="titleCase">Business partner</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc9" class="titleCase">Vendor Name</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc11" class="titleCase">BOL number</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:cc12" class="titleCase">PRO number</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:apptObjTable:_colhdr_idSizeVal100">Size:WPLT</span></td></tr></thead></table></div><div id="dataForm:apptObjTable_bodyDiv" class="advtbl_contr_body"><input type="hidden" id="apptObjTable_hdnMaxIndexHldr" name="apptObjTable_hdnMaxIndexHldr" value="1"><table style="table-layout:auto;" id="dataForm:apptObjTable_body" cellspacing="0"><colgroup><col><col><col><col><col><col><col><col><col><col><col><col><col></colgroup><tbody><tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:apptObjTable" id="checkAll_c0_dataForm:apptObjTable" value="0"><input type="hidden" id="dataForm:apptObjTable:0:PK_0" name="dataForm:apptObjTable:0:PK_0"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td0_edit" class="dshow">&nbsp;&nbsp;&nbsp;<input id="dataForm:apptObjTable:0:poId2" type="text" name="dataForm:apptObjTable:0:poId2" value="F-12926701" onblur="poShipmentChanged(this.id);findMatchingPOs(this);">&nbsp;<input id="dataForm:apptObjTable:0:poIdHid" type="hidden" name="dataForm:apptObjTable:0:poIdHid" value="F-12926701"><input type="hidden" name="dataForm:apptObjTable:0:markForDelete" id="dataForm:apptObjTable:0:markForDelete" value=""></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td1_edit" class="dshow">&nbsp;&nbsp;&nbsp;&nbsp;<script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:apptObjTable:0:shipId2ecId" name="dataForm:apptObjTable:0:shipId2ecId" value=""><input type="hidden" id="dataForm_apptObjTable_0_shipId2_enterKey" value="false"><input type="hidden" id="triggerdataForm_apptObjTable_0_shipId2_enterKey" value="false"><input type="text" id="dataForm:apptObjTable:0:shipId2" name="dataForm:apptObjTable:0:shipId2" onfocus="javascript: focusOnTextBox('dataForm_apptObjTable_0_shipId2_enterKey')" onkeypress="if(enterPressed(event,'dataForm:apptObjTable:0:shipId2') )return false;" value="" alt="Find Shipment" onblur="poShipmentChanged(this.id);">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcboTransFilterLookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcboTransFilterLookupBackingBean.getBUMap%7D&amp;lookupType=Shipment&amp;is3plEnabled=true&amp;returnId=dataForm:apptObjTable:0:shipId2&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code=VSH'; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:apptObjTable:0:shipId2" title="Find Shipment" align="absmiddle" id="trigger_dataForm:apptObjTable:0:shipId2" name="trigger_dataForm:apptObjTable:0:shipId2" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_apptObjTable_0_shipId2_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_apptObjTable_0_shipId2_enterKey')">&nbsp;<input id="dataForm:apptObjTable:0:shipmentIdHid" type="hidden" name="dataForm:apptObjTable:0:shipmentIdHid"></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td2_edit" class="dshow">&nbsp;&nbsp;<input id="dataForm:apptObjTable:0:stopId2" type="text" name="dataForm:apptObjTable:0:stopId2"></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td3_edit" class="dshow">&nbsp;&nbsp;&nbsp;<script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:apptObjTable:0:asnId2ecId" name="dataForm:apptObjTable:0:asnId2ecId" value=""><input type="hidden" id="dataForm_apptObjTable_0_asnId2_enterKey" value="false"><input type="hidden" id="triggerdataForm_apptObjTable_0_asnId2_enterKey" value="false"><input type="text" id="dataForm:apptObjTable:0:asnId2" name="dataForm:apptObjTable:0:asnId2" onfocus="javascript: focusOnTextBox('dataForm_apptObjTable_0_asnId2_enterKey')" onkeypress="if(enterPressed(event,'dataForm:apptObjTable:0:asnId2') )return false;" value="" alt="Find ASN" onblur="asnChanged(this.id);" disabled="">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-c133-ad04-84d0&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcboTransFilterLookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcboTransFilterLookupBackingBean.getBUMap%7D&amp;lookupType=ASN&amp;is3plEnabled=true&amp;returnId=dataForm:apptObjTable:0:asnId2&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code=VSN'; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:apptObjTable:0:asnId2" title="Find ASN" align="absmiddle" id="trigger_dataForm:apptObjTable:0:asnId2" name="trigger_dataForm:apptObjTable:0:asnId2" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_apptObjTable_0_asnId2_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_apptObjTable_0_asnId2_enterKey')" disabled="">&nbsp;<input id="dataForm:apptObjTable:0:asnIdHid" type="hidden" name="dataForm:apptObjTable:0:asnIdHid"></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td4_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:poDueDateId1">07/17/2025</span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td5_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:billingMethodId1">Prepaid</span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td6_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:originFacilityId1">VD005960-009</span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td7_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:vendorIdId1"></span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td8_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:vendorNameId1"></span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td9_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:bolNumber"></span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td10_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:proNumber"></span></div><span style="display:none">&nbsp;</span></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><div id="dataForm:apptObjTable_body_tr0_td11_view" class="dhide">&nbsp;<span id="dataForm:apptObjTable:0:colval_viewcol_idSizeVal100SizeVal100">16.0</span></div><div id="dataForm:apptObjTable_body_tr0_td11_edit" class="dshow">&nbsp;<input id="dataForm:apptObjTable:0:colval_editcol_idSizeVal100SizeVal100" type="text" name="dataForm:apptObjTable:0:colval_editcol_idSizeVal100SizeVal100" value="16.0" maxlength="10" readonly="readonly"></div><span style="display:none">&nbsp;</span></td></tr><tr id="dataForm:apptObjTable:nodataRow" class="advtbl_row trhide"><td class="advtbl_col advtbl_body_col tdhide" colspan="13" align="left">No data found</td></tr></tbody></table></div><div class="emptyHoriScrollDiv"></div></div> <input type="hidden" id="dataForm:apptObjTable_trs_pageallrowskey" name="dataForm:apptObjTable_trs_pageallrowskey" value=""><input type="hidden" id="dataForm:apptObjTable_selectedRows" name="dataForm:apptObjTable_selectedRows" value=""><input type="hidden" id="dataForm:apptObjTable_selectedIdList" name="dataForm:apptObjTable_selectedIdList" value=""><input type="hidden" id="dataForm:apptObjTable_trs_allselectedrowskey" name="dataForm:apptObjTable_trs_allselectedrowskey" value="apptObjTable$:$1752205181860"><script type="text/javascript">//<![CDATA[
var  dataFormapptObjTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='apptObjTable';
tableObjectArray[count]=dataFormapptObjTable_tableObj;dataFormapptObjTable_tableObj.bind(document.getElementById('dataForm:apptObjTable_container'),document.getElementById('dataForm:apptObjTable_headDiv'), document.getElementById('dataForm:apptObjTable_bodyDiv'), document.getElementById('dataForm:apptObjTable_scrollDiv'),document.getElementById('dataForm:apptObjTable_scrollDivBody'), document.getElementById('dataForm:apptObjTable_button'),true,1,2,'dataForm:apptObjTable','edit','yes','no','0','bottom','edit',0,500,'yes','no','even','odd','Invalid Table','dataForm:apptObjTable_selectedIdList','true','apptObjTable','false' ,0,8,'false','Synced','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',1);
//]]>
</script><script type="text/javascript">//<![CDATA[
var msgToActivateTotal;msgToActivateTotal = "To activate this feature, click the Total button";
//]]>
</script> <input id="dataForm:addRowBtn" type="submit" name="dataForm:addRowBtn" value="Add Row" alt="Add Row" onclick="return addApptObjRow();" style="cursor:pointer" class="btn"><input id="dataForm:addMultipleBtn" type="submit" name="dataForm:addMultipleBtn" value="Add Multiple" alt="Add Multiple" onclick="return openAddRowPopUp();" style="cursor:pointer" class="btn"><input id="dataForm:deleteRowBtn" type="submit" name="dataForm:deleteRowBtn" value="Delete Row" alt="Delete Row" onclick="return deleteApptObjRow();" style="cursor:pointer" class="btn"><div id="setsizesdiv" style="display:none"><input class="btn" id="dataForm:setSizesBtn" name="dataForm:setSizesBtn" onclick="storeAppointmentObjectDetails();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:setSizesBtn','oncomplete':function(request,event,data){setAppointmentObjectDetails();},'parameters':{'dataForm:setSizesBtn':'dataForm:setSizesBtn'} } );return false;" value="Set PO sizes" alt="Set PO sizes" style="cursor:pointer" type="button"></div><span class="groupBtnSpace">&nbsp;</span></span> <script type="text/javascript">//<![CDATA[
                var tableId = document.getElementById('dataForm:apptObjTable');
                checkForObjects(tableId);
        //]]>
</script></div></div></td></tr></tbody></table></div></div></div><div class="tabpnlout" id="CONT_dataForm:tab4" style="display:none"><div class="mtabsyncpnlbody -tbs_tbc -tbs_tbgc "><div class="mtabpnlscroll"><table class="tabpnlconttable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div class="pnltopdiv" id="PANEL_additionalDetailsPanel_top"><div id="tr_additionalDetailsPanel" class="pnlcondivhdr"><span id="dataForm:Appt_CAttributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCustAttributes_top"><table id="PANEL_ApptCustAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_CustomAttr_Header_Out">Custom Attributes</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCustAttributes" class="pnlcondiv"><table id="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">LoadType:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:id314e" name="dataForm:id314e" size="1"><option value=""></option><option value="Chep">Chep</option><option value="Peco">Peco</option><option value="Water">Water</option><option value="Broken Pallets">Broken Pallets</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Priority Appointment:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:id1335e" type="text" name="dataForm:id1335e" maxlength="100"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Reserved:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:id1318e" type="text" name="dataForm:id1318e" value="0" maxlength="100"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1_cache_hd" type="hidden" name="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1_cache_hd" value="cu_ca_id314e,cu_ca_id1335e,cu_ca_id1318e,"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table></div></div></span><span id="dataForm:appointmentAdditionalDetailsPanel"><script type="text/javascript" language="JavaScript" xml:space="preserve">//<![CDATA[
var w3c=(document.getElementById)? true: false;
var ie5=(w3c && document.all)? true : false;
var ns6=(w3c && (navigator.appName=="Netscape"))? true: false;
currIDb=null; xoff=0; yoff=0;
currRS=null; rsxoff=0; rsyoff=0;
oldac=null; newac=null; zdx=600; mx=0; my=0;

var idlist=new Array();
idlist.btns=new Array();
idlist.btns[0]=new Image(); idlist.btns[0].src="/lcom/common/image/close.gif";
var currWindow =  null;

function truebody(){ //Dynamic Drive added function
return (document.compatMode && document.compatMode!="BackCompat")? document.documentElement : document.body
}

function isWindow(obj) {
    if (typeof(window.constructor) === 'undefined') {
        return obj instanceof window.constructor;
    } else {
        return obj.window === obj;
    }
}

function hidebox(id,isOverLay,isRefresh){
if(w3c){
var now = new Date();
var startTime = now.getTime();
var fdiv= document.getElementById(id+'_b');
var returnElement;
if (window.frames['iframe1'] && isWindow(window.frames['iframe1'])) {
        returnElement = window.frames['iframe1'].document.getElementById("returnCode");//for MACR00184546: returnId to returnCode
        if(!returnElement)//added this since returnCode is not present in some lookups like Region lookup.
        {
                returnElement = window.frames['iframe1'].document.getElementById("returnId");
        }
}
var returnField;
if (returnElement) {
        returnField = returnElement.value;
}


now = new Date();
var endTime = now.getTime();
if(isRefresh == true){
currWindow.document.body.style.cursor='wait';
currWindow.document.actionForm.dummyButton.click();
}
}
if(isOverLay == true)
        reloadMainDivAfterAjax();
if (returnField) // && document.getElementById(returnField)!=null)
// MACR00389242 : This && check is not required as it is done below.
{
        if (document.getElementById(returnField))
        {
                document.getElementById(returnField).focus();
        }
        else
        {
                var dotIndex = returnField.lastIndexOf(".");
                if ( dotIndex != -1)
                {
                        returnField = returnField.substring(dotIndex+1);
                }
                var retElement = document.getElementById(returnField);
                if (retElement)
                {
                        if(retElement.type!="hidden" && retElement.style.display != "none" && !retElement.disabled)
                        {
                                retElement.focus();
                        }
                }
        }
}
if(fdiv!=null)
fdiv.parentNode.removeChild(fdiv);
}

function reloadMainDivAfterAjax()
{

 var outerPane = document.getElementById('workareadiv');
 var innerPane = document.getElementById('screen');

 if (outerPane) {
   outerPane.style.display = 'block';
 }
 if (innerPane) {
   innerPane.style.display = 'none';
 }
}

function showbox(id){
if(w3c){
var bx=document.getElementById(id+'_b').style;
var sh=document.getElementById(id+'_s').style;
bx.display='block';
sh.display='block';
sh.zIndex=++zdx;
bx.zIndex=++zdx;
}}

function minimize(){
if(w3c){
this.IDS[0].style.height=(ie5)? '28px':'24px';
this.IDS[2].style.display='none';
this.IDS[3].style.display='none';
setTimeout('ns6bugfix()',100);
}}

function restore(){
if(w3c){
var h=this.IDS[8];
this.IDS[0].style.height=h+'px'; //box
this.IDS[2].style.display='block';
this.IDS[3].style.display='block';
setTimeout('ns6bugfix()',100);
}}

function ns6bugfix(){
self.resizeBy(0,1);
self.resizeBy(0,-1);
}

function trackmouse(evt){
mx=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
my=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
if(!ns6)movepopup();
if((currIDb!=null)||(currRS!=null))return false;
}

function movepopup(){
if((currIDb!=null)&&w3c){
var x=mx+xoff;
var y=my+yoff;
currIDb.style.left=x+'px';
//currIDs.style.left=x+8+'px';
currIDb.style.top=y+'px';
//currIDs.style.top=y+8+'px';
}
if((currRS!=null)&&w3c){
var rx=mx+rsxoff;
var ry=my+rsyoff;
var c=currRS;
c.style.left=Math.max(rx,((ie5)?88:92))+'px';
c.style.top=Math.max(ry,((ie5)?68:72))+'px';
c.IDS[0].style.width=Math.max(rx+((ie5)?12:8),100)+'px';
c.IDS[0].style.height=Math.max(ry+((ie5)?12:8),80)+'px';
c.IDS[1].style.width=Math.max(rx+((ie5)?3:8),((ie5)?95:92))+'px';
c.IDS[4].style.left=parseInt(c.IDS[1].style.width)-10+'px';
c.IDS[2].style.width=Math.max(rx-((ie5)?-5:5),((ie5)?92:87))+'px';
c.IDS[2].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[6].style.width=Math.max(rx-((ie5)?-5:0),((ie5)?100:98))+'px';
c.IDS[6].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[9].style.width=Math.max(rx,((ie5)?100:98))+'px';
c.IDS[9].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[7]=parseInt(c.IDS[0].style.height);
}}

function startRS(evt){
var ex=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
var ey=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
rsxoff=parseInt(this.style.left)-ex;
rsyoff=parseInt(this.style.top)-ey;
currRS=this;
if(ns6)this.IDS[2].style.overflow='hidden';
return false;
}

function stopdrag(){
currIDb=null;
ns6bugfix();
}

function grab_id(evt){
var ex=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
var ey=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
xoff=parseInt(this.IDS[0].style.left)-ex;
yoff=parseInt(this.IDS[0].style.top)-ey;
currIDb=this.IDS[0];
return false;
}

/*function getScrollWidth()
{
   var w = window.pageXOffset ||
           document.body.scrollLeft ||
           document.documentElement.scrollLeft;

   return w ? w : 0;
}

function getScrollHeight()
{
   var h = window.pageYOffset ||
           document.body.scrollTop ||
           document.documentElement.scrollTop;

   return h ? h : 0;
}*/

function subBox(x,y,w,h,id,styleclass){
var v=document.createElement('div');
v.setAttribute('id',id);
v.style.left=x+'px';
v.style.top=y+'px';
(w == null || w == 'undefined') ? v.style.width='auto' : v.style.width=w+'px';
v.style.height=h+'px';
v.className=styleclass;
v.style.visibility='visible';
return v;
}

function get_cookie(Name) {
var search = Name + "="
var returnvalue = ""
if (document.cookie.length > 0) {
offset = document.cookie.indexOf(search)
if (offset != -1) {
offset += search.length
end = document.cookie.indexOf(";", offset)
if (end == -1)
end = document.cookie.length;
returnvalue=unescape(document.cookie.substring(offset, end))
}
}
return returnvalue;
}

// find absolute Y position of left corner
function findPosY(obj)
{
        var curtop = 0;
        if(obj.offsetParent)
        while(1)
        {
          curtop += obj.offsetTop;
          if(!obj.offsetParent)
            break;
          obj = obj.offsetParent;
        }
    else if(obj.y)
        curtop += obj.y;

    return curtop;
}

// find absolute X position of left corner
function findPosX(obj)
{
        var curleft = 0;
        if(obj.offsetParent)
        while(1)
        {
                curleft += obj.offsetLeft;
                if(!obj.offsetParent)
                        break;
                obj = obj.offsetParent;
        }
        else if(obj.x)
                curleft += obj.x;
        return curleft;
}

function openSignoutPopup()
{
        var titleSignOut = signOutArray[0];
        var signoutWin = new ConfirmpopUp(1017, 22, 170, 95, 'signoutDiv', titleSignOut, true, true,'/lcom/common/image/confirm.gif',true,true);
        document.getElementById("screen").style.display = "block";
}

function openFilterPopup(url,title)
{
        var filterWin = new popUp(200, 100, 770, 400, 'filterDiv', title, true, false, url,'/lcom/common/image/manage.gif',true,url);
        document.getElementById("screen").style.display = "block";

}

function openPopup(x,y,w,h,url,title)
{
        var fdiv= document.getElementById('lookupDiv_b');
        var n=url.indexOf("basedata/lookup/jsp/dspRegionFacilityLookup.jsp");
        if(url.indexOf("basedata/lookup/jsp/dspRegionFacilityBULookup.jsp")>=0)
        {
                var n=url.indexOf("basedata/lookup/jsp/dspRegionFacilityBULookup.jsp");
        }
        if(n>0)// reset the height and width incase of region lookup
        {
                w=550;
                h=350;
        }
        if(fdiv == null)
                var popupWin = new popUp(x, y, w, h, 'lookupDiv', title, true, false, url,'/lcom/common/image/find.gif',true,url);
        document.getElementById("screen").style.display = "none";

}
function openPopupWithParentDisabled(x,y,w,h,url,title)
{
        var fdiv= document.getElementById('lookupDiv_b');
        if(fdiv == null)
                var popupWin = new popUp(x, y, w, h, 'lookupDiv', title, true, true, url,'',true,url);
        document.getElementById("screen").style.display = "block";
}

function closeLookupPopup()
{
        var fdiv = this.parent.document.getElementById('lookupDiv_b');
        if(fdiv != null)
        fdiv.parentNode.removeChild(fdiv);
}

function closeGeneralPopup()
{
        var fdiv = this.parent.document.getElementById('lookupDiv_b');
        if(fdiv != null)
        fdiv.parentNode.removeChild(fdiv);
}

function closeInfoPopup()
{
        var fdiv = this.parent.document.getElementById('infoDiv_b');
        if(fdiv != null)
        fdiv.parentNode.removeChild(fdiv);
}

function openGeneralPopup(x,y,w,h,url,title)
{
        var fdiv= document.getElementById('lookupDiv_b');
        if(fdiv == null)
                var infoWin = new popUp(x, y, w, h, 'lookupDiv', title, true, false, url,'',true,url);
        document.getElementById("screen").style.display = "none";
}

function openInfoPopup(w,h,url,title)
{
        var fdiv= document.getElementById('infoDiv_b');
        if(fdiv == null)
                var infoWin = new popUp(500, 500, w, h, 'infoDiv', title, false, false, url,'/lcom/common/image/info.gif',true,url);
        document.getElementById("screen").style.display = "none";
}

function openNewInfoPopup(w,h,url,title)
{
        var fdiv= document.getElementById('infoDiv_b');
        if(fdiv == null)
                var infoWin = new popUp(200, 200, w, h, 'infoDiv', title, false, false, url,'/lcom/common/image/info.gif',true,url);
        document.getElementById("screen").style.display = "none";
}

function popUp(x,y,w,h,cid,title,isdrag,isresize,url,titleImg,isOverLay,windowObj){
currWindow = windowObj;
if(w3c){
var tw, th;
w=Math.max(w,100);
h=Math.max(h,80);
var rdiv=new subBox(w-((ie5)?12:8),h-((ie5)?12:8),7,7,cid+'_rs','');
if(isresize){
rdiv.innerHTML='<img src="/lcom/common/image/resize.gif" width="7" height="7">';
rdiv.style.cursor='move';
}

//var top = getScrollHeight();
//var left = getScrollWidth();
//top += (document.documentElement.clientHeight/2) - (h / 2);
//left += (document.documentElement.clientWidth/2)  -  (w / 2);
var top = document.documentElement.clientHeight - h - 20;
var left = document.documentElement.clientWidth  - w - 20;
//alert("top: " + top + "left: " + left);
if( x > left)
        x = left;
if( y > top)
        y = top;
var outerdiv=new subBox(x,y,w,h,cid+'_b','pop');
/*var outerdiv=document.createElement('div');
outerdiv.setAttribute('id',cid+'_b');
outerdiv.className='pop';
outerdiv.style.left=x+'px';
outerdiv.style.top=y+'px';
outerdiv.style.width=w+'px';
outerdiv.style.height=h+'px';
outerdiv.style.visibility='visible';*/

var popsdw=document.createElement('div');
popsdw.setAttribute('id',cid+'_sdw');
popsdw.className='popsdw';

var titlebar=document.createElement('div');
titlebar.setAttribute('id',cid+'_t');
titlebar.className = "pophdr";
var tbw=w-((ie5)?16:15);
titlebar.style.width=tbw+'px';

var imgClick = false;
var processClick = true;
if(titleImg != '')
{
titlebar.innerHTML='<div class=poptitle><img src='+titleImg+'><span id=title>'+title+'<\/span><\/div>';
}
else{
titlebar.innerHTML='<div class=poptitle><span id=title>'+title+'<\/span><\/div>';
}

var popclose=document.createElement('div');
popclose.setAttribute('id',cid+'_btt');
popclose.className='popclose';
var imgClick = false;
popclose.innerHTML='<img  src="/lcom/common/image/close.gif" onclick=hidebox('+cid+','+isOverLay+','+imgClick+')  id="'+cid+'_cls">';

var popbdr=document.createElement('div');
popbdr.setAttribute('id',cid+'_bdr');
popbdr.className='popbdr';

//tw=(ie5)?w-11:w-10;
//th=(ie5)?h-29:h-28;
tw=(ie5)?w-8:w-7;
th=(ie5)?h-24:h-23;

popbdr.style.width=tw+'px';
popbdr.style.height=th+'px';

var popbody=document.createElement('div');
popbody.setAttribute('id',cid+'_bdy');
popbody.className='popbody';

//tw=(ie5)?w-11:w-10;
//th=(ie5)?h-29:h-28;
var content=document.createElement('div');
content.setAttribute('id',cid+'_c');
content.className='popcon';
//content.style.width=tw+'px';
//content.style.height=th+'px';
content.innerHTML="<iframe id='iframe1' src ='"+url+"' width='100%' frameborder='0' height='100%'><\/iframe>";

popbdr.appendChild(popbody);
popbody.appendChild(content);
outerdiv.appendChild(popsdw);
outerdiv.appendChild(titlebar);
outerdiv.appendChild(popclose);
outerdiv.appendChild(popbdr);
outerdiv.appendChild(rdiv);
document.body.appendChild(outerdiv);
content.focus();
//if(!showonstart)hidebox(cid,isOverLay);

var IDS=new Array();
IDS[0]=document.getElementById(cid+'_b');
IDS[1]=document.getElementById(cid+'_t');
IDS[2]=document.getElementById(cid+'_c');
//IDS[3]=document.getElementById(cid+'_s');
IDS[3]=document.getElementById(cid+'_rs');
IDS[4]=document.getElementById(cid+'_btt');
//IDS[6]=document.getElementById(cid+'_min');
//IDS[7]=document.getElementById(cid+'_max');
IDS[5]=document.getElementById(cid+'_cls');
IDS[6]=document.getElementById(cid+'_bdr');
IDS[7]=h;
IDS[8]=document.getElementById(cid+'_dummy');
IDS[9]=document.getElementById(cid+'_bdy');
this.IDb=IDS[0]; this.IDb.IDS=IDS;
this.IDt=IDS[1]; this.IDt.IDS=IDS;
this.IDc=IDS[2]; this.IDc.IDS=IDS;
this.IDrs=IDS[3]; this.IDrs.IDS=IDS;
this.IDbtt=IDS[4]; this.IDbtt.IDS=IDS;
this.IDcls=IDS[5]; this.IDcls.IDS=IDS;
this.IDcls.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");
if(isresize){
this.IDrs.onmousedown=startRS;
this.IDrs.onmouseup=new Function("currRS=null");
}
this.IDb.onmousedown=function(){
   //if(ns6)this.IDS[2].style.overflow='auto';
   this.style.zIndex=++zdx;
   }
if(isdrag){
this.IDt.onmousedown=grab_id;
this.IDt.onmouseup=stopdrag;
}
}
}

if(ns6)setInterval('movepopup()',40);

if(w3c){
document.onmousemove=trackmouse;
document.onmouseup=new Function("currRS=null");
}

/* A general confirm popup with non-url content having an ok and cancel button */
function ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay){
        ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay,false);
}

function ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay,autoWidth){

        var tw, th;
        w=Math.max(w,100);
        h=Math.max(h,80);
        var rdiv= (autoWidth) ? new subBox(w-((ie5)?12:8),h-((ie5)?12:8),null,7,cid+'_rs','') : new subBox(w-((ie5)?12:8),h-((ie5)?12:8),7,7,cid+'_rs','');

        if(isresize){
        rdiv.style.cursor='move';
        }

        var top = document.documentElement.clientHeight - h - 20;
        var left = document.documentElement.clientWidth  - w - 20;

        if( x > left)
                x = left;
        if( y > top)
                y = top;

        var popsdw=document.createElement('div');
        popsdw.setAttribute('id',cid+'_sdw');
        popsdw.className='popsdw';
        // fall back. In case sign out popup doesnt open in the view area, assign default values to x,y.
        if( y < 0 || x < 0 ){
                y = 22;
                x = 1017;
                popsdw.style.visibility='hidden';
        }
        var outerdiv= (autoWidth) ? new subBox(x,y,null,h,cid+'_b','pop') : new subBox(x,y,w,h,cid+'_b','pop');

        titlebar=document.createElement('div');
        titlebar.setAttribute('id',cid+'_t'); //
        titlebar.className = "pophdr";

        var imgClick = false;
        var processClick = true;

        titlebar.innerHTML='<div class=poptitle><img src='+titleImg+'><span id=title>'+title+'<\/span><\/div>';

        var popclose=document.createElement('div');
        popclose.setAttribute('id',cid+'_btt');
        popclose.className='popclose';
        var imgClick = false;

        popclose.innerHTML='<img  src="/lcom/common/image/close.gif" onclick=hidebox('+cid+','+isOverLay+','+imgClick+')  id="'+cid+'_cls">';

        var popbdr=document.createElement('div');
        popbdr.setAttribute('id',cid+'_bdr');
        popbdr.className='popbdr';

        tw=(ie5)?w-9:w-8;
        th=(ie5)?h-25:h-24;

        popbdr.style.width=tw+'px';
        popbdr.style.height=th+'px';
        if(autoWidth) { popbdr.style.width = 'auto' ;}

        var content=document.createElement('div');
        content.setAttribute('id',cid+'_c');
        content.className ='signoutpopbody';
        content.innerHTML='<div class="popcon"><p>'+signOutArray[1]+'<\/p><\/div><div class="jpopftr"><button type="button" class="btn" id="'+cid+'_ok" onclick="event.cancelBubble = true; fnLogout(); return false;">'+signOutArray[2]+'<\/button><button type="button" class="btn" id="'+cid+'_cancel" onclick="event.cancelBubble = true; hidebox('+cid+','+isOverLay+','+imgClick+'); return false;">'+signOutArray[3]+'<\/button><\/div>';

        popbdr.appendChild(content);
        outerdiv.appendChild(popsdw);
        outerdiv.appendChild(titlebar);
        outerdiv.appendChild(popclose);
        outerdiv.appendChild(popbdr);
        outerdiv.appendChild(rdiv);
        document.body.appendChild(outerdiv);
        content.focus();

        var IDS=new Array();
        IDS[0]=document.getElementById(cid+'_b');
        IDS[1]=document.getElementById(cid+'_t');
        IDS[2]=document.getElementById(cid+'_cls');
        IDS[3]=document.getElementById(cid+'_cancel');
        IDS[4]=document.getElementById(cid+'_ok');

        this.IDb=IDS[0]; this.IDb.IDS=IDS;
        this.IDt=IDS[1]; this.IDt.IDS=IDS;
        this.IDcls=IDS[2]; this.IDcls.IDS=IDS;
        this.IDcancel=IDS[3]; this.IDcancel.IDS=IDS;

        this.IDcls.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");
        this.IDcancel.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");

        if(isdrag){
        this.IDt.onmousedown=grab_id;
        this.IDt.onmouseup=stopdrag;
        }
}

function closeCChangePopup()
{
        var fdiv = this.parent.document.getElementById('infoDiv_b');
        if(fdiv != null)
        fdiv.parentNode.removeChild(fdiv);
}

function openCChangePopup(w,h,url,title)
{
        var fdiv= document.getElementById('infoDiv_b');
        if(fdiv == null)
                var infoWin = new popUp(300, 300, w, h, 'infoDiv', title, true, false, url,'',true,url);
        document.getElementById("screen").style.display = "none";
}



//]]>
</script><span id="dataForm:Appt_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptAttributes_top"><table id="PANEL_ApptAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_Header_Out"></span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptAttributes" class="pnlcondiv"><table id="dataForm:gl4" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Appointment priority:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:ap10" name="dataForm:ap10" size="1"><option value="">None</option><option value="4">Critical</option><option value="3">High</option><option value="1" selected="selected">Low</option><option value="5">Most Critical</option><option value="2">Normal</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment status:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:appointmentStatus_det1" type="hidden" name="dataForm:appointmentStatus_det1" value="3"><input id="dataForm:appointmentStatus_det3" type="text" name="dataForm:appointmentStatus_det3" value="Scheduled" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl4","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span><span id="dataForm:Appt_Comment_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCommentAttributes_top"><table id="PANEL_ApptCommentAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_comment_Header_Out">Comments</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCommentAttributes" class="pnlcondiv"><table id="dataForm:gl5" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Comment code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:cc10" name="dataForm:cc10" size="1"><option value="">None</option><option value="936">Carrier Delay</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment comments:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:appointmentComments_det1" type="text" name="dataForm:appointmentComments_det1"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl5","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span><span id="dataForm:Appt_requestor_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptRequestorAttributes_top"><table id="PANEL_ApptRequestorAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_requestor_Header_Out">Requestor</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptRequestorAttributes" class="pnlcondiv"><table id="dataForm:gl6" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Request type:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:rt11" name="dataForm:rt11" size="1"><option value="">None</option><option value="AFAX">Auto-fax</option><option value="EDI">EDI</option><option value="EDI1">EDI1</option><option value="EDI2">EDI2</option><option value="EDI3">EDI3</option><option value="EDI4">EDI4</option><option value="EDI5">EDI5</option><option value="EMAI">Email</option><option value="FAX">Facsimile</option><option value="PDA">PDA</option><option value="PH">Telephone</option><option value="WEB">Web</option><option value="XML">XML</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Requester name:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:requesterName_det1" type="text" name="dataForm:requesterName_det1" value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl6","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span><span id="dataForm:Appt_carrier_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCarrierAttributes_top"><table id="PANEL_ApptCarrierAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_carrier_Header_Out">Carrier</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCarrierAttributes" class="pnlcondiv"></div></div></span><span id="dataForm:Appt_trailer_Attributes" border="true"><script id="dataForm:j_id333" type="text/javascript">//<![CDATA[
                loadTrailerDetails=function(){A4J.AJAX.Submit('dataForm',null,{'similarityGroupingId':'dataForm:j_id333','parameters':{'dataForm:j_id333':'dataForm:j_id333'} } )};
                //]]>
</script><div class="pnltopdiv" id="PANEL_ApptTrailerAttributes_top"><table id="PANEL_ApptTrailerAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_trailer_Header_Out">Trailer</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptTrailerAttributes" class="pnlcondiv"><table id="dataForm:gl8" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Trailer:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerNumber" type="text" name="dataForm:trailerNumber" value="" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id334','parameters':{'dataForm:j_id334':'dataForm:j_id334'} } )"><a id="dataForm:apptPO_dtlLnk_id" name="dataForm:apptPO_dtlLnk_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-c133-ad04-84d0" onclick="return false;"><img id="dataForm:POapptImg" src="/lcom/common/image/find.gif" alt="Find Trailer" onclick="getTrailerLookupforAppointment();" title="Find Trailer"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tractorNumber" type="text" name="dataForm:tractorNumber" value="" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id335','parameters':{'dataForm:j_id335':'dataForm:j_id335'} } )"><a id="dataForm:tractorLookup_id" name="dataForm:tractorLookup_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-c133-ad04-84d0" onclick="return false;"><img id="dataForm:tracLkpImg" src="/lcom/common/image/find.gif" alt="Find Tractor" onclick="getTractorLookupforAppointment();" title="Find Tractor"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Load position:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:lp10" name="dataForm:lp10" size="1"><option value="" selected="selected">None</option><option value="4">Nose</option><option value="8">Tail</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tractorLicenseNumber_det3" type="text" name="dataForm:tractorLicenseNumber_det3" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Trailer license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerLicenseNumber" type="text" name="dataForm:trailerLicenseNumber" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tratcorLicenseState_det3" type="text" name="dataForm:tratcorLicenseState_det3" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Trailer license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerLicenseState" type="text" name="dataForm:trailerLicenseState" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:previousTrailer" type="hidden" name="dataForm:previousTrailer" value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl8","_u8_gl_1",{layout:"v"});
//]]>
</script><input id="dataForm:hiddentractorLicenseNumber" type="hidden" name="dataForm:hiddentractorLicenseNumber" value=""><input id="dataForm:hiddentratcorLicenseState" type="hidden" name="dataForm:hiddentratcorLicenseState" value=""><input id="dataForm:hiddentrailerLicenseState" type="hidden" name="dataForm:hiddentrailerLicenseState" value=""><input id="dataForm:hiddentrailerLicenseNumber" type="hidden" name="dataForm:hiddentrailerLicenseNumber" value=""></div></div></span><span id="dataForm:Appt_driver_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptDriverAttributes_top"><table id="PANEL_ApptDriverAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_driver_Header_Out">Driver</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptDriverAttributes" class="pnlcondiv"><table id="dataForm:gl9" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Driver name:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverName_det2" type="text" name="dataForm:driverName_det2" onblur="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id337','parameters':{'dataForm:j_id337':'dataForm:j_id337'} } )"><a id="dataForm:driver_link" name="dataForm:driver_link" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-c133-ad04-84d0" onclick="return false;"><img id="dataForm:loc3LookupDriver" src="/lps/resources/themes/icons/mablue/find.gif" alt="Find Driver" onclick="getDriverForAppointment(event);" title="Find Driver"></a><input id="dataForm:previousDriver" type="hidden" name="dataForm:previousDriver" value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Driver license expiry:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseExp_det3" type="text" name="dataForm:driverLicenseExp_det3" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Driver license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseState_det3" type="text" name="dataForm:driverLicenseState_det3" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Driver license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseNumber_det3" type="text" name="dataForm:driverLicenseNumber_det3" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Driver license country:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseCountry_det3" type="text" name="dataForm:driverLicenseCountry_det3" value="" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Contact number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:beeperNumber_det3" type="text" name="dataForm:beeperNumber_det3" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl9","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div><input id="dataForm:hiddendriverLicenceState" type="hidden" name="dataForm:hiddendriverLicenceState"><input id="dataForm:hiddendriverLicenceCountry" type="hidden" name="dataForm:hiddendriverLicenceCountry" value=""><input id="dataForm:hiddenLicenseExpiryDate" type="hidden" name="dataForm:hiddenLicenseExpiryDate" value=""><input id="dataForm:hiddenLicenseNumber" type="hidden" name="dataForm:hiddenLicenseNumber"><input id="dataForm:hiddendriverContactNum" type="hidden" name="dataForm:hiddendriverContactNum"></span><span id="dataForm:Appt_cancel_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptcancelAttributes_top"><table id="PANEL_ApptcancelAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_cancel_Header_Out"></span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptcancelAttributes" class="pnlcondiv"><table id="dataForm:g20" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Canceled:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:cancelled_det3" type="checkbox" name="dataForm:cancelled_det3" onclick="enableReasonCode();"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Cancel reason code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:cr21" name="dataForm:cr21" size="1" disabled="disabled" onchange="populateReasonCode();"><option value="">None</option><option value="57">Carrier Error</option><option value="55">Mechanical Breakdown</option><option value="441">Rec Whse Refusal</option><option value="56">Shipper Delay</option><option value="54">Traffic Accident</option></select><input id="dataForm:cancelReasonCode_det3" type="hidden" name="dataForm:cancelReasonCode_det3"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:g20","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span><span id="dataForm:Appt_source_Attributes" border="true"></span><div style="white-space:nowrap;vertical-align:inherit"><div id="uirecommendationscap" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><input id="dataForm:selectedUirecommendation" type="hidden" name="dataForm:selectedUirecommendation"></div></span><input id="dataForm:recurring_saveFromRecurringWindow" type="hidden" name="dataForm:recurring_saveFromRecurringWindow" value="false"><input id="dataForm:recurring_StartDate" type="hidden" name="dataForm:recurring_StartDate" value=""><input id="dataForm:recurring_EndDate" type="hidden" name="dataForm:recurring_EndDate" value=""><input id="dataForm:recurring_daily" type="hidden" name="dataForm:recurring_daily" value="false"><input id="dataForm:recurring_frequencyInDays" type="hidden" name="dataForm:recurring_frequencyInDays" value=""><input id="dataForm:recurring_weekly" type="hidden" name="dataForm:recurring_weekly" value="false"><input id="dataForm:recurring_monthly" type="hidden" name="dataForm:recurring_monthly" value="false"><input id="dataForm:recurring_monday" type="hidden" name="dataForm:recurring_monday" value="false"><input id="dataForm:recurring_tuesday" type="hidden" name="dataForm:recurring_tuesday" value="false"><input id="dataForm:recurring_wednesday" type="hidden" name="dataForm:recurring_wednesday" value="false"><input id="dataForm:recurring_thursday" type="hidden" name="dataForm:recurring_thursday" value="false"><input id="dataForm:recurring_friday" type="hidden" name="dataForm:recurring_friday" value="false"><input id="dataForm:recurring_saturday" type="hidden" name="dataForm:recurring_saturday" value="false"><input id="dataForm:recurring_sunday" type="hidden" name="dataForm:recurring_sunday" value="false"></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">//<![CDATA[
_u8_tnl_5("syncTab",{tab4:"CONT_dataForm:tab4",tab1:"CONT_dataForm:tab1",tab2:"CONT_dataForm:tab2"});
//]]>
</script><script type="text/javascript">//<![CDATA[
_u8_tnl_2("syncTab","tab1");
_u8_tnl_3("syncTab","tab1");
//]]>
</script></div></span></span></div></div></td></tr></tbody></table></div>

					</div>
				</td>
			</tr>
			<tr>
				<td id="workareafootertd" class="workareafootertd">
					<div id="workareafooter" class="workareafooter" style="width: 990px;"><div id="_footerDivComponent"><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:footer_btn"><script type="text/javascript" language="JavaScript" src="/lps/resources/actionpanel/moreButton/scripts/moreButton.js">
</script><div id="soheaderbuttons" class="actionPanelLeft"><input id="dataForm:apptScreenId" type="hidden" name="dataForm:apptScreenId" value=""><input id="dataForm:newCalendarInfo" type="hidden" name="dataForm:newCalendarInfo" value=""><input id="dataForm:apptList_btn_14" type="submit" name="dataForm:apptList_btn_14" value="Cancel" alt="Cancel" class="btn"><input class="btn" id="dataForm:apptList_btn_9" name="dataForm:apptList_btn_9" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_9','parameters':{'dataForm:apptList_btn_9':'dataForm:apptList_btn_9'} } );return false;" value="Validate" alt="Validate" type="button"><input class="btn" id="dataForm:apptList_btn_10" name="dataForm:apptList_btn_10" onclick="dummy();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_10','oncomplete':function(request,event,data){openRecomendationsDialog( false ,  false )},'parameters':{'dataForm:apptList_btn_10':'dataForm:apptList_btn_10'} } );return false;" value="Recommend Time Slots" alt="Recommend Time Slots" type="button"><input type="button" class="btn" id="apptList_btn_12" onclick="if(this.disabled==true) {return false;} else{ disableFooterButtons(); };postForm('#{appointmentBackingBean.saveAction}','apptList_btn_12','soheaderbuttons');" value="Save"><span class="groupBtnSpace">&nbsp;</span><input type="hidden" id="moreActionTargetLinksoheaderbuttons" name="moreActionTargetLinksoheaderbuttons" value=""><input type="hidden" id="moreActionButtonPressedsoheaderbuttons" name="moreActionButtonPressedsoheaderbuttons" value=""></div></span></div>
					</div>
				</td>
			</tr>
		</tbody></table>
                    <input type="hidden" id="backingBeanName" name="backingBeanName" value="appointmentBackingBean" tabindex="461">
                    </td>
                </tr>
            </tbody></table>
            </td>
        </tr>

    </tbody></table>
    </div><input type="hidden" name="javax.faces.ViewState" id="javax.faces.ViewState" value="5851336597602014956:4310148358548290179" autocomplete="off" tabindex="462">
</form><script language="JavaScript">document.title="Appointments | Manhattan Associates";</script><span id="title"></span>

<script type="text/javascript">
//<![CDATA[
    _script_pg_2 ();
   	 UI8Layout.onload.push(appendWindowId);
     if(typeof showFilter == 'function'){
        UI8Layout.onload.push(showFilter);
     }
     UI8Layout.onafterajax = function() {
	   	 appendWindowId();
         if(typeof showFilter == 'function'){
            showFilter();
         }
	   	 if(typeof afterajaxCallback == 'function') {
	   		afterajaxCallback();
	   	 }
		 document.body.style.cursor="default";
		 var layDiv = document.getElementById("overlapLayer");
		  if(layDiv){
			  layDiv.style.height = 0;
			  layDiv.style.width = 0;
			  layDiv.style.left = 0;
			  layDiv.style.top = 0;
		  }
 	}
    A4J.AJAX.AddListener(UI8Layout);

	//code changed for MACR00645636
	A4J.AJAX.onError = function(req, status, message) {
		document.body.style.cursor = "default";
		var layDiv = document.getElementById("overlapLayer");
		if (layDiv) {
			layDiv.style.height = 0;
			layDiv.style.width = 0;
			layDiv.style.left = 0;
			layDiv.style.top = 0;
		}
	}

//]]>

</script>


<script xmlns="http://www.w3.org/1999/xhtml">A4J.AJAX._scriptEvaluated=true;</script></body></html>