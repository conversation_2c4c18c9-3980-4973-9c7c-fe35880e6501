<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ajax="http://www.exadel.com/vcp/components/ajax"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="/a4j/g/3_3_3.Finalorg.ajax4jsf.javascript.AjaxScript.xhtml*.jsf*.jsfx*.jsflps" type="text/javascript"></script><link id="mpsstyles" rel="stylesheet" type="text/css" href="/lps/lpsstyles">
<link id="LPSStyles" rel="stylesheet" type="text/css" href="/lps/resources/common/css/LPSStyles.css">
<link id="LPSLayoutStyles" rel="stylesheet" type="text/css" href="/lps/resources/layout/css/LPSLayoutStyles.css">
<link rel="stylesheet" type="text/css" href="/lps/resources/common/css/richfaces.css">
<link rel="shortcut icon" href="/lps/resources/layout/images/favicon.ico">

<script type="text/javascript">
//<![CDATA[
    // Global java script var for flagging HFI.
    var UI8 = true;
    var HFI = UI8;
    var MPS = UI8;
//]]>
</script><script language="JavaScript" src="/lps/resources/common/UI08Common.js"></script><title>Add Appointment | Manhattan Associates</title></head><body scroll="no" link="#0000FF" vlink="#0000FF" alink="#0000FF" class="-gbl_mtc" style="cursor: default;"><span id="scppCommon"></span><script language="JavaScript" src="/lps/resources/calendar/scripts/calendar.js"></script><span id="scppCalendarDef"></span><script language="JavaScript" src="/lcom/common/js/csrf.js"></script><span id="waf_csrf"></span>

<script type="text/javascript">
//<![CDATA[
    jQuery.noConflict() ;
    browser_Styles ();
	UI8Layout.setExpLayout(true);
//]]>
</script>



<form id="dataForm" name="dataForm" method="post" action="/appointment/ui/jsf/scheduleAppointment.jsflps" enctype="application/x-www-form-urlencoded" onsubmit="checkHyperClicking()">
<input type="hidden" name="dataForm" value="dataForm" tabindex="1">
<input type="hidden" name="uniqueToken" value="1" tabindex="2"><input id="MANH-CSRFToken" type="hidden" name="MANH-CSRFToken" value="wjacskDXX32MLBxxla7LxoObJhUhFdjz/V+AacEcdJ8=" tabindex="3"><script type="text/javascript">
CSRF.Conf.init({tokenHolderId:"MANH-CSRFToken",tokenName:"MANH-CSRFToken",tokenValue:"wjacskDXX32MLBxxla7LxoObJhUhFdjz/V+AacEcdJ8=",enabled:true});
</script>
    <script type="text/javascript">
    //<![CDATA[
        var isServlet = false;
        var helpURL="/lcom/common/jsp/helphelper.jsp?server=960AE9B07F6B5033C7901237699DFC5FCD42DD4FCBE39DDD157E4AD998114A00&uri=%2Fappointment%2Fui%2Fjsf%2FscheduleAppointment.jsflps";
		var showSaveAsTile = "false";
        var dialogHelpURL=null;
        var stkId ="\x20";
        var customizationExists = eval("true");
        var ucl_home_url =  "";
        document.getElementById('dataForm').addParam = _addParam;
    //]]>
    </script>
	<input type="hidden" id="helpurlEle" name="helpurlEle" value="/lcom/common/jsp/helphelper.jsp?server=960AE9B07F6B5033C7901237699DFC5FCD42DD4FCBE39DDD157E4AD998114A00&amp;uri=%2Fappointment%2Fui%2Fjsf%2FscheduleAppointment.jsflps" tabindex="4">

   <script>
   //<![CDATA[
        _script_pg_1 ();
   //]]>
   </script>


    <div id="drag_1" style="position: absolute; z-index: 30000;"><div id="dataForm:er_d1" name="dataForm:er_d1" class="dialog_cont" style="z-index: 0; display: none; top: 0px; left: 273px;"><div class="dialog_inner">
        <div class="erolpop -pdlg_eob">
        <div class="pop_sdw"></div>
        <table class="pophead" cellspacing="0" cellpadding="0" border="0">
            <tbody>
                <tr>
                    <td>
                    <div class="pop_hdr -pdlg_dhbg">
                    <table class="pop_hdr_inner" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <tbody>
                            <tr>
                                <td class="pop_dragHandler" id="er_d1_dh1" unselectable="on"><div unselectable="on" class="pop_title erpoptitle">Messages (<span unselectable="on" id="er_d1_mcnt">5</span>)</div></td>
                                <td class="er_dw_mintd" id="er_d1_mtd1"><div class="erpopmin" id="er_d1_m1"></div></td>
                                <td class="er_dw_clstd"><div class="pop_close" id="er_d1_c1"></div></td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    </td>
                </tr>
                <tr>
                    <td>
                    <div class="pop_bdr -pdlg_dhbg">
                       <div class="pop_body_err -pdlg_dbg" id="er_d1_bid"><ul class="overlayerrorList"><li class="overlayerror -icons_er">Cannot create an appointment without shipments or POs. Please enter at least one shipment or PO.</li><li class="overlayerror -icons_er">Facility is a required field.</li><li class="overlayerror -icons_er">Facility is a required field</li><li class="overlayerror -icons_er">Appointment Start Date/Time is a required field.</li><li class="overlayerror -icons_er">Appointment type is a required field/ Please provide an appointment type</li></ul></div>
                    </div>
                    </td>
                </tr>
            </tbody>
        </table>
        </div></div></div><script type="text/javascript">
UI8Layout.data.put("er_d1",{onClose:"",dialogClientId:"dataForm:er_d1",dragHandleId:"er_d1_dh1",onDialog:"",closeClientId:"er_d1_c1"});
</script>

    <script type="text/javascript">
    //<![CDATA[
         var ui8er = UI8Layout.ErrorOverLay;
         ui8er.init ();
    //]]>
    </script><div id="dataForm:find_d1" name="dataForm:find_d1" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:bdt_1_dHId"><div class="pop_title -pdlg_dttc"> </div></td><td><div class="pop_close" id="dataForm:bdt_1_cCId"><input type="button" tabindex="5"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg">
                    <iframe id="iframe1" width="490px" frameborder="0" scrolling="no" height="453px"></iframe></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("find_d1",{onClose:"onFindClose",dialogClientId:"dataForm:find_d1",dragHandleId:"dataForm:bdt_1_dHId",onDialog:"",closeClientId:"dataForm:bdt_1_cCId"});
</script><span id="dataForm:changePrtReqAjaxPanel"></span><span id="dataForm:reportAjaxPanel"></span>
		<script language="JavaScript">
			var msgcheckDefaultFilter="Default filter already exist for this Object Type. Do you want to override?";
			var msgDeleteFilters="Are you sure that you want to delete selected filters?";
			var msgSelectFilterToDelete="Please select at least one filter to delete";
			var expandedTlTp="Expand";
			var collapseTlTp="Collapse";
		</script><div id="dataForm:filterList" name="dataForm:filterList" class="dialog_cont" style="height:460px;width:800px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_11_dHId"><div class="pop_title -pdlg_dttc">Saved Filters</div></td><td><div class="pop_close" id="dataForm:bdt_11_cCId"><input type="button" tabindex="6"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="width:800px;">


			<div class="popcon" id="filterListContainer">

			<div id="filterListImagecontainer" align="center">
					<br><br><br><br><br><img id="dataForm:animationGenId_start_filter" src="/lps/resources/menu/ribbon/images/loading_animation_liferay.gif">
					<br><br><br><br><br>
			</div>


				<div id="ajaxContainer"><span id="dataForm:filterDetailAJAXPanel"></span>

				</div>

			</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterList",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:filterList",dragHandleId:"dataForm:bdt_11_dHId",onDialog:"",closeClientId:"dataForm:bdt_11_cCId"});
</script><div id="dataForm:filterListFilter" name="dataForm:filterListFilter" class="dialog_cont" style="height:200px;width:350px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_flf_dHId"><div class="pop_title -pdlg_dttc">Filter List Filter</div></td><td><div class="pop_close" id="dataForm:bdt_flf_cCId"><input type="button" tabindex="7"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="width:340px;"><span id="dataForm:filterListFilterPanel">

		<script language="JavaScript">
			var filterListErrMsg1="Filter condition cannot be blank";
			var filterListErrMsg2="Wild card (*) not supported. Please enter different filter criteria";
			var filterListErrMsg3="Filter Criteria cannot be blank - please enter the search criteria";
		</script>&nbsp;&nbsp;<span id="dataForm:filterTitle" class="boldFont">Criteria: </span>
		<br>
		<br><div id="fltrDivId"><input id="dataForm:fltrListFltrId:fieldName" type="hidden" name="dataForm:fltrListFltrId:fieldName" tabindex="8"><input id="dataForm:fltrListFltrId:filterName" type="hidden" name="dataForm:fltrListFltrId:filterName" value="FL_PREPAID" tabindex="9"><input id="dataForm:fltrListFltrId:owner" type="hidden" name="dataForm:fltrListFltrId:owner" value="PREPAID" tabindex="10"><input id="dataForm:fltrListFltrId:objectType" type="hidden" name="dataForm:fltrListFltrId:objectType" value="FL_FILTER" tabindex="11"><input id="dataForm:fltrListFltrId:filterObjectType" type="hidden" name="dataForm:fltrListFltrId:filterObjectType" tabindex="12"><input id="dataForm:fltrListFltrId:field0value1" type="hidden" name="dataForm:fltrListFltrId:field0value1" value="" tabindex="13"><input id="dataForm:fltrListFltrId:field0" type="hidden" name="dataForm:fltrListFltrId:field0" value="FILTER.FILTER_NAME" tabindex="14"><input id="dataForm:fltrListFltrId:field0operator" type="hidden" name="dataForm:fltrListFltrId:field0operator" value="" tabindex="15"><input id="dataForm:fltrListFltrId:field1value1" type="hidden" name="dataForm:fltrListFltrId:field1value1" value="" tabindex="16"><input id="dataForm:fltrListFltrId:field1" type="hidden" name="dataForm:fltrListFltrId:field1" value="FILTER.IS_DEFAULT" tabindex="17"><input id="dataForm:fltrListFltrId:field1operator" type="hidden" name="dataForm:fltrListFltrId:field1operator" value="" tabindex="18"><input id="dataForm:fltrListFltrId:field2value1" type="hidden" name="dataForm:fltrListFltrId:field2value1" value="" tabindex="19"><input id="dataForm:fltrListFltrId:field2" type="hidden" name="dataForm:fltrListFltrId:field2" value="FILTER.IS_PRIVATE" tabindex="20"><input id="dataForm:fltrListFltrId:field2operator" type="hidden" name="dataForm:fltrListFltrId:field2operator" value="" tabindex="21"><input id="dataForm:fltrListFltrId:field3value1" type="hidden" name="dataForm:fltrListFltrId:field3value1" value="" tabindex="22"><input id="dataForm:fltrListFltrId:field3" type="hidden" name="dataForm:fltrListFltrId:field3" value="FILTER.OWNER" tabindex="23"><input id="dataForm:fltrListFltrId:field3operator" type="hidden" name="dataForm:fltrListFltrId:field3operator" value="" tabindex="24"><input id="dataForm:fltrListFltrId:field4value1" type="hidden" name="dataForm:fltrListFltrId:field4value1" value="" tabindex="25"><input id="dataForm:fltrListFltrId:field4" type="hidden" name="dataForm:fltrListFltrId:field4" value="FILTER.IS_DELETED" tabindex="26"><input id="dataForm:fltrListFltrId:field4operator" type="hidden" name="dataForm:fltrListFltrId:field4operator" value="" tabindex="27"><table id="dataForm:fltrListFltrId:j_id230" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Condition:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:fltrListFltrId:fltrCondition" name="dataForm:fltrListFltrId:fltrCondition" class="fltrSelMenuStyle" size="1" tabindex="28">	<option value="" selected="selected"></option>
	<option value="Equals">Equals</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Filter:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:fltrListFltrId:fltrCrtSel" name="dataForm:fltrListFltrId:fltrCrtSel" class="fltrSelMenuStyle" size="1" tabindex="29">	<option value="" selected="selected"></option>
	<option value="1">Yes </option>
	<option value="0">No</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:fltrListFltrId:j_id230","_u8_gl_1",{layout:"v"});
</script></div>

		<br><input class="btn" id="dataForm:clearButton" name="dataForm:clearButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:clearButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:clearButton':'dataForm:clearButton','reRenderParent':''} } );return false;" value="Clear Filter" type="button" tabindex="30">&nbsp;&nbsp;<input class="btn" id="dataForm:applyAndSaveButton" name="dataForm:applyAndSaveButton" onclick="if(!doValTrimming()){ return false;};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:applyAndSaveButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'filterScreenType':'ON_SCREEN','dataForm:fltrListFltrId:filterId':'','fltrClientId':'dataForm:fltrListFltrId','fltrListFltr':'true','defaultChanged':'1','isJSF':'true','reRenderParent':'','dataForm:applyAndSaveButton':'dataForm:applyAndSaveButton'} } );return false;" value="Save and Apply" type="button" tabindex="31">&nbsp;&nbsp;<input class="btn" id="dataForm:applyButton" name="dataForm:applyButton" onclick="if(!doValTrimming()){ return false;};A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:applyButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:applyButton':'dataForm:applyButton','fltrClientId':'dataForm:fltrListFltrId','fltrListFltr':'true','isJSF':'true','reRenderParent':''} } );return false;" value="Apply" type="button" tabindex="32">&nbsp;&nbsp;<input class="btn" id="dataForm:cancelButton" name="dataForm:cancelButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:cancelButton','oncomplete':function(request,event,data){onComplete()},'parameters':{'dataForm:cancelButton':'dataForm:cancelButton','reRenderParent':''} } );return false;" value="Cancel" type="button" tabindex="33">

		<script>
    	//<![CDATA[

    		function filterListOnClick(event){
				var event = ( event ) ? event : window.event;
				if(window.event)
				{
					window.event.cancelBubble = true;
				}
				if(event.cancelBubble)
				{
					event.cancelBubble = true;
				}
				if(event.stopPropagation)
				{
					event.stopPropagation();
				}
			}

    		function onComplete(){
    			UI8Layout.doDialogCloseById('filterListFilter');
    			resizeWidth('filterListTable');
    		}

			function doValTrimming(){

    			var fldName = eByID('dataForm:fltrListFltrId:fieldName');
    			var selMenu= eByID('dataForm:fltrListFltrId:fltrCondition');

    			if(fldName && fldName.value=='FILTER.FILTER_NAME')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field0value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field0operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtText');
    			}
    			else if(fldName && fldName.value=='FILTER.OWNER')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field3value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field3operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtText');
    			}
    			else if(fldName && fldName.value=='FILTER.IS_DEFAULT')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field1value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field1operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
	    		}
    			else if(fldName && fldName.value=='FILTER.IS_PRIVATE')
    			{
	    			var fld= eByID('dataForm:fltrListFltrId:field2value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field2operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
	    		}
    			else
    			{
    				var fld= eByID('dataForm:fltrListFltrId:field4value1');
	    			var fldOper = eByID('dataForm:fltrListFltrId:field4operator');
	    			var crtText = eByID('dataForm:fltrListFltrId:fltrCrtSel');
    			}

    			if(selMenu && selMenu.value=='')
    			{
    				alert(filterListErrMsg1);
    				return false;
    			}
    			if(crtText && crtText.value.indexOf("*")!=-1)
    			{
    				alert(filterListErrMsg2);
     				return false;
     			}

     			if(selMenu && selMenu.value=='Contains' && crtText && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0)
    					{
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Begins with' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Ends with' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" +trim(splitArr[i]);
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && selMenu.value=='Does Not Contains' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "*" + trim(splitArr[i]) + "*";
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + "*" + trim(splitArr[i]) + "*";
    				}
    				fld.value = formatStr;
    				fldOper.value = "!=";
    			}
    			else if(selMenu && crtText && fldOper && selMenu.value=='Equals' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr  + trim(splitArr[i]) ;
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "=";
    			}
    			else if(selMenu && crtText && fldOper && selMenu.value=='Does Not Equals' && crtText.value!='')
    			{
    				var splitArr = crtText.value.split(",");
    				var formatStr ="";
    				for(var i=0;i<splitArr.length;i++){
    					if(i== 0){
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr  + trim(splitArr[i]) ;
    					}
    					else
    						if(trim(splitArr[i])!='')
    						formatStr = formatStr + "," + trim(splitArr[i]);
    				}
    				fld.value = formatStr;
    				fldOper.value = "!=";
    			}
	   			if(crtText!= null && trim(crtText.value)!='')
	   			{
	   				return true;
	   			}
	   			else
	   			{
	   				alert(filterListErrMsg3);
	   				return false;
	   			}
    		}


   		function trim(str, chars) {
		return ltrim(rtrim(str, chars), chars);
		}

		function ltrim(str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("^[" + chars + "]+", "g"), "");
		}

		function rtrim(str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("[" + chars + "]+$", "g"), "");
		}

    	function checkForEnter() {
		     if(window.event.keyCode == 13){
		     	  window.location.reload();
		     }
		}


    	//]]>
	   </script></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterListFilter",{onClose:"",dialogClientId:"dataForm:filterListFilter",dragHandleId:"dataForm:bdt_flf_dHId",onDialog:"",closeClientId:"dataForm:bdt_flf_cCId"});
</script><div id="dataForm:advanced_Filter_Dialog" name="dataForm:advanced_Filter_Dialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:advanced_Filter_BasicDialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Advance Filter</div></td><td><div class="pop_close" id="dataForm:advanced_Filter_BasicDialogTemplate_cCId"><input type="button" tabindex="34"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg">
				<div class="popcon">
					<iframe id="advanced_Filter_iframeId" frameborder="0" scrolling="no" width="800px" height="700px">
					</iframe>
				</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("advanced_Filter_Dialog",{onClose:"",dialogClientId:"dataForm:advanced_Filter_Dialog",dragHandleId:"dataForm:advanced_Filter_BasicDialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:advanced_Filter_BasicDialogTemplate_cCId"});
</script>

		<script language="JavaScript">
			var alertMsg1="Filter Name is required";
			var filterDetailHeight = document.documentElement.clientHeight;
			var expandedTlTp="Expand";
			var collapseTlTp="Collapse";
		</script><div id="dataForm:filterDetail" name="dataForm:filterDetail" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:bdt_222_dHId"><div class="pop_title -pdlg_dttc">Filter Details</div></td><td><div class="pop_close" id="dataForm:bdt_222_cCId"><input type="button" tabindex="35"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg"><span id="dataForm:editfilterDetailAJAXPanel">

				<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy3_clear" onclick="return false;" tabindex="36">
					<script>
						if(document.getElementById("filterDetailOutDiv")){
							if((filterDetailHeight-150)>0 )
								document.getElementById("filterDetailOutDiv").style.maxHeight=(filterDetailHeight-150)+"px";
								document.getElementById("filterDetailOutDiv").style.padding=10+"px";
						}
					</script></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("filterDetail",{onClose:"UIFilterJS.onFilterDtlClose",dialogClientId:"dataForm:filterDetail",dragHandleId:"dataForm:bdt_222_dHId",onDialog:"",closeClientId:"dataForm:bdt_222_cCId"});
</script><span id="dataForm:editfilterDetailAJAXPanelScript"></span><div id="dataForm:editQuickFilterDialog" name="dataForm:editQuickFilterDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:editQuickFilterTemplate_dHId"><div class="pop_title -pdlg_dttc">Edit Quick Filter</div></td><td><div class="pop_close" id="dataForm:editQuickFilterTemplate_cCId"><input type="button" tabindex="37"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg"><span id="dataForm:editQuickFilterAjaxPopup"></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("editQuickFilterDialog",{onClose:"",dialogClientId:"dataForm:editQuickFilterDialog",dragHandleId:"dataForm:editQuickFilterTemplate_dHId",onDialog:"",closeClientId:"dataForm:editQuickFilterTemplate_cCId"});
</script>

    </div>
    <script>
    //<![CDATA[
        jQuery('#drag_1').css('z-index', UI8Layout.dialogZ);
    //]]>
    </script><div id="dataForm:printDialog" name="dataForm:printDialog" class="dialog_cont" style="height:460px;width:800px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/images/foPrint.gif"></div></td><td class="pop_dragHandler" id="dataForm:printTemp_dHId"><div class="pop_title -pdlg_dttc">Print Dialog</div></td><td><div class="pop_close" id="dataForm:printTemp_cCId"><input type="button" tabindex="38"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:420px;width:780px;">
			<iframe name="printFrame" id="printFrame" style="visibility:hidden;display:none;width:100%;height:100%">
			</iframe></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("printDialog",{onClose:"",dialogClientId:"dataForm:printDialog",dragHandleId:"dataForm:printTemp_dHId",onDialog:"",closeClientId:"dataForm:printTemp_cCId"});
</script>
    <div style="position: absolute; background-color: rgb(0, 0, 0); opacity: 0; z-index: 9999999; height: 0px; width: 0px; left: 0px; top: 0px;" id="overlapLayer">
    </div>
    <div id="main">
    <table border="0" width="100%" cellpadding="0" cellspacing="0" id="maintable">
        <tbody><tr>
            <td>
		         <div id="popmenu" class="menuskin_UI08"></div>
		         <script language="javascript">;
		         //<![CDATA[
		             var oPopup = document.getElementById( "popmenu" );
		         //]]>
		         </script><div id="j_id70" style="padding:0px; margin:0px; border:0px;">
		         <div id="toolbar" style="width: auto; overflow: hidden;">
     <script type="text/javascript">
           //<![CDATA[

        function suspendMenuClose(evt)
        {
            if(evt.button == 2 || evt.which == 3)
                UI8Layout.setSuspendClose(false);
            return true;
        }
        function showFilter() {
            var filterid = getFilterId();
            if (filterid) {
                var fltricon = document.getElementById("mpsFltrImg");
                var fltrlink = document.getElementById("mpsFltrLink");
                var filter = document.getElementById(filterid);
                var cookie = getCookieValue('filterExpandState');
                if (fltricon && fltrlink) {
                    fltricon.style.display = 'block';
                    fltrlink.style.display = 'block';
                }
                if (cookie) {
                    if (cookie == "true") {
                        filter.style.display = "block";
                    } else {
                        filter.style.display = "none";
                    }
                } else {
                    filter.style.display = "block";
                    if (top != self) {
                        parent.document.cookie = "filterExpandState=true";
                    } else {
                        document.cookie = "filterExpandState=true";
                    }
                }
            }
        }

        function toggleFilter() {
            var filterid = getFilterId();
            var filter = document.getElementById(filterid);
            var cookie = getCookieValue('filterExpandState');
            if (filter.style.display == "none") {
                filter.style.display = "block";
                if (top != self) {
                    parent.document.cookie = "filterExpandState=true";
                } else {
                    document.cookie = "filterExpandState=true";
                }
            } else {
                filter.style.display = "none";
                if (top != self) {
                    parent.document.cookie = "filterExpandState=false";
                } else {
                    document.cookie = "filterExpandState=false";
                }
            }

        }

        function getCookieValue(key) {
            var currentcookie;
            if (top != self) {
                currentcookie = parent.document.cookie;
            } else {
                currentcookie = document.cookie;
            }
            if (currentcookie.length > 0) {
                firstidx = currentcookie.indexOf(key + "=");
                if (firstidx != -1) {
                    firstidx = firstidx + key.length + 1;
                    lastidx = currentcookie.indexOf(";", firstidx);
                    if (lastidx == -1) {
                        lastidx = currentcookie.length;
                    }
                    return currentcookie.substring(firstidx, lastidx);
                }
            }
            return "";
        }

        function getFilterId() {
            var ids = [], i = 0;
            jQuery("table.fltr_tbl").each(
                    function(idx, el) {
                        if (jQuery(el).parents("div.tabpnlout").length === 0) {
                            ids[i++] = el.parentNode.id;
                        }
                    });
            if (ids.length == 1) {
                return ids[0];
            }
            return null;
        }


    //]]>
    </script>


    <table class="pghdrmps -hft_tis" id="pghdr" cellspacing="0" cellpadding="0">
        <tbody><tr>
            <td class="pghdrrow">
            <table class="hdrt" cellspacing="0">
                <tbody><tr>
                      <td class="hdrto"><a id="backButton" href="/appointment/ui/jsf/appointmentList.xhtml?windowId=wt-2458-b0ae-7757&amp;backclick=true" hidefocus="true"><img id="backImage" src="/lps/resources/layout/images/backnew.png"></a>
                      </td>

                    <td class="hdrto" id="m_act_td"><a hidefocus="true" id="Actions" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" accesskey="A" class="mo -gbl_hhc -md_mhbc">
                    <span class="ul menulbl -hft_pht">A</span><span class="menulbl -hft_pht">ctions</span><span class="moimg -icons_mopt">&nbsp;</span> </a>


                    </td>
                    <td class="hdrto" id="m_tools_td"><a hidefocus="true" class="mo -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="Tools" accesskey="T">
                    <span class="ul menulbl -hft_pht">T</span><span class="menulbl -hft_pht">ools</span><span class="moimg -icons_mopt">&nbsp;</span></a>

                    </td>

                    <td class="hdrto" id="m_roles_td" style="display:none"><a hidefocus="true" class="mo -gbl_hhc -md_mhbc" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="Roles">
                    <span class="menulbl -hft_pht">Role:</span><span class="name -hft_pht"></span><span class="moimg -icons_mopt">&nbsp;</span></a>

                    </td>

                    <td class="hdrtw" id="m_tools_welcome"></td>


                  <td class="phtopicon2"><a hidefocus="true" id="mpsFltrLink" class="btn" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" style="display:none" onclick="javascript:toggleFilter();"><img id="mpsFltrImg" style="display:none;" src="/manh/mps/resources/icons/funnel.png" alt="Filter"></a> </td>


                    <td style="display:none; border:0px;"> <span id="titlePage"><div class="header-left"><span id="dataForm:hLabel" class="sectionheader">Add Appointment</span></div></span>
                    </td>

                    <td><script language="JavaScript" src="/lps/resources/detailNav/scripts/detailNav.js"></script><script language="JavaScript">var popupMessges=new Array();popupMessges[0]="Enter record number greater than zero";popupMessges[1]="Entered record number is greater than the total selected number of records";</script></td>

                </tr>
            </tbody></table>

            </td>
        </tr>
    </tbody></table>

    <div id="phMenu_child" class="menuskin_UI08" onmousedown="suspendMenuClose(event); return true;">
        <div class="mpop -md_obg -md_obc" id="mainMenu">
            <div class="mpopsdw -pdlg_sdw"></div>
            <div class="mpopfnd1 -md_obg" id="mnfind_1"></div>
            <div class="mpopclose"><img src="/lps/resources/dialogControl/images/close.png" title="Close" alt="Close" onclick="menuClose();"></div>
            <div class="mpophdr -md_obg"></div>
            <div class="mpopbdr -md_obg">
                <div class="mpopbody -tbs_tbc -tbs_tbgc">
                </div>
            </div>
        </div>
    </div>
    <div id="Actions_child" class="menuskin_UI08" style="top: 25px; left: 32px; display: none;"><div id="foACTIONS_MENU" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif">Cancel</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif">Recommend Time Slots</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif">Save</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif">Validate</a></li></ul></div></div></div>
    <div id="Tools_child" class="menuskin_UI08" style="top: 25px; left: 103.633px; display: none;"><div id="foTools" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="printOrd_toolsItem" onclick="printPage()" title="Print"><img src="/lps/resources/themes/icons/mablue/foPrint.gif">Print</a></li></ul></div></div></div>
    <div id="Roles_child" class="menuskin_UI08" style="top: -1px; left: 0px; display: none;"></div>
    <div id="MA_child" class="menuskin_UI08">
            <div class="mpop -md_obg" id="MA_child_cont" style="position:static">
            <div class="mpopsdw -pdlg_sdw"></div>

        <div id="favappmenuappcontid" class="favappmenuappcont">

        </div>
        <div class="mpopclose"><img src="/lps/resources/themes/icons/mablue/pinfavapp.png" onclick="UI8Layout.setSuspendClose(true); menuClose();UI8Layout.Menu.AppMgr.SideNavHandler.start();"></div>

            </div>
    </div>



    <script type="text/javascript">
           //<![CDATA[
               (function () {
                   var Mb = UI8Layout.MenuBar;

                   Mb.addItem('Actions',
                      {popupId: 'Actions_child', postOpen: 'AXNPostOpen', preOpen: null});

                   Mb.addItem('Tools',
                      {popupId: 'Tools_child', postOpen: 'ToolsPostOpen', preOpen: null});

                   Mb.addItem('Roles',
                           {popupId: 'Roles_child', postOpen: 'RolesPostOpen', preOpen: null});


                           })();

           //]]>
    </script>
		         </div></div><input id="windowId" name="windowId" type="hidden" value="wt-2458-b0ae-7757" tabindex="39">
            </td>
        </tr>
        <tr>
            <td valign="top">
            <table width="100%" border="0" cellpadding="0" cellspacing="0">
                <tbody><tr id="crowid" style="height: 525px;">
                    <td id="navaredtd" width="1px" valign="top" style="display: none;">
                    <div id="navarea" class="navarea" style="display: none;">

                    </div>
                    </td>
                    <td id="workareatd" valign="top" class="workareatd -gbl_bbg">
		<table width="100%" cellpadding="0" cellspacing="0">
			<tbody><tr>
				<td id="workareahdrtd" class="workareahdrtd">
					<div id="workareaheader" class="workareaheader" style="width: 990px;">

						<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy1_clear" onclick="return false;" tabindex="40">
			<script>
				"success"
				"success"
			</script><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:apptDtlPanel" border="true"><script type="text/javascript" language="JavaScript" src="/lps/resources/panel/scripts/panel.js">
</script><div class="pnltopdiv" id="PANEL_lSizePanel_top"><div id="tr_lSizePanel" class="pnlcondivhdr"><input id="dataForm:addFlag" type="hidden" name="dataForm:addFlag" value="true"><input id="dataForm:flag" type="hidden" name="dataForm:flag" value="true"><input id="dataForm:primaryKey" type="hidden" name="dataForm:primaryKey" value="22254152"><input id="dataForm:apptIdHid" type="hidden" name="dataForm:apptIdHid" value="22254152"><input id="dataForm:tcApptIdHid" type="hidden" name="dataForm:tcApptIdHid" value="*********"><input id="dataForm:editClicked" type="hidden" name="dataForm:editClicked" value="false"><input id="dataForm:shipperCompany" type="hidden" name="dataForm:shipperCompany" value="100"><input id="dataForm:isCarrier" type="hidden" name="dataForm:isCarrier" value="true"><input id="dataForm:isShipper" type="hidden" name="dataForm:isShipper" value="false"><input id="dataForm:templateFlag" type="hidden" name="dataForm:templateFlag" value="N"><input id="dataForm:templateId" type="hidden" name="dataForm:templateId" value="0"><input id="dataForm:actCheckDttm" type="hidden" name="dataForm:actCheckDttm" value=""><input id="dataForm:prevApptType" type="hidden" name="dataForm:prevApptType" value=""><input id="dataForm:selectedRowPOId" type="hidden" name="dataForm:selectedRowPOId" value=""><input id="dataForm:userEnteredPOId" type="hidden" name="dataForm:userEnteredPOId" value=""><table id="dataForm:gl2" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Facility:</span><span class="required" id="caseSizeType1_cptnSpn">*</span></td><td><div style="white-space:nowrap"><input id="dataForm:facility_det" type="text" name="dataForm:facility_det">&nbsp;<a id="dataForm:facility_det_id" name="dataForm:facility_det_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps?windowId=wt-2458-b0ae-7757" onclick="return false;"><img id="dataForm:facility_det_lookupImage" src="/lcom/common/image/find.gif" alt="Find Facility" onclick="populateFromDCFacilityLookupPopup('facility_det');" title="Find Facility"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Load configuration:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:loadConfigurationListSel" name="dataForm:loadConfigurationListSel" multiple="multiple" size="3" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id87','parameters':{'dataForm:j_id87':'dataForm:j_id87'} } )"><option value="">None</option><option value="WPLT" selected="selected">WPLT</option></select><input id="dataForm:appointmentLC_det4" type="hidden" name="dataForm:appointmentLC_det4"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Suggested start date/time:</span><span class="required" id="caseSizeType2_cptnSpn">*</span></td><td><div style="white-space:nowrap"><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/scripts/calendar.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/scripts/calendar-setup.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/calendar/lang/calendar-en.js">
</script><input type="hidden" id="UsrZnOfset" value="-14400000"><input type="text" id="dataForm:startTime_det" name="dataForm:startTime_det" value="" alt="" size="">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:startTime_det" name="dataForm:startTime_det_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="" onclick="dataFormwstartTime_detcalendarSetUp(this.name);return false"> <script type="text/javascript">//<![CDATA[
function dataFormwstartTime_detcalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
        {
                ifFormat    : "%m/%d/%y %H:%M",
                imgName      : name,            showsTime   :  true,            timer  :  false,                splitDate   :  false,           showsTimeZone  :  "",           edDropDown  :  "false",                 dropDownIns  :  ''
        }
)
}
//]]>
</script></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated trailer duration (min):</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><script type="text/javascript" language="JavaScript" src="/lps/resources/inputmask/scripts/mask.js">
</script><input id="dataForm:estTrailerDuration_det0" name="dataForm:estTrailerDuration_det0" type="text" mask="NNNNNNNNN" decseparator="." onkeypress="return checkKey(this,event); " onchange="" onpaste="return checkCopyValue(this); " value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Appointment type:</span><span class="required" id="at0_cptnSpn">*</span></td><td><div style="white-space:nowrap"><select id="dataForm:cd10" name="dataForm:cd10" size="1"><option value="">Select one</option><option value="30">Drop Empty</option><option value="20">Drop Unload</option><option value="40">Live Load</option><option value="10">Live Unload</option><option value="60">Pickup Empty</option><option value="50">Pickup Load</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated tractor duration (min):</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:estTractorDuration_det" name="dataForm:estTractorDuration_det" type="text" mask="NNNNNNNNN" decseparator="." onkeypress="return checkKey(this,event); " onchange="" onpaste="return checkCopyValue(this); " value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Appointment ID:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:appointmentId_det2" type="hidden" name="dataForm:appointmentId_det2" value="*********"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Estimated departure date/time:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:estDepartureDateTime_det" type="text" name="dataForm:estDepartureDateTime_det" value="7/7/25 00:06" disabled="disabled"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Equipment code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><script type="text/javascript" language="JavaScript" src="/lps/resources/editControl/scripts/idLookup.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/editControl/scripts/autocompleteinput.js">
</script><script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:equipment_detecId" name="dataForm:equipment_detecId" value=""><input type="hidden" id="dataForm_equipment_det_enterKey" value="false"><input type="hidden" id="triggerdataForm_equipment_det_enterKey" value="false"><input type="text" id="dataForm:equipment_det" name="dataForm:equipment_det" onfocus="javascript: focusOnTextBox('dataForm_equipment_det_enterKey')" onblur="javascript: blurOnTextBox('dataForm_equipment_det_enterKey')" onkeypress="if(enterPressed(event,'dataForm:equipment_det') )return false;" value="" title="" alt="Find Equipment code">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-2458-b0ae-7757&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BymslookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BymslookupBackingBean.getBUMap%7D&amp;lookupType=EquipmentType&amp;is3plEnabled=true&amp;returnId=dataForm:equipment_det&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:equipment_det" title="Find Equipment code" align="absmiddle" id="trigger_dataForm:equipment_det" name="trigger_dataForm:equipment_det" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_equipment_det_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_equipment_det_enterKey')"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment requested date/time:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:requestedDateTime_det" type="text" name="dataForm:requestedDateTime_det" value="" disabled="disabled"><input id="dataForm:requestedDateTime_dethid" type="hidden" name="dataForm:requestedDateTime_dethid" value=""></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">//<![CDATA[
_u8_tb_1("dataForm:gl2","_u8_gl_1",{layout:"v"});
//]]>
</script></div></div></span>
					</div>
				</td>
			</tr>
			<tr>
				<td id="workareanavtd" class="workareanavtd">
					<div id="workareanavigation" class="workareanav" style="display: none;">
							<script>
								//<![CDATA[
								UI8Layout.hidenavigation = true;
								eByID(UI8Layout.NAVIGATION_DIV_ID).style.display = 'none';
								//]]>
							</script>
					</div>
				</td>
			</tr>
			<tr>
				<td id="workareacontenttd" class="workareacontenttd" style="height: 289px;">
					<div id="workarea" class="workareamain" style="height: 273px; width: 990px;">

						<input type="image" src="/lps/resources/menu/images/clear.gif" id="dummy2_clear" onclick="return false;" tabindex="74"><div id="dataForm:NewTrailerWindow" name="dataForm:NewTrailerWindow" class="dialog_cont" style="height:610px;width:780px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:NewTrailerWindow_temp_dHId"><div class="pop_title -pdlg_dttc">New Trailer</div></td><td><div class="pop_close" id="dataForm:NewTrailerWindow_temp_cCId"><input type="button" tabindex="75"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:610px;width:780px;">
				<div class="popcon"><span id="dataForm:NewTrailerWindow_Alra4j">

	<script>
	/*
		var newTrailerClicked = document.getElementById("dataForm:trailerButtonClicked");
		if (  !(  newTrailerClicked == null || newTrailerClicked == 'undefined')  ) {
			if (  !(newTrailerClicked.value == 'no'  ) ){
				var url =newTrailerClicked.value;
				newTrailerClicked.value = "no";
				//document.getElementById("newTrailerFrame").src =url;
				}
			}
			*/
	</script>

	<iframe id="newTrailerFrame" height="550" width="750" frameborder="0"></iframe>
</span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("NewTrailerWindow",{onClose:"",dialogClientId:"dataForm:NewTrailerWindow",dragHandleId:"dataForm:NewTrailerWindow_temp_dHId",onDialog:"",closeClientId:"dataForm:NewTrailerWindow_temp_cCId"});
</script><span id="dataForm:matchingPOsPanel"><div id="dataForm:MatchingPOs_Dialog" name="dataForm:MatchingPOs_Dialog" class="dialog_cont" style="height:350px;width:650px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/ribbon/images/about.gif"></div></td><td class="pop_dragHandler" id="dataForm:MatchingPOs_DialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Select PO for Appointment</div></td><td><div class="pop_close" id="dataForm:MatchingPOs_DialogTemplate_cCId"><input type="button" tabindex="76"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:350px;width:650px;">
									<div class="popcon"><span id="dataForm:MatchingPOs_Dialog_DetailAJAXPanel_1"><span id="dataForm:matchingPOPanel" border="false"><script language="JavaScript" src="/lps/resources/table/scripts/sortable.js"></script><script language="JavaScript" src="/lps/resources/table/scripts/datatable.js"></script><script language="JavaScript" src="/lps/resources/table/scripts/tableCommon.js"></script><input type="hidden" name="dataForm:matchingPOTable_deleteHidden" value="" id="dataForm:matchingPOTable_deleteHidden" tabindex="77"><input type="hidden" name="dataForm:matchingPOTable_selectedRows" value="DUMMYROW#:#" id="dataForm:matchingPOTable_selectedRows" tabindex="78"><div class="datatbl_contr" id="dataForm:matchingPOTable_container" style=""><div id="dataForm:matchingPOTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:matchingPOTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:matchingPOTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:matchingPOTable:isSortButtonClick" id="dataForm:matchingPOTable:isSortButtonClick" value="" tabindex="79"><input type="hidden" name="dataForm:matchingPOTable:sortDir" id="dataForm:matchingPOTable:sortDir" value="desc" tabindex="80"><input type="hidden" name="dataForm:matchingPOTable:colCount" id="dataForm:matchingPOTable:colCount" value="" tabindex="81"><input type="hidden" name="dataForm:matchingPOTable:tableClicked" id="dataForm:matchingPOTable:tableClicked" value="" tabindex="82"><input type="hidden" name="dataForm:matchingPOTable:tableResized" id="dataForm:matchingPOTable:tableResized" value="false" tabindex="83"><div class="advtbl_contr_head" id="dataForm:matchingPOTable_headDiv"><table id="dataForm:matchingPOTable" cellspacing="0">
<colgroup>
<col>
<col><col><col class="tbl_colHidden"></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox">&nbsp;</td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:matchingPOTable:col1_outputText" class="titleCase">Purchase Order</span></td>
<td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:matchingPOTable:col2_outputText" class="titleCase">Vendor Name</span></td>
<td class="tbl_colHidden advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header " align="left"><span style="display:none">&nbsp;</span></td>
</tr></thead></table></div><div id="dataForm:matchingPOTable_bodyDiv" class="advtbl_contr_body"><table id="dataForm:matchingPOTable_body" cellspacing="0"><colgroup>
<col>
<col><col><col class="tbl_colHidden"></colgroup><tbody>
<tr id="dummyRowIpsTableId" class="advtbl_row -dg_tsr" style=""><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="radio" name="checkAll_c_dataForm:matchingPOTable" id="checkAll_c_dataForm:matchingPOTable" style="margin:2px;" value="0" tabindex="84"><input type="hidden" value="DUMMYROW" id="dataForm:matchingPOTable:0:PK_0" name="dataForm:matchingPOTable:0:PK_0" tabindex="85"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col" align="left"><span id="dataForm:matchingPOTable:0:tcPurchaseOrderId"></span><input id="dataForm:matchingPOTable:0:matchingPOId" type="hidden" name="dataForm:matchingPOTable:0:matchingPOId" tabindex="86"> </td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col" align="left"><span id="dataForm:matchingPOTable:0:vendorName"></span> </td><td style="white-space: nowrap;" class="tbl_colHidden" align="left"><a href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="dataForm:matchingPOTable:0:defaultactionbutton" name="dataForm:matchingPOTable:0:defaultactionbutton" onclick="setSelectedPO();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:matchingPOTable:0:defaultactionbutton','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('MatchingPOs_Dialog');},'parameters':{'dataForm:matchingPOTable:0:defaultactionbutton':'dataForm:matchingPOTable:0:defaultactionbutton'} } );return false;">DefaultAction</a> </td></tr><tr id="dataForm:matchingPOTable:nodataRow" class="advtbl_row trshow" style="visibility: hidden; display: none;"><td class="advtbl_col advtbl_body_col tdshow" colspan="4" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="matchingPOTable_hdnMaxIndexHldr" name="matchingPOTable_hdnMaxIndexHldr" value="0" tabindex="87"></table></div><div class="emptyHoriScrollDiv"></div><div id="sortButton" style="display:none;"><input id="dataForm:matchingPOTable:sortButton" name="dataForm:matchingPOTable:sortButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:matchingPOTable:sortButton','parameters':{'dataForm:matchingPOTable:sortButton':'dataForm:matchingPOTable:sortButton'} } );return false;" value="|" type="button" tabindex="88"></div></div>
<input type="hidden" id="dataForm:matchingPOTable_trs_pageallrowskey" name="dataForm:matchingPOTable_trs_pageallrowskey" value="" tabindex="89"><input type="hidden" id="dataForm:matchingPOTable_selectedRows" name="dataForm:matchingPOTable_selectedRows" value="" tabindex="90"><input type="hidden" id="dataForm:matchingPOTable_selectedIdList" name="dataForm:matchingPOTable_selectedIdList" value="" tabindex="91"><input type="hidden" id="dataForm:matchingPOTable_trs_allselectedrowskey" name="dataForm:matchingPOTable_trs_allselectedrowskey" value="matchingPOTable$:$1751861103068" tabindex="92"><script type="text/javascript">var  dataFormmatchingPOTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='matchingPOTable';
tableObjectArray[count]=dataFormmatchingPOTable_tableObj;dataFormmatchingPOTable_tableObj.bind(document.getElementById('dataForm:matchingPOTable_container'), document.getElementById('dataForm:matchingPOTable_headDiv'), document.getElementById('dataForm:matchingPOTable_bodyDiv'), document.getElementById('dataForm:matchingPOTable_scrollDiv'),document.getElementById('dataForm:matchingPOTable_scrollDivBody'),document.getElementById('dataForm:matchingPOTable_button'),true,1,1,'dataForm:matchingPOTable','edit','yes','no','0','bottom','view',1,2147483647,'yes','no','even','odd','Invalid Table','dataForm:matchingPOTable_selectedIdList','false','matchingPOTable','true' ,0,0,'false','null','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
var dataForm_matchingPOTable_col1="true";var dataForm_matchingPOTable_col2="true";var dataForm_matchingPOTable_defaultactioncolid="true";</script>
<script type="text/javascript">
UI8Layout.ondialogTableLoad("matchingPOTable");
</script><input class="btn" id="dataForm:selectBtnPO" name="dataForm:selectBtnPO" onclick="setSelectedPO();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:selectBtnPO','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('MatchingPOs_Dialog');},'parameters':{'dataForm:selectBtnPO':'dataForm:selectBtnPO'} } );return false;" value="Select" alt="Select" style="cursor:pointer" type="button" tabindex="93"><span class="groupBtnSpace">&nbsp;</span>
       	<script type="text/javascript">
			document.getElementById("checkAll_c_dataForm:matchingPOTable").click();
			var table = document.getElementById("dataForm:matchingPOTable_body");
			var length = table.rows.length;

			for(var i=0; i < length ; i++)
			{
				if(table.rows[i].id != "dataForm:matchingPOTable:nodataRow")
				{
					table.rows[i].removeAttribute("style");
				}
				else
				{
					table.rows[i].style.visibility = "hidden";
					table.rows[i].style.display = "none";

				}
			}

		</script></span>

</span>
									</div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("MatchingPOs_Dialog",{onClose:"",dialogClientId:"dataForm:MatchingPOs_Dialog",dragHandleId:"dataForm:MatchingPOs_DialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:MatchingPOs_DialogTemplate_cCId"});
</script></span><div id="dataForm:Recommendations_Dialog" name="dataForm:Recommendations_Dialog" class="dialog_cont" style="height:350px;width:650px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/ribbon/images/about.gif"></div></td><td class="pop_dragHandler" id="dataForm:Recommendations_DialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Recommendations</div></td><td><div class="pop_close" id="dataForm:Recommendations_DialogTemplate_cCId"><input type="button" tabindex="94"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:350px;width:650px;">
				                <div class="popcon"><span id="dataForm:Recommendations_Dialog_DetailAJAXPanel_1"><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:recolistpanel" border="false"><script type="text/javascript">//<![CDATA[
_u8_ely_1({isAjax:true,list:[{msg:"Cannot create an appointment without shipments or POs. Please enter at least one shipment or PO.",type:1,hideOverrideText:false},{msg:"Facility is a required field.",type:1,hideOverrideText:false},{msg:"Facility is a required field",type:1,hideOverrideText:false},{msg:"Appointment Start Date&#x2f;Time is a required field.",type:1,hideOverrideText:false},{msg:"Appointment type is a required field&#x2f; Please provide an appointment type",type:1,hideOverrideText:false}],ortext:"Override",name:"error_OverLay1"});
//]]>
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/sortable.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/datatable.js">
</script><script type="text/javascript" language="JavaScript" src="/lps/resources/table/scripts/tableCommon.js">
</script><input type="hidden" name="dataForm:recommendationTable_deleteHidden" value="" id="dataForm:recommendationTable_deleteHidden"><input type="hidden" name="dataForm:recommendationTable_selectedRows" value="DUMMYROW#:#" id="dataForm:recommendationTable_selectedRows"><div class="datatbl_contr" id="dataForm:recommendationTable_container"><div id="dataForm:recommendationTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:recommendationTable_scrollDivBody"></div></div><div id="dataForm:recommendationTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:recommendationTable:isSortButtonClick" id="dataForm:recommendationTable:isSortButtonClick" value=""><input type="hidden" name="dataForm:recommendationTable:sortDir" id="dataForm:recommendationTable:sortDir" value="desc"><input type="hidden" name="dataForm:recommendationTable:colCount" id="dataForm:recommendationTable:colCount" value=""><input type="hidden" name="dataForm:recommendationTable:tableClicked" id="dataForm:recommendationTable:tableClicked" value=""><input type="hidden" name="dataForm:recommendationTable:tableResized" id="dataForm:recommendationTable:tableResized" value="false"><div class="advtbl_contr_head" id="dataForm:recommendationTable_headDiv"><table id="dataForm:recommendationTable" cellspacing="0"><colgroup><col><col><col class="tbl_colHidden"></colgroup><thead><tr class="advtbl_hdr_row advtbl_row"><td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox">&nbsp;</td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:recommendationTable:rc3" class="titleCase">Slots</span></td><td class="tbl_colHidden advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header " align="left"><span style="display:none">&nbsp;</span></td></tr></thead></table></div><div id="dataForm:recommendationTable_bodyDiv" class="advtbl_contr_body"><input type="hidden" id="recommendationTable_hdnMaxIndexHldr" name="recommendationTable_hdnMaxIndexHldr" value="0"><table id="dataForm:recommendationTable_body" cellspacing="0"><colgroup><col><col><col class="tbl_colHidden"></colgroup><tbody><tr id="dummyRowIpsTableId" class="advtbl_row -dg_tsr" style="visibility: hidden; display: none;"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="radio" name="checkAll_c_dataForm:recommendationTable" id="checkAll_c_dataForm:recommendationTable" style="margin:2px;" value="0"><input type="hidden" value="DUMMYROW" id="dataForm:recommendationTable:0:PK_0" name="dataForm:recommendationTable:0:PK_0"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col" align="left"><span id="dataForm:recommendationTable:0:recommendationSlotDesc"></span><input id="dataForm:recommendationTable:0:recommendationId" type="hidden" name="dataForm:recommendationTable:0:recommendationId"></td><td style="white-space: nowrap;" class="tbl_colHidden advtbl_col advtbl_body_col" align="left"><a href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="dataForm:recommendationTable:0:defaultactionbutton" name="dataForm:recommendationTable:0:defaultactionbutton" onclick="setSelectedRecommendation();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:recommendationTable:0:defaultactionbutton','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('Recommendations_Dialog');},'parameters':{'dataForm:recommendationTable:0:defaultactionbutton':'dataForm:recommendationTable:0:defaultactionbutton','dataForm:selectedUirecommendation':''} } );return false;">DefaultAction</a></td></tr><tr id="dataForm:recommendationTable:nodataRow" class="advtbl_row trshow" style="visibility: inherit; display: table-row;"><td class="advtbl_col advtbl_body_col tdshow" colspan="3" align="left">No data found</td></tr></tbody></table></div><div class="emptyHoriScrollDiv"></div></div> <input type="hidden" id="dataForm:recommendationTable_trs_pageallrowskey" name="dataForm:recommendationTable_trs_pageallrowskey" value=""><input type="hidden" id="dataForm:recommendationTable_selectedRows" name="dataForm:recommendationTable_selectedRows" value=""><input type="hidden" id="dataForm:recommendationTable_selectedIdList" name="dataForm:recommendationTable_selectedIdList" value=""><input type="hidden" id="dataForm:recommendationTable_trs_allselectedrowskey" name="dataForm:recommendationTable_trs_allselectedrowskey" value="recommendationTable$:$1751860974459"><script type="text/javascript">//<![CDATA[
var  dataFormrecommendationTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='recommendationTable';
tableObjectArray[count]=dataFormrecommendationTable_tableObj;dataFormrecommendationTable_tableObj.bind(document.getElementById('dataForm:recommendationTable_container'), document.getElementById('dataForm:recommendationTable_headDiv'), document.getElementById('dataForm:recommendationTable_bodyDiv'), document.getElementById('dataForm:recommendationTable_scrollDiv'),document.getElementById('dataForm:recommendationTable_scrollDivBody'),document.getElementById('dataForm:recommendationTable_button'),true,1,1,'dataForm:recommendationTable','edit','yes','no','0','bottom','view',1,2147483647,'yes','no','even','odd','Invalid Table','dataForm:recommendationTable_selectedIdList','false','recommendationTable','true' ,0,0,'false','null','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',1);
var dataForm_recommendationTable_r3="true";var dataForm_recommendationTable_defaultactioncolid="true";
//]]>
</script> <script type="text/javascript">//<![CDATA[
UI8Layout.ondialogTableLoad("recommendationTable");
//]]>
</script><input class="btn" id="dataForm:selectBtn" name="dataForm:selectBtn" onclick="setSelectedRecommendation();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:selectBtn','oncomplete':function(request,event,data){closeRecomDialog();},'parameters':{'dataForm:selectBtn':'dataForm:selectBtn'} } );return false;" value="Select" alt="Select" style="cursor:pointer" type="button"><input class="btn" id="dataForm:recocancelBtn" name="dataForm:recocancelBtn" onclick="UI8Layout.doDialogCloseById('Recommendations_Dialog');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:recocancelBtn','parameters':{'dataForm:recocancelBtn':'dataForm:recocancelBtn'} } );return false;" value="Cancel" alt="Cancel" style="cursor:pointer" type="button"><span class="groupBtnSpace">&nbsp;</span> <script type="text/javascript">//<![CDATA[
                //// selectRowByIndex('recommendationTable','0');
                document.getElementById("checkAll_c_dataForm:recommendationTable").click();
        //]]>
</script></span>

</span>
				                </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("Recommendations_Dialog",{onClose:"",dialogClientId:"dataForm:Recommendations_Dialog",dragHandleId:"dataForm:Recommendations_DialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:Recommendations_DialogTemplate_cCId"});
</script><div id="dataForm:Appt_FAC_Dialog" name="dataForm:Appt_FAC_Dialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lcom/common/image/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:Appt_FAC_basicDialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Find Facility</div></td><td><div class="pop_close" id="dataForm:Appt_FAC_basicDialogTemplate_cCId"><input type="button" tabindex="113"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg">
				                <div class="popcon"><span id="dataForm:Appt_FAC_popup_DetailAJAXPanel_1">
				                            <iframe id="APPT_Facility_iframeId" frameborder="0" scrolling="no" width="500px" height="280px">
											</iframe></span>
				                </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("Appt_FAC_Dialog",{onClose:"",dialogClientId:"dataForm:Appt_FAC_Dialog",dragHandleId:"dataForm:Appt_FAC_basicDialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:Appt_FAC_basicDialogTemplate_cCId"});
</script><div id="dataForm:SoftCheckErrorOverride_Dialog" name="dataForm:SoftCheckErrorOverride_Dialog" class="dialog_cont" style="height:420px;width:750px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/menu/ribbon/images/about.gif"></div></td><td class="pop_dragHandler" id="dataForm:SoftCheckErrorOverride_DialogTemplate_dHId"><div class="pop_title -pdlg_dttc">Warnings</div></td><td><div class="pop_close" id="dataForm:SoftCheckErrorOverride_DialogTemplate_cCId"><input type="button" tabindex="114"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:420px;width:750px;">
				                <div class="popcon"><span id="dataForm:SoftCheckErrorOverride_Dialog_DetailAJAXPanel_1"><span id="dataForm:softcheckerrorlistpanel" border="false"><input type="hidden" name="dataForm:softcheckerrorsTable_deleteHidden" value="" id="dataForm:softcheckerrorsTable_deleteHidden" tabindex="115"><input type="hidden" name="dataForm:softcheckerrorsTable_selectedRows" value="#:#" id="dataForm:softcheckerrorsTable_selectedRows" tabindex="116"><div class="advtbl_contr" id="dataForm:softcheckerrorsTable_container" style=""><div id="dataForm:softcheckerrorsTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:softcheckerrorsTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:softcheckerrorsTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:softcheckerrorsTable:isSortButtonClick" id="dataForm:softcheckerrorsTable:isSortButtonClick" value="" tabindex="117"><input type="hidden" name="dataForm:softcheckerrorsTable:sortDir" id="dataForm:softcheckerrorsTable:sortDir" value="desc" tabindex="118"><input type="hidden" name="dataForm:softcheckerrorsTable:colCount" id="dataForm:softcheckerrorsTable:colCount" value="" tabindex="119"><input type="hidden" name="dataForm:softcheckerrorsTable:tableClicked" id="dataForm:softcheckerrorsTable:tableClicked" value="" tabindex="120"><input type="hidden" name="dataForm:softcheckerrorsTable:tableResized" id="dataForm:softcheckerrorsTable:tableResized" value="false" tabindex="121">
<div class="advtbl_contr_head" style="position:relative;" id="dataForm:softcheckerrorsTable_headDiv"><table style="table-layout:auto;" id="dataForm:softcheckerrorsTable" cellspacing="0"><colgroup>
<col>
<col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top"><input type="checkbox" name="dataForm:softcheckerrorsTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'softcheckerrorsTable');" disabled="" tabindex="122"></td><td align="right" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:softcheckerrorsTable:rc1" class="titleCase">Warning</span></td>
<td align="right" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:softcheckerrorsTable:rc2" class="titleCase">Override Code</span></td>
</tr></thead></table></div><div id="dataForm:softcheckerrorsTable_bodyDiv" class="advtbl_contr_body"><table style="table-layout:auto;" id="dataForm:softcheckerrorsTable_body" cellspacing="0"><colgroup>
<col>
<col><col></colgroup><tbody>
<tr style="visibility:hidden;display:none" id="dummyRowIpsTableId" class="advtbl_row"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:softcheckerrorsTable" id="checkAll_c0_dataForm:softcheckerrorsTable" value="0" tabindex="123"><input type="hidden" value="DUMMYROW" id="dataForm:softcheckerrorsTable:0:PK_0" name="dataForm:softcheckerrorsTable:0:PK_0" tabindex="124"></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="right"><div id="dataForm:softcheckerrorsTable_body_tr0_td0_view" class="dshow">&nbsp;<span id="dataForm:softcheckerrorsTable:0:softcheckerror"></span></div><div id="dataForm:softcheckerrorsTable_body_tr0_td0_edit" class="dhide">&nbsp;<span id="dataForm:softcheckerrorsTable:0:softcheckerrorEdit"></span><input type="hidden" name="dataForm:softcheckerrorsTable:0:markForDelete" id="dataForm:softcheckerrorsTable:0:markForDelete" value="" tabindex="125"></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="right"><div id="dataForm:softcheckerrorsTable_body_tr0_td1_view" class="dshow">&nbsp;<select id="dataForm:softcheckerrorsTable:0:reasoncode" name="dataForm:softcheckerrorsTable:0:reasoncode" size="1" tabindex="126">	<option value="">Select one</option>
	<option value="937">Carrier Delay</option>
	<option value="316">Receiving Capacity</option>
	<option value="935">Vendor Delay</option>
</select></div><span style="display:none">&nbsp;</span></td></tr><tr id="dataForm:softcheckerrorsTable:nodataRow" class="advtbl_row trshow" style="visibility: inherit; display: table-row;"><td class="advtbl_col advtbl_body_col tdshow" colspan="3" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="softcheckerrorsTable_hdnMaxIndexHldr" name="softcheckerrorsTable_hdnMaxIndexHldr" value="0" tabindex="127"></table></div><div class="emptyHoriScrollDiv"></div><div id="sortButton" style="display:none;"><input id="dataForm:softcheckerrorsTable:sortButton" name="dataForm:softcheckerrorsTable:sortButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:softcheckerrorsTable:sortButton','parameters':{'dataForm:softcheckerrorsTable:sortButton':'dataForm:softcheckerrorsTable:sortButton'} } );return false;" value="|" type="button" tabindex="128"></div></div>
<input type="hidden" id="dataForm:softcheckerrorsTable_trs_pageallrowskey" name="dataForm:softcheckerrorsTable_trs_pageallrowskey" value="" tabindex="129"><input type="hidden" id="dataForm:softcheckerrorsTable_selectedRows" name="dataForm:softcheckerrorsTable_selectedRows" value="" tabindex="130"><input type="hidden" id="dataForm:softcheckerrorsTable_selectedIdList" name="dataForm:softcheckerrorsTable_selectedIdList" value="" tabindex="131"><input type="hidden" id="dataForm:softcheckerrorsTable_trs_allselectedrowskey" name="dataForm:softcheckerrorsTable_trs_allselectedrowskey" value="softcheckerrorsTable$:$1751860974459" tabindex="132"><script type="text/javascript">var  dataFormsoftcheckerrorsTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='softcheckerrorsTable';
tableObjectArray[count]=dataFormsoftcheckerrorsTable_tableObj;dataFormsoftcheckerrorsTable_tableObj.bind(document.getElementById('dataForm:softcheckerrorsTable_container'),document.getElementById('dataForm:softcheckerrorsTable_headDiv'), document.getElementById('dataForm:softcheckerrorsTable_bodyDiv'), document.getElementById('dataForm:softcheckerrorsTable_scrollDiv'),document.getElementById('dataForm:softcheckerrorsTable_scrollDivBody'), document.getElementById('dataForm:softcheckerrorsTable_button'),true,1,2,'dataForm:softcheckerrorsTable','edit','yes','no','10','bottom','view',1,2147483647,'yes','no','even','odd','Invalid Table','dataForm:softcheckerrorsTable_selectedIdList','true','softcheckerrorsTable','false' ,0,8,'false','null','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
</script><script>var msgToActivateTotal;msgToActivateTotal = "To activate this feature, click the Total button";</script>
<script type="text/javascript">
UI8Layout.ondialogTableLoad("softcheckerrorsTable");
</script><input id="dataForm:saveSoftErrorBtn" type="submit" name="dataForm:saveSoftErrorBtn" value="Save" alt="Save" onmousedown="overrideApptSCE();" class="btn" tabindex="133"><input class="btn" id="dataForm:softErrorcancelBtn" name="dataForm:softErrorcancelBtn" onclick="UI8Layout.doDialogCloseById('SoftCheckErrorOverride_Dialog');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:softErrorcancelBtn','parameters':{'dataForm:softErrorcancelBtn':'dataForm:softErrorcancelBtn'} } );return false;" value="Cancel" alt="Cancel" type="button" tabindex="134"><span class="groupBtnSpace">&nbsp;</span></span>
</span>
				                </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("SoftCheckErrorOverride_Dialog",{onClose:"",dialogClientId:"dataForm:SoftCheckErrorOverride_Dialog",dragHandleId:"dataForm:SoftCheckErrorOverride_DialogTemplate_dHId",onDialog:"",closeClientId:"dataForm:SoftCheckErrorOverride_DialogTemplate_cCId"});
</script><div id="dataForm:ApptDockLookUpDockIdDialog" name="dataForm:ApptDockLookUpDockIdDialog" class="dialog_cont" style="height:200px;width:250px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:dcklkpdocId_id_dHId"><div class="pop_title -pdlg_dttc">Dock</div></td><td><div class="pop_close" id="dataForm:dcklkpdocId_id_cCId"><input type="button" tabindex="135"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;">
                    <div class="popcon"><span id="dataForm:dcklkpdocId_Alra4j">

		<input type="hidden" name="apptDockLookUpTextName" id="apptDockLookUpTextName" tabindex="136"><div class="pnltopdiv" id="PANEL_apptDckLkp__Search_Panel1_top"><table id="PANEL_apptDckLkp__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_apptDckLkp__Search_Panel1" class="pnlcondiv"><table id="dataForm:apptDckLkp22_pg_1">
<tbody>
<tr>
<td><script language="JavaScript" src="/lps/resources/caption/scripts/caption.js"></script><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="apptDckLkpInSrch"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:dockSearchText" type="text" name="dataForm:dockSearchText" value="*" tabindex="137"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:capptDckLkp_locgetlist22" name="dataForm:capptDckLkp_locgetlist22" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:capptDckLkp_locgetlist22','parameters':{'dataForm:capptDckLkp_locgetlist22':'dataForm:capptDckLkp_locgetlist22'} } );return false;" value="Find " type="button" tabindex="138"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:apptDckLkpFilter_Detail"><div class="pnltopdiv" id="PANEL_cpptDckLkp22_Find_Result_pannel_top"><div id="tr_cpptDckLkp22_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:pptDckLkpnlotFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="apptDckLkp_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:apptDockLookUpText" name="dataForm:apptDockLookUpText" size="5" ondblclick="selectApptDockLookUp();return false;" style="width:50%" tabindex="139">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:apptDckLkp22Filter_SelectButton" name="dataForm:apptDckLkp22Filter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptDckLkp22Filter_SelectButton','oncomplete':function(request,event,data){selectApptDockLookUp();return false;},'parameters':{'dataForm:apptDckLkp22Filter_SelectButton':'dataForm:apptDckLkp22Filter_SelectButton'} } );return false;" value="Select" type="button" tabindex="140"></div></td>
</tr>
</tbody>
</table>
</div></div></span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptDockLookUpDockIdDialog",{onClose:"",dialogClientId:"dataForm:ApptDockLookUpDockIdDialog",dragHandleId:"dataForm:dcklkpdocId_id_dHId",onDialog:"",closeClientId:"dataForm:dcklkpdocId_id_cCId"});
</script><div id="dataForm:ApptDockDoorLookUpDialog" name="dataForm:ApptDockDoorLookUpDialog" class="dialog_cont" style="height:200px;width:250px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:dckDoorlkp_id_dHId"><div class="pop_title -pdlg_dttc">Dock Door</div></td><td><div class="pop_close" id="dataForm:dckDoorlkp_id_cCId"><input type="button" tabindex="141"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;">
				<div class="popcon"><span id="dataForm:dckDoorlkp_Ch_Alra4j"><script language="JavaScript" src="/appointment/ui/jsf/ApptDockAndDoorLookup.js"></script><span id="dataForm:dockDoorFilter_script"></span>

		<input type="hidden" name="dockDoorTextName" id="dockDoorTextName" tabindex="142">
		<input type="hidden" name="dockDoorFacility" id="dockDoorFacility" tabindex="143">
		<input type="hidden" name="dockNameText" id="dockNameText" tabindex="144"><div class="pnltopdiv" id="PANEL_dockDoorFilter__Search_Panel1_top"><table id="PANEL_dockDoorFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_dockDoorFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:dockDoorFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="dockDoorCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:dockDoorSearch" type="text" name="dataForm:dockDoorSearch" value="*" onkeypress="if (event.keyCode == 13){ getApptDockDoorLookUpOnKeyPress(); return false; }" tabindex="145"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:dockDoorFilter_locgetlist" name="dataForm:dockDoorFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:dockDoorFilter_locgetlist','parameters':{'dataForm:dockDoorFilter_locgetlist':'dataForm:dockDoorFilter_locgetlist'} } );return false;" value="Find " type="button" tabindex="146"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:dockDoorFilter_Detail"><div class="pnltopdiv" id="PANEL_dockDoorFilter_Find_Result_pannel_top"><div id="tr_dockDoorFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:dockDoorFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="dockDoorFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:dockDoorText" name="dataForm:dockDoorText" size="5" ondblclick="selectApptDockDoorLookUp();return false;" style="width:50%" tabindex="147">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:dockDoorFilter_SelectButton" name="dataForm:dockDoorFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:dockDoorFilter_SelectButton','oncomplete':function(request,event,data){selectApptDockDoorLookUp();return false;},'parameters':{'dataForm:dockDoorFilter_SelectButton':'dataForm:dockDoorFilter_SelectButton'} } );return false;" value="Select" type="button" tabindex="148"></div></td>
</tr>
</tbody>
</table>
</div></div></span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptDockDoorLookUpDialog",{onClose:"",dialogClientId:"dataForm:ApptDockDoorLookUpDialog",dragHandleId:"dataForm:dckDoorlkp_id_dHId",onDialog:"",closeClientId:"dataForm:dckDoorlkp_id_cCId"});
</script><div id="dataForm:ApptPODialog" name="dataForm:ApptPODialog" class="dialog_cont" style="height:450px;width:450px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:apptPOD_id_dHId"><div class="pop_title -pdlg_dttc">Find Purchase Order</div></td><td><div class="pop_close" id="dataForm:apptPOD_id_cCId"><input type="button" tabindex="149"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:450px;width:450px;">
				<div class="popcon"><span id="dataForm:apptPOD_Alra4j"><script language="JavaScript" src="/appointment/lookup/ui/ApptPOLookUp.js"></script><span id="dataForm:apptPOLkp_script"></span>

	<input type="hidden" name="apptPOTextName" tabindex="150"><span id="dataForm:apptPOLkp_DetailFirst"><div class="pnltopdiv" id="PANEL_apptPOLkp__Search_Panel1_top"><table id="PANEL_apptPOLkp__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_apptPOLkp__Search_Panel1" class="pnlcondiv"><table id="dataForm:gl289" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Billing method:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:billingMeth" name="dataForm:billingMeth" size="1" tabindex="151">	<option value="" selected="selected">Select one</option>
	<option value="1">Collect</option>
	<option value="3">Consignee Bill</option>
	<option value="5">Cost &amp; Freight</option>
	<option value="6">Delivery Duty Paid</option>
	<option value="7">Free Domicile</option>
	<option value="4">Free on Board</option>
	<option value="0">Prepaid</option>
	<option value="2">Third Party</option>
	<option value="8">UPS Third Party Freight Collect</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">PO on Shipment:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:poOnShipment" type="checkbox" name="dataForm:poOnShipment" tabindex="152"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><table id="dataForm:gl289Btn" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Search:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:apptPOSearch" type="text" name="dataForm:apptPOSearch" value="" tabindex="153">&nbsp;&nbsp;<input class="btn" id="dataForm:apptPOLkp_locgetlist" name="dataForm:apptPOLkp_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptPOLkp_locgetlist','parameters':{'dataForm:apptPOLkp_locgetlist':'dataForm:apptPOLkp_locgetlist'} } );return false;" value="Find " type="button" tabindex="154"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl289Btn","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:apptPOLkp_Detail"><div class="pnltopdiv" id="PANEL_apptPOLkp_Find_Result_pannel_top"><div id="tr_apptPOLkp_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:gl27" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:apptPOText" name="dataForm:apptPOText" size="10" ondblclick="selectApptPO();return false;" style="width:400px" tabindex="155">	<option value="A-100009">A-100009</option>
	<option value="A-100006">A-100006</option>
	<option value="A-100028">A-100028</option>
	<option value="A-100005">A-100005</option>
	<option value="A-100027">A-100027</option>
	<option value="A-100008">A-100008</option>
	<option value="A-100007">A-100007</option>
	<option value="A-100029">A-100029</option>
	<option value="A-100045">A-100045</option>
	<option value="A-100004">A-100004</option>
	<option value="A-100026">A-100026</option>
	<option value="A-100003">A-100003</option>
	<option value="A-100020">A-100020</option>
	<option value="A-100042">A-100042</option>
	<option value="A-100064">A-100064</option>
	<option value="A-100041">A-100041</option>
	<option value="A-100000">A-100000</option>
	<option value="A-100022">A-100022</option>
	<option value="A-100021">A-100021</option>
	<option value="A-100065">A-100065</option>
	<option value="A-100060">A-100060</option>
	<option value="A-100040">A-100040</option>
	<option value="A-100062">A-100062</option>
	<option value="A-100039">A-100039</option>
	<option value="A-100038">A-100038</option>
	<option value="A-100013">A-100013</option>
	<option value="A-100035">A-100035</option>
	<option value="A-100012">A-100012</option>
	<option value="A-100034">A-100034</option>
	<option value="A-100056">A-100056</option>
	<option value="A-100015">A-100015</option>
	<option value="A-100037">A-100037</option>
	<option value="A-100014">A-100014</option>
	<option value="A-100036">A-100036</option>
	<option value="A-100031">A-100031</option>
	<option value="A-100053">A-100053</option>
	<option value="A-100030">A-100030</option>
	<option value="A-100011">A-100011</option>
	<option value="A-100055">A-100055</option>
	<option value="A-100010">A-100010</option>
	<option value="A-100054">A-100054</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input class="btn" id="dataForm:apptPOLkp_SelectButton" name="dataForm:apptPOLkp_SelectButton" onclick="selectApptPO();return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptPOLkp_SelectButton','parameters':{'dataForm:apptPOLkp_SelectButton':'dataForm:apptPOLkp_SelectButton'} } );return false;" value="Select" type="button" tabindex="156"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table></div></div></span><span id="dataForm:captionExeed">More than 40 records found</span></span></div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("ApptPODialog",{onClose:"",dialogClientId:"dataForm:ApptPODialog",dragHandleId:"dataForm:apptPOD_id_dHId",onDialog:"",closeClientId:"dataForm:apptPOD_id_cCId"});
</script><div id="dataForm:TrailerDialog" name="dataForm:TrailerDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:trlrBrcdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Trailer</div></td><td><div class="pop_close" id="dataForm:trlrBrcdOuter_cCId"><input type="button" tabindex="157"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:400px;width:350px;"><span id="dataForm:trlrBrcdA4J"><div id="trlrBrcdDivInner"><script language="JavaScript" src="/appointment/lookup/js/trailerLookUp.js"></script><span id="dataForm:trlrFilter_script"></span>

		<input type="hidden" name="trailerTextName" tabindex="158"><div class="pnltopdiv" id="PANEL_trlrFilter__Search_Panel1_top"><table id="PANEL_trlrFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_trlrFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:trlrFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="trailerNameCap"><span title="">Trailer Name:</span><span class="notRequired">p</span><br><input id="dataForm:trailerName" type="text" name="dataForm:trailerName" value="*" tabindex="159"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="trailerBrcdCap"><span title="">Trailer Barcode:</span><span class="notRequired">p</span><br><input id="dataForm:trailerBarcode" type="text" name="dataForm:trailerBarcode" value="*" tabindex="160"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:trlrLkpBtn" name="dataForm:trlrLkpBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trlrLkpBtn','parameters':{'dataForm:trlrLkpBtn':'dataForm:trlrLkpBtn'} } );return false;" value="Find " type="button" tabindex="161"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:trlr_Detail"><div class="pnltopdiv" id="PANEL_trlr_Detail_Find_Result_pannel_top"><div id="tr_trlr_Detail_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:trlrGrid1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="trlr_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:trailerText" name="dataForm:trailerText" size="10" ondblclick="selectTrailer();return false;" style="width:50%" tabindex="162">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:trlr_SelectButton" name="dataForm:trlr_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trlr_SelectButton','oncomplete':function(request,event,data){selectTrailer();return false;},'parameters':{'dataForm:trlr_SelectButton':'dataForm:trlr_SelectButton'} } );return false;" value="Select" type="button" tabindex="163"></div></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("TrailerDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:TrailerDialog",dragHandleId:"dataForm:trlrBrcdOuter_dHId",onDialog:"",closeClientId:"dataForm:trlrBrcdOuter_cCId"});
</script><div id="dataForm:TractorDialog" name="dataForm:TractorDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:tractorOuter_dHId"><div class="pop_title -pdlg_dttc">Find Tractor</div></td><td><div class="pop_close" id="dataForm:tractorOuter_cCId"><input type="button" tabindex="164"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:400px;width:350px;"><span id="dataForm:tractorA4J"><div id="tractorDivInner"><script language="JavaScript" src="/appointment/lookup/js/tractorLookUp.js"></script><span id="dataForm:trlrFilter_script"></span>

		<input type="hidden" name="tractorTextName" tabindex="165"><div class="pnltopdiv" id="PANEL_trctrFilter__Search_Panel1_top"><table id="PANEL_trctrFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_trctrFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:trctrFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="tractorNameCap"><span title="">Tractor Name:</span><span class="notRequired">p</span><br><input id="dataForm:tractorName" type="text" name="dataForm:tractorName" value="*" tabindex="166"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:trctrLkpBtn" name="dataForm:trctrLkpBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trctrLkpBtn','parameters':{'dataForm:trctrLkpBtn':'dataForm:trctrLkpBtn'} } );return false;" value="Find " type="button" tabindex="167"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:trctr_Detail"><div class="pnltopdiv" id="PANEL_trctr_Detail_Find_Result_pannel_top"><div id="tr_trctr_Detail_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:trctrGrid1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="trctr_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:tractorText" name="dataForm:tractorText" size="10" ondblclick="selectTractor();return false;" style="width:50%" tabindex="168">	<option value="No matching Records" disabled="disabled">No Matching Records Found</option>
</select>&nbsp;<input class="btn" id="dataForm:trctr_SelectButton" name="dataForm:trctr_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:trctr_SelectButton','oncomplete':function(request,event,data){selectTractor();return false;},'parameters':{'dataForm:trctr_SelectButton':'dataForm:trctr_SelectButton'} } );return false;" value="Select" type="button" tabindex="169"></div></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("TractorDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:TractorDialog",dragHandleId:"dataForm:tractorOuter_dHId",onDialog:"",closeClientId:"dataForm:tractorOuter_cCId"});
</script><div id="dataForm:NumRowsDialog" name="dataForm:NumRowsDialog" class="dialog_cont" style="height:200px;width:200px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:NumRowsIdOuter_dHId"><div class="pop_title -pdlg_dttc">Add multiple rows</div></td><td><div class="pop_close" id="dataForm:NumRowsIdOuter_cCId"><input type="button" tabindex="170"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:200px;width:200px;"><span id="dataForm:NumRowsA4J"><div id="NumRowsDivInner"><script language="JavaScript" src="/appointment/scripts/appointment.js"></script><span id="dataForm:numpop_script"></span>
	<br>
	<br>

	<div id="numOfRowsPanelPopupDiv"><div style="white-space:nowrap;vertical-align:inherit"><div id="numOfRowsPanelCap1" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><span id="dataForm:numOfRowsOut" class="captionData">Enter Rows</span></div><div style="white-space:nowrap;vertical-align:inherit"><div id="numOfRowsPanelCap2" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><input id="dataForm:numOfRows" type="text" name="dataForm:numOfRows" value="" maxlength="2" tabindex="171"></div></div>

	<br>

	<div id="numOfRowsPanelID" align="center"><input class="btn" id="dataForm:numOfRowsYes" name="dataForm:numOfRowsYes" onclick="addMultipleApptObjRow();return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:numOfRowsYes','parameters':{'dataForm:numOfRowsYes':'dataForm:numOfRowsYes'} } );return false;" value="Yes" alt="Yes" style="cursor:pointer" type="button" tabindex="172"><input class="btn" id="dataForm:numOfRowsNo" name="dataForm:numOfRowsNo" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:numOfRowsNo','oncomplete':function(request,event,data){UI8Layout.doDialogCloseById('NumRowsDialog');},'parameters':{'dataForm:numOfRowsNo':'dataForm:numOfRowsNo'} } );return false;" value="Cancel" alt="No" style="cursor:pointer" type="button" tabindex="173"></div></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("NumRowsDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:NumRowsDialog",dragHandleId:"dataForm:NumRowsIdOuter_dHId",onDialog:"",closeClientId:"dataForm:NumRowsIdOuter_cCId"});
</script><div id="dataForm:RecurrenceWindow" name="dataForm:RecurrenceWindow" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="pop_dragHandler" id="dataForm:j_id141_dHId"><div class="pop_title -pdlg_dttc">Recurrence Pattern</div></td><td><div class="pop_close" id="dataForm:j_id141_cCId"><input type="button" tabindex="174"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:570px;"><div id="j_id142" style=" margin-left:20px; ">
				<br>
				<b>Daily <input type="checkbox" id="popup_daily" onchange="dailyChanged()" tabindex="175"></b>
				<br>every <input id="dataForm:popup_frequencyInDays" type="text" name="dataForm:popup_frequencyInDays" tabindex="176"> days
				<br>
				<br>
				<b>Weekly <input type="checkbox" id="popup_weekly" onchange="weeklyChanged()" tabindex="177"></b>
				<br>Sunday <input type="checkbox" id="popup_weekly_sunday" tabindex="178">   Monday <input type="checkbox" id="popup_weekly_monday" tabindex="179">   Tuesday <input type="checkbox" id="popup_weekly_tuesday" tabindex="180">   Wednesday <input type="checkbox" id="popup_weekly_wednesday" tabindex="181">   Thursday <input type="checkbox" id="popup_weekly_thursday" tabindex="182">   Friday <input type="checkbox" id="popup_weekly_friday" tabindex="183">   Saturday <input type="checkbox" id="popup_weekly_saturday" tabindex="184">
				<br>
				<br>
				<b>Monthly </b> <input type="checkbox" id="popup_monthly" onchange="monthlyChanged()" tabindex="185">
				<br>
				<br>
				<b>Recurrence Range </b>
				<br>Start date<span style="color:red;">*  </span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="186"><input type="text" id="dataForm:popup_recurrenceStartTime" name="dataForm:popup_recurrenceStartTime" value="" alt="" size="" tabindex="187">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:popup_recurrenceStartTime" name="dataForm:popup_recurrenceStartTime_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="188" onclick="dataFormwpopup_recurrenceStartTimecalendarSetUp(this.name);return false">
<script type="text/javascript">
function dataFormwpopup_recurrenceStartTimecalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "false", 		dropDownIns  :  ''
	}
)
}
</script>
          End date<span style="color:red;">*  </span><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="189"><input type="text" id="dataForm:popup_recurrenceEndTime" name="dataForm:popup_recurrenceEndTime" value="" alt="" size="" tabindex="190">&nbsp;<input type="image" title="Select date and time" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:popup_recurrenceEndTime" name="dataForm:popup_recurrenceEndTime_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="191" onclick="dataFormwpopup_recurrenceEndTimecalendarSetUp(this.name);return false">
<script type="text/javascript">
function dataFormwpopup_recurrenceEndTimecalendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  true, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "false", 		dropDownIns  :  ''
	}
)
}
</script>

				<br>
				<br>No end date  <input type="checkbox" id="popup_noEndDate" onchange="noEndDateChange()" tabindex="192">
				<br>
				<br><input type="submit" name="dataForm:j_id188" value="Save" onclick="saveTemplate()" class="btn" tabindex="193">   <input type="button" name="dataForm:j_id190" value="Cancel" onclick="closeTemplate()" class="btn" tabindex="194">
				<br>   </div></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("RecurrenceWindow",{onClose:"",dialogClientId:"dataForm:RecurrenceWindow",dragHandleId:"dataForm:j_id141_dHId",onDialog:"",closeClientId:"dataForm:j_id141_cCId"});
</script>
	<script>
		function noEndDateChange()
		{
			if ( jQuery('input[id*="popup_noEndDate"]')[0].checked )
			{
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].value ="" ;
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].disabled = true;
				jQuery('input[id*="popup_recurrenceEndTime"]')[1].disabled = true;
			}
			else
			{
				jQuery('input[id*="popup_recurrenceEndTime"]')[0].disabled = false;
				jQuery('input[id*="popup_recurrenceEndTime"]')[1].disabled = false;
			}
		}
		function weeklyChanged()
		{
			if ( jQuery('input[id*="popup_weekly"]')[0].checked )
			{
				enableWeeklyOptions();
				checkWeeklyOptions();

				jQuery('input[id*="popup_daily"]')[0].checked = false;
				disableFrequency();
				jQuery('input[id*="popup_monthly"]')[0].checked = false;
			}
			else
			{
				uncheckWeeklyOptions();
			}
		}

		function uncheckWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.checked = false;
			});
		}
		function disableWeeklyOptions()
		{
			uncheckWeeklyOptions();
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.disabled = true;
			});
		}
		function enableWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.disabled = false;
			});
		}
		function checkWeeklyOptions()
		{
			jQuery('input[id*="popup_weekly_"]').each ( function() 	{
						this.checked = true;
			});
		}
		function dailyChanged()
		{
			if ( jQuery('input[id*="popup_daily"]')[0].checked )
			{
				jQuery('input[id*="popup_frequencyInDays"]')[0].disabled = false;

				jQuery('input[id*="popup_monthly"]')[0].checked = false;
				jQuery('input[id*="popup_weekly"]')[0].checked = false;
				disableWeeklyOptions();
			}
		}
		function disableFrequency()
		{
			jQuery('input[id*="popup_frequencyInDays"]')[0].disabled = true;
			jQuery('input[id*="popup_frequencyInDays"]')[0].value = "";
		}
		function monthlyChanged()
		{
			if ( jQuery('input[id*="popup_monthly"]')[0].checked )
			{
				jQuery('input[id*="popup_weekly"]')[0].checked = false;
				jQuery('input[id*="popup_daily"]')[0].checked = false;
				disableFrequency();
				disableWeeklyOptions();
			}
		}

		function copyBackValuesToAppointmentPage()
		{
			jQuery('input[id*="recurring_saveFromRecurringWindow"]')[0].value = true;

			jQuery('input[id*="recurring_daily"]')[0].value = jQuery('input[id*="popup_daily"]')[0].checked ;
			jQuery('input[id*="recurring_frequencyInDays"]')[0].value = jQuery('input[id*="popup_frequencyInDays"]')[0].value ;
			jQuery('input[id*="recurring_weekly"]')[0].value = jQuery('input[id*="popup_weekly"]')[0].checked;
			jQuery('input[id*="recurring_sunday"]')[0].value = jQuery('input[id*="popup_weekly_sunday"]')[0].checked;
			jQuery('input[id*="recurring_monday"]')[0].value = jQuery('input[id*="popup_weekly_monday"]')[0].checked;
			jQuery('input[id*="recurring_tuesday"]')[0].value = jQuery('input[id*="popup_weekly_tuesday"]')[0].checked;
			jQuery('input[id*="recurring_wednesday"]')[0].value = jQuery('input[id*="popup_weekly_wednesday"]')[0].checked ;
			jQuery('input[id*="recurring_thursday"]')[0].value = jQuery('input[id*="popup_weekly_thursday"]')[0].checked ;
			jQuery('input[id*="recurring_friday"]')[0].value = jQuery('input[id*="popup_weekly_friday"]')[0].checked ;
			jQuery('input[id*="recurring_saturday"]')[0].value = jQuery('input[id*="popup_weekly_saturday"]')[0].checked
			jQuery('input[id*="recurring_monthly"]')[0].value = jQuery('input[id*="popup_monthly"]')[0].checked ;
			jQuery('input[id*="recurring_StartDate"]')[0].value = jQuery('input[id*="popup_recurrenceStartTime"]')[0].value ;
			jQuery('input[id*="recurring_EndDate"]')[0].value = jQuery('input[id*="popup_recurrenceEndTime"]')[0].value ;
		}

		function saveTemplate()
		{
			copyBackValuesToAppointmentPage();
			UI8Layout.doDialogCloseById('RecurrenceWindow');
		}
		function closeTemplate()
		{
			UI8Layout.doDialogCloseById('RecurrenceWindow');
		}
	</script>

<div id="dataForm:DriverDialog" name="dataForm:DriverDialog" class="dialog_cont"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:driverIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Driver</div></td><td><div class="pop_close" id="dataForm:driverIdOuter_cCId"><input type="button" tabindex="195"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:450px;width:450px;"><span id="dataForm:ddriverA4J"><div id="driverDivInner"><script language="JavaScript" src="/appointment/lookup/js/ILMDriverLookUp.js"></script><span id="dataForm:check_locn_script"></span>

		<input type="hidden" name="driverText" id="driverText" tabindex="196">
		<input type="hidden" name="driverTextName" tabindex="197">
		<input type="hidden" name="driverStateName" tabindex="198">
		<input type="hidden" name="driverCountryName" tabindex="199">
		<input type="hidden" name="driverLicense" tabindex="200">
		<input type="hidden" name="drvLicenseHid" tabindex="201">
		<input type="hidden" name="drvLicExpHid" tabindex="202">
		<input type="hidden" name="drvrLicStateHid" tabindex="203">
		<input type="hidden" name="drvrLicCountryHid" tabindex="204">
		<input type="hidden" name="drvCntNumHid" tabindex="205">
		<input type="hidden" name="driverCode" tabindex="206">
		<input type="hidden" name="carrierCode" tabindex="207">
		<input type="hidden" name="carrierCodeHid" tabindex="208">
		<input type="hidden" name="driverContact" tabindex="209">
		<input type="hidden" name="driverLicExpiry" tabindex="210">
		<input type="hidden" name="driverFound" value="false" tabindex="211">
		<input type="hidden" name="selCountry" value="[US]" tabindex="212"><div class="pnltopdiv" id="PANEL_ydriverLocn33__Search_Panel1_top"><table id="PANEL_ydriverLocn33__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ydriverLocn33__Search_Panel1" class="pnlcondiv"><table id="dataForm:driver22_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driveNameSrch"><span title="">First name:</span><span class="notRequired">p</span><br><input id="dataForm:driverName" type="text" name="dataForm:driverName" value="" tabindex="213"></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLstNmSrch"><span title="">Last name:</span><span class="notRequired">p</span><br><input id="dataForm:driverLastName" type="text" name="dataForm:driverLastName" value="" tabindex="214"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLcSrch"><span title="">License number:</span><span class="notRequired">p</span><br><input id="dataForm:driverLicNo" type="text" name="dataForm:driverLicNo" value="" tabindex="215"></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverCrSrch"><span title="">Carrier:</span><span class="notRequired">p</span><br><script language="JavaScript">var isDemo=false</script><input type="hidden" id="dataForm:driverCRCodeecId" name="dataForm:driverCRCodeecId" value="" tabindex="216"><input type="hidden" id="dataForm_driverCRCode_enterKey" value="false" tabindex="217"><input type="hidden" id="triggerdataForm_driverCRCode_enterKey" value="false" tabindex="218"><input type="text" id="dataForm:driverCRCode" name="dataForm:driverCRCode" onfocus="javascript: focusOnTextBox('dataForm_driverCRCode_enterKey')" onblur="javascript: blurOnTextBox('dataForm_driverCRCode_enterKey')" onkeypress="if(enterPressed(event,'dataForm:driverCRCode') )return false;" value="" title="" alt="Find Carrier" tabindex="219">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-2458-b0ae-7757&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcbolookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcbolookupBackingBean.getBUMap%7D&amp;lookupType=Carrier&amp;is3plEnabled=true&amp;returnId=dataForm:driverCRCode&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:driverCRCode" title="Find Carrier" align="absmiddle" id="trigger_dataForm:driverCRCode" name="trigger_dataForm:driverCRCode" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_driverCRCode_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_driverCRCode_enterKey')" tabindex="220"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:driver_locgetlist22" name="dataForm:driver_locgetlist22" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver_locgetlist22','parameters':{'dataForm:driver_locgetlist22':'dataForm:driver_locgetlist22'} } );return false;" value="Find " type="button" tabindex="221"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:DRVlistPanel"><span id="dataForm:custIdrecordmsg"></span><input type="hidden" name="dataForm:DrvdataTable_deleteHidden" value="" id="dataForm:DrvdataTable_deleteHidden" tabindex="222"><input type="hidden" name="dataForm:DrvdataTable_selectedRows" value="#:#" id="dataForm:DrvdataTable_selectedRows" tabindex="223"><div class="datatbl_contr" id="dataForm:DrvdataTable_container" style=""><div id="dataForm:DrvdataTable_scrollDiv" class="advtbl_scrollDiv"><div id="dataForm:DrvdataTable_scrollDivBody" style="width: 0px; height: 0px;"></div></div><div id="dataForm:DrvdataTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:DrvdataTable:isSortButtonClick" id="dataForm:DrvdataTable:isSortButtonClick" value="" tabindex="224"><input type="hidden" name="dataForm:DrvdataTable:sortDir" id="dataForm:DrvdataTable:sortDir" value="desc" tabindex="225"><input type="hidden" name="dataForm:DrvdataTable:colCount" id="dataForm:DrvdataTable:colCount" value="" tabindex="226"><input type="hidden" name="dataForm:DrvdataTable:tableClicked" id="dataForm:DrvdataTable:tableClicked" value="" tabindex="227"><input type="hidden" name="dataForm:DrvdataTable:tableResized" id="dataForm:DrvdataTable:tableResized" value="false" tabindex="228"><div class="advtbl_contr_head" id="dataForm:DrvdataTable_headDiv"><table id="dataForm:DrvdataTable" cellspacing="0">
<colgroup>
<col>
<col><col><col><col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row">
<td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top"><input type="checkbox" name="dataForm:DrvdataTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'DrvdataTable');" tabindex="229"></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header "><span id="dataForm:DrvdataTable:custId21">Driver name</span></td>
<td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId31">License number</span></td>
<td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId41">Carrier code</span></td>
<td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId61">State</span></td>
<td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header "><span id="dataForm:DrvdataTable:custId51">Appointment status</span></td>
</tr></thead></table></div><div id="dataForm:DrvdataTable_bodyDiv" class="advtbl_contr_body"><table id="dataForm:DrvdataTable_body" cellspacing="0"><colgroup>
<col>
<col><col><col><col><col></colgroup><tbody>
<tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:DrvdataTable" id="checkAll_c0_dataForm:DrvdataTable" value="0" tabindex="230"><input type="hidden" value="102" id="dataForm:DrvdataTable:0:PK_0" name="dataForm:DrvdataTable:0:PK_0" tabindex="231"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId22">Catrena Bishop</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId32">1617121</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId62">ME</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:0:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c1_dataForm:DrvdataTable" id="checkAll_c1_dataForm:DrvdataTable" value="1" tabindex="232"><input type="hidden" value="82" id="dataForm:DrvdataTable:1:PK_1" name="dataForm:DrvdataTable:1:PK_1" tabindex="233"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId22">Dana McQuesten</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId32">3522220</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId62">ME</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:1:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c2_dataForm:DrvdataTable" id="checkAll_c2_dataForm:DrvdataTable" value="2" tabindex="234"><input type="hidden" value="42" id="dataForm:DrvdataTable:2:PK_2" name="dataForm:DrvdataTable:2:PK_2" tabindex="235"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId22">Daniel Greenlee</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId32">G654135234467</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId62">MD</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:2:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c3_dataForm:DrvdataTable" id="checkAll_c3_dataForm:DrvdataTable" value="3" tabindex="236"><input type="hidden" value="142" id="dataForm:DrvdataTable:3:PK_3" name="dataForm:DrvdataTable:3:PK_3" tabindex="237"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId22">Eric Fitzpatrick</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId32">32681535</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId62">PA</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:3:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c4_dataForm:DrvdataTable" id="checkAll_c4_dataForm:DrvdataTable" value="4" tabindex="238"><input type="hidden" value="122" id="dataForm:DrvdataTable:4:PK_4" name="dataForm:DrvdataTable:4:PK_4" tabindex="239"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId22">Gardner Flemming</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId32">4437148</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId62">ME</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:4:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c5_dataForm:DrvdataTable" id="checkAll_c5_dataForm:DrvdataTable" value="5" tabindex="240"><input type="hidden" value="202" id="dataForm:DrvdataTable:5:PK_5" name="dataForm:DrvdataTable:5:PK_5" tabindex="241"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId22">Jeffrey Weymouth</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId32">7484099</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId62">ME</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:5:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c6_dataForm:DrvdataTable" id="checkAll_c6_dataForm:DrvdataTable" value="6" tabindex="242"><input type="hidden" value="2" id="dataForm:DrvdataTable:6:PK_6" name="dataForm:DrvdataTable:6:PK_6" tabindex="243"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId22">Joe Shmo</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId32">12345678</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId42">ADTEST1</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId62">MA</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:6:custId52">On Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tar" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c7_dataForm:DrvdataTable" id="checkAll_c7_dataForm:DrvdataTable" value="7" tabindex="244"><input type="hidden" value="23" id="dataForm:DrvdataTable:7:PK_7" name="dataForm:DrvdataTable:7:PK_7" tabindex="245"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId22">Joe Shmo</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId32">12345678</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId62">MA</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:7:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr class="advtbl_row -dg_tr" width="100%"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c8_dataForm:DrvdataTable" id="checkAll_c8_dataForm:DrvdataTable" value="8" tabindex="246"><input type="hidden" value="163" id="dataForm:DrvdataTable:8:PK_8" name="dataForm:DrvdataTable:8:PK_8" tabindex="247"></td><td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId22">Montie Thompson</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId32">T51254551294</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId42">PREPAID</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId62">IL</span> <span style="display:none">&nbsp;</span></td>
<td style="white-space: nowrap;" class="advtbl_col advtbl_body_col"><span id="dataForm:DrvdataTable:8:custId52">Off Yard</span> <span style="display:none">&nbsp;</span></td>
</tr>
<tr id="dataForm:DrvdataTable:nodataRow" class="advtbl_row trhide"><td class="advtbl_col advtbl_body_col tdhide" colspan="6" align="left"> No data found</td></tr></tbody>
<input type="hidden" id="DrvdataTable_hdnMaxIndexHldr" name="DrvdataTable_hdnMaxIndexHldr" value="9" tabindex="248"></table></div><div class="emptyHoriScrollDiv"></div><div id="sortButton" style="display:none;"><input id="dataForm:DrvdataTable:sortButton" name="dataForm:DrvdataTable:sortButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:DrvdataTable:sortButton','parameters':{'dataForm:DrvdataTable:sortButton':'dataForm:DrvdataTable:sortButton'} } );return false;" value="|" type="button" tabindex="249"></div></div>
<input type="hidden" id="dataForm:DrvdataTable_trs_pageallrowskey" name="dataForm:DrvdataTable_trs_pageallrowskey" value="102#:#82#:#42#:#142#:#122#:#202#:#2#:#23#:#163#:#" tabindex="250"><input type="hidden" id="dataForm:DrvdataTable_selectedRows" name="dataForm:DrvdataTable_selectedRows" value="" tabindex="251"><input type="hidden" id="dataForm:DrvdataTable_selectedIdList" name="dataForm:DrvdataTable_selectedIdList" value="" tabindex="252"><input type="hidden" id="dataForm:DrvdataTable_trs_allselectedrowskey" name="dataForm:DrvdataTable_trs_allselectedrowskey" value="DrvdataTable$:$1751860974459" tabindex="253"><script type="text/javascript">var  dataFormDrvdataTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='DrvdataTable';
tableObjectArray[count]=dataFormDrvdataTable_tableObj;dataFormDrvdataTable_tableObj.bind(document.getElementById('dataForm:DrvdataTable_container'), document.getElementById('dataForm:DrvdataTable_headDiv'), document.getElementById('dataForm:DrvdataTable_bodyDiv'), document.getElementById('dataForm:DrvdataTable_scrollDiv'),document.getElementById('dataForm:DrvdataTable_scrollDivBody'),document.getElementById('dataForm:DrvdataTable_button'),true,1,2,'dataForm:DrvdataTable','edit','yes','no','0','bottom','view',0,2147483647,'yes','no','even','odd','Invalid Table','dataForm:DrvdataTable_selectedIdList','true','DrvdataTable','false' ,0,0,'false','null','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',0);
var dataForm_DrvdataTable_custId2="true";var dataForm_DrvdataTable_custId3="true";var dataForm_DrvdataTable_custId4="true";var dataForm_DrvdataTable_custId6="true";var dataForm_DrvdataTable_custId5="true";</script>
<script type="text/javascript">
UI8Layout.ondialogTableLoad("DrvdataTable");
</script><input id="dataForm:allDrivers" type="hidden" name="dataForm:allDrivers" value="Catrena Bishop;1617121;ME;PREPAID;US;9/22/21#Dana McQuesten;3522220;ME;PREPAID;US;5/4/24#Daniel Greenlee;G654135234467;MD;PREPAID;US;6/18/22#Eric Fitzpatrick;32681535;PA;PREPAID;US;11/11/23#Gardner Flemming;4437148;ME;PREPAID;US;6/16/26#Jeffrey Weymouth;7484099;ME;PREPAID;US;5/18/27#Joe Shmo;12345678;MA;ADTEST1;US;12/31/20#Joe Shmo;12345678;MA;PREPAID;US;12/31/20#Montie Thompson;T51254551294;IL;PREPAID;US;10/16/26#" tabindex="254"><input class="btn" id="dataForm:driver22Filter_SelectButton" name="dataForm:driver22Filter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver22Filter_SelectButton','oncomplete':function(request,event,data){selectDriver();return false;},'parameters':{'dataForm:driver22Filter_SelectButton':'dataForm:driver22Filter_SelectButton'} } );return false;" value="Select" type="button" tabindex="255"></span><span id="dataForm:driverFndJSPanel"><input id="dataForm:drvFound" type="hidden" name="dataForm:drvFound" value="false" tabindex="256"></span><div class="pnltopdiv" id="PANEL_ydriverLocn33__Create_Panel1_top"><table id="PANEL_ydriverLocn33__Create_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><img src="/lps/resources/panel/images/expand.gif" id="tr_ydriverLocn33__Create_Panel1img" onclick="return ExpandCollapse('tr_ydriverLocn33__Create_Panel1','/lps/resources/panel/images/expand.gif','/lps/resources/panel/images/collapse.gif')">New Driver</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ydriverLocn33__Create_Panel1" class="pnlcondiv"><table id="dataForm:driver22_pg_2">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driveName"><span title="">First Name:</span><span class="required" id="driveName_cptnSpn">*</span><br><input id="dataForm:driverNameNew1" type="text" name="dataForm:driverNameNew1" value="" maxlength="25" tabindex="257"></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLstNm"><span title="">Last Name:</span><span class="required" id="driverLstNm_cptnSpn">*</span><br><input id="dataForm:driverLastNameNew1" type="text" name="dataForm:driverLastNameNew1" value="" maxlength="25" tabindex="258"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverLc"><span title="">Driver license number:</span><span class="required" id="driverLc_cptnSpn">*</span><br><input id="dataForm:driverLicNoNew1" type="text" name="dataForm:driverLicNoNew1" value="" maxlength="25" tabindex="259"></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="driverCntctNm"><span title="">Contact number:</span><span class="notRequired">p</span><br><input id="dataForm:driverContactNum" type="text" name="dataForm:driverContactNum" value="" maxlength="25" tabindex="260"></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="carrier"><span title="">Carrier:</span><span class="required" id="carrier_cptnSpn">*</span><br><script language="JavaScript">var isDemo=false</script><input type="hidden" id="dataForm:driverCRCode1ecId" name="dataForm:driverCRCode1ecId" value="" tabindex="261"><input type="hidden" id="dataForm_driverCRCode1_enterKey" value="false" tabindex="262"><input type="hidden" id="triggerdataForm_driverCRCode1_enterKey" value="false" tabindex="263"><input type="text" id="dataForm:driverCRCode1" name="dataForm:driverCRCode1" onfocus="javascript: focusOnTextBox('dataForm_driverCRCode1_enterKey')" onblur="javascript: blurOnTextBox('dataForm_driverCRCode1_enterKey')" onkeypress="if(enterPressed(event,'dataForm:driverCRCode1') )return false;" value="" title="" alt="Find Carrier" maxlength="25" tabindex="264">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-2458-b0ae-7757&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcbolookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcbolookupBackingBean.getBUMap%7D&amp;lookupType=Carrier&amp;is3plEnabled=true&amp;returnId=dataForm:driverCRCode1&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code='; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:driverCRCode1" title="Find Carrier" align="absmiddle" id="trigger_dataForm:driverCRCode1" name="trigger_dataForm:driverCRCode1" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_driverCRCode1_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_driverCRCode1_enterKey')" tabindex="265"></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="DriverEdit_capCountry"><span title="">License Country:</span><span class="required" id="DriverEdit_capCountry_cptnSpn">*</span><br><script language="JavaScript" src="/lps/resources/common/scripts/dependentList.js"></script><script type="text/javascript">

  var State_ListArray = [ [ "",[[ " ","(select one)"]]],[ "US",[[ " ","(none)"],[ "AA","AA"],[ "AE","AE "],[ "AK","AK"],[ "AL","AL"],[ "AP","AP"],[ "AR","AR"],[ "AS","AS"],[ "AZ","AZ"],[ "CA","CA"],[ "CO","CO"],[ "CT","CT"],[ "DC","DC"],[ "DE","DE"],[ "FL","FL"],[ "FM","FM"],[ "GA","GA"],[ "GU","GU"],[ "HI","HI"],[ "IA","IA"],[ "ID","ID"],[ "IL","IL"],[ "IN","IN"],[ "KS","KS"],[ "KY","KY"],[ "LA","LA"],[ "MA","MA"],[ "MD","MD"],[ "ME","ME"],[ "MH","MH"],[ "MI","MI"],[ "MN","MN"],[ "MO","MO"],[ "MP","MP"],[ "MS","MS"],[ "MT","MT"],[ "NC","NC"],[ "ND","ND"],[ "NE","NE"],[ "NH","NH"],[ "NJ","NJ"],[ "NM","NM"],[ "NV","NV"],[ "NY","NY"],[ "OH","OH"],[ "OK","OK"],[ "OR","OR"],[ "PA","PA"],[ "PR","PR"],[ "PW","PW"],[ "RI","RI"],[ "SC","SC"],[ "SD","SD"],[ "TN","TN"],[ "TX","TX"],[ "UT","UT"],[ "VA","VA"],[ "VI","VI"],[ "VT","VT"],[ "WA","WA"],[ "WI","WI"],[ "WV","WV"],[ "WY","WY"]]],[ "AF",[[ " ","(none)"],[ "AF","AF"]]],[ "AL",[[ " ","(none)"],[ "AL","AL"]]],[ "DZ",[[ " ","(none)"],[ "DZ","DZ"]]],[ "AS",[[ " ","(none)"],[ "AS","AS"]]],[ "AD",[[ " ","(none)"],[ "AD","AD"]]],[ "AO",[[ " ","(none)"],[ "AO","AO"]]],[ "AI",[[ " ","(none)"],[ "AI","AI "]]],[ "AQ",[[ " ","(none)"],[ "AQ","AQ"]]],[ "AG",[[ " ","(none)"],[ "AG","AG"]]],[ "AR",[[ " ","(none)"],[ "BA","BA"],[ "CA","CA"],[ "CB","CB"],[ "CD","CD"],[ "CH","CH"],[ "CR","CR"],[ "ER","ER"],[ "FO","FO"],[ "LP","LP"],[ "LR","LR"],[ "MD","MD"],[ "MI","MI"],[ "NE","NE"],[ "PJ","PJ"],[ "RN","RN"],[ "SA","SA"],[ "SC","SC"],[ "SE","SE"],[ "SF","SF"],[ "SJ","SJ"],[ "SL","SL"],[ "TF","TF"],[ "TU","TU"]]],[ "AM",[[ " ","(none)"],[ "AM","AM"]]],[ "AW",[[ " ","(none)"],[ "AW","AW"]]],[ "AU",[[ " ","(none)"],[ "NS","NS"],[ "NT","NT"],[ "QL","QL"],[ "SA","SA"],[ "TS","TS"],[ "VI","VI"],[ "WA","WA"]]],[ "AT",[[ " ","(none)"],[ "AT","AT"]]],[ "AZ",[[ " ","(none)"],[ "AZ","AZ"]]],[ "BS",[[ " ","(none)"],[ "BS","BS"]]],[ "BH",[[ " ","(none)"],[ "BH","BH"]]],[ "BD",[[ " ","(none)"],[ "BD","BD"]]],[ "BB",[[ " ","(none)"],[ "BB","BB"]]],[ "BY",[[ " ","(none)"],[ "BY","BY "]]],[ "BE",[[ " ","(none)"],[ "BE","BE"]]],[ "BZ",[[ " ","(none)"],[ "BZ","BZ"]]],[ "BJ",[[ " ","(none)"],[ "BJ","BJ"]]],[ "BM",[[ " ","(none)"],[ "BM","BM"]]],[ "BT",[[ " ","(none)"],[ "BT","BT"]]],[ "BO",[[ " ","(none)"],[ "BO","BO"]]],[ "BA",[[ " ","(none)"],[ "BA","BA"]]],[ "BW",[[ " ","(none)"],[ "BW","BW "]]],[ "BV",[[ " ","(none)"],[ "BV","BV"]]],[ "BR",[[ " ","(none)"],[ "AC","AC"],[ "AL","AL"],[ "AM","AM"],[ "AP","AP"],[ "BA","BA"],[ "CE","CE"],[ "DF","DF"],[ "ES","ES"],[ "FN","FN"],[ "GO","GO"],[ "MA","MA"],[ "MG","MG"],[ "MS","MS"],[ "MT","MT"],[ "PA","PA"],[ "PB","PB"],[ "PE","PE"],[ "PI","PI"],[ "PR","PR"],[ "RJ","RJ"],[ "RN","RN"],[ "RO","RO"],[ "RR","RR"],[ "RS","RS"],[ "SC","SC"],[ "SE","SE"],[ "SP","SP"],[ "TO","TO"]]],[ "IO",[[ " ","(none)"],[ "IO","IO"]]],[ "BN",[[ " ","(none)"],[ "BN","BN"]]],[ "BG",[[ " ","(none)"],[ "BG","BG"]]],[ "BF",[[ " ","(none)"],[ "BF","BF"]]],[ "BI",[[ " ","(none)"],[ "BI","BI"]]],[ "KH",[[ " ","(none)"],[ "KH","KH"]]],[ "CM",[[ " ","(none)"],[ "CM","CM"]]],[ "CA",[[ " ","(none)"],[ "AB","AB"],[ "BC","BC"],[ "MB","MB"],[ "NB","NB"],[ "NF","NF"],[ "NS","NS"],[ "NT","NT"],[ "NU","NU"],[ "ON","ON"],[ "PE","PE"],[ "QC","QC"],[ "SK","SK"],[ "YT","YT"]]],[ "CV",[[ " ","(none)"],[ "CV","CV"]]],[ "KY",[[ " ","(none)"],[ "KY","KY"]]],[ "CF",[[ " ","(none)"],[ "CF","CF"]]],[ "TD",[[ " ","(none)"],[ "TD","TD"]]],[ "CL",[[ " ","(none)"],[ "CL","CL"]]],[ "CN",[[ " ","(none)"],[ "CN","CN"]]],[ "CX",[[ " ","(none)"],[ "CX","CX "]]],[ "CC",[[ " ","(none)"],[ "CC","CC"]]],[ "CO",[[ " ","(none)"],[ "CO","CO"]]],[ "KM",[[ " ","(none)"],[ "KM","KM"]]],[ "CG",[[ " ","(none)"],[ "CG","CG"]]],[ "CD",[[ " ","(none)"],[ "CD","CD"]]],[ "CK",[[ " ","(none)"],[ "CK","CK"]]],[ "CR",[[ " ","(none)"],[ "CR","CR"]]],[ "HR",[[ " ","(none)"],[ "HR","HR"]]],[ "CU",[[ " ","(none)"],[ "CU","CU"]]],[ "CY",[[ " ","(none)"],[ "CY","CY"]]],[ "CZ",[[ " ","(none)"],[ "CZ","CZ"]]],[ "DK",[[ " ","(none)"],[ "DK","DK"]]],[ "DJ",[[ " ","(none)"],[ "DJ","DJ"]]],[ "DM",[[ " ","(none)"],[ "DM","DM"]]],[ "DO",[[ " ","(none)"],[ "DO","DO"]]],[ "TP",[[ " ","(none)"],[ "TP","TP"]]],[ "EC",[[ " ","(none)"],[ "EC","EC"]]],[ "ED",[[ " ","(none)"],[ "ED","ED"]]],[ "EG",[[ " ","(none)"],[ "EG","EG"]]],[ "SV",[[ " ","(none)"],[ "SV","SV"]]],[ "GQ",[[ " ","(none)"],[ "GQ","GQ"]]],[ "ER",[[ " ","(none)"],[ "ER","ER"]]],[ "EE",[[ " ","(none)"],[ "EE","EE"]]],[ "ET",[[ " ","(none)"],[ "ET","ET"]]],[ "FK",[[ " ","(none)"],[ "FK","FK"]]],[ "FO",[[ " ","(none)"],[ "FO","FO"]]],[ "FJ",[[ " ","(none)"],[ "FJ","FJ"]]],[ "FI",[[ " ","(none)"],[ "FI","FI"]]],[ "CS",[[ " ","(none)"],[ "CS","CS"]]],[ "SU",[[ " ","(none)"],[ "SU","SU"]]],[ "FR",[[ " ","(none)"],[ "FR","FR"]]],[ "FX",[[ " ","(none)"],[ "FX","FX"]]],[ "GF",[[ " ","(none)"],[ "GF","GF"]]],[ "TF",[[ " ","(none)"],[ "TF","TF"]]],[ "GA",[[ " ","(none)"],[ "GA","GA"]]],[ "GM",[[ " ","(none)"],[ "GM","GM"]]],[ "GE",[[ " ","(none)"],[ "GE","GE"]]],[ "DE",[[ " ","(none)"],[ "DE","DE"]]],[ "GH",[[ " ","(none)"],[ "GH","GH"]]],[ "GI",[[ " ","(none)"],[ "GI","GI"]]],[ "GB",[[ " ","(none)"],[ "GB","GB"]]],[ "GR",[[ " ","(none)"],[ "GR","GR"]]],[ "GL",[[ " ","(none)"],[ "GL","GL"]]],[ "GD",[[ " ","(none)"],[ "GD","GD"]]],[ "GP",[[ " ","(none)"],[ "GP","GP"]]],[ "GU",[[ " ","(none)"],[ "GU","GU"]]],[ "GT",[[ " ","(none)"],[ "GT","GT"]]],[ "GN",[[ " ","(none)"],[ "GN","GN"]]],[ "GW",[[ " ","(none)"],[ "GW","GW"]]],[ "GY",[[ " ","(none)"],[ "GY","GY"]]],[ "HT",[[ " ","(none)"],[ "HT","HT"]]],[ "HM",[[ " ","(none)"],[ "HM","HM"]]],[ "VA",[[ " ","(none)"],[ "VA","VA"]]],[ "HN",[[ " ","(none)"],[ "HN","HN"]]],[ "HK",[[ " ","(none)"],[ "HK","HK"]]],[ "HU",[[ " ","(none)"],[ "HU","HU"]]],[ "IS",[[ " ","(none)"],[ "IS","IS"]]],[ "IN",[[ " ","(none)"],[ "IN","IN"]]],[ "ID",[[ " ","(none)"],[ "ID","ID"]]],[ "IR",[[ " ","(none)"],[ "IR","IR"]]],[ "IQ",[[ " ","(none)"],[ "IQ","IQ"]]],[ "IE",[[ " ","(none)"],[ "IE","IE"]]],[ "IL",[[ " ","(none)"],[ "IL","IL"]]],[ "IT",[[ " ","(none)"],[ "IT","IT"]]],[ "CI",[[ " ","(none)"],[ "CI","CI"]]],[ "JM",[[ " ","(none)"],[ "JM","JM"]]],[ "JP",[[ " ","(none)"],[ "JP","JP"]]],[ "JO",[[ " ","(none)"],[ "JO","JO"]]],[ "KZ",[[ " ","(none)"],[ "KZ","KZ"]]],[ "KE",[[ " ","(none)"],[ "KE","KE"]]],[ "KI",[[ " ","(none)"],[ "KI","KI"]]],[ "KV",[[ " ","(none)"]]],[ "KW",[[ " ","(none)"],[ "KW","KW"]]],[ "KG",[[ " ","(none)"],[ "KG","KG"]]],[ "LA",[[ " ","(none)"],[ "LA","LA"]]],[ "LV",[[ " ","(none)"],[ "LV","LV"]]],[ "LB",[[ " ","(none)"],[ "LB","LB"]]],[ "LS",[[ " ","(none)"],[ "LS","LS"]]],[ "LR",[[ " ","(none)"],[ "LR","LR"]]],[ "LY",[[ " ","(none)"],[ "LY","LY"]]],[ "LI",[[ " ","(none)"],[ "LI","LI"]]],[ "LT",[[ " ","(none)"],[ "LT","LT"]]],[ "LU",[[ " ","(none)"],[ "LU","LU"]]],[ "MO",[[ " ","(none)"],[ "MO","MO"]]],[ "MK",[[ " ","(none)"],[ "MK","MK"]]],[ "MG",[[ " ","(none)"],[ "MG","MG"]]],[ "MW",[[ " ","(none)"],[ "MW","MW"]]],[ "MY",[[ " ","(none)"],[ "MY","MY"]]],[ "MV",[[ " ","(none)"],[ "MV","MV"]]],[ "ML",[[ " ","(none)"],[ "ML","ML"]]],[ "MT",[[ " ","(none)"],[ "MT","MT"]]],[ "MH",[[ " ","(none)"],[ "MH","MH"]]],[ "MQ",[[ " ","(none)"],[ "MQ","MQ"]]],[ "MR",[[ " ","(none)"],[ "MR","MR"]]],[ "MU",[[ " ","(none)"],[ "MU","MU"]]],[ "YT",[[ " ","(none)"],[ "YT","YT"]]],[ "MX",[[ " ","(none)"],[ "AG","AG"],[ "BC","BC"],[ "BJ","BJ"],[ "BS","BS"],[ "CH","CH"],[ "CI","CI"],[ "CL","CL"],[ "CP","CP"],[ "CU","CU"],[ "DF","DF"],[ "DG","DG"],[ "EM","EM"],[ "GJ","GJ"],[ "GR","GR"],[ "HG","HG"],[ "JA","JA"],[ "MH","MH"],[ "MR","MR"],[ "MX","MX.CODE"],[ "NA","NA"],[ "NL","NL"],[ "OA","OA"],[ "PU","PU"],[ "QA","QA"],[ "QR","QR"],[ "SI","SI"],[ "SL","SL"],[ "SO","SO"],[ "TA","TA"],[ "TL","TL"],[ "TM","TM"],[ "VL","VL"],[ "YC","YC"],[ "ZT","ZT"]]],[ "FM",[[ " ","(none)"],[ "FM","FM"]]],[ "MD",[[ " ","(none)"],[ "MD","MD"]]],[ "MC",[[ " ","(none)"],[ "MC","MC"]]],[ "MN",[[ " ","(none)"],[ "MN","MN"]]],[ "MS",[[ " ","(none)"],[ "MS","MS"]]],[ "MA",[[ " ","(none)"],[ "MA","MA"]]],[ "MZ",[[ " ","(none)"],[ "MZ","MZ"]]],[ "MM",[[ " ","(none)"],[ "MM","MM"]]],[ "NA",[[ " ","(none)"],[ "NA","NA"]]],[ "NR",[[ " ","(none)"],[ "NR","NR"]]],[ "NP",[[ " ","(none)"],[ "NP","NP"]]],[ "NL",[[ " ","(none)"],[ "NL","NL"]]],[ "AN",[[ " ","(none)"],[ "AN","AN"]]],[ "NT",[[ " ","(none)"],[ "NT","NT"]]],[ "NC",[[ " ","(none)"],[ "NC","NC"]]],[ "NZ",[[ " ","(none)"],[ "NZ","NZ"]]],[ "NI",[[ " ","(none)"],[ "NI","NI"]]],[ "NE",[[ " ","(none)"],[ "NE","NE"]]],[ "NG",[[ " ","(none)"],[ "NG","NG"]]],[ "NU",[[ " ","(none)"],[ "NU","NU"]]],[ "NF",[[ " ","(none)"],[ "NF","NF"]]],[ "KP",[[ " ","(none)"],[ "KP","KP"]]],[ "MP",[[ " ","(none)"],[ "MP","MP"]]],[ "NO",[[ " ","(none)"],[ "NO","NO"]]],[ "OM",[[ " ","(none)"],[ "OM","OM"]]],[ "PK",[[ " ","(none)"],[ "PK","PK"]]],[ "PW",[[ " ","(none)"],[ "PW","PW"]]],[ "PA",[[ " ","(none)"],[ "PA","PA"]]],[ "PG",[[ " ","(none)"],[ "PG","PG"]]],[ "PY",[[ " ","(none)"],[ "PY","PY"]]],[ "PE",[[ " ","(none)"],[ "PE","PE"]]],[ "PH",[[ " ","(none)"],[ "PH","PH"]]],[ "PN",[[ " ","(none)"],[ "PN","PN"]]],[ "PL",[[ " ","(none)"],[ "PL","PL"]]],[ "PF",[[ " ","(none)"],[ "PF","PF"]]],[ "PT",[[ " ","(none)"],[ "PT","PT"]]],[ "PR",[[ " ","(none)"],[ "PR","PR"]]],[ "QA",[[ " ","(none)"],[ "QA","QA"]]],[ "RE",[[ " ","(none)"],[ "RE","RE"]]],[ "RO",[[ " ","(none)"],[ "RO","RO"]]],[ "RU",[[ " ","(none)"],[ "RU","RU"]]],[ "RW",[[ " ","(none)"],[ "RW","RW"]]],[ "GS",[[ " ","(none)"],[ "GS","GS"]]],[ "SH",[[ " ","(none)"],[ "SH","SH"]]],[ "KN",[[ " ","(none)"],[ "KN","KN"]]],[ "LC",[[ " ","(none)"],[ "LC","LC"]]],[ "PM",[[ " ","(none)"],[ "PM","PM"]]],[ "ST",[[ " ","(none)"],[ "ST","ST"]]],[ "VC",[[ " ","(none)"],[ "VC","VC"]]],[ "WS",[[ " ","(none)"],[ "WS","WS"]]],[ "SM",[[ " ","(none)"],[ "SM","SM"]]],[ "SA",[[ " ","(none)"],[ "SA","SA"]]],[ "SN",[[ " ","(none)"],[ "SN","SN"]]],[ "SC",[[ " ","(none)"],[ "SC","SC"]]],[ "SL",[[ " ","(none)"],[ "SL","SL"]]],[ "SG",[[ " ","(none)"],[ "SG","SG"]]],[ "SK",[[ " ","(none)"],[ "SK","SK"]]],[ "SI",[[ " ","(none)"],[ "SI","SI"]]],[ "SB",[[ " ","(none)"],[ "SB","SB"]]],[ "SO",[[ " ","(none)"],[ "SO","SO"]]],[ "ZA",[[ " ","(none)"],[ "ZA","ZA"]]],[ "KR",[[ " ","(none)"],[ "KR","KR"]]],[ "ES",[[ " ","(none)"],[ "ES","ES"]]],[ "LK",[[ " ","(none)"],[ "LK","LK"]]],[ "SD",[[ " ","(none)"],[ "SD","SD"]]],[ "SR",[[ " ","(none)"],[ "SR","SR"]]],[ "SJ",[[ " ","(none)"],[ "SJ","SJ"]]],[ "SZ",[[ " ","(none)"],[ "SZ","SZ"]]],[ "SE",[[ " ","(none)"],[ "SE","SE"]]],[ "CH",[[ " ","(none)"],[ "CH","CH"]]],[ "SY",[[ " ","(none)"],[ "SY","SY"]]],[ "TJ",[[ " ","(none)"],[ "TJ","TJ"]]],[ "TW",[[ " ","(none)"],[ "TW","TW"]]],[ "TZ",[[ " ","(none)"],[ "TZ","TZ"]]],[ "TH",[[ " ","(none)"],[ "TH","TH"]]],[ "TG",[[ " ","(none)"],[ "TG","TG"]]],[ "TK",[[ " ","(none)"],[ "TK","TK"]]],[ "TO",[[ " ","(none)"],[ "TO","TO"]]],[ "TT",[[ " ","(none)"],[ "TT","TT"]]],[ "TN",[[ " ","(none)"],[ "TN","TN"]]],[ "TR",[[ " ","(none)"],[ "TR","TR"]]],[ "TM",[[ " ","(none)"],[ "TM","TM"]]],[ "TC",[[ " ","(none)"],[ "TC","TC"]]],[ "TV",[[ " ","(none)"],[ "TV","TV"]]],[ "UM",[[ " ","(none)"],[ "UM","UM"]]],[ "UG",[[ " ","(none)"],[ "UG","UG"]]],[ "UA",[[ " ","(none)"],[ "UA","UA"]]],[ "AE",[[ " ","(none)"],[ "AE","AE "]]],[ "UK",[[ " ","(none)"],[ "UK","UK"]]],[ "UY",[[ " ","(none)"],[ "UY","UY"]]],[ "UZ",[[ " ","(none)"],[ "UZ","UZ"]]],[ "VU",[[ " ","(none)"],[ "VU","VU"]]],[ "VE",[[ " ","(none)"],[ "VE","VE"]]],[ "VN",[[ " ","(none)"],[ "VN","VN"]]],[ "VG",[[ " ","(none)"],[ "VG","VG"]]],[ "VI",[[ " ","(none)"],[ "VI","VI"]]],[ "WF",[[ " ","(none)"],[ "WF","WF"]]],[ "EH",[[ " ","(none)"],[ "EH","EH"]]],[ "YE",[[ " ","(none)"],[ "YE","YE"]]],[ "YU",[[ " ","(none)"],[ "YU","YU"]]],[ "ZR",[[ " ","(none)"],[ "ZR","ZR"]]],[ "ZM",[[ " ","(none)"],[ "ZM","ZM"]]],[ "ZW",[[ " ","(none)"],[ "ZW","ZW"]]] ] </script><input type="hidden" name="primaryDependentListId" value="dataForm:Country_List" tabindex="266"><select size="1" name="dataForm:Country_List" id="dataForm:Country_List" onchange="delegateMethod(this,'State_List','null','null','null')" tabindex="267"><option value=""> (select one)</option><option value="US" selected=""> United States</option><option value="AF"> Afghanistan</option><option value="AL"> Albania</option><option value="DZ"> Algeria</option><option value="AS"> American Samoa</option><option value="AD"> Andorra</option><option value="AO"> Angola                                                                   </option><option value="AI"> Anguilla                                                               </option><option value="AQ"> Antarctica                                                           </option><option value="AG"> Antigua &amp; Barbuda                                       </option><option value="AR"> Argentina                                                             </option><option value="AM"> Armenia                                                                 </option><option value="AW"> Aruba                                                                     </option><option value="AU"> Australia                                                             </option><option value="AT"> Austria                                                                 </option><option value="AZ"> Azerbaijan                                                           </option><option value="BS"> Bahamas                                                                 </option><option value="BH"> Bahrain                                                                 </option><option value="BD"> Bangladesh                                                           </option><option value="BB"> Barbados                                                               </option><option value="BY"> Belarus                                                                 </option><option value="BE"> Belgium                                                                 </option><option value="BZ"> Belize                                                                   </option><option value="BJ"> Benin                                                                     </option><option value="BM"> Bermuda                                                                 </option><option value="BT"> Bhutan                                                                   </option><option value="BO"> Bolivia                                                                 </option><option value="BA"> Bosnia-Herzegovina                                           </option><option value="BW"> Botswana                                                               </option><option value="BV"> Bouvet Island                                                    </option><option value="BR"> Brazil                                                                   </option><option value="IO"> British Indian Ocean Terr.</option><option value="BN"> Brunei Darussalam                                            </option><option value="BG"> Bulgaria                                                               </option><option value="BF"> Burkina Faso                                                      </option><option value="BI"> Burundi                                                                 </option><option value="KH"> Cambodia                                     </option><option value="CM"> Cameroon                                                               </option><option value="CA"> Canada                                                                   </option><option value="CV"> Cape Verde                                                          </option><option value="KY"> Cayman Islands                                                  </option><option value="CF"> Central African Republic                             </option><option value="TD"> Chad                                                                       </option><option value="CL"> Chile                                                                     </option><option value="CN"> China                                                                     </option><option value="CX"> Christmas Island                                              </option><option value="CC"> Cocos (Keeling) Isls.                              </option><option value="CO"> Colombia                                                               </option><option value="KM"> Comoros                                                                 </option><option value="CG"> Congo                                                                     </option><option value="CD"> Congo, Democratic Rep.</option><option value="CK"> Cook Islands                                                      </option><option value="CR"> Costa Rica                                                          </option><option value="HR"> Croatia                                                                 </option><option value="CU"> Cuba                                                                       </option><option value="CY"> Cyprus                                                                   </option><option value="CZ"> Czech Republic                                                  </option><option value="DK"> Denmark                                                                 </option><option value="DJ"> Djibouti                                                               </option><option value="DM"> Dominica                                                               </option><option value="DO"> Dominican Republic                                          </option><option value="TP"> East Timor                                                          </option><option value="EC"> Ecuador                                                                 </option><option value="ED"> Educational                                                         </option><option value="EG"> Egypt                                                                     </option><option value="SV"> El Salvador                                                        </option><option value="GQ"> Equatorial Guinea                                            </option><option value="ER"> Eritrea                                                                 </option><option value="EE"> Estonia                                                                 </option><option value="ET"> Ethiopia                                                               </option><option value="FK"> Falkland Islands                                              </option><option value="FO"> Faroe Islands                                                    </option><option value="FJ"> Fiji                                                                       </option><option value="FI"> Finland                                                                 </option><option value="CS"> Former Czechoslovakia                                    </option><option value="SU"> Former USSR                                                        </option><option value="FR"> France                                                                   </option><option value="FX"> France, European Terr.</option><option value="GF"> French Guyana                                                    </option><option value="TF"> French S. Territories</option><option value="GA"> Gabon                                                                     </option><option value="GM"> Gambia                                                                   </option><option value="GE"> Georgia                                                                 </option><option value="DE"> Germany                                                                 </option><option value="GH"> Ghana                                                                     </option><option value="GI"> Gibraltar                                                             </option><option value="GB"> Great Britain                                                    </option><option value="GR"> Greece                                                                   </option><option value="GL"> Greenland                                                             </option><option value="GD"> Grenada                                                                 </option><option value="GP"> Guadeloupe</option><option value="GU"> Guam</option><option value="GT"> Guatemala                                                             </option><option value="GN"> Guinea                                                                   </option><option value="GW"> Guinea Bissau                                                    </option><option value="GY"> Guyana                                                                   </option><option value="HT"> Haiti                                                                     </option><option value="HM"> Heard &amp; McDonald Isls.</option><option value="VA"> Holy See, Vatican City</option><option value="HN"> Honduras                                                               </option><option value="HK"> Hong Kong                                                            </option><option value="HU"> Hungary                                                                 </option><option value="IS"> Iceland                                                                 </option><option value="IN"> India                                                                     </option><option value="ID"> Indonesia                                                             </option><option value="IR"> Iran                                                                       </option><option value="IQ"> Iraq                                                                       </option><option value="IE"> Ireland                                                                 </option><option value="IL"> Israel                                                                   </option><option value="IT"> Italy                                                                     </option><option value="CI"> Ivory Coast                      </option><option value="JM"> Jamaica                                                                 </option><option value="JP"> Japan                                                                     </option><option value="JO"> Jordan                                                                   </option><option value="KZ"> Kazakhstan                                                           </option><option value="KE"> Kenya                                                                     </option><option value="KI"> Kiribati                                                               </option><option value="KV"> Kosovo</option><option value="KW"> Kuwait                                                                   </option><option value="KG"> Kyrgyzstan</option><option value="LA"> Laos                                                                       </option><option value="LV"> Latvia                                                                   </option><option value="LB"> Lebanon                                                                 </option><option value="LS"> Lesotho                                                                 </option><option value="LR"> Liberia                                                                 </option><option value="LY"> Libya                                                                     </option><option value="LI"> Liechtenstein                                                     </option><option value="LT"> Lithuania                                                             </option><option value="LU"> Luxembourg                                                           </option><option value="MO"> Macau                                                                     </option><option value="MK"> Macedonia                                                             </option><option value="MG"> Madagascar                                                           </option><option value="MW"> Malawi                                                                   </option><option value="MY"> Malaysia                                                               </option><option value="MV"> Maldives                                                               </option><option value="ML"> Mali                                                                       </option><option value="MT"> Malta                                                                     </option><option value="MH"> Marshall Islands                                              </option><option value="MQ"> Martinique</option><option value="MR"> Mauritania                                                           </option><option value="MU"> Mauritius                                                             </option><option value="YT"> Mayotte                                                                 </option><option value="MX"> Mexico                                                                   </option><option value="FM"> Micronesia                                                           </option><option value="MD"> Moldavia                                                               </option><option value="MC"> Monaco                                                                   </option><option value="MN"> Mongolia                                                               </option><option value="MS"> Montserrat                                                           </option><option value="MA"> Morocco                                                                 </option><option value="MZ"> Mozambique                                                           </option><option value="MM"> Myanmar                                                                 </option><option value="NA"> Namibia                                                                 </option><option value="NR"> Nauru                                                                     </option><option value="NP"> Nepal                                                                     </option><option value="NL"> Netherlands                                                         </option><option value="AN"> Netherlands Antilles                                      </option><option value="NT"> Neutral Zone                                                      </option><option value="NC"> New Caledonia</option><option value="NZ"> New Zealand                                                        </option><option value="NI"> Nicaragua                                                             </option><option value="NE"> Niger                                                                     </option><option value="NG"> Nigeria                                                                 </option><option value="NU"> Niue                                                                       </option><option value="NF"> Norfolk Island                                                  </option><option value="KP"> North Korea                                                        </option><option value="MP"> Northern Mariana Isls.</option><option value="NO"> Norway                                                                   </option><option value="OM"> Oman                                                                       </option><option value="PK"> Pakistan                                                               </option><option value="PW"> Palau                                                                     </option><option value="PA"> Panama                                                                   </option><option value="PG"> Papua New Guinea                                             </option><option value="PY"> Paraguay                                                               </option><option value="PE"> Peru                                                                       </option><option value="PH"> Philippines                                                         </option><option value="PN"> Pitcairn Island                                                </option><option value="PL"> Poland                                                                   </option><option value="PF"> Polynesia, French</option><option value="PT"> Portugal                                                               </option><option value="PR"> Puerto Rico                                                        </option><option value="QA"> Qatar                                                                     </option><option value="RE"> Reunion</option><option value="RO"> Romania                                                                 </option><option value="RU"> Russian Federation                                          </option><option value="RW"> Rwanda                                                                   </option><option value="GS"> S. Georgia &amp; S. Sandwich Isls.              </option><option value="SH"> Saint Helena                                                      </option><option value="KN"> Saint Kitts &amp; Nevis</option><option value="LC"> Saint Lucia                                                        </option><option value="PM"> Saint Pierre &amp; Miquelon</option><option value="ST"> Sao Tome &amp; Principe      </option><option value="VC"> Saint Vincent &amp; Grenadines                        </option><option value="WS"> Samoa                                                                     </option><option value="SM"> San Marino                                                          </option><option value="SA"> Saudi Arabia                                                      </option><option value="SN"> Senegal                                                                 </option><option value="SC"> Seychelles                                                           </option><option value="SL"> Sierra Leone                                                      </option><option value="SG"> Singapore                                                             </option><option value="SK"> Slovakia</option><option value="SI"> Slovenia                                                               </option><option value="SB"> Solomon Islands                                                </option><option value="SO"> Somalia                                                                 </option><option value="ZA"> South Africa                                                      </option><option value="KR"> South Korea                                                        </option><option value="ES"> Spain                                                                     </option><option value="LK"> Sri Lanka                                                            </option><option value="SD"> Sudan                                                                     </option><option value="SR"> Suriname                                                               </option><option value="SJ"> Svalbard &amp; Jan Mayen</option><option value="SZ"> Swaziland                                                             </option><option value="SE"> Sweden                                                                   </option><option value="CH"> Switzerland                                                         </option><option value="SY"> Syria                                                                     </option><option value="TJ"> Tadjikistan                                                         </option><option value="TW"> Taiwan                                                                   </option><option value="TZ"> Tanzania                                                               </option><option value="TH"> Thailand                                                               </option><option value="TG"> Togo                                                                       </option><option value="TK"> Tokelau                                                                 </option><option value="TO"> Tonga                                                                     </option><option value="TT"> Trinidad &amp; Tobago</option><option value="TN"> Tunisia                                                                 </option><option value="TR"> Turkey                                                                   </option><option value="TM"> Turkmenistan                                                       </option><option value="TC"> Turks &amp; Caicos Isls.</option><option value="TV"> Tuvalu                                                                   </option><option value="UM"> US Minor Outlying Isls.</option><option value="UG"> Uganda                                                                   </option><option value="UA"> Ukraine                                                                 </option><option value="AE"> United Arab Emirates                                     </option><option value="UK"> United Kingdom</option><option value="UY"> Uruguay</option><option value="UZ"> Uzbekistan                                                           </option><option value="VU"> Vanuatu                                                                 </option><option value="VE"> Venezuela                                                             </option><option value="VN"> Vietnam                                                                 </option><option value="VG"> Virgin Islands, British</option><option value="VI"> Virgin Islands, US</option><option value="WF"> Wallis &amp; Futuna Isls.</option><option value="EH"> Western Sahara                                                  </option><option value="YE"> Yemen                                                                     </option><option value="YU"> Yugoslavia                                                           </option><option value="ZR"> Zaire                                                                     </option><option value="ZM"> Zambia                                                                   </option><option value="ZW"> Zimbabwe                                                               </option></select><script type="text/javascript"> var panel_visibility_Array;
if(panel_visibility_Array==undefined)
{
	panel_visibility_Array=new Array();
}
var count=panel_visibility_Array.length;panel_visibility_Array[count]='Country_List_panel_visibility_Array';
var Country_List_panel_visibility_Array= []; var input_object_selection_Array;
if(input_object_selection_Array==undefined)
{
	input_object_selection_Array=new Array();
}
var count=input_object_selection_Array.length;input_object_selection_Array[count]='Country_List_input_object_selection_Array';
var Country_List_input_object_selection_Array= [["US"],"State_List"]; var input_Element_selection_Array;
if(input_Element_selection_Array==undefined)
{
	input_Element_selection_Array=new Array();
}
var count=input_Element_selection_Array.length;input_Element_selection_Array[count]='Country_Listinput_Element_selection_Array';
var Country_Listinput_Element_selection_Array= []; var input_object_disable_Array;
if(input_object_disable_Array==undefined)
{
	input_object_disable_Array=new Array();
}
var count=input_object_disable_Array.length;input_object_disable_Array[count]='Country_List_input_disable_selection_Array';
var Country_List_input_disable_selection_Array= [];</script></div></div></td>
</tr>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="DriverEdit_capState"><span title="">Driver license state:</span><span class="required" id="DriverEdit_capState_cptnSpn">*</span><br><select name="dataForm:State_List" id="dataForm:State_List" size="1" onchange="" tabindex="268"><option value=" " selected=""> (none)</option><option value="AA"> AA</option><option value="AE"> AE </option><option value="AK"> AK</option><option value="AL"> AL</option><option value="AP"> AP</option><option value="AR"> AR</option><option value="AS"> AS</option><option value="AZ"> AZ</option><option value="CA"> CA</option><option value="CO"> CO</option><option value="CT"> CT</option><option value="DC"> DC</option><option value="DE"> DE</option><option value="FL"> FL</option><option value="FM"> FM</option><option value="GA"> GA</option><option value="GU"> GU</option><option value="HI"> HI</option><option value="IA"> IA</option><option value="ID"> ID</option><option value="IL"> IL</option><option value="IN"> IN</option><option value="KS"> KS</option><option value="KY"> KY</option><option value="LA"> LA</option><option value="MA"> MA</option><option value="MD"> MD</option><option value="ME"> ME</option><option value="MH"> MH</option><option value="MI"> MI</option><option value="MN"> MN</option><option value="MO"> MO</option><option value="MP"> MP</option><option value="MS"> MS</option><option value="MT"> MT</option><option value="NC"> NC</option><option value="ND"> ND</option><option value="NE"> NE</option><option value="NH"> NH</option><option value="NJ"> NJ</option><option value="NM"> NM</option><option value="NV"> NV</option><option value="NY"> NY</option><option value="OH"> OH</option><option value="OK"> OK</option><option value="OR"> OR</option><option value="PA"> PA</option><option value="PR"> PR</option><option value="PW"> PW</option><option value="RI"> RI</option><option value="SC"> SC</option><option value="SD"> SD</option><option value="TN"> TN</option><option value="TX"> TX</option><option value="UT"> UT</option><option value="VA"> VA</option><option value="VI"> VI</option><option value="VT"> VT</option><option value="WA"> WA</option><option value="WI"> WI</option><option value="WV"> WV</option><option value="WY"> WY</option></select></div></div></td>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="lkpdrvexpdaid_1"><span title="">Driver license expiry:</span><span class="notRequired">p</span><br><input type="hidden" id="UsrZnOfset" value="-14400000" tabindex="269"><input type="text" id="dataForm:driverLicExp1" name="dataForm:driverLicExp1" value="7/7/26" alt="" size="" tabindex="270">&nbsp;<input type="image" title="Select date" style="cursor: pointer; border: 0px" align="absmiddle" id="trigger_dataForm:driverLicExp1" name="dataForm:driverLicExp1_trigger" alt="" src="/lps/resources/themes/icons/mablue/calendar.gif" tabindex="271" onclick="dataFormwdriverLicExp1calendarSetUp(this.name);return false">
<script type="text/javascript">
function dataFormwdriverLicExp1calendarSetUp(name)
{
var calT = new CalendarTimer( );calT.setup(
	{
		ifFormat    : "%m/%d/%y %H:%M",
		imgName      : name,		showsTime   :  false, 		timer  :  false, 		splitDate   :  false, 		showsTimeZone  :  "", 		edDropDown  :  "false", 		dropDownIns  :  ''
	}
)
}
</script>
</div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:driver_locgetlist23" name="dataForm:driver_locgetlist23" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:driver_locgetlist23','oncomplete':function(request,event,data){createDriver();return false;},'parameters':{'dataForm:driver_locgetlist23':'dataForm:driver_locgetlist23'} } );return false;" value="Create" type="button" tabindex="272"></td>
</tr>
</tbody>
</table>
</div></div></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("DriverDialog",{onClose:"",dialogClientId:"dataForm:DriverDialog",dragHandleId:"dataForm:driverIdOuter_dHId",onDialog:"",closeClientId:"dataForm:driverIdOuter_cCId"});
</script><div id="dataForm:SlotGrpDialog" name="dataForm:SlotGrpDialog" class="dialog_cont" style="height:400px;width:720px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:slotGrpIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Slot Group</div></td><td><div class="pop_close" id="dataForm:slotGrpIdOuter_cCId"><input type="button" tabindex="273"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;"><span id="dataForm:slotGrpA4J"><div id="slotGrpDivInner"><script language="JavaScript" src="/appointment/lookup/js/ILMSlotLookUp.js"></script><span id="dataForm:SlotGrpFilter_script"></span>

		<input type="hidden" name="slotGrpTextName" id="slotGrpTextName" tabindex="274"><div class="pnltopdiv" id="PANEL_SlotGrpFilter__Search_Panel1_top"><table id="PANEL_SlotGrpFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_SlotGrpFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:SlotGrpFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="slotGrpCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:slotGrpNameSearch" type="text" name="dataForm:slotGrpNameSearch" value="" tabindex="275"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:slotGrpFilter_locgetlist" name="dataForm:slotGrpFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotGrpFilter_locgetlist','parameters':{'dataForm:slotGrpFilter_locgetlist':'dataForm:slotGrpFilter_locgetlist'} } );return false;" value="Find " type="button" tabindex="276"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:slotGrpFilter_Detail"><div class="pnltopdiv" id="PANEL_slotGrpFilter_Find_Result_pannel_top"><div id="tr_slotGrpFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:slotGrpFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="slotGrpFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:slotGrpText" name="dataForm:slotGrpText" size="5" ondblclick="selectSlotGrp();return false;" style="width:90%" tabindex="277">	<option value="No matching Records">No Matching Records Found</option>
</select></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:slotGrpFilter_SelectButton" name="dataForm:slotGrpFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotGrpFilter_SelectButton','oncomplete':function(request,event,data){selectSlotGrp();return false;},'parameters':{'dataForm:slotGrpFilter_SelectButton':'dataForm:slotGrpFilter_SelectButton'} } );return false;" value="Select" type="button" tabindex="278"></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("SlotGrpDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:SlotGrpDialog",dragHandleId:"dataForm:slotGrpIdOuter_dHId",onDialog:"",closeClientId:"dataForm:slotGrpIdOuter_cCId"});
</script><div id="dataForm:SlotDialog" name="dataForm:SlotDialog" class="dialog_cont" style="height:400px;width:720px;"><div class="dialog_inner"><div class="pop -pdlg_dhbg"><div class="pop_sdw -pdlg_sdw"></div><table class="pop_tbl" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr -pdlg_dhbg"><table class="pop_hdr_inner" border="0" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="pop_hdr_img"><img src="/lps/resources/editControl/images/find.gif"></div></td><td class="pop_dragHandler" id="dataForm:slotIdOuter_dHId"><div class="pop_title -pdlg_dttc">Find Slot</div></td><td><div class="pop_close" id="dataForm:slotIdOuter_cCId"><input type="button" tabindex="279"></div></td></tr></tbody></table></div></td></tr><tr><td><div class="pop_bdr -pdlg_dhbg"><div class="pop_body -pdlg_dbg" style="height:300px;width:280px;"><span id="dataForm:slotA4J"><div id="slotDivInner"><script language="JavaScript" src="/appointment/lookup/js/ILMSlotLookUp.js"></script><span id="dataForm:SlotFilter_script"></span>

		<input type="hidden" name="slotTextName" id="slotTextName" tabindex="280"><div class="pnltopdiv" id="PANEL_SlotFilter__Search_Panel1_top"><table id="PANEL_SlotFilter__Search_Panel1headerdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc">Search Criteria</span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_SlotFilter__Search_Panel1" class="pnlcondiv"><table id="dataForm:SlotFilter_pg_1">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div class="caption" id="slotCap"><span title="">Search:</span><span class="notRequired">p</span><br><input id="dataForm:slotNameSearch" type="text" name="dataForm:slotNameSearch" value="" tabindex="281"></div></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:slotFilter_locgetlist" name="dataForm:slotFilter_locgetlist" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotFilter_locgetlist','parameters':{'dataForm:slotFilter_locgetlist':'dataForm:slotFilter_locgetlist'} } );return false;" value="Find " type="button" tabindex="282"></td>
</tr>
</tbody>
</table>
</div></div><span id="dataForm:slotFilter_Detail"><div class="pnltopdiv" id="PANEL_slotFilter_Find_Result_pannel_top"><div id="tr_slotFilter_Find_Result_pannel" class="pnlcondivhdr"><table id="dataForm:slotFilter__spg_1" width="100%">
<tbody>
<tr>
<td><div style="white-space:nowrap;vertical-align:inherit"><div id="slotFilter_SelectOneList_CC" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><select id="dataForm:slotText" name="dataForm:slotText" size="5" ondblclick="selectSlot();return false;" style="width:90%" tabindex="283">	<option value="No matching Records">No Matching Records Found</option>
</select></div></td>
</tr>
<tr>
<td><input class="btn" id="dataForm:slotFilter_SelectButton" name="dataForm:slotFilter_SelectButton" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:slotFilter_SelectButton','oncomplete':function(request,event,data){selectSlot();return false;},'parameters':{'dataForm:slotFilter_SelectButton':'dataForm:slotFilter_SelectButton'} } );return false;" value="Select" type="button" tabindex="284"></td>
</tr>
</tbody>
</table>
</div></div></span></div></span></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
UI8Layout.data.put("SlotDialog",{onClose:"UIFilterJS.clrParamFilterListPopup",dialogClientId:"dataForm:SlotDialog",dragHandleId:"dataForm:slotIdOuter_dHId",onDialog:"",closeClientId:"dataForm:slotIdOuter_cCId"});
</script><script language="JavaScript" xml:space="preserve">var shipment = false;
var PO = false;
var index;
var shipmentChanged=false;
var orderChanged=false;
var count = 0;
var deleteRow;
var checkAllRows;
var insertHere;
var checkDel;
var blindAppointment = false;
var toDeleteCount = 0;

function centerX(width)
{
	var winWidth = 1024;
	if( typeof( window.innerWidth ) == 'number' )
	{
		//Non-IE
		winWidth = window.innerWidth;
	}
	else if( document.documentElement &&
	( document.documentElement.clientWidth || document.documentElement.clientHeight ) )
	{
		//IE 6+ in 'standards compliant mode'
		winWidth = document.documentElement.clientWidth;
	}
	else if( document.body && ( document.body.clientWidth || document.body.clientHeight ) )
	{
		//IE 4 compatible
		winWidth = document.body.clientWidth;
	}
	//alert("screen width is: " + winWidth);
	return (winWidth - width)/2;
}

function centerY(height)
{
	var winHeight = 768;
	if( typeof( window.innerWidth ) == 'number' )
	{
		//Non-IE
		winHeight = window.innerHeight;
	}
	else if( document.documentElement &&
	( document.documentElement.clientWidth || document.documentElement.clientHeight ) )
	{
		//IE 6+ in 'standards compliant mode'
		winHeight = document.documentElement.clientHeight;
	}
	else if( document.body && ( document.body.clientWidth || document.body.clientHeight ) )
	{
		//IE 4 compatible
		winHeight = document.body.clientHeight;
	}

	//alert("screen height is: " + winHeight);
	return (winHeight - height)/2;
}

function shipmentDetails(shipmentPk,shipperId,entityType,displayType,screenType,formName,url)
{
	var shipper = eval(shipperId).value;
	if(screenType=="CARRIER_APPT_VIEW")
	{
		url="/ofr/ra/jsp/ShipmentDetails.jsp?previousPage=webTenders&shipmentId="+shipmentPk;
	}
	else if (screenType=="MAIN_VIEW")
	{
		url=url+"?shipmentId="+shipmentPk+"&Entity_Type="+entityType+"&Display_Type="+displayType
			+"&Screen_Type="+screenType;
	}
	else if (screenType=="SUPPLIER_APPT_VIEW")
	{
		 //url="/ofr/vcp/jsp/ViewShipmentDetails.jsp?theShipmentIdString="+shipmentPk+"&shipperId="+shipperId;
		url=url+"?shipmentId="+shipmentPk+"&Entity_Type="+entityType+"&Display_Type="+displayType
			+"&Screen_Type=MAIN_VIEW";
	}
	 document.forms[formName].action = url;
     document.forms[formName].submit();
}


function purchaseOrderDetails(orderId,shipperId,screenType,formName,url)
{
	var shipper = eval(shipperId).value;
	if(screenType=='SUPPLIER_APPT_VIEW')
	{
		//url='/ofr/VcpPurchaseOrderDetail.do?Entity_Type=VCP_PURCHASE_ORDER&Display_Type=DETAIL_VIEW&Screen_Type=MAIN_VIEW&orderId='+orderId;
		url='/cbo/transactional/purchaseorder/view/PODetail.xhtml?poId='+orderId;
	}
	else
	{
		//url='/ofr/ViewPODetail.do?Entity_Type=PURCHASEORDER&Display_Type=DETAIL_VIEW&Screen_Type=MAIN_VIEW&orderId='+orderId+'&shipperId='+shipper;
		url='/cbo/transactional/purchaseorder/view/PODetail.xhtml?poId='+orderId;
	}
	document.forms[formName].action = url;
     document.forms[formName].submit();
}

function asnDetails(asnId,shipperId,formName,url)
{
   var shipper = eval(shipperId).value;
   url='/cbo/transactional/asn/ASNDetails.jsflps?asnId='+asnId;
   //url='/cbo/ViewASNDetail.do?asnId='+asnId+'&shipperId='+shipper+'&Entity_Type=ASN&Display_Type=DETAIL_VIEW&Screen_Type=MAIN_VIEW';
   document.forms[formName].action = url;
     document.forms[formName].submit();
}

function showLookupEquipment(lookupType,permission,returnId,returnHiddenId,view_type,shipperId,urlString)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}

	eqCodeLkp = window.open(urlString + "?lookupType=Trailer Type&permission_code=DPTVIEW&returnId="+returnId+"&returnHiddenId="+returnHiddenId + vcpUrl,"EquipmentCodeLookup","width=480,height=220");
	eqCodeLkp.focus();
}

function showLookupEquipmentHFI(lookupType,permission,returnId,returnHiddenId,view_type,shipperId, title, urlString)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}
	var redirecturl = urlString + "?lookupType=Trailer Type&returnId="
	+returnId+"&returnHiddenId="+returnHiddenId + vcpUrl;
	/*eqCodeLkp = window.open(urlString + "?lookupType=Trailer Type&permission_code=DPTVIEW&returnId="
	+returnId+"&returnHiddenId="+returnHiddenId + vcpUrl,"EquipmentCodeLookup","width=480,height=220");
	eqCodeLkp.focus();*/
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,title);
}

function businessPartnerIdLookup_Appt(formName, obj, idFieldName, codeFieldName, shipperId, title, urlString)
{
	//alert("businessPartnerIdLookup(formName, obj, fieldName, shipperId, title, urlString)");
	if ( document.forms[formName][codeFieldName].length != null )
	{
		currentRowIndex = obj.parentElement.parentElement.rowIndex;
		window.businessPartnerId=document.forms[formName][idFieldName](currentRowIndex-1);
		window.businessPartnerCode=document.forms[formName][codeFieldName](currentRowIndex-1);
	}
	else
	{
		window.businessPartnerId=document.forms[formName][idFieldName];
		window.businessPartnerCode=document.forms[formName][codeFieldName];
	}
	/*businessPartner=window.open(urlString + "?returnCode=businessPartnerCode&returnId=businessPartnerId"
		+	"&lookupType=Business Partner&theShipperId=" + shipperId,"BusinessPartnerLookup",
			"width=480,height=220");
	businessPartner.focus();*/

	redirecturl = urlString + "?returnCode=businessPartnerCode&returnId=businessPartnerId"
		+	"&lookupType=Business Partner&theShipperId=" + shipperId;
	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);
}

function facilityIdLookupPopup_Appt(formName, obj, fieldName,idFieldName,codeFieldName,
										view_type,shipperId,urlString)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";

	//alert("facilityIdLookupPopup: fieldName="+fieldName);
	if ( document.forms[formName][fieldName].length != null )
	{
		currentRowIndex = obj.parentElement.parentElement.rowIndex;
		window.facilityId=document.forms[formName][fieldName](currentRowIndex-1);
		window.facilityPk=document.forms[formName][idFieldName](currentRowIndex-1);
	}
	else
	{
		window.facilityId=document.forms[formName][fieldName];
		window.facilityPk=document.forms[formName][idFieldName];
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}
	//window.open(urlString + "?returnId=facilityPk&returnCode=facilityId&lookupType=facility"
	//+ vcpUrl, 'Facility', 'width=480,height=240');
	openPopup(centerX(450),centerY(400),450,400, urlString + "?returnCode=facilityId&BusinesUnitSelected="
		+shipperId+"&returnId=facilityPk&lookupType=facility",'Find Facility');
}

function facilityIdLookupPopup_ApptWithTitle(formName, obj, fieldName,idFieldName,codeFieldName,
		view_type,shipperId,title,urlString)
{
var shipperId = eval(shipperId).value;
var vcpUrl = "";

//alert("facilityIdLookupPopup: fieldName="+fieldName);
if ( document.forms[formName][fieldName].length != null )
{
currentRowIndex = obj.parentElement.parentElement.rowIndex;
window.facilityId=document.forms[formName][fieldName](currentRowIndex-1);
window.facilityPk=document.forms[formName][idFieldName](currentRowIndex-1);
}
else
{
window.facilityId=document.forms[formName][fieldName];
window.facilityPk=document.forms[formName][idFieldName];
}
if(view_type=="SUPPLIER_APPT_VIEW")
{
vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
}
//window.open(urlString + "?returnId=facilityPk&returnCode=facilityId&lookupType=facility"
//+ vcpUrl, 'Facility', 'width=480,height=240');
openPopup(centerX(450),centerY(400),450,400, urlString + "?returnCode=facilityId&BusinesUnitSelected="
+shipperId+"&returnId=facilityPk&lookupType=facility",title);
}

function fieldChanged(field)
{

	if(field == "document.dataForm.shipmentNumber")
		shipmentChanged=true;

	if(field == "document.dataForm.purchaseOrder")
		orderChanged=true;

   if(field == "document.dataForm.asn")
		asnChanged=true;

}

function clearRecommendations()
{
	document.dataForm.checkInTime.value='';
	document.dataForm.checkInDuration.value='';
	document.dataForm.doorType.value='';
	document.dataForm.doorId.value='';
}

function disableRadioGroup (radioGroup) {
  if (!radioGroup.disabled) {
    radioGroup.disabled = true;
    if (document.all || document.getElementById) {
      if (!radioGroup.length)
        radioGroup.disabled = true;
      else
        for (var b = 0; b < radioGroup.length; b++)
          radioGroup[b].disabled = true;
     }
    else {
      if (!radioGroup.length) {
        radioGroup.storedChecked = radioGroup.checked;
        radioGroup.oldOnClick = radioGroup.onclick;
        radioGroup.onclick = preserveRadioGroup;
      }
      else
        for (var b = 0; b < radioGroup.length; b++) {
          radioGroup[b].storedChecked = radioGroup[b].checked;
          radioGroup[b].oldOnClick = radioGroup[b].onclick;
          radioGroup[b].onclick = preserveRadioGroup;
        }
    }
  }
}

function reject()
{


	document.dataForm.action = "/ilm/appointment/jsp/rejectAppointment.jsp?appointmentId="+document.dataForm.appointmentId.value;
	document.dataForm.submit();



}

function verifyForm(formName, shipmentStopMsg, POMsg, entityType, displayType,screentype,planning, urlString)
{
	var verifyFlag = false;
	var alreadyRaised = false;
	if (document.dataForm.appointmentObjectChoiceType[1].checked)
	{
		document.forms[formName].facilitySerialId.value = document.dataForm.pofacility.value;

		if(document.forms[formName].facilitySerialId.value == 'undefined')
		{
			document.forms[formName].facilitySerialId.value = document.dataForm.pofacility[0].value
		}
	}


	facility = document.forms[formName].facility.value;
	facilitySerialId = document.forms[formName].facilitySerialId.value;
	shipmentNumber = trim(document.forms[formName].shipmentNumber.value);
	shipmentSerialId = document.forms[formName].shipmentSerialId.value;
	stopNumber = trim(document.forms[formName].stopNumber.value);
//	purchaseOrder = document.forms[formName].purchaseOrder.value
	orderSerialId = document.forms[formName].orderSerialId.value;
//	alert("Order Serial Id is : " + orderSerialId);
	var purchaseOrder = new Array();
	var singlePOCheck = false;
	var mutliPOCheck = false;

	if (index >= 1)
	{
		for(i=0;i<document.forms[formName].purchaseOrder.length;i++)
			purchaseOrder[i] = trim(document.forms[formName].purchaseOrder[i].value);
		singlePOCheck = false;
		mutliPOCheck = true;
	}

//	alert("Purchase Order is : " + purchaseOrder);
	if(purchaseOrder.length == 0)
	{
		purchaseOrder = trim(document.forms[formName].purchaseOrder.value);
		singlePOCheck = true;
		mutliPOCheck = false;
	}
	if(((shipmentNumber == null || shipmentNumber.length == 0) || stopNumber.length == 0) &&
		shipment == true && PO == false)
	{
		alert(shipmentStopMsg);
	}
	else
	{
		verifyFlag = true;

		if((singlePOCheck == true || mutliPOCheck == true) && shipment == false && PO == true)
		{
			var flag = false;
			if(singlePOCheck == true)
			{
				if(purchaseOrder == "")
				{
					alert(POMsg);
					verifyFlag = false;
				}
				else
				{
					verifyFlag = true;
				}
			}
			else if(mutliPOCheck == true)
			{
				for(i=0;i<purchaseOrder.length;i++)
				{
					if(purchaseOrder[i]=="")
					{
						flag = true;
					}
					else
					{
						flag = false;
					}

					if(flag == true && alreadyRaised == false)
					{
						alert(POMsg);
						verifyFlag = false;
						alreadyRaised = true;
					}
/*					else
					{
						verifyFlag = true;
					}
 */
				}
			}
		}

		if(verifyFlag == true)
		{
			shipmentId = document.forms[formName].shipmentSerialId.value;
			facilityId = document.forms[formName].facilitySerialId.value;
			stopNumber = document.forms[formName].stopNumber.value;
			document.forms[formName].action = urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=APPTPRELOAD_VIEW&shipmentId=" + shipmentId+ "&facilityid=" + facilityId+ "&stopnumber=" + stopNumber+ "&preload=YES&preloadAction=NO&planning="+planning+"&saveFlag=VERIFY";
			document.forms[formName].submit();
		}
	}
}

function trim(string)
{
	var ch = string.substring(0, 1);
	while (ch == " ")
	{ // Check for spaces at the beginning of the string
		string = string.substring(1, string.length);
		ch = string.substring(0, 1);
	}

	ch = string.substring(string.length-1, string.length);
	while (ch == " ")
	{ // Check for spaces at the end of the string
		string = string.substring(0, string.length-1);
		ch = string.substring(string.length-1, string.length);
	}

	return string;
}
function clearAllFields(formName,shipmentNumber,PO)
{
	shipmentNumber.value="";
	PO.value="";
	document.forms[formName].appointmentObjectType.value="";
	document.forms[formName].appointmentObjectsId.value="";
	document.forms[formName].stopNumber.value="";
}

function submitApptData(formName,entityType, displayType,screentype, cancel_appt_msg, not_sel_reason_code_msg, confirmMessage, saveUpdateFlag,urlString)
{
	if(((saveUpdateFlag == 'EDITREFRESH') || (saveUpdateFlag == 'EDITRECURRING'))
		&& document.dataForm.appointmentStatus.value != 10
		&& document.forms[formName].cancelled != null
		&& document.forms[formName].cancelled.checked)
	{
		document.forms[formName].cancelled.checked = false;
		document.forms[formName].cancelReasonCode.options[0].selected = true;
		document.forms[formName].cancelReasonCode.disabled = true;
	}

     if(document.forms[formName].cancelled != null && document.forms[formName].cancelled.checked) {
         if(confirm(cancel_appt_msg))
         {
             var selectedReasonCode = cancelReasonCodeSelectionListener();
            if(selectedReasonCode=='')
            {
                alert(not_sel_reason_code_msg);
                return;
            }
               saveUpdateFlag = "CANCELAPPT";
         }
         else return;
     }

	if(saveUpdateFlag == "CREATESAVEBESTFIT")
    {
		//selected Save with Best fit, check if there are no PO and shipments selected
        //and question for Blind appointment if there are none.
		if("CARRIER_APPT_VIEW" == screentype)
		{
            if(!confirmBlindAppointmentForCarrier(formName. confirmMessage))
            {
                return;
            }
        }
		else
		{
			if(!confirmBlindAppointment(formName, confirmMessage))
            {
                return;
            }
		}

	}
    document.forms[formName].action = urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&USERACTION=" + saveUpdateFlag;
    document.forms[formName].submit();

}

function submitSupplierApptData(formName,vendor, entityType, displayType,screentype, cancel_appt_msg, not_sel_reason_code_msg,saveUpdateFlag,urlString)
{

	if(saveUpdateFlag == 'EDITREFRESH'
		&& document.dataForm.appointmentStatus.value != 10
		&& document.forms[formName].cancelled != null
		&& document.forms[formName].cancelled.checked)
	{
		document.forms[formName].cancelled.checked = false;
		document.forms[formName].cancelReasonCode.options[0].selected = true;
		document.forms[formName].cancelReasonCode.disabled = true;
	}

      // Changes for CR : 28859 (Implementing cancelling appointment on vendor side)
      if(document.forms[formName].cancelled != null && document.forms[formName].cancelled.checked) {
          // CR: 41560 (Selecting reason code on vendor side)
          if(confirm(cancel_appt_msg))
          {
             var selectedReasonCode = cancelReasonCodeSelectionListener();
             if(selectedReasonCode=='')
             {
                 alert(not_sel_reason_code_msg);
                 return;
             }
             saveUpdateFlag = "CANCELAPPT";
         }
         else return;
     }

	 document.forms[formName].action = urlString + "?Entity_Type=" + entityType + "&vendor=" + vendor + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&USERACTION=" + saveUpdateFlag;
     	document.forms[formName].submit();
}
//confirmBlindAppointment: returns true if user wishes to go for a blind appointment else returns false
function confirmBlindAppointment(formName, confirmMessage)
{
    //check if user has not selected any PO or shipment or ASN
	if(formName != null && document.forms[formName].tcPOId != null)
	{
		if ((document.forms[formName].tcPOId.length == undefined))
		{
			if((trim(document.forms[formName].tcPOId.value) == "")
				&& (trim(document.forms[formName].tcShipmentId.value) == "")
				&& (trim(document.forms[formName].tcASN.value) == ""))
			{

					if(confirm(confirmMessage))
				{
					 return true;
				}
				else
				{
					return  false;
				}
			}
		}
	    for(var i=0;i<document.forms[formName].tcPOId.length;i++)
	    {
	        if((trim(document.forms[formName].tcPOId[i].value) == "")
				&& (trim(document.forms[formName].tcShipmentId[i].value) == "")
				&& (trim(document.forms[formName].tcASN[i].value) == ""))
	            continue;
	        else
	            break;
	    }
	}
    if( formName != null && document.forms[formName].tcPOId != null && i == document.forms[formName].tcPOId.length )
    {
        //user has not selected shipment or PO or ASN
        //A popup message should display which says ?No PO/Shipment on appointment. Do you want to save it as Blind Appointment?.
        if(confirm(confirmMessage))
        {
            //If user clicks on ?Yes? ? Save appointment as blind appointment ? display appointment list with message ?Appointment XXXX successfully created.?
             return true;
        }
        else
        {
            return  false;
        }
    }
    else
    {
         //user has selected atleast a shipment or PO
        //so no need for blind appointment confirmation, return true
        return true;
    }
}

function confirmBlindAppointmentForCarrier(formName, confirmMessage)
{
    //check if user has not selected any PO or shipment
	if(formName != null && document.forms[formName].tcPOId != null)
	{
	    for(var i=0;i<document.forms[formName].tcPOId.length;i++)
	    {
	        if((trim(document.forms[formName].tcPOId[i].value) == "")
				&& (trim(document.forms[formName].tcShipmentId[i].value) == ""))
	            continue;
	        else
	            break;
	    }
	}
    if(formName != null && document.forms[formName].tcPOId != null && i == document.forms[formName].tcPOId.length )
    {
        //user has not selected shipment or PO
        //A popup message should display which says ?No PO/Shipment on appointment. Do you want to save it as Blind Appointment?.
        if(confirm(confirmMessage))
        {
            //If user clicks on ?Yes? ? Save appointment as blind appointment ? display appointment list with message ?Appointment XXXX successfully created.?
             return true;
        }
        else
        {
            return  false;
        }
    }
    else
    {
         //user has selected atleast a shipment or PO
        //so no need for blind appointment confirmation, return true
        return true;
    }
}

// see method carrierIdLookupPopupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function carrierIdLookupPopup(formName, obj,objid,carrierContactName,carrierContactPhone,view_type,shipperId, urlString)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";

	window.carrierId=document.forms[formName].obj;

	if(eval(obj).value !='')
	{
		var searchString = eval(obj).value;
	}
	else
	{
		var searchString='';
	}

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}

	//alert(urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
	//	+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString="
	//	+searchString+"&lookupType=Carrier","CarrierCodeLookup","width=480,height=220");
	var searchstr = document.dataForm.carrierCode.value;
	var buSelected = document.dataForm.BusinesUnitSelected.value;
	//CR 20035: 3PL changes sending a different lookup type so that on carrier code lookup
	//we get the carrier contact's name and tel number
	//will change from 'Carrier' to ILMCarrier
	//carrierCode=window.open(urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
	//	+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString="
	//	+ searchstr +"&lookupType=ILMCarrier" + vcpUrl,"CarrierCodeLookup","width=480,height=220");
	//carrierCode.focus();

	redirecturl = urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
		+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString=" + searchstr
		+"&lookupType=ILMCarrier&BusinesUnitSelected=" + buSelected  + vcpUrl;
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Carrier');

}

function carrierIdLookupPopupTitleGlobalized(formName, obj,objid,carrierContactName,carrierContactPhone,view_type,shipperId, title, urlString)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";

	window.carrierId=document.forms[formName].obj;

	if(eval(obj).value !='')
	{
		var searchString = eval(obj).value;
	}
	else
	{
		var searchString='';
	}

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		vcpUrl="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}

	//alert(urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
	//	+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString="
	//	+searchString+"&lookupType=Carrier","CarrierCodeLookup","width=480,height=220");
	var searchstr = document.dataForm.carrierCode.value;
	var buSelected = document.dataForm.BusinesUnitSelected.value;
	//CR 20035: 3PL changes sending a different lookup type so that on carrier code lookup
	//we get the carrier contact's name and tel number
	//will change from 'Carrier' to ILMCarrier
	//carrierCode=window.open(urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
	//	+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString="
	//	+ searchstr +"&lookupType=ILMCarrier" + vcpUrl,"CarrierCodeLookup","width=480,height=220");
	//carrierCode.focus();

	redirecturl = urlString + "?returnId="+obj+"&returnHiddenId="+objid+"&carrierContactName="
		+carrierContactName+"&carrierContactPhone="+carrierContactPhone+"&searchString=" + searchstr
		+"&lookupType=ILMCarrier&BusinesUnitSelected=" + buSelected  + vcpUrl;
	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);

}


function updateVerify(formName,entityType, displayType,screentype, urlString)
{
 // shipmentid = document.forms[formName].shipmentId.value
 document.forms[formName].action = urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&updateFlag=TRUE&preload=YES";
 document.forms[formName].submit();
}

function verifyRefreshForm(formName,entityType, displayType,screentype, vari, urlString)
{
 appointmentid = document.forms[formName].appointmentId.value;
 var urlParams = urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&updateFlag=TRUE&preload=YES" + "&refresh=1";
 if(appointmentid != null && appointmentid.length > 0)
 {
 	urlParams = urlParams + "&appointmentId="+appointmentid;
 }
 document.forms[formName].action = urlParams;
 document.forms[formName].submit();
}

function updateAppointment(formName,entityType, displayType,screentype, urlString)
{
  //shipmentid = document.forms[formName].shipmentId.value
 document.forms[formName].action = urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&updateFlag=TRUE&preload=YES";
 document.forms[formName].submit();
}

function showCalendar(obj, url)
{
	window.dateField = eval(obj);
	window.inputDateFormat='M/d/yy HH:mm';
	var calendar1 = window.open(url, "cal", "width=250,height=350");
	calendar1.focus();

}

function showDoorDuration(doorDuration)
{
	if(document.dataForm.checkInDuration.value != "")
		document.dataForm.doorDuration.value = document.dataForm.checkInDuration.value;
}

function showCheckInDuration(doorDuration)
{
	if(document.dataForm.doorDuration.value != "")
		document.dataForm.checkInDuration.value = document.dataForm.doorDuration.value;
}

function showDoorDate(checkInTime)
{
	if(document.dataForm.checkInTime.value != "")
		document.dataForm.doorStartDateTime.value = document.dataForm.checkInTime.value;
}

function showCheckInDate(doorStartDateTime)
{
	if(document.dataForm.doorStartDateTime.value != "")
		document.dataForm.checkInTime.value = document.dataForm.doorStartDateTime.value;
}
function checkShipment(obj)
{
    poelement = document.getElementsByName('purchaseOrder');
    if(typeof(poelement[0].value) != 'undefined' && poelement[0].value != null && poelement[0].value.length!=0)
	    clearErrors();
    for(cnt = 0; cnt < poelement.length; cnt++)
        poelement[cnt].value="";
	shipment = true;
	PO = false;

	document.dataForm.appointmentObjectChoiceType[0].checked = true;
	document.dataForm.orderSerialId.value = "";
	ShipmentDiv.style.display = "inline";
	PODivChild.style.display = "none";
	PODivChild1.style.display = "none";
}

var cachedErrorRows=null;
var cachedErrorTableCount=null;
var numberOfErrors=null;

function clearErrors() {

var tables = document.getElementsByTagName('table');
for ( cnt =0 ; cnt < tables.length; cnt++ ) {
    var rows = tables[cnt].getElementsByTagName('TR');
    if( typeof (rows) == 'undefined' || rows.length == 0) continue;
    var columns = rows[0].getElementsByTagName('td');
    if( typeof(columns)== 'undefined' || columns.length == 0) continue;
    if(columns[0].innerText == 'Type' && columns[1].innerText == 'Error'){
        cachedErrorTableCount = cnt;
        var length = tables[cnt].rows.length;
        numberOfErrors = length;
        cachedErrorRows = document.createDocumentFragment();
        for( j =0; j< length;j++ ) {
            cachedErrorRows.appendChild(tables[cnt].rows(j).cloneNode(true));
        }
        for( i = 0; i < length; i++) {
            tables[cnt].deleteRow();
        }
    }
}
}


function checkPO(obj)
{
	stopelement = document.getElementsByName('stopNumber');
    if(typeof(stopelement[0].value) != 'undefined' && stopelement[0].value != null && stopelement[0].value.length > 0)
        clearErrors();
	shipment = false;
	PO = true;

	document.dataForm.appointmentObjectChoiceType[1].checked = true;
	document.dataForm.shipmentSerialId.value = "";
	document.dataForm.shipmentNumber.value ="";
	document.dataForm.stopNumber.value = "";
	document.dataForm.facility.value = "";

	ShipmentDiv.style.display = "none";
	PODivChild.style.display = "inline";
	PODivChild1.style.display = "inline";
}

cachedErrorRows=null;
var cachedErrorTableCount=null;
var numberOfErrors=null;

function showLookupTrailer(lookuptype,obj,objidurl)

{
            //alert('Inside showLookupTrailer');
            var   currentRowIndex = obj.parentElement.parentElement.rowIndex;
            var redirecturl="";
            redirecturl = url + "?returnId=" + obj +  "&returnHiddenId=" + objid +"&lookupType=" + lookuptype +"&isArray=false&arrayIndex=-1";
            var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
            lookup.focus();
}

function checkBlind(obj)
{
	blindAppointment = true;
//	document.dataForm.blindCheck.checked = true;
	if(document.dataForm.appointmentObjectChoiceType)
	document.dataForm.appointmentObjectChoiceType[2].checked = true;
	SetFacilityId ( 'dataForm', 'document.dataForm.facility' );

	var appointmentId = trim(document.dataForm.appointmentId.value);
	if (appointmentId != "" )
	{
		document.dataForm.appointmentNumber.readOnly = true;
	}
}

function checkDetailEdit(obj)
{
/*	var appointmentId = trim(document.dataForm.appointmentId.value);

	if(appointmentId == "" )	// create
	{
		document.dataForm.appointmentNumber.readOnly = false;
		//document.dataForm.appointmentStatus.selectedIndex = 2;
		// Added for TT 32684

		var apptStatusLength = document.dataForm.appointmentStatus.length;

		var defApptStatusIndex;
        if(document.dataForm.appointmentStatus.options) {
    		for(i = 0; i<apptStatusLength ; i++)

	    	{
		    	if (document.dataForm.appointmentStatus.options[i].text == 'Scheduled')
        	    	defApptStatusIndex = i;
            }
        }
		document.dataForm.appointmentStatus.selectedIndex = defApptStatusIndex;


	}
	else if(appointmentId != "" )	//detail_edit
	{
		document.dataForm.appointmentNumber.readOnly = true;
	}

	var shipPOField = document.dataForm.appointmentObjectType.value;
	// added by Jayostu - In case of multiple POs the previous version was failing

	if(typeof document.dataForm.appointmentObjectType.length != 'undefined')
		shipPOField = eval(document.dataForm.appointmentObjectType[0]).value;
//	alert("SHIP OBJ TYPE IS : " + shipPOField);
	if(shipPOField == '30')	//Shipment
	{
		PODivEdit.style.display = "none";
		ShipmentDivEdit.style.display = "inline";
		if (document.dataForm.appointmentObjectChoiceType && document.dataForm.appointmentObjectChoiceType.length > 0) //[TT#36796  fix]
		{
			document.dataForm.appointmentObjectChoiceType[0].checked = true;
		}
	}
	else  if(shipPOField == '40')
	{
		PODivEdit.style.display = "inline";
		ShipmentDivEdit.style.display = "none";
		if (document.dataForm.appointmentObjectChoiceType && document.dataForm.appointmentObjectChoiceType.length > 1) //[TT#36796  fix]
		{
			document.dataForm.appointmentObjectChoiceType[1].checked = true;
		}
        	deleteRow = document.getElementById("delete");
        	checkAllRows = document.getElementById("checkAllDelete");
            if (deleteRow && deleteRow.style) //[TT#36796  fix]
            {
				deleteRow.style.display = "inline";
            }
			if (checkAllRows && checkAllRows.style) //[TT#36796  fix]
			{
                checkAllRows.style.display = "inline";
			}
	}
	else
	{
		PODivEdit.style.display = "inline";
		ShipmentDivEdit.style.display = "inline";
		if (document.dataForm.appointmentObjectChoiceType && document.dataForm.appointmentObjectChoiceType.length > 2) //[TT#36796  fix]
		{
			document.dataForm.appointmentObjectChoiceType[2].checked = true;
		}
	}
	if ( document.dataForm.appointmentObjectChoiceType )
	disableRadioGroup(document.dataForm.appointmentObjectChoiceType);
	*/
}

function checkDetail(obj)
{
/*	var shipPOField = document.dataForm.appointmentObjectType.value;
	if(eval(document.dataForm.appointmentObjectType[0]) != null)
		shipPOField = eval(document.dataForm.appointmentObjectType[0]).value;

	if (shipPOField==30)	//Shipment
	{
		PODivDetail.style.display = "none";
		ShipmentDivDetail.style.display = "inline";
	}
	else if(shipPOField==40)
	{
		PODivDetail.style.display = "inline";
		ShipmentDivDetail.style.display = "none";
	}
	else // blind appointment
	{
		PODivDetail.style.display = "inline";
		ShipmentDivDetail.style.display = "inline";
	}
	*/
}


function showTrailerLookup(lookuptype, obj, objid,license, state,carriercode,facilityId, equipment,view_type,shipperId,url)

{
	var searchStr = '';
	var validated = true;
	var facilityIdVar = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carriercodeVar;
	if(eval(carriercode) != null && eval(carriercode) != 'undefined')
	{
		carriercodeVar = eval(carriercode).value;
		var token = carriercodeVar.split(';');
		carriercodeVar = token[0];
	}
	var dock = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";
	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	if(view_type=='CARRIER_APPT_VIEW')
	{
		if(lookuptype=="driverName")
		{
			lookuptype='Driver';
		}
		url='/ofr/lookup/jsp/EquipmentLookup.jsp';
	}
	if(lookuptype == 'driverName')
	{
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert("Please provide a Carrier");
			return;
		}

		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" +carriercodeVar + "&searchString="+ searchStr + "&facilityId=" + facilityIdVar +"&license=" +license+"&state=" +state + "&driverName=";
	}
	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercodeVar + "&searchString="+ searchStr + "&facilityId=" + facilityIdVar +"&license=" +license+"&state=" +state + "&dock=";
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercodeVar + "&searchString="+ searchStr + "&facilityId=" + facilityIdVar +"&license=" +license+"&state=" +state + "&equipment=" + equipment + "&dock=";

	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state;
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state +"&equipment=" + equipment;

	}
	if(view_type=='CARRIER_APPT_VIEW')
	{
		redirecturl +='&theShipperId='+shipperId;
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{	// CR60928
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert("Please select a Carrier");
			return;
		}
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId + "&carrierCode=" +carriercodeVar;
	}
	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
//	lookup = window.open(url);
	lookup.focus();
}

// see method showDriverLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showDriverLookup(lookuptype, obj, objid,license, state,carriercode,facilityId,
							equipment,view_type,shipperId, provide_carrier_msg, select_carrier_msg,url)
{
	var searchStr = '';
	var validated = true;
	var facilityIdVar = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carriercodeVar;
	if(eval(carriercode) != null && eval(carriercode) != 'undefined')
	{
		carriercodeVar = eval(carriercode).value;
		var token = carriercodeVar.split(';');
		carriercodeVar = token[0];
	}
	var dock = "";

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
	{
		if(view_type != 'CARRIER_APPT_VIEW')
		{
			alert(provide_carrier_msg);
			return;
		}
	}

	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype
		+ "&carrierCode=" +carriercodeVar + "&searchString="+ searchStr + "&facilityId=" + facilityIdVar +"&license="
		+license+"&state=" +state + "&driverName=";

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert(select_carrier_msg);
			return;
		}
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId + "&carrierCode=" +carriercodeVar;
	}
	if(view_type == 'CARRIER_APPT_VIEW')
	{
		redirecturl = redirecturl + "&theShipperId="+shipperId;
	}
	//alert(redirecturl);
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Driver');
}

function showDriverLookupTitleGlobalized(lookuptype, obj, objid,license, state,carriercode,facilityId,
							equipment,view_type,shipperId, provide_carrier_msg, select_carrier_msg, title, url)
{
	var searchStr = '';
	var validated = true;
	var facilityIdVar = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carriercodeVar;
	if(eval(carriercode) != null && eval(carriercode) != 'undefined')
	{
		carriercodeVar = eval(carriercode).value;
		var token = carriercodeVar.split(';');
		carriercodeVar = token[0];
	}
	var dock = "";

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
	{
		if(view_type != 'CARRIER_APPT_VIEW')
		{
			alert(provide_carrier_msg);
			return;
		}
	}

	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype
		+ "&carrierCode=" +carriercodeVar + "&searchString="+ searchStr + "&facilityId=" + facilityIdVar +"&license="
		+license+"&state=" +state + "&driverName=";

	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert(select_carrier_msg);
			return;
		}
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId + "&carrierCode=" +carriercodeVar;
	}
	if(view_type == 'CARRIER_APPT_VIEW')
	{
		redirecturl = redirecturl + "&theShipperId="+shipperId;
	}
	//alert(redirecturl);
	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);
}

function showTractorLookupTitleGlobalized(lookuptype, select_carrier_msg, obj, objid,license, state,carriercode,facilityId,
			equipment,view_type,shipperId,title, url)

{
	//alert('appointment.js:in tractor lookup'+title);
	var searchStr = '';
	var validated = true;
	var facilityIdVar = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carriercodeVar;
	if(eval(carriercode) != null && eval(carriercode) != 'undefined')
	{
		carriercodeVar = eval(carriercode).value;
		var token = carriercodeVar.split(';');
		carriercodeVar = token[0];
	}
	var dock = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";
	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state;
	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state +"&equipment=" + equipment;

	if(view_type=='CARRIER_APPT_VIEW')
	{
		redirecturl +='&theShipperId='+shipperId ;
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert(select_carrier_msg);
			return;
		}
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId + "&carrierCode=" +carriercodeVar;
	}

	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);
}

// see method showTractorLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showTractorLookup(lookuptype, select_carrier_msg, obj, objid,license, state,carriercode,facilityId,
			equipment,view_type,shipperId,url)

{
	//alert('appointment.js:in tractor lookup');
	var searchStr = '';
	var validated = true;
	var facilityIdVar = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carriercodeVar;
	if(eval(carriercode) != null && eval(carriercode) != 'undefined')
	{
		carriercodeVar = eval(carriercode).value;
		var token = carriercodeVar.split(';');
		carriercodeVar = token[0];
	}
	var dock = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";
	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state;
	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCodeVar=" + carriercode + "&searchString=" +searchStr + "&facilityId=" + facilityIdVar+"&license=" +license+"&state=" +state +"&equipment=" + equipment;

	if(view_type=='CARRIER_APPT_VIEW')
	{
		redirecturl +='&theShipperId='+shipperId ;
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(carriercodeVar == '' || typeof carriercodeVar == 'undefined' )
		{
			alert(select_carrier_msg);
			return;
		}
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId + "&carrierCode=" +carriercodeVar;
	}

	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Tractor');
}

function showLookup(lookuptype, obj, objid,license, state,carriercode,facilityId, equipment,url)
{

	var validated = true;
	var facilityId = eval(facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";

	if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=*"  + "&facilityId=" + facilityId +"&license=" +license+"&state=" +state + "&dock=";
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=*"  + "&facilityId=" + facilityId +"&license=" +license+"&state=" +state + "&equipment=" + equipment + "&dock=";

	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +facilityId + "&facilityId=" + facilityId+"&license=" +license+"&state=" +state;
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +facilityId + "&facilityId=" + facilityId+"&license=" +license+"&state=" +state +"&equipment=" + equipment;

	}
//	alert(redirecturl);
	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
//	lookup = window.open(url);
	lookup.focus();
}

function showTrailerByStatus(formName,lookuptype, obj, objid,license, state,carriercode,facilityId, equipment,appointmentType1,url)

{

	var appointmentType = document.dataForm.appointmentType.value;
	var validated = true;
	var facilityId = eval(facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var redirecturl = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";

	if(lookuptype != 'yard')
	{
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=*"  + "&facilityId=" + facilityId +"&license=" +license+"&state=" +state + "&equipment=" + equipment + "&appointmentType=" + appointmentType; + "&dock=";

	}
	else
	{
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +facilityId + "&facilityId=" + facilityId+"&license=" +license+"&state=" +state +"&equipment=" + equipment + "&appointmentType=" + appointmentType;

	}
	//redirecturl += "&emptyAndPartiallyLoaded=true";
//	alert(redirecturl);
	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
//	lookup = window.open(url);
	lookup.focus();
}

// see method showOffYardTrailerTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showOffYardTrailer(lookuptype, obj, objid,license, state,carriercode,
			carrierId, facilityId, equipment, equipmentId, view_type,shipperId,url)
{
	//alert('in showOffYardTrailer');
	var validated = true;
	var searchStr = '';
	var facilityId = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carrier = carriercode;
	var carrierName='';
	if(eval(carriercode) != null && eval(carriercode).value !='undefined'){

		carrierName = eval(carriercode).value;
	}

	var dock = "";
	var redirecturl = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;
	if(view_type=="CARRIER_APPT_VIEW")
	{
		if(lookuptype=="trailerNumber")
		{
			lookuptype='Trailer';
		}
		// forwarding to carrier equipment lookup
		url='/ofr/lookup/jsp/EquipmentLookup.jsp';
	}

	if(lookuptype != 'yard')
	{
		//CR 27830: No Trailer found message is displaying on page load in New Appointment page.
		if(lookuptype == 'trailerNumber')
		{
			if(searchStr == '')
			{
				searchStr = searchStr + "*";
			}
          lookuptype = "AllTrailers"   ;
        }
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carrierName + "&carrierId="+carrierId + "&searchString=" + searchStr + "&facilityId=" + facilityId +"&license=" +license+"&state=" +state + "&equipment=" + equipment + "&dock="+"&equipmentId="+equipmentId;

	}
	else
	{
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carrierName +"&searchString=" +facilityId + "&facilityId=" + facilityId+"&license=" +license+"&state=" +state +"&equipment=" + equipment ;

	}
	// Fix for CR : 36091  To populate carrier value if not entered.
	redirecturl += "&checkStatus=true&offYard=true&carrierValue="+carrier;
	if(view_type=="CARRIER_APPT_VIEW")
	{
		redirecturl+="&theShipperId="+shipperId;
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}

	//alert(redirecturl);
    //var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//lookup = window.open(url);
	//lookup.focus();
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Trailer');
}


function showOffYardTrailerTitleGlobalized(lookuptype, obj, objid,license, state,carriercode,
			carrierId, facilityId, equipment, equipmentId, view_type,shipperId, title, url)
{
	var validated = true;
	var searchStr = '';
	var facilityId = eval(facilityId).value;
	var shipperId = eval(shipperId).value;

	var carrier = carriercode;
	var carrierName='';
	if(eval(carriercode) != null && eval(carriercode).value !='undefined'){

		carrierName = eval(carriercode).value;
	}

	var dock = "";
	var redirecturl = "";
	if (lookuptype == 'Dock Door')
		dock = "document.dataForm.doorType";

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;
	if(view_type=="CARRIER_APPT_VIEW")
	{
		if(lookuptype=="trailerNumber")
		{
			lookuptype='Trailer';
		}
		// forwarding to carrier equipment lookup
		url='/ofr/lookup/jsp/EquipmentLookup.jsp';
	}

	if(lookuptype != 'yard')
	{
		//CR 27830: No Trailer found message is displaying on page load in New Appointment page.
		if(lookuptype == 'trailerNumber')
		{
			if(searchStr == '')
			{
				searchStr = searchStr + "*";
			}
          lookuptype = "AllTrailers"   ;
        }
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carrierName + "&carrierId="+carrierId + "&searchString=" + searchStr + "&facilityId=" + facilityId +"&license=" +license+"&state=" +state + "&equipment=" + equipment + "&dock="+"&equipmentId="+equipmentId;

	}
	else
	{
		redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carrierName +"&searchString=" +facilityId + "&facilityId=" + facilityId+"&license=" +license+"&state=" +state +"&equipment=" + equipment ;

	}
	// Fix for CR : 36091  To populate carrier value if not entered.
	redirecturl += "&checkStatus=true&offYard=true&carrierValue="+carrier;
	if(view_type=="CARRIER_APPT_VIEW")
	{
		redirecturl+="&theShipperId="+shipperId;
	}
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		redirecturl+="&isVCP=true&theShipperId="+shipperId+"&BusinesUnitSelected="+shipperId;
	}

	//alert(redirecturl);
    //var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//lookup = window.open(url);
	//lookup.focus();
	openPopup(centerX(300),centerY(300),300,300, redirecturl, title);
}

function DoorType(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg, cannot_lookup_dock_msg, provide_dockId_msg,url)
{
	var dockDoor =trim(document.dataForm.dockDoor.value);
	var dockDoorId=document.dataForm.dockDoorId.value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	//alert('facilityIdVar inside door type--- ' + facilityIdVar);
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		alert(provide_facilityId_msg);
		return;
	}
	if((((dockDoor)!='') && (dockDoorId!='')) && (lookuptype=='Dock'))
	{
		alert(cannot_lookup_dock_msg);

	}
	else
	{
		if(dockDoor=='')
		{
			document.dataForm.dockDoorId.value=null;
			showDLookup(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg, provide_dockId_msg, url)
		}
	}


}

// see method showDockLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showDockLookup(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg, cannot_lookup_dock_msg, provide_dockId_msg, url)
{
	var dockDoor =trim(document.dataForm.dockDoor.value);
	var dockDoorId=document.dataForm.dockDoorId.value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	//alert('facilityIdVar inside door type--- ' + facilityIdVar);
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		alert(provide_facilityId_msg);
		return;
	}
	if((((dockDoor)!='') && (dockDoorId!='')) && (lookuptype=='Dock'))
	{
		alert(cannot_lookup_dock_msg);

	}
	else
	{
		if(dockDoor=='')
		{
			document.dataForm.dockDoorId.value=null;
			doDockLookup(lookuptype, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, url)
		}
	}


}

function showDockLookupTitleGlobalized(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg, cannot_lookup_dock_msg, provide_dockId_msg, title, url)
{
	var dockDoor =trim(document.dataForm.dockDoor.value);
	var dockDoorId=document.dataForm.dockDoorId.value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	//alert('facilityIdVar inside door type--- ' + facilityIdVar);
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		alert(provide_facilityId_msg);
		return;
	}
	if((((dockDoor)!='') && (dockDoorId!='')) && (lookuptype=='Dock'))
	{
		alert(cannot_lookup_dock_msg);

	}
	else
	{
		if(dockDoor=='')
		{
			document.dataForm.dockDoorId.value=null;
			doDockLookupTitleGlobalized(lookuptype, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, title, url)
		}
	}


}

function doDockLookupTitleGlobalized(lookuptype, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, title, url)
{
	var validated = true;
	var searchStr = '';
	var labelStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var dockId="";
	var urlAppend="";
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Dock Door')
	{
		if (document.dataForm.dockDoor != undefined &&
		      document.dataForm.dockDoor.readOnly == true )
		{
		   return ;
		}
        	dock = "document.dataForm.dockAssigned";
		dockId="document.dataForm.dockId";
		urlAppend="&dock=" + dock + "&dockId=" +dockId ;
		//alert(" url---- " + urlAppend);
		var dockVar = eval(document.dataForm.dockAssigned).value;
		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(dockVar == '' || typeof dockVar == 'undefined' )
		{
			alert(provide_dockId_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	if (searchStr == '')
			searchStr = '*';

	if(lookuptype == 'Dock' || lookuptype == 'Dock Door')
	{
		//alert('url----'+ url);
		var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
		+ lookuptype + urlAppend + "&carrierCode=" + carriercode + "&searchString=" + searchStr
		+ "&dockValue="+dockVar;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	//var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//lookup = window.open(url);
	//lookup.focus();

	if(lookuptype == 'Dock')
	{
		labelStr = 'Find Dock';
	}
	else if(lookuptype == 'Dock Door')
	{
		labelStr = 'Find Dock Door';
	}
	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);

}

// see the method doDockLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function doDockLookup(lookuptype, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, url)
{
	var validated = true;
	var searchStr = '';
	var labelStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var dockId="";
	var urlAppend="";
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Dock Door')
	{
		if (document.dataForm.dockDoor != undefined &&
		      document.dataForm.dockDoor.readOnly == true )
		{
		   return ;
		}
        	dock = "document.dataForm.dockAssigned";
		dockId="document.dataForm.dockId";
		urlAppend="&dock=" + dock + "&dockId=" +dockId ;
		//alert(" url---- " + urlAppend);
		var dockVar = eval(document.dataForm.dockAssigned).value;
		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(dockVar == '' || typeof dockVar == 'undefined' )
		{
			alert(provide_dockId_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;

	if(lookuptype == 'Dock' || lookuptype == 'Dock Door')
	{
		//alert('url----'+ url);
		var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
		+ lookuptype + urlAppend + "&carrierCode=" + carriercode + "&searchString=" + searchStr
		+ "&dockValue="+dockVar;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	//var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//lookup = window.open(url);
	//lookup.focus();

	if(lookuptype == 'Dock')
	{
		labelStr = 'Find Dock';
	}
	else if(lookuptype == 'Dock Door')
	{
		labelStr = 'Find Dock Door';
	}
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,labelStr);

}

function showSlotGroupLookupGlobalized(lookuptype, indexobj, obj, objid, carriercode,facilityId,provide_facilityId_msg, cannot_lookup_dock_msg, provide_dockId_msg, title, url)
{


		var slotGrp = trim(document.dataForm.slotGroup.value);
		var slotGrpId = document.dataForm.slotGroup.value;
		var facilityIdVar = eval(document.dataForm.facilityId).value;

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
			alert(provide_facilityId_msg);
			return;
		}

		doSlotGroupLookup(lookuptype, indexobj, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, title, url)

}


function doSlotGroupLookup(lookuptype, indexobj, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg, title, url)
{

	var validated = true;
	var searchStr = '';
	var labelStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var slotGroup = "";
	var slotGroupId="";
	var urlAppend="";
	var	index  =      indexobj.parentElement.parentElement.rowIndex -1 ;
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId

	/*

	if(eval(obj).value != null && eval(obj).value != 'undefined')
               searchString = eval(obj).value;
            else
				searchString = eval(obj)[index].value;

				*/

	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}


	var slotGrpVar = eval(document.dataForm.slotGroup).value;
	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value != null && eval(obj).value != 'undefined')
		searchStr= eval(obj).value;
	else
	   searchString = eval(obj)[index].value;

	if(lookuptype == 'Slot Group' )
	{

		if(eval(obj).value !=null && eval(obj).value != 'undefined')
			var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
			+ lookuptype + urlAppend + "&carrierCode=" + carriercode + "&searchString=" + searchStr
			+ "&slotGroupValue="+slotGrpVar;
		else
			var redirecturl =  url + "?returnId=" + obj + "[" + index +  "]" + "&returnHiddenId=" + objid +  "[" + index +  "]" + "&lookupType="
			+ lookuptype + urlAppend + "&carrierCode=" + carriercode + "&searchString=" + searchStr
			+ "&slotGroupValue="+slotGrpVar;



	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}


	if(lookuptype == 'Slot Group')
	{
		labelStr = title;
	}

	openPopup(centerX(300),centerY(300),300,300, redirecturl ,labelStr);


}

function showDLookup(lookuptype, obj, objid,carriercode,facilityId, provide_facilityId_msg, provide_dockId_msg,url)
{
	var validated = true;
	var searchStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var dockId="";
	var urlAppend="";
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Dock Door')
	{
		if (document.dataForm.dockDoor != undefined &&
		      document.dataForm.dockDoor.readOnly == true )
		{
		   return ;
		}
        	dock = "document.dataForm.dockAssigned";
		dockId="document.dataForm.dockId";
		urlAppend="&dock=" + dock + "&dockId=" +dockId ;
		//alert(" url---- " + urlAppend);
		var dockVar = eval(document.dataForm.dockAssigned).value;
		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(dockVar == '' || typeof dockVar == 'undefined' )
		{
			alert(provide_dockId_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;


	if(lookuptype == 'Dock' || lookuptype == 'Dock Door')
	{
		//alert('url----'+ url);
		var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
		+ lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr+urlAppend
		+ "&dockValue="+dockVar;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//	lookup = window.open(url);
	lookup.focus();

}

function showDockDoorLookupTitleGlobalized(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg,provide_dockId_msg, title, url)
{
	var validated = true;
	var searchStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var dockId="";
	var urlAppend="";
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Dock Door')
	{
		if (document.dataForm.dockDoor != undefined &&
		      document.dataForm.dockDoor.readOnly == true )
		{
		   return ;
		}
        	dock = document.dataForm.dockAssigned.value;
		dockId="document.dataForm.dockId";
		urlAppend="&dockVal=" + dock + "&dockId=" +dockId ;
		//alert(" url---- " + urlAppend);
		var dockVar = eval(document.dataForm.dockAssigned).value;
		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(dockVar == '' || typeof dockVar == 'undefined' )
		{
			alert(provide_dockId_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;


	if(lookuptype == 'Dock' || lookuptype == 'Dock Door')
	{
		//alert('url----'+ url);
		var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
		+ lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr+urlAppend
		+ "&dock="+dockVar;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	//var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//	lookup = window.open(url);
	//lookup.focus();
	openPopup(centerX(300),centerY(300),300,300, redirecturl , title);
}

// see method showDockDoorLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showDockDoorLookup(lookuptype, obj, objid,carriercode,facilityId,provide_facilityId_msg,provide_dockId_msg, url)
{
	var validated = true;
	var searchStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var dock = "";
	var dockId="";
	var urlAppend="";
	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Dock Door')
	{
		if (document.dataForm.dockDoor != undefined &&
		      document.dataForm.dockDoor.readOnly == true )
		{
		   return ;
		}
        	dock = document.dataForm.dockAssigned.value;
		dockId="document.dataForm.dockId";
		urlAppend="&dockVal=" + dock + "&dockId=" +dockId ;
		//alert(" url---- " + urlAppend);
		var dockVar = eval(document.dataForm.dockAssigned).value;
		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(dockVar == '' || typeof dockVar == 'undefined' )
		{
			alert(provide_dockId_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value !=null && eval(obj).value !="")
		searchStr= eval(obj).value;


	if(lookuptype == 'Dock' || lookuptype == 'Dock Door')
	{
		//alert('url----'+ url);
		var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
		+ lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr+urlAppend
		+ "&dock="+dockVar;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	//var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//	lookup = window.open(url);
	//lookup.focus();
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Dock Door');
}

function showSlotLookupGlobalized(lookuptype, indexobj, obj, objid,carriercode,facilityId,provide_facilityId_msg,provide_SlotGroup_msg, title, url)
{
	var validated = true;
	var searchStr = '';
	//var facilityIdVar = eval(facilityId).value;
	var facilityIdVar = eval(document.dataForm.facilityId).value;
	var carriercode = eval(carriercode).value;
	var slotGrp = "";
	var slotGrpId="";
	var urlAppend="";
	var	index  =      indexobj.parentElement.parentElement.rowIndex -1 ;

	//this condition is added for Blind Appointment by default first facility is selected.
	//Setting here the FacilitySerialId
	if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
	{
		if (typeof document.dataForm.blindfacilityId != 'undefined')
		{
			facilityIdVar=document.dataForm.blindfacilityId.options[document.dataForm.blindfacilityId.selectedIndex].value;
			eval(facilityId).value=facilityIdVar;
		}
	}

	//these Lines are Commented for Lookup's:-vinayak

	if (lookuptype == 'Slot')
	{
		if (document.dataForm.slotNumber != undefined &&
		      document.dataForm.slotNumber.readOnly == true )
		{
		   return ;
		}

		if(eval(obj).value !=null && eval(obj).value != 'undefined')
		{
        	slotGrp = document.dataForm.slotGroup.value;
			slotGrpId="document.dataForm.slotGroupId";
			urlAppend="&slotGrpVal=" + slotGrp + "&slotGrpId=" +slotGrpId ;
		}else
		{
			slotGrp = eval(document.dataForm.slotGroup)[index].value;
			slotGrpId="document.dataForm.slotGroupId"+ "["  + index + "]" ;
			urlAppend="&slotGrpVal=" + slotGrp + "&slotGrpId=" +slotGrpId ;
		}

		//alert(" url---- " + urlAppend);

		//alert('dockvar--'+dockVar);

		if(facilityIdVar == '' || typeof facilityIdVar == 'undefined' )
		{
		alert(provide_facilityId_msg);
		return;
		}

		if(slotGrp == '' || typeof slotGrp == 'undefined' )
		{
			alert(provide_SlotGroup_msg);
			return;
		}

	}

	if (typeof eval(facilityId) != 'undefined')
	{
		urlAppend=urlAppend+"&facilityAlias="+document.dataForm.facilityId.value;
	}

	if(eval(obj).value != null && eval(obj).value != 'undefined')
		searchStr= eval(obj).value;
	else
	   searchString = eval(obj)[index].value;


	if(lookuptype == 'Slot Group' || lookuptype == 'Slot')
	{
		//alert('url----'+ url);
		if(eval(obj).value !=null && eval(obj).value != 'undefined')
			var redirecturl =  url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType="
			+ lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr+urlAppend;
		else
			var redirecturl =  url + "?returnId=" + obj + "[" + index +  "]" + "&returnHiddenId=" + objid +  "[" + index +  "]" + "&lookupType="
			+ lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr+urlAppend;
		//alert("redirecturl for dock.."+redirecturl);
	}

	else if(lookuptype != 'yard')
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" + searchStr + "&facilityId=" + eval(facilityId).value +urlAppend;
	}
	else
	{
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +searchStr  + "&facilityId=" + eval(facilityId).value;
	}
	//alert("Dock.."+redirecturl);
	//alert("returnHiddenId..."+objid);
	//var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	//	lookup = window.open(url);
	//lookup.focus();
	openPopup(centerX(300),centerY(300),300,300, redirecturl ,title);
}

function clearDockDoor(dockdoor)
{
	//alert("Coming iside this method");
	document.dataForm.dockDoor.value="";
	document.dataForm.dockDoorId.value="";
}

function showDLookupForFacility(lookuptype, obj, objid,carriercode,facilityId, url)
{
	var validated = true;
	//var facilityId = eval(facilityId).options[eval(facilityId).selectedIndex].value;
	var facilityId = eval(facilityId).value;
	var carriercode = eval(carriercode).value;
	var objValue = eval(obj).value;
	var dock = "";


	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&carrierCode=" + carriercode + "&searchString=" +objValue + "&facilityId=" + facilityId;

	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
	lookup.focus();

}

function showFacilityLookup(lookuptype, obj, objid, url)

{

	var objValue = eval(obj).value;

	if(objValue != null || objValue != "*")

	{

		fac = objValue.split(',');

		objValue = fac[0];

	}



	var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&lookupType=" + lookuptype + "&searchString=" +objValue ;

	var lookup = window.open(redirecturl, "lookup", "width=350,height=450");

	lookup.focus();

}

function checkCreate(tst)
{
	document.dataForm.appointmentObjectChoiceType[1].checked = true;
	var obj = null;
	//The field is checked here as this field is not coming from blind appointment view properly
	if (document.all.ApptTypeChecked)   //[TT #33622 fix]
	   obj = document.all.ApptTypeChecked.value;
	if (obj=='' || obj==null)
	{
		obj = tst;
	}
	if (obj == 'PO')
	{

		shipment = false;
		PO = true;
		checkPO(obj);
	}
	else
	{
		shipment = true;
		PO = false;
		checkShipment(obj);
	}
	deleteRow = document.getElementById("delete");
	checkAllRows = document.getElementById("checkAllDelete");
	if (typeof document.all.purchaseOrder.length != 'undefined')
		count = document.all.purchaseOrder.length - 1;
	if ((count == 0) || (typeof count == 'undefined'))
	{
		deleteRow.style.display = "none";
		checkAllRows.style.display = "none";
	}
}

function showApptLookup(lookup_Type,obj, objid, index, url)
{
	if(lookup_Type=="SHIPMENT")
	{

		if(eval(obj).value !='')
		{
			var searchString=eval(obj).value;
		}
		else
		{
			var searchString='';
		}

		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index+"&lookupType=" + lookup_Type + "&searchString="+searchString;
		if(obj == 'document.dataForm.lookupShipmentNumber')
			redirecturl = redirecturl + '&onChangeFunction=cleanStopFieldOnly';
		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
	else if(lookup_Type=="ORDER")
	{
		//var indexType = document.dataForm.lookupOrder.length;
		var indexType = eval(obj).length;
		if (typeof indexType =='undefined')
		{
			if(eval(obj).value !='')
			{
				var searchString = eval(obj).value;
			}
			else
			{
				var searchString='';
			}

		}
		else
		{

			if(eval(obj)[index].value !='')
			{
				var searchString =eval(obj)[index].value ;
			}
			else
			{
				var searchString='';
			}
		}
//		alert(obj);
//		alert(objid);
//		alert(index);
//		alert(searchString);

		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= "
			+ index + "&lookupType=" + lookup_Type + "&searchString="+searchString;

		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
}

	function cleanStopField(){
  		//document.dataForm.stopNumber.value = '';
  		//document.dataForm.facilitySerialId.value = '';
  		//document.dataForm.facility.value = '';
	}
	function cleanStopFieldOnly(){
  		document.dataForm.stopNumber.value = '';
	}

// see method showASNLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showASNLookup(lookup_Type,obj, index, view_type,shipperId, url)
{
	index = index.parentElement.parentElement.rowIndex;
	index = index -1;
	var searchString ='';
	var objStr = '';
	if(index == 0)
	{
			if(eval(obj).value != null && eval(obj).value != 'undefined')
			{
				searchString = eval(obj).value;
				objStr = "" + obj;
			}
			else
			{
				searchString = eval(obj)[index].value;
				objStr = "" + obj +"[" + index + "]";
			}
	}
	else if(index > 0)
	{
			searchString = eval(obj)[index].value;
			objStr = "" + obj +"[" + index + "]";
	}
	var  redirecturl = url + "?returnId="  + objStr +
		  "&indexValue= " + index+ "&searchString="+searchString + "&lookupType=ASN";

	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find ASN');
}

function showASNLookupTitleGlobalized(lookup_Type,obj, index, view_type,shipperId, title, url)
{
	index = index.parentElement.parentElement.rowIndex;
	index = index -1;
	var searchString ='';
	var objStr = '';
	if(index == 0)
	{
			if(eval(obj).value != null && eval(obj).value != 'undefined')
			{
				searchString = eval(obj).value;
				objStr = "" + obj;
			}
			else
			{
				searchString = eval(obj)[index].value;
				objStr = "" + obj +"[" + index + "]";
			}
	}
	else if(index > 0)
	{
			searchString = eval(obj)[index].value;
			objStr = "" + obj +"[" + index + "]";
	}
	var  redirecturl = url + "?returnId="  + objStr +
		  "&indexValue= " + index+ "&searchString="+searchString + "&lookupType=ASN";

	openPopup(centerX(300),centerY(300),300,300, redirecturl, title);
}

function showAppointmentLookupTitleGlobalized(lookup_Type,obj, objid, index, view_type,shipperId, title, url)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(lookup_Type == "SHIPMENT")
        {
            vcpUrl="&lookupType=Vcp Shipment&isVCP=true";
        }
        else if(lookup_Type == "ORDER")
        {
            vcpUrl="&lookupType=Master Order&isVCP=true&permission_code=VVP";
        }
    }

	if(lookup_Type=="SHIPMENT")
	{
        index = index.parentElement.parentElement.rowIndex;
        index = index -1;
		var searchString ='';
		pos = document.getElementsByName('tcShipmentId');
		lookup_Type ="Shipment";
        if(index ==0)
		{
            if(eval(obj).value != null && eval(obj).value != 'undefined')
               searchString = eval(obj).value;
            else
				searchString = eval(obj)[index].value;
        }else if(index > 0) {
				searchString = eval(obj)[index].value;
		}
        url = '/cbo/transactional/lookup/idLookup.jsp';

        if(view_type=="CARRIER_APPT_VIEW")
		{
			// forwarding to carrier shipment look up
			url = '/ofr/lookup/jsp/shipmentLookup.jsp';
		}
		var redirecturl = "";
		if(pos.length==1)
		{
			redirecturl = url + "?returnId=" + obj + "&indexValue= "
			+ index+ "&searchString="+searchString ;
		}
		else
		{
			redirecturl = url + "?returnId="  + obj +"[" + index + "]"
				+ "&indexValue= " + index+ "&searchString="+searchString;
		}
		// fix for CR : 31376
		if(view_type=="CARRIER_APPT_VIEW")
		{
			if(pos.length==1)
			{
				redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= "
				+ index+ "&searchString="+searchString + "&permission_code=VSH";
			}
			else
			{
				redirecturl = url + "?returnId="  + obj +"[" + index + "]" +"&returnHiddenId="
					+ objid + "&indexValue= " + index+ "&searchString="+searchString + "&permission_code=VSH";
			}

			redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
		}
        else if(view_type=="SUPPLIER_APPT_VIEW")
             redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField&theShipperId='+shipperId + '&isVCP=true';
       else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
        if(view_type=="CARRIER_APPT_VIEW")
        {
        	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Shipment');
        	//var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
			//lookup.focus();
		}
		else
		{
 			openPopup(centerX(300),centerY(300),300,300, redirecturl , title);
		}
	}
	else if(lookup_Type=="ORDER")
	{
		index = index.parentElement.parentElement.rowIndex;
        index = index -1;
        pos = document.getElementsByName('tcPOId');
        if(pos[index].value !='')
        {
            var searchString =pos[index].value ;
        }
        else
        {
            var searchString='';
        }
        url = ' /ilm/lookup/jsp/apptPOLookup.jsp';
        if(view_type=="SUPPLIER_APPT_VIEW")
			url = '/cbo/transactional/lookup/idLookup.jsp';

        var redirecturl = "";
       if(pos.length==1)
			redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index  + "&searchString="+searchString +
                        "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=Master Order";
		else
			redirecturl = url + "?returnId=" + obj +"[" + index + "]" + "&returnHiddenId=" + objid + "&indexValue= " + index + "&searchString="+searchString +
                          "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=Master Order";
        if(view_type=="SUPPLIER_APPT_VIEW")
                      redirecturl = redirecturl + '&onChangeFunction=cleanStopField&theShipperId='+shipperId + '&isVCP=true';
        else  if(view_type=="CARRIER_APPT_VIEW")
        	redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField&carrierPortal=1&isVCP=true&theShipperId='+shipperId;
        else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
        //alert('redirecturl + vcpUrl:'+ redirecturl + vcpUrl);
        //var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
		openPopup(centerX(300),centerY(300),300,300, redirecturl ,title);
		//lookup.focus();
	}
   else if(lookup_Type=="ASN")
   {
      index = index.parentElement.parentElement.rowIndex;
        index = index -1;
        asns = document.getElementsByName('field32value1');
        if(asns[index].value !='')
        {
            var searchString =asns[index].value ;
        }
        else
        {
            var searchString='';
        }
        url = '/cbo/transactional/lookup/idLookup.jsp';

        var redirecturl = "";
       if(asns.length==1)
			redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index  + "&searchString="+searchString +
                        "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VASN&lookupType=ASN";
		else
			redirecturl = url + "?returnId=" + obj +"[" + index + "]" + "&returnHiddenId=" + objid + "&indexValue= " + index + "&searchString="+searchString +
                          "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=ASN";
        if(view_type=="SUPPLIER_APPT_VIEW")
            redirecturl = redirecturl + '&onChangeFunction=cleanStopField&theShipperId='+shipperId;
        else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
        //alert('redirecturl + vcpUrl:'+ redirecturl + vcpUrl);
        var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
		lookup.focus();
   }
}

// see method showAppointmentLookupTitleGlobalized for any popup page title globalization issue.
// just pass the globalized string as the value for the title arg there.
function showAppointmentLookup(lookup_Type,obj, objid, index, view_type,shipperId, url)
{
	var shipperId = eval(shipperId).value;
	var vcpUrl = "";
	if(view_type=="SUPPLIER_APPT_VIEW")
	{
		if(lookup_Type == "SHIPMENT")
        {
            vcpUrl="&lookupType=Vcp Shipment&isVCP=true";
        }
        else if(lookup_Type == "ORDER")
        {
            vcpUrl="&lookupType=Master Order&isVCP=true&permission_code=VVP";
        }
    }

	if(lookup_Type=="SHIPMENT")
	{
        index = index.parentElement.parentElement.rowIndex;
        index = index -1;
		var searchString ='';
		pos = document.getElementsByName('tcShipmentId');
		lookup_Type ="Shipment";
        if(index ==0)
		{
            if(eval(obj).value != null && eval(obj).value != 'undefined')
               searchString = eval(obj).value;
            else
				searchString = eval(obj)[index].value;
        }else if(index > 0) {
				searchString = eval(obj)[index].value;
		}
        url = '/cbo/transactional/lookup/idLookup.jsp';

        if(view_type=="CARRIER_APPT_VIEW")
		{
			// forwarding to carrier shipment look up
			url = '/ofr/lookup/jsp/shipmentLookup.jsp';
		}
		var redirecturl = "";
		if(pos.length==1)
		{
			redirecturl = url + "?returnId=" + obj + "&indexValue= "
			+ index+ "&searchString="+searchString + "&permission_code=VSH";
		}
		else
		{
			redirecturl = url + "?returnId="  + obj +"[" + index + "]"
				+ "&indexValue= " + index+ "&searchString="+searchString
				+ "&permission_code=VSH";
		}
		// fix for CR : 31376
		if(view_type=="CARRIER_APPT_VIEW")
		{
			if(pos.length==1)
			{
				redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= "
				+ index+ "&searchString="+searchString + "&permission_code=VSH";
			}
			else
			{
				redirecturl = url + "?returnId="  + obj +"[" + index + "]" +"&returnHiddenId="
					+ objid + "&indexValue= " + index+ "&searchString="+searchString + "&permission_code=VSH";
			}

			redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
		}
        else if(view_type=="SUPPLIER_APPT_VIEW")
             redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField&theShipperId='+shipperId;
       else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
        if(view_type=="CARRIER_APPT_VIEW")
        {
        	openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Shipment');
        	//var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
			//lookup.focus();
		}
		else
		{
 			openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Shipment');
		}
	}
	else if(lookup_Type=="ORDER")
	{
		index = index.parentElement.parentElement.rowIndex;
        index = index -1;
        pos = document.getElementsByName('tcPOId');
        if(pos[index].value !='')
        {
            var searchString =pos[index].value ;
        }
        else
        {
            var searchString='';
        }
        url = ' /ilm/lookup/jsp/apptPOLookup.jsp';

        var redirecturl = "";
       if(pos.length==1)
			redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index  + "&searchString="+searchString +
                        "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=Master Order";
		else
			redirecturl = url + "?returnId=" + obj +"[" + index + "]" + "&returnHiddenId=" + objid + "&indexValue= " + index + "&searchString="+searchString +
                          "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=Master Order";
        if(view_type=="SUPPLIER_APPT_VIEW")
            redirecturl = redirecturl + '&onChangeFunction=cleanStopField&theShipperId='+shipperId;
        else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';

		 if(view_type=="CARRIER_APPT_VIEW")
           {
           	redirecturl = redirecturl + '&objectType=CarrierPO';
           }

        //alert('redirecturl + vcpUrl:'+ redirecturl + vcpUrl);
        //var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
		openPopup(centerX(300),centerY(300),300,300, redirecturl ,'Find Purchase Order');
		//lookup.focus();
	}
   else if(lookup_Type=="ASN")
   {
      index = index.parentElement.parentElement.rowIndex;
        index = index -1;
        asns = document.getElementsByName('field32value1');
        if(asns[index].value !='')
        {
            var searchString =asns[index].value ;
        }
        else
        {
            var searchString='';
        }
        url = '/cbo/transactional/lookup/idLookup.jsp';

        var redirecturl = "";
       if(asns.length==1)
			redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index  + "&searchString="+searchString +
                        "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VASN&lookupType=ASN";
		else
			redirecturl = url + "?returnId=" + obj +"[" + index + "]" + "&returnHiddenId=" + objid + "&indexValue= " + index + "&searchString="+searchString +
                          "&LookupBusinessUnit=ALL&LookupBusinessUnitSelect=ALL&permission_code=VPO&lookupType=ASN";
        if(view_type=="SUPPLIER_APPT_VIEW")
            redirecturl = redirecturl + '&onChangeFunction=cleanStopField&theShipperId='+shipperId;
        else
            redirecturl = redirecturl + '&lookupType=' + lookup_Type +'&onChangeFunction=cleanStopField';
        //alert('redirecturl + vcpUrl:'+ redirecturl + vcpUrl);
        var lookup = window.open(redirecturl + vcpUrl, "lookup", "width=350,height=450");
		lookup.focus();
   }
}

function showNewStopLookup(lookup_Type, obj, objid, objalias, url)
{
	if(lookup_Type=="STOP")
	{

		var shipmentId = document.dataForm.lookupShipmentNumber.value;
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid +  "&returnAliasId=" + objalias + "&indexValue=-1&lookupType=" + lookup_Type + "&searchString=&shipmentId=" + shipmentId;
		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
}
function showStopLookup(lookup_Type, obj, objid, objalias, url)
{
	if(lookup_Type=="STOP")
	{

		var shipmentId = document.dataForm.shipmentNumber.value;
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid +  "&returnAliasId=" + objalias + "&indexValue=-1&lookupType=" + lookup_Type + "&searchString=&shipmentId=" + shipmentId;
		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
}
function showStopLookupEdit(lookup_Type, obj, objid, objalias, url)
{
	if(lookup_Type=="STOP")
	{
		var shipmentId = document.dataForm.shipmentNumber.value;
		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid +  "&returnAliasId=" + objalias + "&indexValue=-1&lookupType=" + lookup_Type + "&searchString=*&shipmentId=" + shipmentId;
		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
}

function refreshPlanning(obj, url)
{


	var checkInDate = document.dataForm.checkInTime.value;



		if(isValidateDate(checkInDate) == false)
		{
			alert("Date Time entered has Invalid Format");
			return false;

		}
		else
		{
			document.dataForm.action = url;
			document.dataForm.submit();
			return true;
		}











}

function savePlanning(obj, url)
{
	var checkInDate = document.dataForm.checkInTime.value;
	document.dataForm.planning.value = "SAVE";
	document.dataForm.action = url;
	document.dataForm.submit();

}
function toggleBooleanStringField(formName, obj, fieldName)
{
	currentForm=document.forms[formName];
	currentElement=currentForm[fieldName];

	if ( currentElement.length != null )
	{

		currentRowIndex = obj.parentElement.parentElement.rowIndex;
		if (currentElement[currentRowIndex -1].value == 'true')
		{

			currentElement[currentRowIndex -1].value = 'false';
		}
		else
		{

			currentElement[currentRowIndex -1].value = 'true';
		}
	}
	else
	{

		if (currentElement.value == 'true')
		{

			currentElement.value = 'false';
		}
		else
		{

			currentElement.value = 'true';
		}
	}
}

function redirectPage(formName,url)
{

	if(document.dataForm.appointmentObjectChoiceType[0].checked)
	{
		if(document.dataForm.shipmentPOFlag != undefined)
		{
			document.dataForm.shipmentPOFlag.value = "SHIPMENT";
		}
	}
	else if(document.dataForm.appointmentObjectChoiceType[1].checked)
	{
		if(document.dataForm.shipmentPOFlag != undefined)
		{
			document.dataForm.shipmentPOFlag.value = "PO";
		}
	}
	document.forms[formName].action = url;
	document.forms[formName].submit();
}

function createNewAppointment(formName,url)
{
	document.forms[formName].action = url;
	document.forms[formName].submit();
}

function clickDetect(button)
{
	var whichButton = button;
	if(whichButton == "save")
		document.dataForm.saveUpdateFlag.value="SAVEUPDATE";
	else if(whichButton == "refresh")
		document.dataForm.saveUpdateFlag.value="refresh";
}

function checkAtLeastOneValue_AptsApprove(arrchecked,arr,valForCheck)
{
	 var len = arr.length;
	 var i=0;
	// alert('arr.length='+len);
	 for( i=0 ; i<len ; i++)
	 {
		//alert('arr[i].value='+arr[i].value+',valForCheck='+valForCheck);
		if(arrchecked[i].checked && arr[i].value != valForCheck){
			//atleast one value in the array is expected value ..
			return false;
		}
	 }
	 return true ;
}

function approveList(formName, appointmentStatus,selectMsg,scheduleMsg, apptStatMsg, urlString)
{
	var temp = document.getElementsByName( "appointmentStatus" );
	var temp1 = document.getElementsByName( "appointmentId" );
	//Check that at least one selected appointment has status 'Requested'
	// so that a form submit can proceed.

	if( temp1[0].value == 1 || checkForSelections_Apts( 'appointmentId' ) )
	{


		if( checkAtLeastOneValue_AptsApprove(temp1,temp,'Requested')
			|| checkAtLeastOneValue_AptsApprove(temp1,temp,'17')
			|| document.dataForm.appointmentStatus.value == 17 )
		{
			if( confirm(scheduleMsg) )
			{
			 	document.forms[formName].action = urlString+'?USERACTION=APPROVEAPPTS' ;
			 	document.forms[formName].submit();
			}
		}
		else
		{
			alert(apptStatMsg);
		}
	}
	else
	{
		alert(selectMsg);
	}
}

function approveListSupp(formName, appointmentStatus,selectMsg,scheduleMsg, apptStatMsg, urlString)
{

	var temp =document.getElementsByName("appointmentStatus");
	var temp1 = document.getElementsByName( "editFlag" );

	//Check that at least one selected appointment has status 'Countered'
	// so that a form submit can proceed.

	if( temp1[0].value == 1 || checkForSelections_Apts( 'appointmentId' ) )
	{
		if( checkAtLeastOneValue_Apts_Supp(temp,'Countered', 'appointmentId')
			|| checkAtLeastOneValue_Apts_Supp(temp,'15', 'appointmentId')
			|| document.dataForm.appointmentStatus.value == 15 )
		{
			if( confirm(scheduleMsg) )
			{
			 	document.forms[formName].action = urlString+'?USERACTION=APPROVEAPPTS' ;
			 	document.forms[formName].submit();
			}
		}
		else
		{
			alert(apptStatMsg);
		}
	}
	else
	{
		alert(selectMsg);
	}

}

function approveListCarr(formName, appointmentStatus,selectMsg,scheduleMsg, apptStatMsg, urlString)
{

	var temp =document.getElementsByName("appointmentStatus");
	var temp1 = document.getElementsByName( "editFlag" );

	//Check that at least one selected appointment has status 'Countered'
	// so that a form submit can proceed.

	if( temp1[0].value == 1 || checkForSelections_Apts( 'appointmentIdCarr' ) )
	{
		if( checkAtLeastOneValue_Apts_Carr(temp,'Countered','appointmentIdCarr')
			|| checkAtLeastOneValue_Apts_Carr(temp,'15','appointmentIdCarr')
			|| document.dataForm.appointmentStatus.value == 15 )
		{
			if( confirm(scheduleMsg) )
			{
			 	document.forms[formName].action = urlString+'?USERACTION=APPROVEAPPTS' ;
			 	document.forms[formName].submit();
			}
		}
		else
		{
			alert(apptStatMsg);
		}
	}
	else
	{
		alert(selectMsg);
	}

}


function checkAtLeastOneValue_Apts_Carr(arr,valForCheck, checkBoxName)
{
	var checkboxObj = document.getElementsByName( checkBoxName );
	var checkboxLen = checkboxObj.length;
	var len = arr.length;
	var i=0;

	for( i=0 ; i<len ; i++)
	{
		//alert('arr[i].value='+arr[i].value+',valForCheck='+valForCheck);

		if(arr[i].value == valForCheck)
		{
			//atleast one value in the array is expected value ..
			if(checkboxLen > 1)
			{
				if(checkboxObj[i].checked)
				{
					return true;
				}
			}
			else if(checkboxLen == 1)
			{
				if(checkboxObj[0].checked)
				{
					return true;
				}
			}

		}
	 }
	 return false ;
}

function showLevellingListAnalysis(formName, selectMsg, maxAppointmentsMsg, statusMsg,appointmentTypeMsg, urlString)
{
	var temp 		= document.getElementsByName( "appointmentStatus" );
	var checkboxObj = document.getElementsByName( "appointmentId" );

	var appointmentTypeVar = document.getElementsByName("appointmentTypeVal");

	if(checkForSelections_Apts( 'appointmentId' ) )
	{

		var numberOfAppts = getSelectionsCnt_Apts('appointmentId');
		if(numberOfAppts <= 5)
		{
			var len = temp.length;
			var i=0;
			//alert('arr.length='+len);
			for( i=0 ; i<len ; i++)
			{

				if( checkboxObj[i].checked == true )
				{
					if(!(temp[i].value == 3 || temp[i].value == 5))
					{
						alert(statusMsg);
						return false;
					}
				}
			}

			if(checkAtLeastOneValue_Apts_Checked(appointmentTypeVar,'Live Unload', 'appointmentId')
				|| checkAtLeastOneValue_Apts_Checked(appointmentTypeVar,'Drop Unload', 'appointmentId')
				|| checkAtLeastOneValue_Apts_Checked(appointmentTypeVar,'10', 'appointmentId')
				|| checkAtLeastOneValue_Apts_Checked(appointmentTypeVar,'20', 'appointmentId')
				|| (document.dataForm.appointmentType != null
				&& document.dataForm.appointmentType != 'undefined'
				&& (document.dataForm.appointmentType.value=='10'
				|| document.dataForm.appointmentType.value=='20')))
			{
				document.forms[formName].action = urlString;
				document.forms[formName].submit();
				return true;
			}
			else
			{
				alert(appointmentTypeMsg);
			}
		}
		else
		{
			alert(maxAppointmentsMsg);
		}
	}
	else
	{
		alert(selectMsg);
	}
	return false;
}

function showLevellingAnalysisForDetail(formName, statusMsg, appointmentTypeMsg, urlString)
{
	var temp =document.getElementsByName("appointmentStatus");
	var len = temp.length;
	var i=0;
	//alert('arr.length='+len);
	for( i=0 ; i<len ; i++)
	{
		//alert('temp[i].value='+temp[i].value);
		if(!(temp[i].value == 3 || temp[i].value == 5))
		{
			alert(statusMsg);
			return false;
		}

	 }
	var appointmentTypeVar = document.getElementsByName("appointmentTypeVal");

	if(checkAtLeastOneValue_Apts(appointmentTypeVar,'Live Unload')||checkAtLeastOneValue_Apts(appointmentTypeVar,'Drop Unload')||
            checkAtLeastOneValue_Apts(appointmentTypeVar,'10')||checkAtLeastOneValue_Apts(appointmentTypeVar,'20')||
               (document.dataForm.appointmentType !=null && document.dataForm.appointmentType !='undefined' &&
					(document.dataForm.appointmentType.value=='10' || document.dataForm.appointmentType.value=='20')))
	{
		document.forms[formName].action = urlString;
		document.forms[formName].submit();
		return true;
	}
	else
	{
		alert(appointmentTypeMsg);
	}
	return false;
}

function approveFromDetails(formName,scheduleMsg,urlString)
{
    if( confirm(scheduleMsg) )
    {
        document.forms[formName].action = urlString+'?USERACTION=APPROVE' ;
        document.forms[formName].submit();
    }
}

function approveFromDetailsForVendor(formName,scheduleMsg,urlString)
{
    if( confirm(scheduleMsg) )
    {
        document.forms[formName].action = urlString+'?USERACTION=APPROVE' ;
        document.forms[formName].submit();
    }
}

//This function name is duplicate hence a new function was created below.
function checkForSelections(checkBoxName)
{
	 var selected1 = false;
	 var checkboxObj = document.getElementsByName( checkBoxName );
	 len = checkboxObj.length;
	 var i=0;
	 for( i=0 ; i<len ; i++)
	 {

	  if(checkboxObj[i].checked == true)
	  {
	   selected1 = true ;
	  }
	  if(selected1)
	  {
	   break ;
	  }
	 }

	 return selected1 ;
}


// _Apts (Appointments abbr) gives namespace kinda uniqueness to function names
// which can avoid havoc caused by duplicate functions with generic name.

function checkForSelections_Apts(checkBoxName)
{
	 var selected1 = false;
	 var checkboxObj = document.getElementsByName( checkBoxName );
	 len = checkboxObj.length;
	 var i=0;
	 for( i=0 ; i<len ; i++)
	 {

	  if(checkboxObj[i].checked == true)
	  {
	   selected1 = true ;
	  }
	  if(selected1)
	  {
	   break ;
	  }
	 }

	 return selected1 ;
}

function getSelectionsCnt_Apts(checkBoxName)
{
	 var checkboxObj = document.getElementsByName( checkBoxName );
	 len = checkboxObj.length;
	 var i=0;
	 var cnt=0;
	 for( i=0 ; i<len ; i++)
	 {

	  if(checkboxObj[i].checked == true)
	  {
	  	cnt++;
	  }

	 }

	 return cnt ;
}


//_Apts gives namespace kinda uniqueness to function names which can avoid duplicate function with generic name.
function checkAtLeastOneValue_Apts(arr,valForCheck)
{
	 var len = arr.length;
	 var i=0;
	 //alert('arr.length='+len);
	 for( i=0 ; i<len ; i++)
	 {
		//alert('arr[i].value='+arr[i].value+',valForCheck='+valForCheck);

		if(arr[i].value != valForCheck){
			//atleast one value in the array is expected value ..
			return false;
		}
	 }
	 return true ;
}

function checkAtLeastOneValue_Apts_Supp(arr,valForCheck, checkBoxName)
{
	var checkboxObj = document.getElementsByName( checkBoxName );
	var len = arr.length;
	var i=0;
	 //alert('arr.length='+len);
	 for( i=0 ; i<len ; i++)
	 {
		//alert('arr[i].value='+arr[i].value+',valForCheck='+valForCheck);

		if(arr[i].value == valForCheck){
			//atleast one value in the array is expected value ..
			if(checkboxObj[i].checked == true)
			{
				return true;
			}
		}
	 }
	 return false ;
}

function checkAtLeastOneValue_Apts_Checked(arr,valForCheck, checkBoxName)
{
	var checkboxObj = document.getElementsByName( checkBoxName );
	var len = arr.length;
	var i=0;
	var returnvalue = false;
	 //alert('arr.length='+len);
	 for( i=0 ; i<len ; i++)
	 {
		if(checkboxObj[i].checked == true)
		{
			if((arr[i].value == valForCheck))
			{
				returnvalue = true;
			}
			else
			{
				returnvalue = false;
				//break if any selected appointment fails.
				break;
			}
		}
	 }
	 return returnvalue ;
}

function editApptData(formName,obj,entityType, displayType,screentype,saveUpdateFlag,urlString)
{
	//alert("IN EDIT"+saveUpdateFlag+"type.."+obj);
	PODivChild.style.display = "none";
	ShipmentDiv.style.display = "none";

	if(document.dataForm.appointmentObjectChoiceType[0].checked)
	{
		ShipmentDiv.style.display = "inline";
	}
	else if(document.dataForm.appointmentObjectChoiceType[1].checked)
	{
		PODivChild.style.display = "inline";
		document.forms[formName].facilitySerialId.value = document.dataForm.pofacility.value;
	}

	shipmentCheck = document.forms[formName].shipmentCheck.checked;
	poCheck = document.forms[formName].poCheck.checked;
	facility = document.forms[formName].facility.value;
	facilitySerialId = document.forms[formName].facilitySerialId.value;
	shipmentNumber = document.forms[formName].shipmentNumber.value;
	shipmentSerialId = document.forms[formName].shipmentSerialId.value;
	stopNumber = document.forms[formName].stopNumber.value;
	purchaseOrder = document.forms[formName].purchaseOrder.value;
	orderSerialId = document.forms[formName].orderSerialId.value;
	saveUpdateFlag = document.forms[formName].saveUpdateFlag.value;
//	alert(orderSerialId);
	if(facility == "" && facilitySerialId == "" && shipmentNumber == "" //&& shipmentSerialId == ""
			&& stopNumber == "" && purchaseOrder == "" && orderSerialId == "")
		alert("Please enter Facility, Shipment/PO/Stop fields.");
/*	else if(facilitySerialId == "")//facility == "" ||
	{
		alert("Please enter Facility fields.");
	} */
	else if((shipmentNumber == "" ) && (shipment == true && PO == false ))//|| shipmentSerialId == ""
	{
		alert("Please enter Shipment fields.");
	}
	else if(stopNumber == "" && shipment == true && PO == false )
	{
		alert("Please enter Stop Number field.");
	}
	else if((orderSerialId == "") && (shipment == false && PO == true))//purchaseOrder == "" ||
	{
		alert("Please enter Purchase Order fields.");
	}
	else
	{
		 document.forms[formName].action =urlString + "?Entity_Type=" + entityType + "&Display_Type=" + displayType + "&Screen_Type=" + screentype + "&saveUpdateFlag="+saveUpdateFlag+ "&shipmentCheck="+shipmentCheck ;
		 document.forms[formName].submit();
	}

}

function SetFacilityId(formName,facility)
{
	var facilityId=document.forms[formName].blindfacilityId.value;
	var index=document.forms[formName].blindfacilityId.selectedIndex;
	var facility=document.forms[formName].blindfacilityId.options[index].text;
	document.forms[formName].facilitySerialId.value=facilityId;
}




function disableAllOthers(formName, obj, fieldName)
{

	toggleBooleanStringField(formName, obj, fieldName);
	checkForRecurring(formName);
}

function checkForRecurring(formName)
{
	if(document.forms[formName].recurring.checked==false)
	{
		enableDisable(formName,true);

	}
	else
	{
		enableDisable(formName,false);
	}
}

function enableDisable(formName,status)
{
	document.forms[formName].daily.disabled=status;
	document.forms[formName].weekly.disabled=status;
	document.forms[formName].sunday.disabled=status;
	document.forms[formName].monday.disabled=status;
	document.forms[formName].tuesday.disabled=status;
	document.forms[formName].wednesday.disabled=status;
	document.forms[formName].thursday.disabled=status;
	document.forms[formName].friday.disabled=status;
	document.forms[formName].saturday.disabled=status;
	document.forms[formName].monthly.disabled=status;
	document.forms[formName].StartDate.disabled=status;
	document.forms[formName].EndDate.disabled=status;
	//document.forms[formName].every.disabled=status;
	if (document.forms[formName].recurring.checked==false)
		document.forms[formName].every.disabled=status;
}

function goToCalendar(formName,invalidDateMsg,urlString)
{
	var dateField=eval(document.forms[formName].checkInTime);
    	var shipmentNumber= eval(document.forms[formName].shipmentNumber);
	var PO=eval(document.forms[formName].purchaseOrder);
	var duration = document.dataForm.checkInDuration.value;
	if(isNaN(duration))
	{
		alert("Duration Should be a Number");
		return;
	}
	if(duration.indexOf('.') != -1)
	{
		alert("Duration cannot have fractional value");
		return;
	}
	if(shipmentNumber != null && PO != null)
	{
		var multiPO= eval(PO[0]);
		if(multiPO != null)
			PO=multiPO;
		if(shipmentNumber.value != "" && PO.value != "")
		{
			alert("Please select either Shipment or Purchase Order");
			clearAllFields(formName,shipmentNumber,PO);
			return;
		}
		else if (shipmentNumber.value != "")
		{
			document.forms[formName].appointmentObjectType.value='30';
		}
		else if(PO.value != "")
		{
			document.forms[formName].appointmentObjectType.value='40';
		}
	}
	// check for TT  29388  ILM:App Error on hitting balance from appt details with invalid date
	if(dateField.value.length >= 8)
	{
		if(isValidDate(dateField.value,'M/d/yy HH:mm')==false)
		{
			if(isValidDate(dateField.value,'M/d/yyyy HH:mm')==false) {
                alert(invalidDateMsg);
			    return;
            }
        }
	}
	else
	{
		if(dateField.value !="" )
		{
			alert(invalidDateMsg);
			return;
		}
	}
	var facilityAlias = eval(document.forms[formName].facilityAlias);



	if(facilityAlias != null)

	{

		urlString=urlString+"?facilityAlias="+facilityAlias.value;

	}
	document.forms[formName].action =urlString;
	document.forms[formName].submit();
}


function isValidateDate(date)
{
	date = trim(date);
	var dateLength = date.length;
	var format = "M/d/yy";
	var dateFormatLength = format.length;;
	var formatCheck = false;
	var formatArray1 = new Array();
	var formatArray2 = new Array();
	var formatArray3 = new Array();
	var checkstr = "0123456789/";
	var leap = false;
	var month;
	var day;
	var year;
	var hrs;
	var min;
	var sep1 = "/";

	if(date.indexOf(sep1) != -1)
	{
		var DateTemp = "";
	    DateValue = date;

		// This check has been done as to check for the both formats whether its is the "M/d/yy" format or "M/d/yy HH:mm"
		if( (dateLength == dateFormatLength+6) || (dateLength == dateFormatLength+7) ||
			(dateLength == dateFormatLength+8) || (dateLength == dateFormatLength+9))
		{
			format = "M/d/yy HH:mm";
			dateFormatLength = format.length;
			formatCheck = true;
		}

		if(dateLength != (dateFormatLength+5))
		{
			if(dateLength != (dateFormatLength+2))
			{
				if(dateLength != (dateFormatLength+1))
				{
					if(dateLength != dateFormatLength)
						return false;
				}
			}
		}


		if(formatCheck == false)
		{
			dateArray = date.split("/");
			month = dateArray[0];
			day = dateArray[1];
			year = dateArray[2];
		}else
		{
			strDate = getDDate(date);
			dateArray = strDate.split("|");
			month = dateArray[0];
			day = dateArray[1];
			year = dateArray[2];
			hrs = dateArray[3];
			min = dateArray[4];
		}

	if(year >= 0)
	{
		year = '20' +year;
		if(month >0 && month <=12)
		{
			if(day >0)
			{
				// Validation leap-year / february / day
				   if((year % 4 == 0) || (year % 100 == 0) || (year % 400 == 0))
				   {
					   leap = true;

				   }
				   if((month == 2) && (leap == true) && (day > 29))
					  return false;
				   if((month == 2) && (leap != true) && (day > 28))
					 return false;
				   if((day > 31) && ((month == 1) || (month == 3) ||
					   (month == 5) || (month == 7) || (month == 8) || (month == 10) || (month == 12)))
					 return false;
				   if((day > 30) && ((month == 4) || (month == 6) || (month == 9) || (month == 11)))
					 return false;
				   if(hrs == 24 && min >= 0)
					   return false;
				   if(hrs < 0 || hrs > 24)
					   return false;
				   if(min < 0 || min > 59)
					   return false;
				   return true;
			}
			else
				return false;
		}
		else
			return false;
	  }
	  else
		 return false;
	}
	else
		return false;
}
function getFormatedDateValue(date,format)
	{

		var formatArray1 = new Array();
		var formatArray2 = new Array();
		var formatArray3 = new Array();
		//var checkstr = "0123456789/: ";
		var month;
		var day;
		var year;
		var hrs;
		var min;

	//	var sep1 = "/";
	//	var sep2 = " ";
	//	var sep3 = ":";


		var formatUC = format.toUpperCase();
		var	dateFormatLength = formatUC.length;
		var indexspace = formatUC.indexOf(' ');
		var formatUCTime = formatUC.substring(indexspace+1,dateFormatLength);
		var indexTime = formatUCTime.indexOf('H');

		if(indexTime == 0)
		{
			var indexTime = formatUCTime.indexOf('M');
			var sepTime = formatUCTime.substring(indexTime-1,indexTime);
		}
		else
		{
			var sepTime = formatUCTime.substring(indexTime-1,indexTime);
		}
		var indexDate = formatUC.indexOf('D');
		if(indexDate == 0)
		{
			var indexDate = formatUC.indexOf('Y');
			var sepDate = formatUC.substring(indexDate-1,indexDate);
		}
		else
		{
			var sepDate = formatUC.substring(indexDate-1,indexDate);
		}
		var sep1 = sepDate;
		var sep2 = " ";
		var sep3 = sepTime;
		var checkstr = "0123456789"+sep1+sep3+" ";

		if(date.indexOf(sep1) != -1)
		{
			formatArray1 =  date.split(sep1);
			month = formatArray1[0];
			day = formatArray1[1];
			var temp = formatArray1[2];

			if(temp.indexOf(sep2) != -1)
			{
				formatArray2 = temp.split(sep2);
				year = formatArray2[0];
				var tempTime = formatArray2[1];
				if(tempTime.indexOf(sep3) != -1)
				{
					formatArray3 = tempTime.split(sep3);
					hrs = formatArray3[0];

					min = formatArray3[1];
				}
			}
		}

			var formatUCDate = formatUC.substring(0,indexspace);
			dateFormatArray = formatUCDate.split(sepDate);
			timeFormatArray = formatUCTime.split(sepTime);
			if(((dateFormatArray[0]=="DD")||(dateFormatArray[0]=="D"))
			&&((dateFormatArray[1]=="MM")||(dateFormatArray[1]=="M")||(dateFormatArray[1]=="MMM"))
			&&((dateFormatArray[2]=="YY")||(dateFormatArray[2]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = day+'|'+month+'|'+year+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = day+'|'+month+'|'+year+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}else if(((dateFormatArray[1]=="DD")||(dateFormatArray[1]=="D"))
                    &&((dateFormatArray[0]=="MM")||(dateFormatArray[0]=="M")||(dateFormatArray[0]=="MMM"))
                    &&((dateFormatArray[2]=="YY")||(dateFormatArray[2]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = month+'|'+day+'|'+year+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = month+'|'+day+'|'+year+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}else if(((dateFormatArray[1]=="DD")||(dateFormatArray[1]=="D"))
                    &&((dateFormatArray[2]=="MM")||(dateFormatArray[2]=="M")||(dateFormatArray[2]=="MMM"))
                    &&((dateFormatArray[0]=="YY")||(dateFormatArray[0]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = year+'|'+day+'|'+month+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = year+'|'+day+'|'+month+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}else if(((dateFormatArray[2]=="DD")||(dateFormatArray[2]=="D"))
                    &&((dateFormatArray[1]=="MM")||(dateFormatArray[1]=="M")||(dateFormatArray[1]=="MMM"))
                    &&((dateFormatArray[0]=="YY")||(dateFormatArray[0]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = year+'|'+month+'|'+day+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = year+'|'+month+'|'+day+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}else if(((dateFormatArray[0]=="DD")||(dateFormatArray[0]=="D"))
                    &&((dateFormatArray[2]=="MM")||(dateFormatArray[2]=="M")||(dateFormatArray[2]=="MMM"))
                    &&((dateFormatArray[1]=="YY")||(dateFormatArray[1]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = day+'|'+year+'|'+month+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = day+'|'+year+'|'+month+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}else if(((dateFormatArray[2]=="DD")||(dateFormatArray[2]=="D"))
                    &&((dateFormatArray[0]=="MM")||(dateFormatArray[0]=="M")||(dateFormatArray[0]=="MMM"))
                    &&((dateFormatArray[1]=="YY")||(dateFormatArray[1]=="YYYY")))
			{
				if((timeFormatArray[0]=="HH")&&(timeFormatArray[1]=="MM"))
				{
					var strDateTime = month+'|'+year+'|'+day+'|'+hrs+'|'+min;
					return strDateTime;
				}else if((timeFormatArray[0]=="MM")&&(timeFormatArray[1]=="HH"))
				{
					var strDateTime = month+'|'+year+'|'+day+'|'+min+'|'+hrs ;
					return strDateTime;
				}
			}

}
function isValidDate(date,dateFormat) {
		date = trim(date);
		var format = dateFormat;
		var dateFormatLength = format.length;
		var dateLength = date.length;
		var formatArray1 = new Array();
		var formatArray2 = new Array();
		var formatArray3 = new Array();
		var formatUC = format.toUpperCase();
		var indexspace = formatUC.indexOf(' ');
		var formatUCTime = formatUC.substring(indexspace+1,dateFormatLength);
		var indexTime = formatUCTime.indexOf('H');

		if(indexTime == 0)
		{
			var indexTime = formatUCTime.indexOf('M');
			var sepTime = formatUCTime.substring(indexTime-1,indexTime);
		}
		else
		{
			var sepTime = formatUCTime.substring(indexTime-1,indexTime);
		}
		var indexDate = formatUC.indexOf('D');

		if(indexDate == 0)
		{
			var indexDate = formatUC.indexOf('Y');
			var sepDate = formatUC.substring(indexDate-1,indexDate);
		}
		else
		{
			var sepDate = formatUC.substring(indexDate-1,indexDate);
		}
		var sep1 = sepDate;
		var sep2 = " ";
		var sep3 = sepTime;
		var checkstr = "0123456789"+sep1+sep3+" ";
		var leap = false;
		var month;
		var day;
		var year;
		var hrs;
		var min;

		// Check that date separator should occur twice.
		var indexspaceDate = date.indexOf(' ');
		var inputDate = date.substring(0,indexspace);
		var inputTime = date.substring(indexspace+1);
		var dateSepCount =0;
		var timeSepCount =0;
		for (i = 0; i < inputDate.length; i++)
		{
        	var charDate = inputDate.charAt(i);
        	if (charDate == sep1)
        	{
        		dateSepCount++;
        	}
	    }

    	if(dateSepCount != 2)
    	{
    		return false;
   		}
   		for (i = 0; i < inputTime.length; i++)
		{
        	var charDate = inputTime.charAt(i);
        	if (charDate == sep3)
        	{
        		timeSepCount++;
        	}
	    }

    	if(timeSepCount != 1)
    	{
    		return false;
   		}
		if(date.indexOf(sep1) != -1 || date.indexOf(sep2) != -1 || date.indexOf(sep3) != -1)
		{

			/*var DateTemp = "";
		    DateValue = date;
			if(dateLength != (dateFormatLength+2))
			{
				if(dateLength != (dateFormatLength+1))
				{
					if(dateLength != dateFormatLength)
						return false;
				}
			}*/
			strDate = getFormatedDateValue(date,format);
			dateArray = strDate.split("|");
			var formatUCDate = formatUC.substring(0,indexspace);
			dateFormatArray = formatUCDate.split(sepDate);
			timeFormatArray = formatUCTime.split(sepTime);
			month = dateArray[0];
			day = dateArray[1];
			year = dateArray[2];
			hrs = dateArray[3];
			min = dateArray[4];
		if(isNaN(month) || month.length ==0 || isNaN(year) || year.length == 0  || isNaN(day)
			|| day.length == 0|| isNaN(hrs) || hrs.length == 0
			|| isNaN(min) || min.length == 0)
			return false;
		if(year >= 0)
		{
			year = '20' +year;
			if(month >0 && month <=12)
			{
				if(day >0)
				{
					// Validation leap-year / february / day
				   if((year % 4 == 0) || (year % 100 == 0) || (year % 400 == 0))
				   {
					   leap = true;
				   }
				   if((month == 2) && (leap == true) && (day > 29))
					  return false;
				   if((month == 2) && (leap != true) && (day > 28))
					 return false;
				   if((day > 31) && ((month == 1) || (month == 3) ||
					   (month == 5) || (month == 7) || (month == 8) || (month == 10) || (month == 12)))
					 return false;
				   if((day > 30) && ((month == 4) || (month == 6) || (month == 9) || (month == 11)))
					 return false;
				  if(hrs < 0 || hrs > 23)
				   return false;
				   if(min < 0 || min > 59)
					   return false;
				   return true;
				}
				else
					return false;
			}
			else
				return false;
		  }
		  else
			 return false;
		}
		else
			return false;
}

function getDDate(date)
{
	var formatArray1 = new Array();
	var formatArray2 = new Array();
	var formatArray3 = new Array();
	var checkstr = "0123456789/: ";
	var month;
	var day;
	var year;
	var hrs;
	var min;
	var sep1 = "/";
	var sep2 = " ";
	var sep3 = ":";

	if(date.indexOf(sep1) != -1)
	{
		formatArray1 =  date.split(sep1);
		month = formatArray1[0];
		day = formatArray1[1];
		var temp = formatArray1[2];

		if(temp.indexOf(sep2) != -1)
		{
			formatArray2 = temp.split(sep2);
			year = formatArray2[0];

			var tempTime = formatArray2[1];
			if(tempTime.indexOf(sep3) != -1)
			{
				formatArray3 = tempTime.split(sep3);
				hrs = formatArray3[0];
				min = formatArray3[1];
			}
		}
	}
	return month +'|'+ day +'|'+ year +'|'+ hrs + '|' + min;
}


function dockrefresh()
{
	document.dataForm.doorId.value="";
}
function newTrailer(formName,ILMTrailerFlag, obj, objid,license, state,carriercode,facilityId,view_type,url)
{
	ILMTrailerId = eval(obj).value;
	var carrier;
	if(eval(carriercode) != null && eval(carriercode).value != 'unidefined')
	{
		carrier = eval(carriercode).value;
	}

	// Show carrier new trailer page
	if(view_type=="CARRIER_APPT_VIEW")
	{
		url='/basedata/equipmentinstance/jsp/RaEquipmentInstanceSummarySheet.jsp?operation=create&fromappointmentPage=true';
		var lookup = window.open(url, "newTrailer", "width=850,height=850");
		lookup.focus();
        //document.forms[formName].action = url;
	    //document.forms[formName].submit();
	}
	// show shipper new trailer as look up
	else if(view_type=="MAIN_VIEW")
	{
		url=url+"&ILMTrailerFlag="+ILMTrailerFlag+"&ILMTrailerId="+ILMTrailerId+"&ILMCarrierCode="+carrier;
		var lookup = window.open(url, "newTrailer", "width=850,height=850");
		lookup.focus();
	}
}

function newTrailer(formName,ILMTrailerFlag, obj, objid,license, state,carriercode,facilityId,equipmentcode,view_type,shipperId,winTitle,url)
{
	ILMTrailerId = document.getElementById(objid).value;
	var carrier = "";
	var equipment;

	if(document.getElementById(carriercode) != null && document.getElementById(carriercode).value != 'undefined')
	{
		carrier = document.getElementById(carriercode).value;
		if(carrier.indexOf(";") > 0 )
		{
            carrier =  carrier.substring(0,carrier.indexOf(";") );
		}
	}


	if(document.getElementById(equipmentcode) != null && document.getElementById(equipmentcode).value != 'undefined')
	{
		equipment = document.getElementById(equipmentcode).value;
	}
	// Show carrier new trailer page
	if(view_type=="CARRIER_APPT_VIEW")
	{
		var shipper = document.getElementById(shipperId).value;
		url='/basedata/equipmentinstance/jsp/RaEquipmentInstanceSummarySheet.jsp?operation=create&fromappointmentPage=true&trailerId='+ILMTrailerId+'&equipmentCode='+equipment+'&shipperId='+shipper+'&carrierCode='+carrier;
		var lookup = window.open(url, "newTrailer", "width=850,height=850");
		lookup.focus();
        //document.forms[formName].action = url;
	    //document.forms[formName].submit();
	}
	// show shipper new trailer as look up
	else if(view_type=="MAIN_VIEW")
	{
		var windowId = document.getElementById("windowId").value;
		if(windowId == null||windowId == ''||windowId == 'undefined'){
			windowId = getWindowIdFromFrame();
		}
		url=url+"&ILMTrailerFlag="+ILMTrailerFlag+"&ILMTrailerId="+ILMTrailerId+"&ILMCarrierCode="+carrier+"&ILMEquipmentCode="+equipment+"&windowId="+windowId;

		document.getElementById("newTrailerFrame").src =url;

		UI8Layout.doDialogById('NewTrailerWindow',false);

		//var lookup = window.open(url, "newTrailer", "width=850,height=850");
		//lookup.focus();

		//var lookup = window.open(url, "newTrailer", "width=750,height=600,scrollbars=yes,resizable=no");
		//lookup.focus();
		return false;
	}
}

function getWindowIdFromFrame(){
	var currentUrl = window.frameElement.src;
	var idStart = currentUrl.substring( currentUrl.indexOf('windowId') ) .substring(9);
	var endPos = idStart.indexOf('&');
	if ( endPos == -1 ){
		windowId = idStart;
	}
	else {
		windowId = idStart.substring(0,endPos);
	}
	return windowId;
}

function showCalendarPreLoad(obj, url)
{
	window.dateField = eval(obj);
	window.inputDateFormat='M/d/yy';
	window.allowsDateOnly="true";
	var calendar1 = window.open(url, "cal", "width=250,height=350");
	calendar1.focus();

}

function viewDetailsLink(flag , url)
{
	url = url+"&ILMFlag="+flag;
	var lookup = window.open(url,"viewDetails", "width=850,height=650,scrollbars=yes");
	lookup.focus();
}

function checkAllAppointments(object, no_rows_to_sel_msg)
{
	var obj1 = eval(object);
	//variable to help with checking and unchecking
	//CR 280076:  The check icon selects all, but does not de-select all items on list
	var CHECKED = false;

	if(obj1 == 'undefined' || obj1 == null)
	{
		alert(no_rows_to_sel_msg);
		return;
	}
	if (obj1.length != null && obj1.length != 'undefined')
	{
		if (obj1.length > 1)
		{
			for (i = 0; i < obj1.length; i++)
			{
				if(obj1[i].checked == false)
				{ //if its not checked currently make it false
					CHECKED = false;
				}
				else
				{
					CHECKED = true;
				}
			}
			if(CHECKED == true)
			{ //means the boxes were checked so now uncheck them
				for (i = 0; i < obj1.length; i++)
				{
					obj1[i].checked = false;
				}
			}
			else
			{// check all the unchecked boxes
				for (i = 0; i < obj1.length; i++)
				{
					obj1[i].checked = true;
				}
			}
		}
		else
		{
			if(obj1.checked == false)
			{
				CHECKED = false;
			}
			else
			{
				CHECKED = true;
			}
			if(CHECKED == true)
			{//means the boxes were checked so now uncheck them
				obj1.checked = false;
			}
			else
			{// check all the unchecked boxes
				obj1.checked = true;
			}
		}
	}
	else if((typeof obj1 !='undefined') && (typeof obj1.length == 'undefined'))
	{
		obj1.checked = true;
	}
}


function goToCalendarFromView(formName,invalidDateMsg,urlString)

{

	document.forms[formName].action =urlString;

	document.forms[formName].submit();

}

function openWindow(url, title)
{
	var mywindow = window.open(url, title, "width=600,height=600,resizable=yes");
	mywindow.focus();
}

function raiseLookup(lookup_Type,obj, objid, index, url)
 {
	if(lookup_Type=="SHIPMENT")
	{

		if(eval(obj).value !='')
		{
			var searchString=eval(obj).value;
		}
		else
		{
			var searchString='';
		}

		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index+"&lookupType=" + lookup_Type + "&searchString="+searchString;
		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
	else if(lookup_Type=="ORDER")
	{
		var indexType = document.dataForm.lookupOrder.length;

		if (typeof indexType =='undefined')
		{

			if(eval(obj).value !='')
			{
				var searchString = eval(obj).value;
			}
			else
			{
				var searchString='*';
			}

		}
		else
		{
			if(eval(obj)[index].value !='')
			{
				var searchString =eval(obj)[index].value ;
			}
			else
			{
				var searchString='*';
			}
		}

		var redirecturl = url + "?returnId=" + obj + "&returnHiddenId=" + objid + "&indexValue= " + index + "&lookupType=" + lookup_Type + "&searchString="+searchString;

		var lookup = window.open(redirecturl, "lookup", "width=350,height=450");
		lookup.focus();
	}
 }



 function attachedPONum( tr) {
	 var inputs = document.getElementsByName('purchaseOrderNumber');
	 //alert('inputs.length ' + inputs.length);
	 for (var num = 0; num <  tr.childNodes.length;num++) {
		//alert(tr.childNodes[num].innerHTML);
		var pnum = 'purchaseorderNumber';
		if( tr.childNodes[num].innerHTML.toUpperCase().indexOf(pnum.toUpperCase()) != -1 ) {

			for (i = 0; i < inputs.length; i++) {
				//alert ('inputs['+i+'].value'+ inputs[i].value);
				if(tr.childNodes[num].innerHTML.indexOf(inputs[i].value) != -1) {
				    //alert ('inputs['+i+'].value'+ inputs[i].value);
					return inputs[i].value;
				}
			}

		}
	 }
	 return 'nopo';
 }

function searchAndAddPOListener(apptObjTableId,view_type)
{
	// changed for search & add po button on vendor side
	var urlString = '/cbo/transactional/filter/FilterDetails.jsp?objectType=MASTER_ORDER_APPT&new=1&previousPage=MasterOrderList&shortcut=1&isPopup=true';
    if(view_type == 'SUPPLIER_APPT_VIEW')
	{
		urlString = '/cbo/transactional/filter/FilterDetails.jsp?objectType=VCP_MASTER_ORDER_APPT&new=1&previousPage=MasterOrderList&shortcut=1&isPopup=true';
	}
	//pop up PO List for user selection
    //alert('urlString:'+urlString);
    poListWindow = window.open( urlString, 'POList', 'width=800,height=600,scrollbars=yes');
    poListWindow.document.initValue=''
    poListWindow.focus();
}

 var IFrameObj = null;

function setSizes(apptObjTableId, view_type, shipperId, mesg1, mesg2)
{

	//alert('Inside setSizes, tableId:'+apptObjTableId);
	var shipperId = eval(shipperId).value;
    var totalSelected=0;
	var shipmentId='';

	var tableId = document.getElementById(apptObjTableId);
	var shipIdLst = document.getElementsByName('tcShipmentId');

	for (i = 0; i < tableId.rows.length - 1; i++)
	{
			if(shipIdLst[i].value != '')
			{
				totalSelected =  totalSelected+1;
				shipmentId += shipIdLst[i].value + '^';
			}
			//alert('shipmentId:'+shipmentId);
	}

	var loadCfgLst = document.getElementsByName('loadConfiguration');
	var loadCfg = loadCfgLst[0];
	var loadCfgCode = '';
	var totalLoadConfigsSelected = 0;

	for(j = 0; j < loadCfg.options.length; j++)
	{
		if(loadCfg.options[j].selected)
		{
			totalLoadConfigsSelected = totalLoadConfigsSelected + 1;
			loadCfgCode += loadCfg.options[j].value + '^';
		}
	}

	if(totalLoadConfigsSelected == 0)
	{
		//CR 27459: Invalid Message Getting displayed if Set Size is clicked without Selecting any Load Configuration
		alert(mesg2);
		return;
	}

	if(totalSelected == 0)
	{
		alert(mesg1);
		return;
	}


	shipmentId = shipmentId.substring(0, shipmentId.length-1);
	loadCfgCode = loadCfgCode.substring(0, loadCfgCode.length-1);

	//IFrameDoc = establishIFrameForServerSideScripting(dataForm);
	var URLString = "/ilm/appointment/jsp/ProcessSetSize.jsp";
	/*if(IFrameObj == null) {
		return false;
	}*/

	//alert('loadCfgCode:'+loadCfgCode);
	//alert('apptObjTableId:'+apptObjTableId);
	URLString = URLString + "?shipmentIds="+shipmentId+"&loadConfigCodes="+loadCfgCode+"&tableId="+apptObjTableId;
    if(view_type == 'SUPPLIER_APPT_VIEW')
    {
        URLString = URLString + "&isVCP=true"+"&shipperId="+shipperId;
    }
    else
    {
       URLString = URLString + "&isVCP=false";
    }
    //alert(URLString);
//	IFrameDoc.location.replace(URLString);
	openPopup(500,500,450,400, URLString ,'Set  PO Sizes');
}


function sortAppointmentJS(direction, fieldName)
{
	var id = "";
	var sortCrit = "";

	id = "appointment" + "main_view" + "list_view";

	if (fieldName == null && fieldName == '')
		fieldName = "ILM_APPOINTMENTS.TC_APPOINTMENT_ID";
	sortCrit = "&" + id + "SortFieldName=" + fieldName + "&" + id + "SortDirection=" + direction;
	document.forms["dataForm"].action= "/ilm/appointmentList.do?Display_Type=LIST_VIEW&Entity_Type=APPOINTMENT&Screen_Type=MAIN_VIEW" + sortCrit;
	document.forms["dataForm"].submit();
}

function openFilterPopupForAppt(objectName)
{
	if(objectName = 'RAAPPOINTMENT')
	{
		openFilterPopup('/ofr/filter/jsp/FilterList.jsp?objectType='+objectName+'&newList=1',"Filters");
	}
	else
	{
		openFilterPopup('/ilm/filter/jsp/ILMFilterList.jsp?objectType='+objectName+'&newList=1',"Filters");
	}
}

function openFilterPopupForAppointment(objectName)
{
	openFilterPopup('/ilm/filter/jsp/ILMFilterList.jsp?objectType='+objectName+'&newList=1',"Filters");
}

function openFilterPopupForAppointmentWithTitle(objectName, title)
{
	openFilterPopup('/ilm/filter/jsp/ILMFilterList.jsp?objectType='+objectName+'&newList=1',title);
}

function openFilterPopupForArchiveAppointment(objectName, isNew, isArchive, clearFilter, popupTitle)
{
	var parms = 'objectType='+objectName+'&new='+isNew+'&archive='+1+'&clearFilter='+clearFilter;
	openFilterPopup('/ilm/filter/jsp/ILMFilter.jsp?'+parms,popupTitle);
}
function openPopup(dialogId)
{
	UI8Layout.doDialogById(dialogId);
	return true;
}


function openDialogwithId(dialogId)
{
	UI8Layout.doDialogById(dialogId);
	return true;
}


function setSelectedRecommendation()
{
	if(isRowSelected('recommendationTable'))
	{
		var recommendationId  = getFieldForRow('recommendationTable',getSelectedRows('recommendationTable'),'recommendationId');
		document.getElementById("dataForm:selectedUirecommendation").value = recommendationId.value;
	}
	else
	{
		alert(messageArray[0]);
		return false;
	}
}

function populateFromFacilityLookupPopup(objid)
{
      var facilityObject = document.getElementById('dataForm:'+objid);
      facilityLookupPopup(facilityObject);
}
function facilityLookupPopup(facilityObject)
{
      var buID = getBUId();
      url='/cbo/facility/FacilityLookupPopup.xhtml?facilityId='+encodeURIComponent(facilityObject.value)+'&facilityObjectId='+encodeURIComponent(facilityObject.name);
	  if(buID!=null || buID!=undefined)
	  {
		url = url+'&dependantId='+encodeURIComponent(buID.value);
	  }
      document.getElementById('APPT_Facility_iframeId').src = url;
	  openDialogwithId('Appt_FAC_Dialog');
}
function getBUId()
{
	if(document.getElementById('dataForm:primaryBuList'))
	{
		return document.getElementById('dataForm:primaryBuList');
	}
	return document.getElementById('EditBusinessUnitInTypeHid');
}
function closeFacilityPopup()
{
	UI8Layout.doDialogCloseById ('Appt_FAC_Dialog');
	return true;
}
function dummy()
{
	return true;
}
function overrideSCE()
{
	return UI8Layout.doDialogById('SoftCheckErrorOverride_Dialog');
}
function addApptObjRow()
{
	AddRow('apptObjTable');
	return false;
}
function addApptSlotRow()
{
	AddRow('slotDetailsTable');
	return false;
}
function addMultipleApptObjRow()
{
	AddRow('apptObjTable');
	AddRow('apptObjTable');
	AddRow('apptObjTable');
	return false;
}
function deleteApptObjRow()
{
	if(isRowSelected('apptObjTable'))
	{
		if(confirm(messageArray[4]))
		{
			DeleteRow('apptObjTable');
		}
		return false;
	}
	else
	{
		alert(messageArray[0]);
		return false;
	}
}
function deleteApptSlotRow()
{
	if(isRowSelected('slotDetailsTable'))
	{
		if(confirm(messageArray[4]))
		{
			DeleteRow('slotDetailsTable');
		}
		return false;
	}
	else
	{
		alert(messageArray[0]);
		return false;
	}
}

function approve()
{
	var isShipper=document.getElementById("dataForm:isShipper");
	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		if(selectedIndex.length>1)
		{
			alert(messageArray[3]);
		}
		else
		{
			var validForApprove=true;
			for ( var i = 0; i < selectedIndex.length; i++)
			{
				var index = selectedIndex[i];
				var status=getFieldForRow('dataTable',index,'apptTypeHid');
				if(isShipper!=null && isShipper.value!=null && isShipper.value!='' && isShipper.value=='true')
				{
					if(status.value!='Requested' && status.value!='17')
					{
						validForApprove=false;
					}
				}
				else
				{
					if(status.value!='Countered' && status.value!='15')
					{
						validForApprove=false;
					}
				}
			}
			if(validForApprove==false)
			{
				alert(messageArray[1]);
			}
			else
			{
				sForm('#{appointmentBackingBean.approveAction}');
			}

		}

	}
	else
	{
		alert(messageArray[0]);
	}

}

function checkInAppointment(tranId)
{
	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		if(selectedIndex.length>1)
		{
			alert(messageArray[3]);
			return false;
		}
		else
		{
			var validForApprove=true;
			var tcApptId;
			for ( var i = 0; i < selectedIndex.length; i++)
			{
				var index = selectedIndex[i];
				var status=getFieldForRow('dataTable',index,'apptTypeHid');
				var type=getFieldForRow('dataTable',index,'appointmentTypeHid');
				tcApptId=getFieldForRow('dataTable',index,'apptHid').value;
				if(type.value == 'Pickup Load' || type.value == 'Misc')		{
					var stat = status.value;
					if(stat == 'Scheduled' || stat == '3' || stat == 'Checked In' || stat == '5' || stat == 'Loaded' || stat == '11' || stat == 'Allocated for door' || stat == '6'|| stat == 'In Transit' || stat == '19'  || stat == 'Loading' || stat == '8')	{
						validForApprove = true;
					} else {
						validForApprove = false;
					}
				} else {
					if(status.value!='Scheduled' && status.value!='3')
					{
						validForApprove=false;
					}
				}
			}
			if(validForApprove==false)
			{
				alert(messageArray[2]);
				return false;
			}
			else
			{
						var screen = {
						name : 'Checkin.screen.CheckIn',
						screenCfg : {
												tcAppointmentId : tcApptId,
												tranId	:	tranId,
												title  : document.getElementById('dataForm:listView:apptList_btn_4').value
											}
						};
						createWebtopScreen(screen);
						return true;
			}
		}
	}
	else
	{
		alert(messageArray[0]);
		return false;
	}
	return false;
}

function rejectAppointment()
{

	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		if(selectedIndex.length>1)
		{
			alert(messageArray[3]);
		}
		else
		{
			var validForReject=false;
			for ( var i = 0; i < selectedIndex.length; i++)
			{
				var index = selectedIndex[i];
				var status=getFieldForRow('dataTable',index,'apptTypeHid');
				if(status.value=='Checked In' || status.value=='5' ||
						status.value=='Allocated for door' || status.value=='6')
				{
					validForReject=true;
				}
			}
			if(validForReject==false)
			{
				alert(messageArray[8]);
			}
			else
			{
				sForm('#{appointmentBackingBean.rejectAction}');
			}

		}
	}
	else
	{
		alert(messageArray[0]);
	}
/*	if(isRowSelected('dataTable'))
	{
		sForm('#{appointmentBackingBean.rejectAction}');
	}
	else
	{
		alert(messageArray[0]);
	}*/
}

function calendar()
{
	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		if(selectedIndex.length>1)
		{
			alert(messageArray[3]);
		}
		else
		{
			sForm('#{appointmentBackingBean.calendarAction}');
		}
	}
	else
	{
		alert(messageArray[0]);
	}

}

function appointmentAudit()
{
	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		if(selectedIndex.length>1)
		{
			alert(messageArray[3]);
		}
		else
		{
			sForm('#{appointmentBackingBean.appointmentAuditAction}');
		}
	}
	else
	{
		alert(messageArray[0]);
	}

}

function closeRecomDialog()
{
	if(isRowSelected('recommendationTable'))
	{
		UI8Layout.doDialogCloseById('Recommendations_Dialog');
	}
}
function closeSCEDialog()
{
	var errors=checkSCEErrors();
	if(errors==false)
	{
		UI8Layout.doDialogCloseById('SoftCheckErrorOverride_Dialog');
	}
}
function overrideApptSCE()
{
	var errors=checkSCEErrors();

	if(errors==true)
	{
		var reasoncodeID  = getFieldForRow('softcheckerrorsTable',0,'reasoncode');
		if(isRowSelected('softcheckerrorsTable'))
		{
			alert("Override code is required")
		}
		else if(GetRowCount('softcheckerrorsTable')>1)
		{
			alert(messageArray[0]);
		}
		else if(reasoncodeID.value == undefined || reasoncodeID.value == '')
		{
			alert("Override code is required");
		}

		return false;
	}
}
function checkSCEErrors()
{
	if(isRowSelected('softcheckerrorsTable'))
	{
		var selectedIndex = getSelectedRows('softcheckerrorsTable');
		var error=0;
		for ( var i = 0; i < selectedIndex.length; i++)
		{
			var index = selectedIndex[i];
			var reasoncodeID  = getFieldForRow('softcheckerrorsTable',index,'reasoncode');
			if(reasoncodeID.value=="" || reasoncodeID.value == '')
			{
				error=1;
			}
		}
		if(error == 0)
		{
			return false;
		}
		else
		{
			return true;
		}
	}
	else
	{
		return true;
	}
}
function checkBlindAppointment()
{
	var po=document.getElementById("dataForm:apptObjTable:0:poId2");
	var ship=document.getElementById("dataForm:apptObjTable:0:shipId2");
	var asn=document.getElementById("dataForm:apptObjTable:0:asnId2");

	if( (po == null ||  po.value=='') && (ship == null ||  ship.value=='')  && (asn== null || asn.value==''))
	{
		if(confirm(messageArray[5]))
		{
			sForm('#{appointmentBackingBean.saveWithBestFitAction}');
			disableFooterButtons();
		}
	}
	else
	{
		sForm('#{appointmentBackingBean.saveWithBestFitAction}');
	}
}

function customOnLoad()
{

	//reloadWhenSingleRow();

	if(document.getElementById('dataForm:loadConfigurationListSel') != null  &&  document.getElementById('dataForm:loadConfigurationListSel') != undefined )
	{
		var selectloadConfig = document.getElementById('dataForm:loadConfigurationListSel').value;
		if ( selectloadConfig != null)
		{
			if (document.getElementById('dataForm:setSizesBtnship') != null && document.getElementById('dataForm:setSizesBtnship')!= undefined)
				{
					document.getElementById('dataForm:setSizesBtnship').click();
					//A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:setSizesBtn','parameters':{'dataForm:setSizesBtn':'dataForm:setSizesBtn'} } );
				}else
				{
					//sForm('#{appointmentBackingBean.setSizes}');
					A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:setSizesBtn','parameters':{'dataForm:setSizesBtn':'dataForm:setSizesBtn'} } );
				}
		}
	}
}
function openAddRowPopUp()
{
	document.getElementById("dataForm:numOfRows").value = '';
	UI8Layout.doDialogById('NumRowsDialog', true);
	return false;

}
function addMultipleApptObjRow()
{
    var res = document.getElementById("dataForm:numOfRows");
    if(res.value==null || res.value=='')
    {
    	alert("Enter number of rows to add");
    }
    else
    {
    	if(isNaN(res.value))
    	{
    		alert("Please enter a numeric value");
    	}
    	else
    	{
    		for(var i=0;i<res.value;i++)
    		{
    			AddRow('apptObjTable');
    		}
    	    UI8Layout.doDialogCloseById('NumRowsDialog');
    	}
    }
    return false;
}

function showApptLeveling ()
{
	if(isRowSelected('dataTable'))
	{
		var selectedIndex = getSelectedRows('dataTable');
		var statusValidForLeveling=true;
		var typeValidForLeveling=true;
		for ( var i = 0; i < selectedIndex.length; i++)
		{
			var index = selectedIndex[i];
			var status=getFieldForRow('dataTable',index,'apptTypeHid');
			var type=getFieldForRow('dataTable',index,'appointmentTypeHid');
			if((status.value!='Scheduled') && (status.value!='Checked_In'))
			{
				statusValidForLeveling=false;
			}

			if ((type.value!= 'Live Unload') && (type.value!= 'Drop Unload'))
			{
				typeValidForLeveling=false;
			}

		}
		if(statusValidForLeveling==false)
		{
			alert(messageArray[6]);
		}
		else if (typeValidForLeveling==false)
		{
			alert(messageArray[7]);
		}
		else
		{
			sForm('#{appointmentBackingBean.appointmentLevellingAnalysis}');
		}
	}
	else
	{
		alert(messageArray[0]);
	}
}


function openBUPopup()
{
	UI8Layout.doDialogById('BUPopup',true);
	return false;
}
function poShipmentChanged(elementId) {
	var preRowElementId ="";
	if ( elementId.indexOf("poId2") != -1 )			preRowElementId = elementId.substring( 0,  (elementId.length -5 ) );
	else preRowElementId = elementId.substring( 0,  (elementId.length -7 ) );

	var asnId = preRowElementId + "asnId2";
	var asnLookupId = "trigger_"+asnId;

	var poEntered="";
	var shipmentEntered="";
	poEntered = document.getElementById(preRowElementId+"poId2").value;
	shipmentEntered  = document.getElementById(preRowElementId+"shipId2").value;

	var disableAsn = false;

	if (  poEntered.length > 0  || shipmentEntered.length >0 ) disableAsn = true;
	else disableAsn = false;


	document.getElementById(asnId).disabled = disableAsn;
	document.getElementById(asnLookupId).disabled = disableAsn;
}

function asnChanged(elementId) {

	var preRowElementId ="";
	preRowElementId = elementId.substring( 0,  (elementId.length -6 ) );

	var poId = preRowElementId + "poId2";
	var poLookupId = preRowElementId+"POapptImg";

	var shipmentId = preRowElementId + "shipId2";
	var shipmentLookupId = "trigger_"+shipmentId;

	var disablePOShipment = false;

	var asnEntered = document.getElementById(elementId).value;

	if (  asnEntered.length > 0   ) disablePOShipment = true;
	else disablePOShipment = false;


	document.getElementById(poId).disabled = disablePOShipment;
	document.getElementById(poLookupId).disabled = disablePOShipment;

	document.getElementById(shipmentId).disabled = disablePOShipment;
	document.getElementById(shipmentLookupId).disabled = disablePOShipment;
}
function closeAppointmentPopup(){
	var windowId = document.getElementById("windowId").value;
	if ( windowId != null && windowId !='' && windowId != undefined )
	{
		var currScreenObj = parent.Webtop.ScreenManager.get(windowId);
		var navigatedFromPOorShipmentListPage =  currScreenObj.config.navigatedFromPOorShipmentListPage;
		if ( navigatedFromPOorShipmentListPage  == true  )
		{
			currScreenObj.close();
			return false;
		}
	}
}


function  populateScreenId() {
	var windowId = document.getElementById("windowId").value ;
	if ( windowId == null || windowId =='' || windowId == 'undefined' )
	{
			var currentUrl = window.frameElement.src;
			var idStart = currentUrl.substring( currentUrl.indexOf('windowId') ) .substring(9);
			var endPos = idStart.indexOf('&');
			var windowId = '' ;
			if ( endPos == -1 )
				windowId = idStart;
			else {
				windowId = idStart.substring(0,endPos);
			}
			document.getElementById("windowId").value  = windowId;
		}
		document.getElementById('dataForm:apptScreenId').value = windowId	;
		return true;
}

function openNewCalendar( hasDataErrors, hasHCEerrors ) {
		var currentScreenId = document.getElementById('windowId').value;

		var info = document.getElementById ( 'dataForm:newCalendarInfo').value;

		if ( info != null && info != '' && info!= 'undefined' && info == 'update'  )
		{
				document.getElementById ( 'dataForm:newCalendarInfo').value = '';
				return;
		}
	else if (  info != null && info != '' && info!= 'undefined' && info == 'save' )
	{
		document.getElementById ( 'dataForm:newCalendarInfo').value = '';

		var saveButton =  document.getElementById('dataForm:apptList_btn_12') ;
		if ( saveButton == null ||saveButton =='undefined' || saveButton =='' )
		{
				saveButton =  document.getElementById('apptList_btn_12') ;
		}
		saveButton.click();
		return;
	}

		var workspace = parent.Manh.mps.Webtop.getActiveWorkspace();

		if (  hasDataErrors || hasHCEerrors )
		{
			return ;
		}

		var tcAppointmentId = "";
		if(document.getElementById("dataForm:appointmentId_d"))
		{
			tcAppointmentId = document.getElementById("dataForm:appointmentId_d").innerHTML;
		}
		else if(document.getElementById("dataForm:appointmentId_det2"))
		{
			tcAppointmentId = document.getElementById("dataForm:appointmentId_det2").value;
		}
		var screen = { name : 'Appointment.calendar.screen.Calendar' 	 ,
			screenCfg : {
					title : 'Appointment Calendar',
					apptScreenId : currentScreenId,
					tcAppointmentId : tcAppointmentId,
					appointmentType : jQuery("#dataForm\\:cd10 :selected").text() ,
					appointmentTime : document.getElementById("dataForm:startTime_det").value,
					originalSlots : getGroupsAndSlots(),
					}
	};

	var currScreenObj =  parent.Webtop.ScreenManager.get(currentScreenId)  ;

	if (workspace) {
		workspace.addScreen(screen);
		//currScreenObj.close();
		currScreenObj.hide();
	}
}


function getGroupsAndSlots ( )
{
	var numOfRows = document.getElementById("dataForm:slotDetailsTable_body").rows.length;

	var groups = [];
	var slots = [];

	var index = 0;
	for ( var i=0 ;i<numOfRows; i++)
	{
		var group = document.getElementById("dataForm:slotDetailsTable:"+i+":slotId2");
		if ( group != null && group != 'undefined' && group!= '' )
		{
			groups[index] = group.value;
			slots[index] = document.getElementById("dataForm:slotDetailsTable:"+i+":slt2").value;
			index++;
		}
	}

	var originalSlots = { groups : groups , slots : slots  }
	return originalSlots ;

}


function reloadWhenSingleRow() {
		var count = GetRowCount("dataTable");
		if ( count == 1)
		{
			document.getElementById('dataForm:listView:filterId1:filterId1apply').click();
		}
}

function  storeAppointmentObjectDetails () {

	var poDueDate_cache = [] ;
	var billingMethod_cache = [];
	var  originFacility_cache =[];
	var  vendorId_cache =[];
	var vendorName_cache =[];
	var  bolNumber_cache=[];
	var  proNumber_cache =[];

	var numberofRows = document.getElementById("dataForm:apptObjTable") .rows.length;
	for ( var i=0; i< numberofRows ; i ++ )
	{

		var poDueDate = document.getElementById("dataForm:apptObjTable:"+i+":poDueDateId1").innerHTML ;
		var billingMethod = document.getElementById("dataForm:apptObjTable:"+i+":billingMethodId1").innerHTML ;
		var originFacility = document.getElementById("dataForm:apptObjTable:"+i+":originFacilityId1").innerHTML ;
		var vendorId = document.getElementById("dataForm:apptObjTable:"+i+":vendorIdId1").innerHTML ;
		var vendorName = document.getElementById("dataForm:apptObjTable:"+i+":vendorNameId1").innerHTML ;
		var bolNumber = document.getElementById("dataForm:apptObjTable:"+i+":bolNumber").innerHTML ;
		var proNumber = document.getElementById("dataForm:apptObjTable:"+i+":proNumber").innerHTML ;

		poDueDate_cache[i] = poDueDate;
		billingMethod_cache[i] = billingMethod;
		originFacility_cache[i] = originFacility;
		vendorId_cache[i] = vendorId;
		vendorName_cache[i] = vendorName;
		bolNumber_cache[i] = bolNumber;
		proNumber_cache[i] = proNumber;
	}

	document.getElementById("dataForm:poDueDate_cache").value = poDueDate_cache ;
	document.getElementById("dataForm:billingMethod_cache").value = billingMethod_cache ;
	document.getElementById("dataForm:originFacility_cache").value = originFacility_cache ;
	document.getElementById("dataForm:vendorId_cache").value = vendorId_cache ;
	document.getElementById("dataForm:vendorName_cache").value = vendorName_cache ;
	document.getElementById("dataForm:bolNumber_cache").value = bolNumber_cache ;
	document.getElementById("dataForm:proNumber_cache").value = proNumber_cache ;

}


function setAppointmentObjectDetails () {
	var poDueDate_cache = document.getElementById("dataForm:poDueDate_cache").value  ;
	var billingMethod_cache = document.getElementById("dataForm:billingMethod_cache").value ;
	var  originFacility_cache =document.getElementById("dataForm:originFacility_cache").value ;
	var  vendorId_cache =document.getElementById("dataForm:vendorId_cache").value;
	var vendorName_cache =document.getElementById("dataForm:vendorName_cache").value ;
	var  bolNumber_cache= document.getElementById("dataForm:bolNumber_cache").value ;
	var  proNumber_cache = document.getElementById("dataForm:proNumber_cache").value ;

	poDueDate_cache = poDueDate_cache.split(",");
	billingMethod_cache= billingMethod_cache.split(",");
	originFacility_cache=originFacility_cache.split(",");
	vendorId_cache=vendorId_cache.split(",");
	vendorName_cache=vendorName_cache.split(",");
	bolNumber_cache=bolNumber_cache.split(",");
	proNumber_cache=proNumber_cache.split(",");

	var numberofRows = document.getElementById("dataForm:apptObjTable") .rows.length;
	for ( var i=0; i< numberofRows ; i ++ )  {

		var poDueDate = document.getElementById("dataForm:apptObjTable:"+i+":poDueDateId1").innerHTML;
		var billingMethod = document.getElementById("dataForm:apptObjTable:"+i+":billingMethodId1").innerHTML;
		var originFacility = document.getElementById("dataForm:apptObjTable:"+i+":originFacilityId1").innerHTML;
		var vendorId = document.getElementById("dataForm:apptObjTable:"+i+":vendorIdId1").innerHTML;
		var vendorName = document.getElementById("dataForm:apptObjTable:"+i+":vendorNameId1").innerHTML;
		var bolNumber = document.getElementById("dataForm:apptObjTable:"+i+":bolNumber").innerHTML;
		var proNumber = document.getElementById("dataForm:apptObjTable:"+i+":proNumber").innerHTML;

		if ( poDueDate == null || poDueDate == '' || poDueDate == 'undefined'    ){
			document.getElementById("dataForm:apptObjTable:"+i+":poDueDateId1").innerHTML = poDueDate_cache[i];
		}
		if  ( billingMethod == null || billingMethod == '' || billingMethod == 'undefined'   ){
			document.getElementById("dataForm:apptObjTable:"+i+":billingMethodId1").innerHTML = billingMethod_cache[i];
		}
		if  ( originFacility == null || originFacility == '' || originFacility == 'undefined'    ){
			document.getElementById("dataForm:apptObjTable:"+i+":originFacilityId1").innerHTML = originFacility_cache[i];
		}
		if ( vendorId == null || vendorId == '' || vendorId == 'undefined'       ){
			document.getElementById("dataForm:apptObjTable:"+i+":vendorIdId1").innerHTML = vendorId_cache[i];
		}
		if  ( vendorName == null || vendorName == '' || vendorName == 'undefined' )  {
			document.getElementById("dataForm:apptObjTable:"+i+":vendorNameId1").innerHTML = vendorName_cache[i];
		}
		if  ( bolNumber == null || bolNumber == '' || bolNumber == 'undefined'  ){
			document.getElementById("dataForm:apptObjTable:"+i+":bolNumber").innerHTML = bolNumber_cache[i];
		}
		if ( proNumber == null || proNumber == '' || proNumber == 'undefined'     ){
			document.getElementById("dataForm:apptObjTable:"+i+":proNumber").innerHTML = proNumber_cache[i];
		}

	}

}


function regularCalendarButtonClicked() {
	try
	{
		document.getElementById("returnFromCalendarPage").value = false;
		document.getElementById("isSave").value = false;
	}
	catch ( error)
	{
	}
}





function disableFooterButtons() {
	var buttons  = jQuery( "#dataForm\\:footer_btn").find ( "input[type=button] , input[type=submit]") ;
	for (  button in buttons )
	{
		buttons[button].disabled = true;
	}
}

function enableFooterButtons () {
	var buttons  = jQuery( "#dataForm\\:footer_btn").find ( "input[type=button] ,  input[type=submit]") ;
	for (  button in buttons )
	{
		buttons[button].disabled  = false;
	}
}



function appointmentListSave() {
	jQuery(".overlayerrorList").remove(0);
	disableFooterButtons();
}

function afterAppointmentListSave () {

	var errorsLength = jQuery(".overlayerrorList").length ;

	if ( errorsLength > 0 )
	{
		enableFooterButtons();
		return ;
	}
	else {
		location.reload(true);
	}

}

function editChecks()
{
	if ( document.getElementById("dataForm:editClicked").value =="true" )
	{
		return false;
	}
	if(isRowSelected("dataTable"))		{
		var selectedIndex = getSelectedRows('dataTable');
		var status=getFieldForRow('dataTable',selectedIndex,'appointmentTypeHid');
		var prevApptType = document.getElementById("dataForm:prevApptType");
		if(status != undefined && prevApptType != null && prevApptType != undefined  )		{
			prevApptType.value = status.value;
		}
		return true;
	} else {
		alert(messageArray[0]);
                return false;
	}
}

function editComplete()
{
	var selectedRow =  getSelectedRows('dataTable') ;
	jQuery('*[id*='+selectedRow+':tgeLink]')[0].click();

}

function enableReasonCode(){
	if(document.getElementById("dataForm:cancelled_det3").checked==true){
		document.getElementById("dataForm:cr21").disabled = false;
	}
	else{
		document.getElementById("dataForm:cr21").disabled = true;
		document.getElementById("dataForm:cr21").options[0].selected = true;
	}
}

function populateReasonCode()	{
	if(document.getElementById('dataForm:cr21') != null  &&  document.getElementById('dataForm:cr21') != undefined )	{
		document.getElementById('dataForm:cancelReasonCode_det3').value = document.getElementById('dataForm:cr21').value;
	}
}

function emailClick()
{
	if(!isRowSelected('dataTable'))
	{
		alert(messageArray[0]);
		return false;
	}
	return true;
}

function openRecomendationsDialog( hasDataErrors, hasHCEerrors ) {

	if (  hasDataErrors || hasHCEerrors )
	{
		return ;
	}
	openDialogwithId('Recommendations_Dialog');
}

function hideSynchListDetails()
{
	jQuery('*[id*=syncTabjx]').children().find('div').each (
												function ( ) {
												this.style.visibility = 'hidden'
											});
}

function checkForObjects(tableId)
{
	if( tableId != undefined )
	{
		for (i = 0; i < tableId.rows.length; i++)
		{
			poShipmentChanged('dataForm:apptObjTable:'+i+':poId2');
			poShipmentChanged('dataForm:apptObjTable:'+i+':shipId2');
			asnChanged('dataForm:apptObjTable:'+i+':asnId2');
		}
	}
}

function beforeRecurrenceSubmit()
{
	jQuery('input[id*="recurring_saveFromRecurringWindow"]')[0].value = false;
}
function openRecurrenceWindow( hasErrors, create )
{
	if ( hasErrors )
	{
		return ;
	}
	UI8Layout.doDialogById('RecurrenceWindow', true);

	jQuery('input[id*="popup_daily"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_daily"]')[0].value);
	jQuery('input[id*="popup_frequencyInDays"]')[0].value = jQuery('input[id*="recurring_frequencyInDays"]')[0].value;
	jQuery('input[id*="popup_weekly"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_weekly"]')[0].value);
	jQuery('input[id*="popup_weekly_sunday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_sunday"]')[0].value);
	jQuery('input[id*="popup_weekly_monday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_monday"]')[0].value);
	jQuery('input[id*="popup_weekly_tuesday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_tuesday"]')[0].value);
	jQuery('input[id*="popup_weekly_wednesday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_wednesday"]')[0].value);
	jQuery('input[id*="popup_weekly_thursday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_thursday"]')[0].value);
	jQuery('input[id*="popup_weekly_friday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_friday"]')[0].value);
	jQuery('input[id*="popup_weekly_saturday"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_saturday"]')[0].value);
	jQuery('input[id*="popup_monthly"]')[0].checked = JSON.parse(jQuery('input[id*="recurring_monthly"]')[0].value);
	jQuery('input[id*="popup_recurrenceStartTime"]')[0].value = jQuery('input[id*="recurring_StartDate"]')[0].value;
	jQuery('input[id*="popup_recurrenceEndTime"]')[0].value = jQuery('input[id*="recurring_EndDate"]')[0].value;

	if ( create )
	{
		jQuery('input[id*="popup_weekly"]')[0].checked = true;
		enableWeeklyOptions();
		checkWeeklyOptions();
		disableFrequency();
	}

	return false;
}
</script><script language="JavaScript" xml:space="preserve">
function populateFromDCFacilityLookupPopup(objid)
{
      var facilityObject = document.getElementById('dataForm:'+objid);
      dcFacilityLookupPopup(facilityObject);
}
function dcFacilityLookupPopup(facilityObject)
{
      var buID = getBUId();
      url='/cbo/facility/STSHDCFacilityLookupPopup.xhtml?facilityId='+encodeURIComponent(facilityObject.value)+'&facilityObjectId='+encodeURIComponent(facilityObject.name);
	  if(buID!=null || buID!=undefined)
	  {
		url = url+'&dependantId='+encodeURIComponent(buID.value);
	  }
      document.getElementById('APPT_Facility_iframeId').src = url;
	  openDialogwithId('Appt_FAC_Dialog');
}

function findMatchingPOs(selectedPOId) {

	var hidUserEnteredPOId = document.getElementById("dataForm:userEnteredPOId");
	var hidSelectedRowPOId = document.getElementById("dataForm:selectedRowPOId");
	hidSelectedRowPOId.value = selectedPOId.id;
	hidUserEnteredPOId.value = selectedPOId.value;
	document.getElementById('dataForm:apptList_btn_15').click();
}

function openMatchingPOsDialog( hasDataErrors, hasHCEerrors, noOfMatchingPOs, matchingPO ) {

	//alert(hasDataErrors + "," + hasHCEerrors  + "," +noOfMatchingPOs + "," + matchingPO);
	if (  hasDataErrors || hasHCEerrors || noOfMatchingPOs == 0)
	{
		//return ;
	}
	else if( noOfMatchingPOs == 1)
	{
		var hidSelectedRowPOId = document.getElementById("dataForm:selectedRowPOId");
		var selectedRowPO = document.getElementById(hidSelectedRowPOId.value);
		selectedRowPO.value = matchingPO;
		//return ;
	}
	else if( noOfMatchingPOs >= 1)
	{
		//openDialogwithId('MatchingPOs_Dialog');
		//return ;
	}
}

function setSelectedPO()
{
	if(isRowSelected('matchingPOTable'))
	{
		var matchingPOId  = getFieldForRow('matchingPOTable',getSelectedRows('matchingPOTable'),'matchingPOId');
		var hidSelectedRowPOId = document.getElementById("dataForm:selectedRowPOId");
		var selectedRowPO = document.getElementById(hidSelectedRowPOId.value);
		selectedRowPO.value = matchingPOId.value;

	}
	else
	{
		alert(messageArray[0]);

	}
	return false;
}

</script><input type="hidden" name="targetLink" id="targetLink" value="" tabindex="285"><div id="Actions_div" style="display:none"><div id="foACTIONS_MENU" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif">Cancel</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif">Recommend Time Slots</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif">Save</a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_menuItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true||this.className=='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif">Validate</a></li></ul></div></div></div><div id="Tools_div" style="display:none"><div id="foTools" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" id="printOrd_toolsItem" onclick="printPage()" title="Print"><img src="/lps/resources/themes/icons/mablue/foPrint.gif">Print</a></li></ul></div></div></div><div id="mBtnContainer"><input id="dataForm:apptList_menu_14_menuItemBtn" type="submit" name="dataForm:apptList_menu_14_menuItemBtn" value="" onclick="return closeAppointmentPopup();" style="display:none" tabindex="286"><input id="dataForm:apptList_menu_10_menuItemBtn" name="dataForm:apptList_menu_10_menuItemBtn" onclick="dummy();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_10_menuItemBtn','oncomplete':function(request,event,data){openRecomendationsDialog( false ,  false );},'parameters':{'dataForm:apptList_menu_10_menuItemBtn':'dataForm:apptList_menu_10_menuItemBtn'} } );return false;" style="display:none" type="button" tabindex="287"><input id="dataForm:apptList_menu_12_menuItemBtn" type="submit" name="dataForm:apptList_menu_12_menuItemBtn" value="" onclick="return ;" style="display:none" tabindex="288"><input id="dataForm:apptList_menu_9_menuItemBtn" name="dataForm:apptList_menu_9_menuItemBtn" onclick=";A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_menu_9_menuItemBtn','oncomplete':function(request,event,data){},'parameters':{'dataForm:apptList_menu_9_menuItemBtn':'dataForm:apptList_menu_9_menuItemBtn'} } );return false;" style="display:none" type="button" tabindex="289"><input id="dataForm:print_toolsItemBtn" type="submit" name="dataForm:print_toolsItemBtn" value="" style="display:none" tabindex="290"></div><script language="JavaScript" src="/lps/resources/rightclickmenu/scripts/rightclickmenu.js"></script><div id="rmenu" class="menuUI8" style="display: none;"><div id="fo" class="mo"><div class="fobody"><ul class="fotop"><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_14_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_14_menuItemBtn');}" title="Cancel"><img src="/lps/resources/menu/images/clear.gif"><span>Cancel</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_10_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_10_menuItemBtn');}" title="Recommend Time Slots"><img src="/lps/resources/menu/images/clear.gif"><span>Recommend Time Slots</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_12_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_12_menuItemBtn');}" title="Save"><img src="/lps/resources/menu/images/clear.gif"><span>Save</span></a></li><li><a class="foopt -gbl_hhc -md_mhbc" hidefocus="true" id="apptList_menu_9_rCMItem" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps#?windowId=wt-2458-b0ae-7757" onclick="if(this.disabled==true || this.className =='disabled'){return false;} else{callActionMethod('apptList_menu_9_menuItemBtn');}" title="Validate"><img src="/lps/resources/menu/images/clear.gif"><span>Validate</span></a></li></ul></div></div></div><script language="JavaScript"> var printMultiTab='false'; </script><span id="dataForm:errors"><script type="text/javascript">
_u8_ely_1({isAjax:false,list:[],ortext:"Override",name:"error_OverLay"});
</script></span><span id="dataForm:scheduleApptpanel" border="true"><div class="mtabplnbdr" id="scheduleApptDetailsTab"><script type="text/javascript">
_u8_tnl_1("scheduleApptDetailsTab",{ajaxEnabled:false,width:null,height:null,forSyncedList:false,mainContainerId:"scheduleApptDetailsTab",submitOnTabClick:false});
</script><input type="hidden" id="ajaxTabClicked" name="ajaxTabClicked" value="" tabindex="291"><input type="hidden" name="scheduleApptDetailsTab_SubmitOnTabClick" value="Clicked" id="scheduleApptDetailsTab_SubmitOnTabClick" tabindex="292"><input type="hidden" name="scheduleApptDetailsTab_selectedTab" value="tab2" id="scheduleApptDetailsTab_selectedTab" tabindex="293"><div id="scheduleApptDetailsTab_tabsout" class="tbpnltabsout"><table id="scheduleApptDetailsTab_tabstable" class="mtabs" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div id="TABH_dataForm:tab2" onclick="return _u8_tnl_6('tab2','scheduleApptDetailsTab','');" class="tab" title="Appointment Objects"><span class="tab_span tab_sel_span tab_sel_top"><a hidefocus="true" id="tab2_lnk" name="tab2" class="tab_link tab_sel_link" href="javascript:{}">Appointment Objects</a></span></div></td><td><div id="TABH_dataForm:tab4" onclick="return _u8_tnl_6('tab4','scheduleApptDetailsTab','');" class="tab" title="Additional Details"><span class="tab_span tab_top"><a hidefocus="true" id="tab4_lnk" name="tab4" class="tab_link" href="javascript:{}">Additional Details</a></span></div></td></tr></tbody></table></div><div class="tabbarstrip"></div><script type="text/javascript">
_u8_tnl_7("scheduleApptDetailsTab",{TabToHeadingMap:{tab4:"TABH_dataForm:tab4",tab2:"TABH_dataForm:tab2"},TabsOuterId:"scheduleApptDetailsTab_tabsout",TabsTableId:"scheduleApptDetailsTab_tabstable",TabIdList:["tab2","tab4"]});
</script><div class="tabpnlout" id="CONT_dataForm:tab2" style="display: block; min-width: 1559px;"><div class="mtabpnlbody -tbs_tbc -tbs_tbgc " style="min-width: 1559px;"><div class="mtabpnlscroll"><table class="tabpnlconttable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div class="pnltopdiv" id="PANEL_apptObj_listpanel_top"><div id="tr_apptObj_listpanel" class="pnlcondivhdr">
	<script>
		var messageArr = new Array(5);
		messageArr[0] = 'Please select a row';
	</script><input id="dataForm:poDueDate_cache" type="hidden" name="dataForm:poDueDate_cache" tabindex="294"><input id="dataForm:billingMethod_cache" type="hidden" name="dataForm:billingMethod_cache" tabindex="295"><input id="dataForm:originFacility_cache" type="hidden" name="dataForm:originFacility_cache" tabindex="296"><input id="dataForm:vendorId_cache" type="hidden" name="dataForm:vendorId_cache" tabindex="297"><input id="dataForm:vendorName_cache" type="hidden" name="dataForm:vendorName_cache" tabindex="298"><input id="dataForm:bolNumber_cache" type="hidden" name="dataForm:bolNumber_cache" tabindex="299"><input id="dataForm:proNumber_cache" type="hidden" name="dataForm:proNumber_cache" tabindex="300"><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:apptObjlistPanel"><div class="pager" id="dataForm:apptObjTable::pager"><span class="pagerNoWrap">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:first" name="dataForm:apptObjTable:pager:first" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:first','parameters':{'dataForm:apptObjTable:pager:first':'dataForm:apptObjTable:pager:first'} } );return false;" alt="First" type="image" src="/lps/resources/themes/icons/mablue/arrow_first_disabled.gif">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:previous" name="dataForm:apptObjTable:pager:previous" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:previous','parameters':{'dataForm:apptObjTable:pager:previous':'dataForm:apptObjTable:pager:previous'} } );return false;" alt="Previous" type="image" src="/lps/resources/themes/icons/mablue/arrow_left_disabled.gif"><input id="dataForm:apptObjTable:pager:pageInput" name="dataForm:apptObjTable:pager:pageInput" type="text" mask="N" decseparator="." onkeypress="return( (checkKey(this,event))? checkPageCount(event,0,'dataForm:apptObjTable'):false);" onchange="" onpaste="return checkCopyValue(this); " value="" size="1" style="border-style: solid; border-color:darkgray; color:darkgray; padding: .1em; margin: .1em; border-width: 1px; text-align: left;" class="PageNumberText" onfocus="this.blur();" disabled="disabled">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:next" name="dataForm:apptObjTable:pager:next" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:next','parameters':{'dataForm:apptObjTable:pager:next':'dataForm:apptObjTable:pager:next'} } );return false;" alt="Next" type="image" src="/lps/resources/themes/icons/mablue/arrow_right_disabled.gif">&nbsp;<input class="paginationCtrlDisabledCls" id="dataForm:apptObjTable:pager:last" name="dataForm:apptObjTable:pager:last" onclick="return false;;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:last','parameters':{'dataForm:apptObjTable:pager:last':'dataForm:apptObjTable:pager:last'} } );return false;" alt="Last" type="image" src="/lps/resources/themes/icons/mablue/arrow_last_disabled.gif"></span>&nbsp;&nbsp;<span class="pagerNoWrap pagersep"><input class="paginationCtrlCls" id="dataForm:apptObjTable:pager:apptObjTable_rfsh_but" name="dataForm:apptObjTable:pager:apptObjTable_rfsh_but" onclick="changeCursor('wait');resetFilterIfChanged('');;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:pager:apptObjTable_rfsh_but','oncomplete':function(request,event,data){changeCursor('default')},'parameters':{'dataForm:apptObjTable:pager:apptObjTable_rfsh_but':'dataForm:apptObjTable:pager:apptObjTable_rfsh_but'} } );return false;" alt="Refresh" type="image" src="/lps/resources/themes/icons/mablue/refresh.gif"></span><span class="pagerNoWrap">&nbsp;Displaying 0 - 0 of 0&nbsp;</span><span class="pagerNoWrap">(<span id="dataForm:apptObjTable_TotalSelectedRow">0</span> selected)</span></div><input type="hidden" id="dataForm:apptObjTable:pagerBoxValue" name="dataForm:apptObjTable:pagerBoxValue" value=""><input type="hidden" id="dataForm:apptObjTable:isPaginationEvent" name="dataForm:apptObjTable:isPaginationEvent" value=""><input type="hidden" id="dataForm:apptObjTable:pagerAction" name="dataForm:apptObjTable:pagerAction" value=""><input id="dataForm:apptObjTable:tableAjaxSubmitBtn" name="dataForm:apptObjTable:tableAjaxSubmitBtn" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptObjTable:tableAjaxSubmitBtn','parameters':{'dataForm:apptObjTable:tableAjaxSubmitBtn':'dataForm:apptObjTable:tableAjaxSubmitBtn'} } );return false;" style="display:none" type="button"><input type="hidden" name="dataForm:apptObjTable_deleteHidden" value="" id="dataForm:apptObjTable_deleteHidden"><input type="hidden" name="dataForm:apptObjTable_selectedRows" value="#:#" id="dataForm:apptObjTable_selectedRows"><div class="advtbl_contr" id="dataForm:apptObjTable_container" style="width: 1727px; height: 45px; cursor: default;"><div id="dataForm:apptObjTable_scrollDiv" class="advtbl_scrollDiv" style="height: 43px; width: 0px;"><div id="dataForm:apptObjTable_scrollDivBody" style="width: 1725px; height: 43px;"></div></div><div id="dataForm:apptObjTable_resizeDiv" class="advtbl_resizeIndi"></div><input type="hidden" name="dataForm:apptObjTable:isSortButtonClick" id="dataForm:apptObjTable:isSortButtonClick" value=""><input type="hidden" name="dataForm:apptObjTable:sortDir" id="dataForm:apptObjTable:sortDir" value="desc"><input type="hidden" name="dataForm:apptObjTable:colCount" id="dataForm:apptObjTable:colCount" value=""><input type="hidden" name="dataForm:apptObjTable:tableClicked" id="dataForm:apptObjTable:tableClicked" value=""><input type="hidden" name="dataForm:apptObjTable:tableResized" id="dataForm:apptObjTable:tableResized" value="false"><div class="advtbl_contr_head" style="position: relative; width: 1725px;" id="dataForm:apptObjTable_headDiv"><table style="table-layout: fixed; width: 1564px;" id="dataForm:apptObjTable" cellspacing="0"><colgroup><col><col><col><col><col><col><col><col><col><col><col><col><col></colgroup><thead><tr class="advtbl_hdr_row advtbl_row"><td class="advtbl_hdr_col advtbl_col -dg_tbh tbl_checkBox" valign="top" style="width: 25px;"><input type="checkbox" name="dataForm:apptObjTable_checkAll" onclick="FacesTable.prototype.checkAllClick(this,'apptObjTable');" disabled=""></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header " style="width: 171px; cursor: col-resize;"><span id="dataForm:apptObjTable:cc1" class="titleCase">Purchase Order</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header " style="width: 193px; cursor: default;"><span id="dataForm:apptObjTable:cc2" class="titleCase">Shipment</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header " style="width: 164px; cursor: default;"><span id="dataForm:apptObjTable:cc3" class="titleCase">Stop</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 190px;"><span id="dataForm:apptObjTable:cc4" class="titleCase">ASN</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 90px;"><span id="dataForm:apptObjTable:cc5" class="titleCase">PO due date</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 95px;"><span id="dataForm:apptObjTable:cc6" class="titleCase">Billing Method</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 92px;"><span id="dataForm:apptObjTable:cc7" class="titleCase">Origin Facility</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 112px;"><span id="dataForm:apptObjTable:cc8" class="titleCase">Business partner</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 93px;"><span id="dataForm:apptObjTable:cc9" class="titleCase">Vendor Name</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 88px;"><span id="dataForm:apptObjTable:cc11" class="titleCase">BOL number</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 91px;"><span id="dataForm:apptObjTable:cc12" class="titleCase">PRO number</span></td><td align="left" class="NotSortCol advtbl_hdr_col advtbl_col -dg_tbh wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header  wrap-column-header " style="width: 160px;"><span id="dataForm:apptObjTable:_colhdr_idSizeVal100">Size:WPLT</span></td></tr></thead></table></div><div id="dataForm:apptObjTable_bodyDiv" class="advtbl_contr_body" style="width: 1725px; height: 19px;"><input type="hidden" id="apptObjTable_hdnMaxIndexHldr" name="apptObjTable_hdnMaxIndexHldr" value="0"><table style="table-layout: fixed; width: 1725px;" id="dataForm:apptObjTable_body" cellspacing="0"><colgroup><col><col><col><col><col><col><col><col><col><col><col><col><col></colgroup><tbody><tr style="visibility: hidden; display: none;" id="dummyRowIpsTableId" class="advtbl_row"><td class="tbl_checkBox advtbl_col advtbl_body_col"><input type="checkbox" name="checkAll_c0_dataForm:apptObjTable" id="checkAll_c0_dataForm:apptObjTable" value="0"><input type="hidden" value="DUMMYROW" id="dataForm:apptObjTable:0:PK_0" name="dataForm:apptObjTable:0:PK_0"></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td0_edit" class="dshow">&nbsp;&nbsp;&nbsp;<input id="dataForm:apptObjTable:0:poId2" type="text" name="dataForm:apptObjTable:0:poId2" onblur="poShipmentChanged(this.id);findMatchingPOs(this);">&nbsp;<input id="dataForm:apptObjTable:0:poIdHid" type="hidden" name="dataForm:apptObjTable:0:poIdHid"><input type="hidden" name="dataForm:apptObjTable:0:markForDelete" id="dataForm:apptObjTable:0:markForDelete" value=""></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td1_edit" class="dshow">&nbsp;&nbsp;&nbsp;&nbsp;<script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:apptObjTable:0:shipId2ecId" name="dataForm:apptObjTable:0:shipId2ecId" value=""><input type="hidden" id="dataForm_apptObjTable_0_shipId2_enterKey" value="false"><input type="hidden" id="triggerdataForm_apptObjTable_0_shipId2_enterKey" value="false"><input type="text" id="dataForm:apptObjTable:0:shipId2" name="dataForm:apptObjTable:0:shipId2" onfocus="javascript: focusOnTextBox('dataForm_apptObjTable_0_shipId2_enterKey')" onkeypress="if(enterPressed(event,'dataForm:apptObjTable:0:shipId2') )return false;" value="" alt="Find Shipment" onblur="poShipmentChanged(this.id);">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-2458-b0ae-7757&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcboTransFilterLookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcboTransFilterLookupBackingBean.getBUMap%7D&amp;lookupType=Shipment&amp;is3plEnabled=true&amp;returnId=dataForm:apptObjTable:0:shipId2&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code=VSH'; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:apptObjTable:0:shipId2" title="Find Shipment" align="absmiddle" id="trigger_dataForm:apptObjTable:0:shipId2" name="trigger_dataForm:apptObjTable:0:shipId2" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_apptObjTable_0_shipId2_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_apptObjTable_0_shipId2_enterKey')">&nbsp;<input id="dataForm:apptObjTable:0:shipmentIdHid" type="hidden" name="dataForm:apptObjTable:0:shipmentIdHid"></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td2_edit" class="dshow">&nbsp;&nbsp;<input id="dataForm:apptObjTable:0:stopId2" type="text" name="dataForm:apptObjTable:0:stopId2"></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td3_edit" class="dshow">&nbsp;&nbsp;&nbsp;<script type="text/javascript" language="JavaScript">//<![CDATA[
var isDemo=false
//]]>
</script><input type="hidden" id="dataForm:apptObjTable:0:asnId2ecId" name="dataForm:apptObjTable:0:asnId2ecId" value=""><input type="hidden" id="dataForm_apptObjTable_0_asnId2_enterKey" value="false"><input type="hidden" id="triggerdataForm_apptObjTable_0_asnId2_enterKey" value="false"><input type="text" id="dataForm:apptObjTable:0:asnId2" name="dataForm:apptObjTable:0:asnId2" onfocus="javascript: focusOnTextBox('dataForm_apptObjTable_0_asnId2_enterKey')" onkeypress="if(enterPressed(event,'dataForm:apptObjTable:0:asnId2') )return false;" value="" alt="Find ASN" onblur="asnChanged(this.id);">&nbsp;<input type="image" onclick="javascript:var controlName=this.getAttribute('data');var defaultT = encodeURIComponent(document.getElementById(controlName).value); var dependentIdVar = getLookupValue('null'); var url = '/lps/resources/editControl/lookup/idLookup.jsfx?windowId=wt-2458-b0ae-7757&amp;controlName='+this.getAttribute('data')+'&amp;valueBindingString=%23%7BcboTransFilterLookupBackingBean.getOptionConstructMap%7D&amp;valueBindingBUString=%23%7BcboTransFilterLookupBackingBean.getBUMap%7D&amp;lookupType=ASN&amp;is3plEnabled=true&amp;returnId=dataForm:apptObjTable:0:asnId2&amp;dependantId='+dependentIdVar+'&amp;isJSF=true&amp;maxLength=&amp;allowSpecialChars=true&amp;formNameStr='+this.form.id+'&amp;lookup='+defaultT+'&amp;paginReq=false&amp;permission_code=VSN'; doFindDialog(url); return false;" style="cursor: pointer; border: 0px" data="dataForm:apptObjTable:0:asnId2" title="Find ASN" align="absmiddle" id="trigger_dataForm:apptObjTable:0:asnId2" name="trigger_dataForm:apptObjTable:0:asnId2" src="/lps/resources/themes/icons/mablue/find.gif" onfocus="javascript: focusOnImage('triggerdataForm_apptObjTable_0_asnId2_enterKey')" onblur="javascript: blurOnImage('triggerdataForm_apptObjTable_0_asnId2_enterKey')">&nbsp;<input id="dataForm:apptObjTable:0:asnIdHid" type="hidden" name="dataForm:apptObjTable:0:asnIdHid"></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td4_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:poDueDateId1"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td5_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:billingMethodId1"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td6_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:originFacilityId1"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td7_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:vendorIdId1"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td8_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:vendorNameId1"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td9_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:bolNumber"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td10_edit" class="dshow">&nbsp;<span id="dataForm:apptObjTable:0:proNumber"></span></div><span style="display:none">&nbsp;</span></td><td class="advtbl_col advtbl_body_col" style="white-space: nowrap;" align="left"><div id="dataForm:apptObjTable_body_tr0_td11_view" class="dhide">&nbsp;<span id="dataForm:apptObjTable:0:colval_viewcol_idSizeVal100SizeVal100"></span></div><div id="dataForm:apptObjTable_body_tr0_td11_edit" class="dshow">&nbsp;<input id="dataForm:apptObjTable:0:colval_editcol_idSizeVal100SizeVal100" type="text" name="dataForm:apptObjTable:0:colval_editcol_idSizeVal100SizeVal100" maxlength="10" readonly="readonly"></div><span style="display:none">&nbsp;</span></td></tr><tr id="dataForm:apptObjTable:nodataRow" class="advtbl_row trshow" style="visibility: inherit; display: table-row;"><td class="advtbl_col advtbl_body_col tdshow" colspan="13" align="left">No data found</td></tr></tbody></table></div><div class="emptyHoriScrollDiv"></div></div> <input type="hidden" id="dataForm:apptObjTable_trs_pageallrowskey" name="dataForm:apptObjTable_trs_pageallrowskey" value=""><input type="hidden" id="dataForm:apptObjTable_selectedRows" name="dataForm:apptObjTable_selectedRows" value=""><input type="hidden" id="dataForm:apptObjTable_selectedIdList" name="dataForm:apptObjTable_selectedIdList" value=""><input type="hidden" id="dataForm:apptObjTable_trs_allselectedrowskey" name="dataForm:apptObjTable_trs_allselectedrowskey" value="apptObjTable$:$1751860974459"><script type="text/javascript">//<![CDATA[
var  dataFormapptObjTable_tableObj=new FacesTable();
 var tableNameArray;
var tableObjectArray;
if(tableNameArray==undefined)
{
  tableNameArray=new Array();
  tableObjectArray=new Array();
}
var count=tableNameArray.length;tableNameArray[count]='apptObjTable';
tableObjectArray[count]=dataFormapptObjTable_tableObj;dataFormapptObjTable_tableObj.bind(document.getElementById('dataForm:apptObjTable_container'),document.getElementById('dataForm:apptObjTable_headDiv'), document.getElementById('dataForm:apptObjTable_bodyDiv'), document.getElementById('dataForm:apptObjTable_scrollDiv'),document.getElementById('dataForm:apptObjTable_scrollDivBody'), document.getElementById('dataForm:apptObjTable_button'),true,1,2,'dataForm:apptObjTable','edit','yes','no','0','bottom','edit',1,500,'yes','no','even','odd','Invalid Table','dataForm:apptObjTable_selectedIdList','true','apptObjTable','false' ,0,8,'false','null','null','false','-dg_tr','-dg_tar','-dg_tsr','auto','',1);
//]]>
</script><script type="text/javascript">//<![CDATA[
var msgToActivateTotal;msgToActivateTotal = "To activate this feature, click the Total button";
//]]>
</script> <input id="dataForm:addRowBtn" type="submit" name="dataForm:addRowBtn" value="Add Row" alt="Add Row" onclick="return addApptObjRow();" style="cursor:pointer" class="btn"><input id="dataForm:addMultipleBtn" type="submit" name="dataForm:addMultipleBtn" value="Add Multiple" alt="Add Multiple" onclick="return openAddRowPopUp();" style="cursor:pointer" class="btn"><input id="dataForm:deleteRowBtn" type="submit" name="dataForm:deleteRowBtn" value="Delete Row" alt="Delete Row" onclick="return deleteApptObjRow();" style="cursor:pointer" class="btn"><div id="setsizesdiv" style="display:none"><input class="btn" id="dataForm:setSizesBtn" name="dataForm:setSizesBtn" onclick="storeAppointmentObjectDetails();;A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:setSizesBtn','oncomplete':function(request,event,data){setAppointmentObjectDetails();},'parameters':{'dataForm:setSizesBtn':'dataForm:setSizesBtn'} } );return false;" value="Set PO sizes" alt="Set PO sizes" style="cursor:pointer" type="button"></div><span class="groupBtnSpace">&nbsp;</span></span>

		<script>
		var tableId = document.getElementById('dataForm:apptObjTable');
		checkForObjects(tableId);
	</script></div></div></td></tr></tbody></table></div></div></div><div class="tabpnlout" id="CONT_dataForm:tab4" style="display: none; min-width: 758px;"><div class="mtabpnlbody -tbs_tbc -tbs_tbgc " style="min-width: 758px;"><div class="mtabpnlscroll"><table class="tabpnlconttable" cellpadding="0" cellspacing="0" border="0"><tbody><tr><td><div class="pnltopdiv" id="PANEL_additionalDetailsPanel_top"><div id="tr_additionalDetailsPanel" class="pnlcondivhdr"><span xmlns="http://www.w3.org/1999/xhtml" id="dataForm:Appt_CAttributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCustAttributes_top"><table id="PANEL_ApptCustAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_CustomAttr_Header_Out">Custom Attributes</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCustAttributes" class="pnlcondiv"><table id="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">LoadType:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:id314e" name="dataForm:id314e" size="1"><option value=""></option><option value="Chep">Chep</option><option value="Peco">Peco</option><option value="Water">Water</option><option value="Broken Pallets">Broken Pallets</option></select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Priority Appointment:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:id1335e" type="text" name="dataForm:id1335e" maxlength="100"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Reserved:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:id1318e" type="text" name="dataForm:id1318e" maxlength="100"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1_cache_hd" type="hidden" name="dataForm:ApptDeatilCreate_GridLayAttr11_Panel1_cache_hd" value="cu_ca_id314e,cu_ca_id1335e,cu_ca_id1318e,"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table></div></div></span><span id="dataForm:appointmentAdditionalDetailsPanel"><script language="JavaScript" xml:space="preserve">var w3c=(document.getElementById)? true: false;
var ie5=(w3c && document.all)? true : false;
var ns6=(w3c && (navigator.appName=="Netscape"))? true: false;
currIDb=null; xoff=0; yoff=0;
currRS=null; rsxoff=0; rsyoff=0;
oldac=null; newac=null; zdx=600; mx=0; my=0;

var idlist=new Array();
idlist.btns=new Array();
idlist.btns[0]=new Image(); idlist.btns[0].src="/lcom/common/image/close.gif";
var currWindow =  null;

function truebody(){ //Dynamic Drive added function
return (document.compatMode && document.compatMode!="BackCompat")? document.documentElement : document.body
}

function isWindow(obj) {
    if (typeof(window.constructor) === 'undefined') {
        return obj instanceof window.constructor;
    } else {
        return obj.window === obj;
    }
}

function hidebox(id,isOverLay,isRefresh){
if(w3c){
var now = new Date();
var startTime = now.getTime();
var fdiv= document.getElementById(id+'_b');
var returnElement;
if (window.frames['iframe1'] && isWindow(window.frames['iframe1'])) {
	returnElement = window.frames['iframe1'].document.getElementById("returnCode");//for MACR00184546: returnId to returnCode
	if(!returnElement)//added this since returnCode is not present in some lookups like Region lookup.
	{
		returnElement = window.frames['iframe1'].document.getElementById("returnId");
	}
}
var returnField;
if (returnElement) {
	returnField = returnElement.value;
}


now = new Date();
var endTime = now.getTime();
if(isRefresh == true){
currWindow.document.body.style.cursor='wait';
currWindow.document.actionForm.dummyButton.click();
}
}
if(isOverLay == true)
	reloadMainDivAfterAjax();
if (returnField) // && document.getElementById(returnField)!=null)
// MACR00389242 : This && check is not required as it is done below.
{
	if (document.getElementById(returnField))
	{
		document.getElementById(returnField).focus();
	}
	else
	{
		var dotIndex = returnField.lastIndexOf(".");
		if ( dotIndex != -1)
		{
			returnField = returnField.substring(dotIndex+1);
		}
		var retElement = document.getElementById(returnField);
		if (retElement)
		{
			if(retElement.type!="hidden" && retElement.style.display != "none" && !retElement.disabled)
			{
				retElement.focus();
			}
		}
	}
}
if(fdiv!=null)
fdiv.parentNode.removeChild(fdiv);
}

function reloadMainDivAfterAjax()
{

 var outerPane = document.getElementById('workareadiv');
 var innerPane = document.getElementById('screen');

 if (outerPane) {
   outerPane.style.display = 'block';
 }
 if (innerPane) {
   innerPane.style.display = 'none';
 }
}

function showbox(id){
if(w3c){
var bx=document.getElementById(id+'_b').style;
var sh=document.getElementById(id+'_s').style;
bx.display='block';
sh.display='block';
sh.zIndex=++zdx;
bx.zIndex=++zdx;
}}

function minimize(){
if(w3c){
this.IDS[0].style.height=(ie5)? '28px':'24px';
this.IDS[2].style.display='none';
this.IDS[3].style.display='none';
setTimeout('ns6bugfix()',100);
}}

function restore(){
if(w3c){
var h=this.IDS[8];
this.IDS[0].style.height=h+'px'; //box
this.IDS[2].style.display='block';
this.IDS[3].style.display='block';
setTimeout('ns6bugfix()',100);
}}

function ns6bugfix(){
self.resizeBy(0,1);
self.resizeBy(0,-1);
}

function trackmouse(evt){
mx=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
my=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
if(!ns6)movepopup();
if((currIDb!=null)||(currRS!=null))return false;
}

function movepopup(){
if((currIDb!=null)&&w3c){
var x=mx+xoff;
var y=my+yoff;
currIDb.style.left=x+'px';
//currIDs.style.left=x+8+'px';
currIDb.style.top=y+'px';
//currIDs.style.top=y+8+'px';
}
if((currRS!=null)&&w3c){
var rx=mx+rsxoff;
var ry=my+rsyoff;
var c=currRS;
c.style.left=Math.max(rx,((ie5)?88:92))+'px';
c.style.top=Math.max(ry,((ie5)?68:72))+'px';
c.IDS[0].style.width=Math.max(rx+((ie5)?12:8),100)+'px';
c.IDS[0].style.height=Math.max(ry+((ie5)?12:8),80)+'px';
c.IDS[1].style.width=Math.max(rx+((ie5)?3:8),((ie5)?95:92))+'px';
c.IDS[4].style.left=parseInt(c.IDS[1].style.width)-10+'px';
c.IDS[2].style.width=Math.max(rx-((ie5)?-5:5),((ie5)?92:87))+'px';
c.IDS[2].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[6].style.width=Math.max(rx-((ie5)?-5:0),((ie5)?100:98))+'px';
c.IDS[6].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[9].style.width=Math.max(rx,((ie5)?100:98))+'px';
c.IDS[9].style.height=Math.max(ry-((ie5)?24:28),44)+'px';
c.IDS[7]=parseInt(c.IDS[0].style.height);
}}

function startRS(evt){
var ex=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
var ey=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
rsxoff=parseInt(this.style.left)-ex;
rsyoff=parseInt(this.style.top)-ey;
currRS=this;
if(ns6)this.IDS[2].style.overflow='hidden';
return false;
}

function stopdrag(){
currIDb=null;
ns6bugfix();
}

function grab_id(evt){
var ex=(ie5)?event.clientX+truebody().scrollLeft:evt.pageX;
var ey=(ie5)?event.clientY+truebody().scrollTop:evt.pageY;
xoff=parseInt(this.IDS[0].style.left)-ex;
yoff=parseInt(this.IDS[0].style.top)-ey;
currIDb=this.IDS[0];
return false;
}

/*function getScrollWidth()
{
   var w = window.pageXOffset ||
           document.body.scrollLeft ||
           document.documentElement.scrollLeft;

   return w ? w : 0;
}

function getScrollHeight()
{
   var h = window.pageYOffset ||
           document.body.scrollTop ||
           document.documentElement.scrollTop;

   return h ? h : 0;
}*/

function subBox(x,y,w,h,id,styleclass){
var v=document.createElement('div');
v.setAttribute('id',id);
v.style.left=x+'px';
v.style.top=y+'px';
(w == null || w == 'undefined') ? v.style.width='auto' : v.style.width=w+'px';
v.style.height=h+'px';
v.className=styleclass;
v.style.visibility='visible';
return v;
}

function get_cookie(Name) {
var search = Name + "="
var returnvalue = ""
if (document.cookie.length > 0) {
offset = document.cookie.indexOf(search)
if (offset != -1) {
offset += search.length
end = document.cookie.indexOf(";", offset)
if (end == -1)
end = document.cookie.length;
returnvalue=unescape(document.cookie.substring(offset, end))
}
}
return returnvalue;
}

// find absolute Y position of left corner
function findPosY(obj)
{
	var curtop = 0;
	if(obj.offsetParent)
        while(1)
        {
          curtop += obj.offsetTop;
          if(!obj.offsetParent)
            break;
          obj = obj.offsetParent;
        }
    else if(obj.y)
        curtop += obj.y;

    return curtop;
}

// find absolute X position of left corner
function findPosX(obj)
{
	var curleft = 0;
	if(obj.offsetParent)
	while(1)
	{
		curleft += obj.offsetLeft;
		if(!obj.offsetParent)
			break;
		obj = obj.offsetParent;
	}
	else if(obj.x)
		curleft += obj.x;
	return curleft;
}

function openSignoutPopup()
{
	var titleSignOut = signOutArray[0];
	var signoutWin = new ConfirmpopUp(1017, 22, 170, 95, 'signoutDiv', titleSignOut, true, true,'/lcom/common/image/confirm.gif',true,true);
	document.getElementById("screen").style.display = "block";
}

function openFilterPopup(url,title)
{
	var filterWin = new popUp(200, 100, 770, 400, 'filterDiv', title, true, false, url,'/lcom/common/image/manage.gif',true,url);
	document.getElementById("screen").style.display = "block";

}

function openPopup(x,y,w,h,url,title)
{
	var fdiv= document.getElementById('lookupDiv_b');
	var n=url.indexOf("basedata/lookup/jsp/dspRegionFacilityLookup.jsp");
	if(url.indexOf("basedata/lookup/jsp/dspRegionFacilityBULookup.jsp")>=0)
	{
		var n=url.indexOf("basedata/lookup/jsp/dspRegionFacilityBULookup.jsp");
	}
	if(n>0)// reset the height and width incase of region lookup
	{
		w=550;
		h=350;
	}
	if(fdiv == null)
		var popupWin = new popUp(x, y, w, h, 'lookupDiv', title, true, false, url,'/lcom/common/image/find.gif',true,url);
	document.getElementById("screen").style.display = "none";

}
function openPopupWithParentDisabled(x,y,w,h,url,title)
{
	var fdiv= document.getElementById('lookupDiv_b');
	if(fdiv == null)
		var popupWin = new popUp(x, y, w, h, 'lookupDiv', title, true, true, url,'',true,url);
	document.getElementById("screen").style.display = "block";
}

function closeLookupPopup()
{
	var fdiv = this.parent.document.getElementById('lookupDiv_b');
	if(fdiv != null)
	fdiv.parentNode.removeChild(fdiv);
}

function closeGeneralPopup()
{
	var fdiv = this.parent.document.getElementById('lookupDiv_b');
	if(fdiv != null)
	fdiv.parentNode.removeChild(fdiv);
}

function closeInfoPopup()
{
	var fdiv = this.parent.document.getElementById('infoDiv_b');
	if(fdiv != null)
	fdiv.parentNode.removeChild(fdiv);
}

function openGeneralPopup(x,y,w,h,url,title)
{
	var fdiv= document.getElementById('lookupDiv_b');
	if(fdiv == null)
		var infoWin = new popUp(x, y, w, h, 'lookupDiv', title, true, false, url,'',true,url);
	document.getElementById("screen").style.display = "none";
}

function openInfoPopup(w,h,url,title)
{
	var fdiv= document.getElementById('infoDiv_b');
	if(fdiv == null)
		var infoWin = new popUp(500, 500, w, h, 'infoDiv', title, false, false, url,'/lcom/common/image/info.gif',true,url);
	document.getElementById("screen").style.display = "none";
}

function openNewInfoPopup(w,h,url,title)
{
	var fdiv= document.getElementById('infoDiv_b');
	if(fdiv == null)
		var infoWin = new popUp(200, 200, w, h, 'infoDiv', title, false, false, url,'/lcom/common/image/info.gif',true,url);
	document.getElementById("screen").style.display = "none";
}

function popUp(x,y,w,h,cid,title,isdrag,isresize,url,titleImg,isOverLay,windowObj){
currWindow = windowObj;
if(w3c){
var tw, th;
w=Math.max(w,100);
h=Math.max(h,80);
var rdiv=new subBox(w-((ie5)?12:8),h-((ie5)?12:8),7,7,cid+'_rs','');
if(isresize){
rdiv.innerHTML='<img src="/lcom/common/image/resize.gif" width="7" height="7">';
rdiv.style.cursor='move';
}

//var top = getScrollHeight();
//var left = getScrollWidth();
//top += (document.documentElement.clientHeight/2) - (h / 2);
//left += (document.documentElement.clientWidth/2)  -  (w / 2);
var top = document.documentElement.clientHeight - h - 20;
var left = document.documentElement.clientWidth  - w - 20;
//alert("top: " + top + "left: " + left);
if( x > left)
	x = left;
if( y > top)
	y = top;
var outerdiv=new subBox(x,y,w,h,cid+'_b','pop');
/*var outerdiv=document.createElement('div');
outerdiv.setAttribute('id',cid+'_b');
outerdiv.className='pop';
outerdiv.style.left=x+'px';
outerdiv.style.top=y+'px';
outerdiv.style.width=w+'px';
outerdiv.style.height=h+'px';
outerdiv.style.visibility='visible';*/

var popsdw=document.createElement('div');
popsdw.setAttribute('id',cid+'_sdw');
popsdw.className='popsdw';

var titlebar=document.createElement('div');
titlebar.setAttribute('id',cid+'_t');
titlebar.className = "pophdr";
var tbw=w-((ie5)?16:15);
titlebar.style.width=tbw+'px';

var imgClick = false;
var processClick = true;
if(titleImg != '')
{
titlebar.innerHTML='<div class=poptitle><img src='+titleImg+'><span id=title>'+title+'</span></div>';
}
else{
titlebar.innerHTML='<div class=poptitle><span id=title>'+title+'</span></div>';
}

var popclose=document.createElement('div');
popclose.setAttribute('id',cid+'_btt');
popclose.className='popclose';
var imgClick = false;
popclose.innerHTML='<img  src="/lcom/common/image/close.gif" onclick=hidebox('+cid+','+isOverLay+','+imgClick+')  id="'+cid+'_cls">';

var popbdr=document.createElement('div');
popbdr.setAttribute('id',cid+'_bdr');
popbdr.className='popbdr';

//tw=(ie5)?w-11:w-10;
//th=(ie5)?h-29:h-28;
tw=(ie5)?w-8:w-7;
th=(ie5)?h-24:h-23;

popbdr.style.width=tw+'px';
popbdr.style.height=th+'px';

var popbody=document.createElement('div');
popbody.setAttribute('id',cid+'_bdy');
popbody.className='popbody';

//tw=(ie5)?w-11:w-10;
//th=(ie5)?h-29:h-28;
var content=document.createElement('div');
content.setAttribute('id',cid+'_c');
content.className='popcon';
//content.style.width=tw+'px';
//content.style.height=th+'px';
content.innerHTML="<iframe id='iframe1' src ='"+url+"' width='100%' frameborder='0' height='100%'></iframe>";

popbdr.appendChild(popbody);
popbody.appendChild(content);
outerdiv.appendChild(popsdw);
outerdiv.appendChild(titlebar);
outerdiv.appendChild(popclose);
outerdiv.appendChild(popbdr);
outerdiv.appendChild(rdiv);
document.body.appendChild(outerdiv);
content.focus();
//if(!showonstart)hidebox(cid,isOverLay);

var IDS=new Array();
IDS[0]=document.getElementById(cid+'_b');
IDS[1]=document.getElementById(cid+'_t');
IDS[2]=document.getElementById(cid+'_c');
//IDS[3]=document.getElementById(cid+'_s');
IDS[3]=document.getElementById(cid+'_rs');
IDS[4]=document.getElementById(cid+'_btt');
//IDS[6]=document.getElementById(cid+'_min');
//IDS[7]=document.getElementById(cid+'_max');
IDS[5]=document.getElementById(cid+'_cls');
IDS[6]=document.getElementById(cid+'_bdr');
IDS[7]=h;
IDS[8]=document.getElementById(cid+'_dummy');
IDS[9]=document.getElementById(cid+'_bdy');
this.IDb=IDS[0]; this.IDb.IDS=IDS;
this.IDt=IDS[1]; this.IDt.IDS=IDS;
this.IDc=IDS[2]; this.IDc.IDS=IDS;
this.IDrs=IDS[3]; this.IDrs.IDS=IDS;
this.IDbtt=IDS[4]; this.IDbtt.IDS=IDS;
this.IDcls=IDS[5]; this.IDcls.IDS=IDS;
this.IDcls.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");
if(isresize){
this.IDrs.onmousedown=startRS;
this.IDrs.onmouseup=new Function("currRS=null");
}
this.IDb.onmousedown=function(){
   //if(ns6)this.IDS[2].style.overflow='auto';
   this.style.zIndex=++zdx;
   }
if(isdrag){
this.IDt.onmousedown=grab_id;
this.IDt.onmouseup=stopdrag;
}
}
}

if(ns6)setInterval('movepopup()',40);

if(w3c){
document.onmousemove=trackmouse;
document.onmouseup=new Function("currRS=null");
}

/* A general confirm popup with non-url content having an ok and cancel button */
function ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay){
	ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay,false);
}

function ConfirmpopUp(x,y,w,h,cid,title,isdrag,isresize,titleImg,isOverLay,autoWidth){

	var tw, th;
	w=Math.max(w,100);
	h=Math.max(h,80);
	var rdiv= (autoWidth) ? new subBox(w-((ie5)?12:8),h-((ie5)?12:8),null,7,cid+'_rs','') : new subBox(w-((ie5)?12:8),h-((ie5)?12:8),7,7,cid+'_rs','');

	if(isresize){
	rdiv.style.cursor='move';
	}

	var top = document.documentElement.clientHeight - h - 20;
	var left = document.documentElement.clientWidth  - w - 20;

	if( x > left)
		x = left;
	if( y > top)
		y = top;

	var popsdw=document.createElement('div');
	popsdw.setAttribute('id',cid+'_sdw');
	popsdw.className='popsdw';
	// fall back. In case sign out popup doesnt open in the view area, assign default values to x,y.
	if( y < 0 || x < 0 ){
		y = 22;
		x = 1017;
		popsdw.style.visibility='hidden';
	}
	var outerdiv= (autoWidth) ? new subBox(x,y,null,h,cid+'_b','pop') : new subBox(x,y,w,h,cid+'_b','pop');

	titlebar=document.createElement('div');
	titlebar.setAttribute('id',cid+'_t'); //
	titlebar.className = "pophdr";

	var imgClick = false;
	var processClick = true;

	titlebar.innerHTML='<div class=poptitle><img src='+titleImg+'><span id=title>'+title+'</span></div>';

	var popclose=document.createElement('div');
	popclose.setAttribute('id',cid+'_btt');
	popclose.className='popclose';
	var imgClick = false;

	popclose.innerHTML='<img  src="/lcom/common/image/close.gif" onclick=hidebox('+cid+','+isOverLay+','+imgClick+')  id="'+cid+'_cls">';

	var popbdr=document.createElement('div');
	popbdr.setAttribute('id',cid+'_bdr');
	popbdr.className='popbdr';

	tw=(ie5)?w-9:w-8;
	th=(ie5)?h-25:h-24;

	popbdr.style.width=tw+'px';
	popbdr.style.height=th+'px';
	if(autoWidth) { popbdr.style.width = 'auto' ;}

	var content=document.createElement('div');
	content.setAttribute('id',cid+'_c');
	content.className ='signoutpopbody';
	content.innerHTML='<div class="popcon"><p>'+signOutArray[1]+'</p></div><div class="jpopftr"><button type="button" class="btn" id="'+cid+'_ok" onclick="event.cancelBubble = true; fnLogout(); return false;">'+signOutArray[2]+'</button><button type="button" class="btn" id="'+cid+'_cancel" onclick="event.cancelBubble = true; hidebox('+cid+','+isOverLay+','+imgClick+'); return false;">'+signOutArray[3]+'</button></div>';

	popbdr.appendChild(content);
	outerdiv.appendChild(popsdw);
	outerdiv.appendChild(titlebar);
	outerdiv.appendChild(popclose);
	outerdiv.appendChild(popbdr);
	outerdiv.appendChild(rdiv);
	document.body.appendChild(outerdiv);
	content.focus();

	var IDS=new Array();
	IDS[0]=document.getElementById(cid+'_b');
	IDS[1]=document.getElementById(cid+'_t');
	IDS[2]=document.getElementById(cid+'_cls');
	IDS[3]=document.getElementById(cid+'_cancel');
	IDS[4]=document.getElementById(cid+'_ok');

	this.IDb=IDS[0]; this.IDb.IDS=IDS;
	this.IDt=IDS[1]; this.IDt.IDS=IDS;
	this.IDcls=IDS[2]; this.IDcls.IDS=IDS;
	this.IDcancel=IDS[3]; this.IDcancel.IDS=IDS;

	this.IDcls.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");
	this.IDcancel.onclick=new Function("hidebox('"+cid+"',"+isOverLay+","+imgClick+");");

	if(isdrag){
	this.IDt.onmousedown=grab_id;
	this.IDt.onmouseup=stopdrag;
	}
}

function closeCChangePopup()
{
	var fdiv = this.parent.document.getElementById('infoDiv_b');
	if(fdiv != null)
	fdiv.parentNode.removeChild(fdiv);
}

function openCChangePopup(w,h,url,title)
{
	var fdiv= document.getElementById('infoDiv_b');
	if(fdiv == null)
		var infoWin = new popUp(300, 300, w, h, 'infoDiv', title, true, false, url,'',true,url);
	document.getElementById("screen").style.display = "none";
}



</script><span id="dataForm:Appt_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptAttributes_top"><table id="PANEL_ApptAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_Header_Out"></span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptAttributes" class="pnlcondiv"><table id="dataForm:gl4" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Appointment priority:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:ap10" name="dataForm:ap10" size="1" tabindex="351">	<option value="">None</option>
	<option value="4">Critical</option>
	<option value="3">High</option>
	<option value="1">Low</option>
	<option value="5">Most Critical</option>
	<option value="2">Normal</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment status:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:appointmentStatus_det1" type="hidden" name="dataForm:appointmentStatus_det1" tabindex="352"><input id="dataForm:appointmentStatus_det3" type="text" name="dataForm:appointmentStatus_det3" disabled="disabled" tabindex="353"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl4","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:Appt_Comment_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCommentAttributes_top"><table id="PANEL_ApptCommentAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_comment_Header_Out">Comments</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCommentAttributes" class="pnlcondiv"><table id="dataForm:gl5" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Comment code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:cc10" name="dataForm:cc10" size="1" tabindex="354">	<option value="">None</option>
	<option value="936">Carrier Delay</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Appointment comments:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:appointmentComments_det1" type="text" name="dataForm:appointmentComments_det1" tabindex="355"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl5","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:Appt_requestor_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptRequestorAttributes_top"><table id="PANEL_ApptRequestorAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_requestor_Header_Out">Requestor</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptRequestorAttributes" class="pnlcondiv"><table id="dataForm:gl6" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Request type:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:rt11" name="dataForm:rt11" size="1" tabindex="356">	<option value="">None</option>
	<option value="AFAX">Auto-fax</option>
	<option value="EDI">EDI</option>
	<option value="EDI1">EDI1</option>
	<option value="EDI2">EDI2</option>
	<option value="EDI3">EDI3</option>
	<option value="EDI4">EDI4</option>
	<option value="EDI5">EDI5</option>
	<option value="EMAI">Email</option>
	<option value="FAX">Facsimile</option>
	<option value="PDA">PDA</option>
	<option value="PH">Telephone</option>
	<option value="WEB">Web</option>
	<option value="XML">XML</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Requester name:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:requesterName_det1" type="text" name="dataForm:requesterName_det1" tabindex="357"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl6","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:Appt_carrier_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptCarrierAttributes_top"><table id="PANEL_ApptCarrierAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_carrier_Header_Out">Carrier</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptCarrierAttributes" class="pnlcondiv"></div></div></span><span id="dataForm:Appt_trailer_Attributes" border="true"><script id="dataForm:j_id218" type="text/javascript">//<![CDATA[
		loadTrailerDetails=function(){A4J.AJAX.Submit('dataForm',null,{'similarityGroupingId':'dataForm:j_id218','parameters':{'dataForm:j_id218':'dataForm:j_id218'} } )};
		//]]></script><div class="pnltopdiv" id="PANEL_ApptTrailerAttributes_top"><table id="PANEL_ApptTrailerAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_trailer_Header_Out">Trailer</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptTrailerAttributes" class="pnlcondiv"><table id="dataForm:gl8" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Trailer:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerNumber" type="text" name="dataForm:trailerNumber" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id219','parameters':{'dataForm:j_id219':'dataForm:j_id219'} } )" tabindex="358"><a id="dataForm:apptPO_dtlLnk_id" name="dataForm:apptPO_dtlLnk_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps?windowId=wt-2458-b0ae-7757" onclick="return false;"><img id="dataForm:POapptImg" src="/lcom/common/image/find.gif" alt="Find Trailer" onclick="getTrailerLookupforAppointment();" title="Find Trailer"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tractorNumber" type="text" name="dataForm:tractorNumber" onchange="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id220','parameters':{'dataForm:j_id220':'dataForm:j_id220'} } )" tabindex="362"><a id="dataForm:tractorLookup_id" name="dataForm:tractorLookup_id" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps?windowId=wt-2458-b0ae-7757" onclick="return false;"><img id="dataForm:tracLkpImg" src="/lcom/common/image/find.gif" alt="Find Tractor" onclick="getTractorLookupforAppointment();" title="Find Tractor"></a></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Load position:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:lp10" name="dataForm:lp10" size="1" tabindex="359">	<option value="">None</option>
	<option value="4">Nose</option>
	<option value="8">Tail</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tractorLicenseNumber_det3" type="text" name="dataForm:tractorLicenseNumber_det3" disabled="disabled" tabindex="363"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Trailer license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerLicenseNumber" type="text" name="dataForm:trailerLicenseNumber" disabled="disabled" tabindex="360"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Tractor license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:tratcorLicenseState_det3" type="text" name="dataForm:tratcorLicenseState_det3" disabled="disabled" tabindex="364"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Trailer license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:trailerLicenseState" type="text" name="dataForm:trailerLicenseState" disabled="disabled" tabindex="361"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap"></span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:previousTrailer" type="hidden" name="dataForm:previousTrailer" tabindex="365"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl8","_u8_gl_1",{layout:"v"});
</script><input id="dataForm:hiddentractorLicenseNumber" type="hidden" name="dataForm:hiddentractorLicenseNumber" tabindex="366"><input id="dataForm:hiddentratcorLicenseState" type="hidden" name="dataForm:hiddentratcorLicenseState" tabindex="367"><input id="dataForm:hiddentrailerLicenseState" type="hidden" name="dataForm:hiddentrailerLicenseState" tabindex="368"><input id="dataForm:hiddentrailerLicenseNumber" type="hidden" name="dataForm:hiddentrailerLicenseNumber" tabindex="369"></div></div></span><span id="dataForm:Appt_driver_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptDriverAttributes_top"><table id="PANEL_ApptDriverAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_driver_Header_Out">Driver</span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptDriverAttributes" class="pnlcondiv"><table id="dataForm:gl9" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Driver name:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverName_det2" type="text" name="dataForm:driverName_det2" onblur="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:j_id222','parameters':{'dataForm:j_id222':'dataForm:j_id222'} } )" tabindex="370"><a id="dataForm:driver_link" name="dataForm:driver_link" href="https://ahold-tlm.logistics.com/appointment/ui/jsf/appointmentList.jsflps?windowId=wt-2458-b0ae-7757" onclick="return false;"><img id="dataForm:loc3LookupDriver" src="/lps/resources/themes/icons/mablue/find.gif" alt="Find Driver" onclick="getDriverForAppointment(event);" title="Find Driver"></a><input id="dataForm:previousDriver" type="hidden" name="dataForm:previousDriver" tabindex="371"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Driver license expiry:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseExp_det3" type="text" name="dataForm:driverLicenseExp_det3" disabled="disabled" tabindex="374"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Driver license state:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseState_det3" type="text" name="dataForm:driverLicenseState_det3" disabled="disabled" tabindex="372"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Driver license number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseNumber_det3" type="text" name="dataForm:driverLicenseNumber_det3" disabled="disabled" tabindex="375"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr><tr><td><span style="white-space:nowrap">Driver license country:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:driverLicenseCountry_det3" type="text" name="dataForm:driverLicenseCountry_det3" disabled="disabled" tabindex="373"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Contact number:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:beeperNumber_det3" type="text" name="dataForm:beeperNumber_det3" disabled="disabled" tabindex="376"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:gl9","_u8_gl_1",{layout:"v"});
</script></div></div><input id="dataForm:hiddendriverLicenceState" type="hidden" name="dataForm:hiddendriverLicenceState" tabindex="377"><input id="dataForm:hiddendriverLicenceCountry" type="hidden" name="dataForm:hiddendriverLicenceCountry" tabindex="378"><input id="dataForm:hiddenLicenseExpiryDate" type="hidden" name="dataForm:hiddenLicenseExpiryDate" tabindex="379"><input id="dataForm:hiddenLicenseNumber" type="hidden" name="dataForm:hiddenLicenseNumber" tabindex="380"><input id="dataForm:hiddendriverContactNum" type="hidden" name="dataForm:hiddendriverContactNum" tabindex="381"></span><span id="dataForm:Appt_cancel_Attributes" border="true"><div class="pnltopdiv" id="PANEL_ApptcancelAttributes_top"><table id="PANEL_ApptcancelAttributesheaderdiv" class="pnlheader"><tbody><tr><td><span class="pnlhdrchcont -cpl_pttc"><span id="dataForm:Apptcreate_cancel_Header_Out"></span></span></td><td class="pnlhdrsepln"></td></tr></tbody></table><div id="tr_ApptcancelAttributes" class="pnlcondiv"><table id="dataForm:g20" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Canceled:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><input id="dataForm:cancelled_det3" type="checkbox" name="dataForm:cancelled_det3" onclick="enableReasonCode();" tabindex="382"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><span style="white-space:nowrap">Cancel reason code:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:cr21" name="dataForm:cr21" size="1" disabled="disabled" onchange="populateReasonCode();" tabindex="383">	<option value="">None</option>
	<option value="57">Carrier Error </option>
	<option value="55">Mechanical Breakdown</option>
	<option value="441">Rec Whse Refusal</option>
	<option value="56">Shipper Delay </option>
	<option value="54">Traffic Accident </option>
</select><input id="dataForm:cancelReasonCode_det3" type="hidden" name="dataForm:cancelReasonCode_det3" tabindex="384"></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:g20","_u8_gl_1",{layout:"v"});
</script></div></div></span><span id="dataForm:Appt_source_Attributes" border="true"></span><div style="white-space:nowrap;vertical-align:inherit"><div id="uirecommendationscap" class="captionLeftNoWrap" style="width:50%;"><span title=""></span><span class="notRequired">p</span>&nbsp;</div><input id="dataForm:selectedUirecommendation" type="hidden" name="dataForm:selectedUirecommendation" tabindex="385"></div></span><input id="dataForm:recurring_saveFromRecurringWindow" type="hidden" name="dataForm:recurring_saveFromRecurringWindow" value="false" tabindex="386"><input id="dataForm:recurring_StartDate" type="hidden" name="dataForm:recurring_StartDate" value="" tabindex="387"><input id="dataForm:recurring_EndDate" type="hidden" name="dataForm:recurring_EndDate" value="" tabindex="388"><input id="dataForm:recurring_daily" type="hidden" name="dataForm:recurring_daily" value="false" tabindex="389"><input id="dataForm:recurring_frequencyInDays" type="hidden" name="dataForm:recurring_frequencyInDays" value="" tabindex="390"><input id="dataForm:recurring_weekly" type="hidden" name="dataForm:recurring_weekly" value="false" tabindex="391"><input id="dataForm:recurring_monthly" type="hidden" name="dataForm:recurring_monthly" value="false" tabindex="392"><input id="dataForm:recurring_monday" type="hidden" name="dataForm:recurring_monday" value="false" tabindex="393"><input id="dataForm:recurring_tuesday" type="hidden" name="dataForm:recurring_tuesday" value="false" tabindex="394"><input id="dataForm:recurring_wednesday" type="hidden" name="dataForm:recurring_wednesday" value="false" tabindex="395"><input id="dataForm:recurring_thursday" type="hidden" name="dataForm:recurring_thursday" value="false" tabindex="396"><input id="dataForm:recurring_friday" type="hidden" name="dataForm:recurring_friday" value="false" tabindex="397"><input id="dataForm:recurring_saturday" type="hidden" name="dataForm:recurring_saturday" value="false" tabindex="398"><input id="dataForm:recurring_sunday" type="hidden" name="dataForm:recurring_sunday" value="false" tabindex="399"></div></div></td></tr></tbody></table></div></div></div><script type="text/javascript">
_u8_tnl_5("scheduleApptDetailsTab",{tab4:"CONT_dataForm:tab4",tab2:"CONT_dataForm:tab2"});
</script><script type="text/javascript">
_u8_tnl_2("scheduleApptDetailsTab","tab2");
</script></div><input id="dataForm:prevPage" type="hidden" name="dataForm:prevPage" value="/appointment/ui/jsf/scheduleAppointment.xhtml" tabindex="400"></span><div id="dataForm:spi"></div>

					</div>
				</td>
			</tr>
			<tr>
				<td id="workareafootertd" class="workareafootertd">
					<div id="workareafooter" class="workareafooter" style="width: 990px;"><div id="_footerDivComponent">

	            	<script>
                        var messageArray = new Array(5);
                        messageArray[0] = 'Please\x20select\x20a\x20row';
                        messageArray[1] = 'Invalid\x20Appointment\x20Status\x20to\x20approve\x20the\x20Appointment.';
                        messageArray[2] = 'Invalid\x20Appointment\x20Status\x20to\x20check\x2Din\x20the\x20Appointment.';
                        messageArray[3] = 'Multiple\x20selection\x20not\x20allowed\x20for\x20this\x20action.';
                        messageArray[4] = 'Do\x20you\x20want\x20to\x20delete\x20this\x20row\x3F';
                        messageArray[5] = 'No\x20PO\x2FShipment\x2FASN\x20on\x20Appointment.Do\x20you\x20want\x20to\x20save\x20it\x20as\x20Blind\x20Appointment\x3F';

                  </script><span id="dataForm:footer_btn"><script language="JavaScript" src="/lps/resources/actionpanel/moreButton/scripts/moreButton.js"></script><div id="soheaderbuttons" class="actionPanelLeft"><input id="dataForm:apptScreenId" type="hidden" name="dataForm:apptScreenId" tabindex="401"><input id="dataForm:newCalendarInfo" type="hidden" name="dataForm:newCalendarInfo" tabindex="402"><input type="button" class="btn" id="apptList_btn_14" onclick="if(this.disabled==true) {return false;} else{closeAppointmentPopup()};postForm('#{appointmentBackingBean.cancelAction}','apptList_btn_14','soheaderbuttons');" value="Cancel" tabindex="403"><input class="btn" id="dataForm:apptList_btn_10" name="dataForm:apptList_btn_10" onclick="dummy();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_10','oncomplete':function(request,event,data){openRecomendationsDialog( false ,  false );},'parameters':{'dataForm:apptList_btn_10':'dataForm:apptList_btn_10'} } );return false;" value="Recommend Time Slots" alt="Recommend Time Slots" type="button" tabindex="404"><input type="button" class="btn" id="apptList_btn_12" onclick="if(this.disabled==true) {return false;} else{populateScreenId(); disableFooterButtons(); };postForm('#{appointmentBackingBean.saveAction}','apptList_btn_12','soheaderbuttons');" value="Save" tabindex="405"><input class="btn" id="dataForm:apptList_btn_11" name="dataForm:apptList_btn_11" onclick="populateScreenId();checkBlindAppointment();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_11','parameters':{'dataForm:apptList_btn_11':'dataForm:apptList_btn_11'} } );return false;" value="Save with Best Fit" alt="Save with Best Fit" type="button" tabindex="406"><input class="btn" id="dataForm:apptList_btn_9" name="dataForm:apptList_btn_9" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_9','parameters':{'dataForm:apptList_btn_9':'dataForm:apptList_btn_9'} } );return false;" value="Validate" alt="Validate" type="button" tabindex="407"><input class="btn" id="dataForm:apptList_btn_15" name="dataForm:apptList_btn_15" onclick="A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:apptList_btn_15','oncomplete':function(request,event,data){openMatchingPOsDialog( false ,  false , 0, ''  );},'parameters':{'dataForm:apptList_btn_15':'dataForm:apptList_btn_15'} } );return false;" value="matchingPOs" alt="matchingPOs" style="visibility: hidden;" type="button" tabindex="408"><span class="groupBtnSpace">&nbsp;</span><input type="hidden" id="moreActionTargetLinksoheaderbuttons" name="moreActionTargetLinksoheaderbuttons" value="" tabindex="409"><input type="hidden" id="moreActionButtonPressedsoheaderbuttons" name="moreActionButtonPressedsoheaderbuttons" value="" tabindex="410"></div></span></div>
					</div>
				</td>
			</tr>
		</tbody></table>
                    <input type="hidden" id="backingBeanName" name="backingBeanName" value="appointmentBackingBean" tabindex="411">
                    </td>
                </tr>
            </tbody></table>
            </td>
        </tr>

    </tbody></table>
    </div><input type="hidden" name="javax.faces.ViewState" id="javax.faces.ViewState" value="-599432405341708189:6058846941603891548" autocomplete="off" tabindex="412">
</form><script language="JavaScript">document.title="Add Appointment | Manhattan Associates";</script><span id="title"></span>

<script type="text/javascript">
//<![CDATA[
    _script_pg_2 ();
   	 UI8Layout.onload.push(appendWindowId);
     if(typeof showFilter == 'function'){
        UI8Layout.onload.push(showFilter);
     }
     UI8Layout.onafterajax = function() {
	   	 appendWindowId();
         if(typeof showFilter == 'function'){
            showFilter();
         }
	   	 if(typeof afterajaxCallback == 'function') {
	   		afterajaxCallback();
	   	 }
		 document.body.style.cursor="default";
		 var layDiv = document.getElementById("overlapLayer");
		  if(layDiv){
			  layDiv.style.height = 0;
			  layDiv.style.width = 0;
			  layDiv.style.left = 0;
			  layDiv.style.top = 0;
		  }
 	}
    A4J.AJAX.AddListener(UI8Layout);

	//code changed for MACR00645636
	A4J.AJAX.onError = function(req, status, message) {
		document.body.style.cursor = "default";
		var layDiv = document.getElementById("overlapLayer");
		if (layDiv) {
			layDiv.style.height = 0;
			layDiv.style.width = 0;
			layDiv.style.left = 0;
			layDiv.style.top = 0;
		}
	}

//]]>

</script>


<script xmlns="http://www.w3.org/1999/xhtml">A4J.AJAX._scriptEvaluated=true;</script></body></html>