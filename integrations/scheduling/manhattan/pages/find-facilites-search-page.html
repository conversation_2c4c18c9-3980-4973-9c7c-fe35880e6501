<html xmlns="http://www.w3.org/1999/xhtml"><head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<script src="/a4j/g/3_3_3.Finalorg.ajax4jsf.javascript.AjaxScript.xhtml*.jsf*.jsfx*.jsflps" type="text/javascript"></script><link rel="stylesheet" type="text/css" href="/lcom/common/css/styles.css">
		<link rel="stylesheet" type="text/css" href="/lcom/common/css/treeview.css">
		<link rel="stylesheet" type="text/css" href="/lcom/common/css/navbar.css">
		<link rel="stylesheet" type="text/css" href="/lcom/common/css/LPSNavbar.css">
		<link rel="stylesheet" type="text/css" href="/lps/resources/common/css/LPSStyles.css">
		<script language="JavaScript">
		var UI8 = true;
	    var HFI = UI8;
		</script>
	<script language="JavaScript" src="/cbo/js/FacilityLookup.js"></script></head><body><span id="FacilityLookup_script"></span><script language="JavaScript" src="/lps/resources/common/UI08Common.js"></script><span id="UILayout_script"></span>
<form id="dataForm" name="dataForm" method="post" action="/cbo/facility/STSHDCFacilityLookupPopup.xhtml" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="dataForm" value="dataForm">
<input type="hidden" name="uniqueToken" value="20"><table id="dataForm:FacilityLookup_bu_1" cellpadding="2" border="0"><tbody><tr><td><span style="white-space:nowrap">Business Unit:</span><span class="notRequired">&nbsp;</span></td><td><div style="white-space:nowrap"><select id="dataForm:bussinessUnit" name="dataForm:bussinessUnit" size="1" onchange="selectBU()">	<option value="ALL" selected="selected">ALL</option>
	<option value="849">PREPAID</option>
</select></div></td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr></tbody></table><script type="text/javascript">
_u8_tb_1("dataForm:FacilityLookup_bu_1","_u8_gl_1",{layout:"v"});
</script><script language="JavaScript" src="/lps/resources/panel/scripts/panel.js"></script><div class="pnltopdiv" id="PANEL_FacilityLookup_Serc_Pnl_top"><div id="tr_FacilityLookup_Serc_Pnl" class="pnlcondivhdr"><table id="dataForm:FacilityLookup_pg_1" width="100%">
<tbody>
<tr>
<td><span id="dataForm:FacilityLookup_FindFacilityLabelText">Find Facility</span></td>
<td><span id="dataForm:FacilityLookup_PrimaryAliasLabelText">Primary Alias</span></td>
</tr>
<tr>
<td><input id="dataForm:FacilityLookup_FindFacilityText" type="text" name="dataForm:FacilityLookup_FindFacilityText" value="DCDA09P"></td>
<td><input id="dataForm:FacilityLookup_PrimaryAliasText" type="text" name="dataForm:FacilityLookup_PrimaryAliasText" value="*">&nbsp;<input class="btn" id="dataForm:FacilityLookup_LookupImage" name="dataForm:FacilityLookup_LookupImage" onclick="searchFacilityAliasIds();A4J.AJAX.Submit('dataForm',event,{'similarityGroupingId':'dataForm:FacilityLookup_LookupImage','parameters':{'dataForm:FacilityLookup_LookupImage':'dataForm:FacilityLookup_LookupImage'} } );return false;" value="Find" type="button"></td>
</tr>
<tr>
<td><span id="dataForm:FacilityLookup_CityLabelText">City</span></td>
<td><span id="dataForm:FacilityLookup_StateProvLabelText">State/Province</span></td>
</tr>
<tr>
<td><input id="dataForm:FacilityLookup_CityText" type="text" name="dataForm:FacilityLookup_CityText" value="*"></td>
<td><input id="dataForm:FacilityLookup_StateProvText" type="text" name="dataForm:FacilityLookup_StateProvText" value="*"><input id="dataForm:FacilityLookup_FacilityObjectId" type="hidden" name="dataForm:FacilityLookup_FacilityObjectId" value="dataForm:facility_det"><input id="dataForm:FacilityLookup_ShipThrough" type="hidden" name="dataForm:FacilityLookup_ShipThrough" value="false">
							<input type="hidden" name="dependantId" value=""></td>
</tr>
<tr>
<td><input id="dataForm:FacilityLookup_GroupId" type="hidden" name="dataForm:FacilityLookup_GroupId"></td>
<td><input id="dataForm:DCFacilityLookup" type="hidden" name="dataForm:DCFacilityLookup" value="true"></td>
</tr>
</tbody>
</table>
</div></div><div class="pnltopdiv" id="PANEL_FacilityLookup_List_Pnl_top"><div id="tr_FacilityLookup_List_Pnl" class="pnlcondivhdr"><table id="dataForm:FacilityLookup_spg_1" width="100%">
<tbody>
<tr>
<td><span id="dataForm:FacilityLookup_NoRecordsText">No Matching Records Found</span></td>
</tr>
</tbody>
</table>
</div></div><input type="hidden" name="javax.faces.ViewState" id="javax.faces.ViewState" value="1727778904649385865:5115581354967995661" autocomplete="off">
</form>
			<script>
				fac_msgs[0]="Please\x20enter\x20a\x20search\x20criteria";
			    fac_msgs[1]="Please\x20select\x20a\x20facility\x20id"
			    fac_msgs[2]="Facility\x20Alias\x20Id\x20not\x20found";
			    fac_msgs[3]="combo\x20object\x20not\x20found";
			</script>

</body></html>