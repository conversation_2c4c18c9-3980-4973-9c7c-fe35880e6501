"""Manhattan utilities."""

import re
import time
from datetime import datetime, timedelta

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.common.exceptions import ElementClickInterceptedException

from integrations.scheduling.models import AppointmentSlot, WarehouseDetails
from utils.logging import logger
from utils.selenium import safe_click, wait_for_element, wait_for_page_load


def wait_for_ajax_refresh(driver, locator, old_html, timeout=10):
    try:
        WebDriverWait(driver, timeout, poll_frequency=0.5).until(
            lambda d: d.find_element(*locator).get_attribute("innerHTML")
            != old_html
        )
        print("AJAX complete.")
        return True
    except Exception as e:
        print(f"AJAX update wait failed: {e}")
        return False


def change_iframe(
    driver: WebDriver,
    iframe_locator: tuple,
    timeout: int = 10,
    index: int = False,
    switch_to_default: bool = True,
) -> None:
    """
    Change the iframe context of the driver.

    Args:
        driver: WebDriver instance
        iframe_locator: Locator of the iframe
        timeout: Timeout in seconds
        index: Index of the iframe if there are multiple iframes with the same locator
        switch_to_default: Whether to switch to the default content before changing iframe
    """
    if switch_to_default:
        driver.switch_to.default_content()

    wait_for_page_load(driver)

    logger.info(f"Changing iframe to {iframe_locator}")

    iframe = wait_for_element(driver, iframe_locator, timeout)
    if not index:
        if not iframe:
            raise Exception("Required Iframe not found")
        driver.switch_to.frame(iframe)
    else:
        iframes = driver.find_elements(*iframe_locator)
        if not iframes:
            raise Exception("Required Iframe not found")
        driver.switch_to.frame(iframes[index])


def is_stale(element):
    try:
        # Any harmless property access like `.tag_name` or `.is_enabled()` will do
        _ = element.tag_name
        return False
    except Exception:
        return True


def parse_warehouse_details(
    facility_text: str, open_slots: list[AppointmentSlot] = None
) -> WarehouseDetails:
    try:
        name_part, address_part = re.match(
            r"^(.*?)\s*\((.*?)\)$", facility_text
        ).groups()
        address_parts = [part.strip() for part in address_part.split(",")]

        # Parse address components
        warehouse_id = ""
        if name_part.strip():
            warehouse_id = name_part.strip()

        city = address_parts[1]
        state = address_parts[2]
        zip_code = address_parts[3]
        country = address_parts[4]

        return WarehouseDetails(
            id=warehouse_id,
            city=city,
            state=state,
            zipCode=zip_code,
            country=country,
            openSlots=open_slots,
        )
    except Exception as e:
        print(f"Error parsing facilityText: {e}")
        return WarehouseDetails()


def parse_warehouse_name(driver: WebDriver) -> str:
    """Parse warehouse name from the web page.

    Args:
        driver: WebDriver instance
        warehouse: WarehouseDetails object containing facility information

    Returns:
        WarehouseDetails: Updated warehouse object with name
    """
    driver.implicitly_wait(1)
    warehouse_name = ""

    try:
        warehouse_name_locator = (By.ID, "dataForm:facility_d00")
        warehouse_name = driver.find_element(
            *warehouse_name_locator
        ).text.strip()
    except Exception as e:
        logger.error(f"Error fetchinf warehouse name: {e}")

    driver.implicitly_wait(10)
    return warehouse_name


def click_on_toolbar(driver: WebDriver) -> bool:
    """Click on the specified toolbar in the Manhattan UI.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if successful, False otherwise
    """
    toolbar_locator = (By.ID, "button-1013")
    overlay_locator = (By.CLASS_NAME, "x-mask")  # <-- the blocker
    max_retries = 3

    for attempt in range(max_retries):
        try:
            logger.info(
                f"Clicking on toolbar (attempt {attempt + 1}/{max_retries})"
            )

            WebDriverWait(driver, 10).until_not(
                EC.presence_of_element_located(overlay_locator)
            )

            toolbar = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable(toolbar_locator)
            )

            safe_click(driver, toolbar)
            return True

        except Exception as e:
            logger.warning(
                f"Toolbar not clickable yet or overlay present: {e}"
            )
            continue

    return False


def click_appointment_link(driver: WebDriver) -> bool:
    """Click on the appointment link in the Manhattan UI.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if successful, False otherwise
    """
    count = 0

    while count < 3:
        try:
            logger.info(f"Clicking on appointment link (attempt {count+1}/3)")

            appointment_link_locator = (
                By.XPATH,
                "//div[@class='wt-menu-item' and @part='Resource Management']//span[normalize-space(text())='Appointments']",
            )
            appointment_link = wait_for_element(
                driver, appointment_link_locator, timeout=10
            )
            if not appointment_link:
                count += 1
                continue

            safe_click(driver, appointment_link)
            return True
        except Exception as e:
            return False

    return False


def click_add_button(driver: WebDriver) -> bool:
    """Click on the add button in the Manhattan UI.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if successful, False otherwise
    """
    count = 0

    while count < 3:
        try:
            logger.info(f"Clicking on add button (attempt {count+1}/3)")

            add_button_locator = (
                By.XPATH,
                "//input[@type='submit' and @value='Add']",
            )
            add_button = wait_for_element(
                driver, add_button_locator, timeout=10
            )
            if not add_button:
                count += 1
                continue

            safe_click(driver, add_button)
            return True
        except Exception as e:
            return False

    return False


def fetch_or_select_facilities(
    driver: WebDriver, city: str, state: str, facility_name: str = ""
) -> dict | None:
    """Fetch or select facilities in the Manhattan UI.

    Args:
        driver: WebDriver instance
        city: City to search for facilities
        state: State to search for facilities
        facility_name: Name of the facility to select (if any)

    Returns:
        dict: Dictionary of facility IDs and names if found, otherwise None
    """

    logger.info(f"Fetching or selecting facilities: {facility_name}")

    try:
        # Wait for the facility search icon to be present
        facility_search_icon_locator = (By.ID, "dataForm:facility_det_id")

        facility_search_icon = driver.find_element(
            *facility_search_icon_locator
        )

        if not facility_search_icon:
            logger.error("Facility search icon not found")
            return None

        # Click on the facility search icon
        safe_click(driver, facility_search_icon)

        facility_iframe_locator = (
            By.XPATH,
            "//iframe[@id='APPT_Facility_iframeId']",
        )
        change_iframe(driver, facility_iframe_locator, switch_to_default=False)

        # Wait for the facility city/state search input to be present
        facility_city_input_locator = (
            By.ID,
            "dataForm:FacilityLookup_CityText",
        )
        facility_city_input = wait_for_element(
            driver, facility_city_input_locator, timeout=10
        )

        if not facility_city_input:
            logger.error("Facility city input not found")
            return None

        facility_state_input_locator = (
            By.ID,
            "dataForm:FacilityLookup_StateProvText",
        )
        facility_state_input = wait_for_element(
            driver, facility_state_input_locator, timeout=10
        )

        if not facility_state_input:
            logger.error("Facility state input not found")
            return None

        # Enter the city and state
        facility_city_input.clear()
        facility_city_input.send_keys(city)
        facility_state_input.clear()
        facility_state_input.send_keys(state)

        # Click the search button
        search_button_locator = (
            By.XPATH,
            "//input[@type='button' and @value='Find']",
        )

        search_button = wait_for_element(
            driver, search_button_locator, timeout=10
        )

        if not search_button:
            logger.error("Search button not found")
            return None

        safe_click(driver, search_button)

        # Wait for the results to load
        facility_results_locator = (
            By.ID,
            "dataForm:FacilityLookup_SearchResult",
        )
        facility_results = wait_for_element(
            driver, facility_results_locator, timeout=10
        )

        if not facility_results:
            logger.error("Facility search results not found")
            return None

        select = Select(facility_results)
        facility_dict = {}

        for option in select.options:
            value = option.get_attribute("value")
            text = option.text.strip()

            if facility_name:
                if value == facility_name:
                    facility_dict[value] = text
                    select.select_by_value(value)

                    # Click the search button
                    search_button_locator = (
                        By.XPATH,
                        "//input[@type='button' and @value='Find']",
                    )
                    search_button = wait_for_element(
                        driver, search_button_locator, timeout=5
                    )
                    safe_click(driver, search_button)

                    # Click the select button after search
                    select_button_locator = (
                        By.ID,
                        "dataForm:FacilityLookup_SelectButton",
                    )
                    select_button = wait_for_element(
                        driver, select_button_locator, timeout=5
                    )
                    safe_click(driver, select_button)

                    return facility_dict
            else:
                facility_dict[value] = text

        return facility_dict
    except Exception as e:
        logger.error(f"Error fetching facilities: {str(e)}")
        return None


def fill_appointment_details(
    driver: WebDriver,
    po_id: str,
    facility_id: str,
    scheduled_datetime: datetime,
    appointment_type: str,
) -> tuple[bool, str]:
    """Input appointment details in the Manhattan UI.

    Args:
        driver: WebDriver instance
        po_id: Purchase Order ID
        facility_id: Facility ID
        scheduled_datetime: Scheduled date and time in ISO format
        appointment_type: Type of appointment
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Inputting appointment details")

        facility_locator = (By.ID, "dataForm:facility_det")
        facility_input = wait_for_element(
            driver,
            facility_locator,
            condition=EC.visibility_of_element_located,
        )

        if not facility_input:
            logger.error("Facility input not found")
            return False, "Facility input not found"

        facility_input.clear()
        facility_input.send_keys(facility_id)

        formatted_value = scheduled_datetime.strftime("%-m/%-d/%y %H:%M")
        start_time_input = wait_for_element(
            driver,
            (By.ID, "dataForm:startTime_det"),
            condition=EC.visibility_of_element_located,
        )
        start_time_input.clear()
        start_time_input.send_keys(formatted_value)

        select_element = wait_for_element(
            driver,
            (By.ID, "dataForm:cd10"),
            condition=EC.visibility_of_element_located,
        )
        select = Select(select_element)
        select.select_by_visible_text(appointment_type)

        po_input_cell = None
        count = 0
        while not po_input_cell:
            po_input_cell_locator = (By.ID, "dataForm:apptObjTable:0:poId2")
            po_input_cell = wait_for_element(
                driver,
                po_input_cell_locator,
                condition=EC.visibility_of_element_located,
            )

            if is_stale(po_input_cell):
                count += 1
                if count >= 3:
                    time.sleep(1)  # Wait for UI to stabilize
                    logger.error("PO input cell not found.")
                    return False, "PO input cell not found"

        po_input_cell.clear()
        po_input_cell.send_keys(po_id)
        safe_click(driver, (By.TAG_NAME, "body"))

        # For proper authentication of PO
        driver.implicitly_wait(1)
        wait_for_element(driver, (By.ID, "dataForm:MatchingPOs_Dialog"))
        wait_for_element(driver, (By.ID, "dataForm:MatchingPOs_Dialog"))
        driver.implicitly_wait(10)

        return True, "Appointment details input successfully"
    except Exception as e:
        logger.error(f"Error inputting appointment details: {str(e)}")
        return False, "Error inputting appointment details"


def fetch_recommended_slots(
    driver: WebDriver, start_date: str, end_date: str
) -> tuple[list[AppointmentSlot], str]:
    """
    Fetch recommended slots from the Manhattan UI.

    Args:
        driver: WebDriver instance
        start_date: Start date in "YYYY-MM-DD" format
        end_date: End date in "YYYY-MM-DD" format

    Returns:
        tuple: (list of AppointmentSlot, message string)
    """
    try:
        logger.info("Fetching recommended slots")

        recommended_slots = []
        appended_slots = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")

        while current_date <= end_date_obj:
            try:
                # 1. Input start date into input field
                formatted_value = current_date.strftime("%-m/%-d/%y 00:00")
                input_field = driver.find_element(
                    By.ID, "dataForm:startTime_det"
                )
                input_field.clear()
                input_field.send_keys(formatted_value)

                # 2. Click "Recommend Time Slots" button
                recommend_btn = driver.find_element(
                    By.ID, "dataForm:apptList_btn_10"
                )
                safe_click(driver, recommend_btn)

                # 4. Skip date if error is present
                error_element = wait_for_element(
                    driver,
                    (
                        By.XPATH,
                        "//*[@id='er_d1_bid' and normalize-space(.) != '']",
                    ),
                )

                if error_element and error_element.is_displayed():
                    current_date += timedelta(days=1)
                    try:
                        close_error_btn = driver.find_element(
                            By.ID, "er_d1_c1"
                        )
                        safe_click(driver, close_error_btn)
                    except Exception:
                        pass
                    continue

                wait_for_element(
                    driver,
                    (
                        By.XPATH,
                        "//*[@id='dataForm:recommendationTable_bodyDiv' and normalize-space(.) != '']",
                    ),
                )

                WebDriverWait(driver, 10).until(
                    lambda d: d.find_element(
                        By.ID, "dataForm:recommendationTable_bodyDiv"
                    ).text.strip()
                    != ""
                )

                # 5. Extract recommended slot data
                table_div = driver.find_element(
                    By.ID, "dataForm:recommendationTable_bodyDiv"
                )
                rows = table_div.find_elements(
                    By.XPATH,
                    "//table[@id='dataForm:recommendationTable_body']//tr[contains(@class, 'advtbl_row')]",
                )

                for row in rows:
                    try:
                        desc_span = row.find_element(
                            By.XPATH,
                            ".//span[contains(@id, 'recommendationSlotDesc')]",
                        )
                        text = desc_span.text.strip()
                        if not text:
                            continue

                        # Extract start time
                        start_raw = (
                            text.split("Start-Time:")[1]
                            .split("Departure-Time:")[0]
                            .strip()
                        )
                        start_dt = datetime.strptime(
                            start_raw, "%m/%d/%y %H:%M"
                        )

                        if start_dt in appended_slots:
                            continue

                        recommended_slots.append(
                            AppointmentSlot(
                                scheduledTime=start_dt.strftime(
                                    "%Y-%m-%dT%H:%M:%S.000Z"
                                ),
                                duration=60,
                            )
                        )

                        appended_slots.append(start_dt)
                    except Exception:
                        continue

                try:
                    rec_popup_close = driver.find_element(
                        By.ID, "dataForm:Recommendations_DialogTemplate_cCId"
                    )
                    safe_click(driver, rec_popup_close)
                except Exception:
                    pass
            except Exception:
                pass  # Ignore and move to next date

            current_date += timedelta(days=1)

        return recommended_slots, "Slots fetched successfully."
    except Exception as e:
        logger.error(f"Error fetching recommended slots: {str(e)}")
        return [], "Error fetching recommended slots"


def select_slot(
    driver: WebDriver, schedule_time: datetime
) -> tuple[bool, str]:
    """Select a specific appointment slot in the Manhattan UI.

    Args:
        driver: WebDriver instance
        schedule_time : Scheduled time of the slot as a datetime object
    Returns:
        tuple: (bool indicating success, message string)
    """
    try:
        logger.info(f"Selecting slot: {schedule_time}")

        formatted_value = schedule_time.strftime("%-m/%-d/%y %H:%M")
        input_field = driver.find_element(By.ID, "dataForm:startTime_det")
        input_field.clear()
        input_field.send_keys(formatted_value)

        # 2. Click "Recommend Time Slots" button
        recommend_btn = driver.find_element(By.ID, "dataForm:apptList_btn_10")
        safe_click(driver, recommend_btn)

        error_element = wait_for_element(
            driver,
            (By.XPATH, "//*[@id='er_d1_bid' and normalize-space(.) != '']"),
        )

        if error_element and error_element.is_displayed():
            error_message = error_element.text.strip().replace("\n", " ")
            return False, f"Error: {error_message}"

        WebDriverWait(driver, 10).until(
            lambda d: d.find_element(
                By.ID, "dataForm:recommendationTable_bodyDiv"
            ).text.strip()
            != ""
        )

        try:
            radio_button = driver.find_element(
                By.XPATH,
                f"//table[@id='dataForm:recommendationTable_body']//tr[contains(., 'Start-Time:{formatted_value}')]//input[@type='radio']",
            )
            safe_click(driver, radio_button)
        except Exception:
            return False, f"No matching slot found for {formatted_value}"

        recommended_slot_select_button = driver.find_element(
            By.ID, "dataForm:selectBtn"
        )
        safe_click(driver, recommended_slot_select_button)

        return True, "Slot selected successfully"
    except Exception as e:
        logger.error(f"Error selecting slot: {str(e)}")
        return False, "Error selecting slot"


def handle_override_if_present(driver: WebDriver, timeout: int = 10) -> None:
    """Clicks the 'Override' link if the warning popup appears."""
    try:
        override_xpath = (
            "//div[@id='er_d1_bid']//a[contains(text(), 'Override')]"
        )

        override_link = wait_for_element(
            driver,
            (By.XPATH, override_xpath),
            timeout=timeout,
            condition=EC.visibility_of_element_located,
        )

        if override_link.is_displayed():
            override_link.click()
    except Exception:
        # Override link not found or not clickable
        pass


def get_appointment_id(driver: WebDriver, timeout: int = 10) -> str:
    """
    Fetches and returns the Appointment ID from the Manhattan UI.

    Args:
        driver (WebDriver): The Selenium WebDriver instance.
        timeout (int): Timeout for waiting for the element to be visible.

    Returns:
        str: The Appointment ID, or an empty string if not found.
    """
    try:
        elem = wait_for_element(
            driver,
            (By.ID, "dataForm:appointmentId_d"),
            timeout=timeout,
            condition=EC.visibility_of_element_located,
        )
        return elem.text.strip() if elem else "-"
    except Exception as e:
        logger.warning(f"Could not fetch appointment ID: {e}")
        return "-"


def search_appointment(
    driver: WebDriver, appt_id: str, timeout: int = 10
) -> bool:
    """
    Uses the 'Quick filter → Appointment' field to search for a specific appointment ID.
    Returns True if the Apply button got clicked, False otherwise.
    """
    try:
        # 1. Ensure Quick Filter panel is expanded
        toggle = wait_for_element(
            driver,
            (By.ID, "dataForm:listView:filterId1:QFCap"),
            timeout=timeout,
        )
        safe_click(driver, toggle)

        # 2. Click on quick filter
        quick_filter = driver.find_element(By.ID, "filterId1_li2")
        safe_click(driver, quick_filter)

        # 3. Locate the Appointment input field
        appt_input = wait_for_element(
            driver,
            (By.ID, "dataForm:listView:filterId1:field10value1"),
            timeout=timeout,
            condition=EC.element_to_be_clickable,
        )
        appt_input.clear()
        appt_input.send_keys(appt_id)

        # 4. Click the 'Apply' button next to Quick filter
        apply_btn = wait_for_element(
            driver,
            (By.ID, "dataForm:listView:filterId1:filterId1apply"),
            timeout=timeout,
            condition=EC.element_to_be_clickable,
        )

        table_body_locator = (By.ID, "dataForm:listView:dataTable_body")
        table_body_element = driver.find_element(
            By.ID, "dataForm:listView:dataTable_body"
        )
        table_body_html = table_body_element.get_attribute("innerHTML")

        safe_click(driver, apply_btn)

        # 5. Wait for AJAX to complete and table to refresh
        if not wait_for_ajax_refresh(
            driver, table_body_locator, table_body_html, timeout=timeout
        ):
            logger.error(
                "Failed to refresh appointment search results after applying filter."
            )
            return False

        return True
    except Exception as e:
        logger.error(
            f"Failed to search appointment {appt_id} via quick filter: {e}"
        )
        return False


def wait_until_clickable(driver, by_locator, timeout=10):
    WebDriverWait(driver, timeout).until(EC.element_to_be_clickable(by_locator))

    # Additional safety: wait until no overlay visually blocks the element
    WebDriverWait(driver, timeout).until_not(
        lambda d: d.find_element(By.ID, "overlapLayer").is_displayed()
    )

    # And double-check it's not overlapping the checkbox
    overlay = driver.find_element(By.ID, "overlapLayer")
    if overlay.is_displayed():
        raise Exception("Overlay still visible after wait")

    return driver.find_element(*by_locator)

def select_appointment_if_found(driver: WebDriver, timeout: int = 10) -> bool:
    """
    Checks if an appointment is present in the search result table,
    and selects it by clicking the checkbox.

    Returns:
        True if appointment found and checkbox clicked, False if no appointment.
    """
    try:
        wait_for_element(
            driver,
            (By.ID, "dataForm:listView:dataTable_body"),
            timeout=timeout,
        )

        # Check if 'No data found' row is visible
        start_time = time.time()
        no_data_row = driver.find_element(
            By.ID, "dataForm:listView:dataTable:nodataRow"
        )
        print(f"Waited for {time.time() - start_time} seconds for no data row")
        if no_data_row.is_displayed():
            print("No appointment found.")
            return False

        # Wait for overlay to disappear (important!)
        start_time = time.time()
        WebDriverWait(driver, timeout).until(
            EC.invisibility_of_element_located((By.ID, "overlapLayer"))
        )
        print(f"Waited for {time.time() - start_time} seconds for overlay")

        # Then attempt checkbox click
        checkbox = wait_until_clickable(driver, (By.ID, "checkAll_c0_dataForm:listView:dataTable"), timeout)

        # JS fallback
        try:
            checkbox.click()
            return True
        except ElementClickInterceptedException:
            print("Click intercepted again. Using JS click.")
            driver.execute_script("arguments[0].click();", checkbox)
            return True
        # print("Checkbox found and clickable", checkbox)
        # print("Checkbox found and clickable", checkbox.is_displayed())
        # if checkbox.is_displayed() and not checkbox.is_selected():
        #     checkbox.click()
        #     wait_for_element(
        #         driver,
        #         (By.ID, "syncTab"),
        #         condition=EC.visibility_of_element_located,
        #     )
        #     return True

        # return False

    except Exception as e:
        print(f"Error during appointment selection: {e}")
        return False


def click_edit_button(driver: WebDriver, timeout: int = 10) -> bool:
    """
    Clicks the 'Edit' button in the Manhattan UI to modify an appointment.

    Args:
        driver: WebDriver instance
        timeout: Timeout in seconds for waiting for the button to be clickable

    Returns:
        bool: True if the button was clicked successfully, False otherwise
    """
    try:
        edit_button_locator = (By.ID, "dataForm:listView:apptList_btn_2")
        edit_button = wait_for_element(
            driver,
            edit_button_locator,
            timeout=timeout,
            condition=EC.element_to_be_clickable,
        )
        safe_click(driver, edit_button)

        wait_for_element(
            driver,
            (By.ID, "syncTab"),
            condition=EC.visibility_of_element_located,
        )

        return True
    except Exception as e:
        logger.error(f"Error clicking edit button: {e}")
        return False
