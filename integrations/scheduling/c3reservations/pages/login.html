<!doctype html>
<html
  lang="en"
  class="notranslate TridactylThemeMidnight dxFirefox dxWindowsPlatform dxBrowserVersion-134"
  translate="no"
  data-critters-container=""
>
  <head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta charset="utf-8" />
    <meta name="robots" content="noindex" />
    <meta name="google" content="notranslate" />
    <title>C3 - Login</title>
    <!-- base href="https://www.c3reservations.com/cswg/app/" -->
    <meta name="theme-color" content="#2A6871" />
    <link
      rel="manifest"
      href="https://www.c3reservations.com/cswg/app/manifest.json"
    />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://www.c3reservations.com/cswg/app/favicon.ico"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="preconnect stylesheet" href="login_files/roboto.css" />
    <link rel="preconnect stylesheet" href="login_files/icon.css" />

    <!-- DevEx -->

    <link rel="stylesheet" href="login_files/c3-devex-light.css" />

    <!-- Google OAUTH -->
    <script src="login_files/cb=gapi.loaded_0" async=""></script>
    <script async="" src="login_files/google-analytics_analytics.js"></script>
    <script src="login_files/platform.js" gapi_processed="true"></script>

    <!-- Google Analytics -->
    <script>
      (function (i, s, o, g, r, a, m) {
        i["GoogleAnalyticsObject"] = r;
        (i[r] =
          i[r] ||
          function () {
            (i[r].q = i[r].q || []).push(arguments);
          }),
          (i[r].l = 1 * new Date());
        (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m);
      })(
        window,
        document,
        "script",
        "https://www.google-analytics.com/analytics.js",
        "ga",
      );

      ga("create", "UA-990658-3", "auto"); // Change the UA-ID to the one you got from Google Analytics
    </script>

    <script type="text/javascript">
      function consumeAuthentication(params) {
        if (window.authenticationReference) {
          window.authenticationReference.zone.run(() => {
            window.authenticationReference.consumeAuthentication(params);
          });
        }
      }
    </script>
    <style>
      @charset "UTF-8";
      body {
        scrollbar-color: #daf6ee;
      }
      html {
        --mat-ripple-color: rgba(0, 0, 0, 0.1);
      }
      html {
        --mat-option-selected-state-label-text-color: #378a9f;
        --mat-option-label-text-color: rgba(0, 0, 0, 0.87);
        --mat-option-hover-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-option-focus-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-option-selected-state-layer-color: rgba(0, 0, 0, 0.04);
      }
      html {
        --mat-optgroup-label-text-color: rgba(0, 0, 0, 0.87);
      }
      html {
        --mat-full-pseudo-checkbox-selected-icon-color: #5dd9b5;
        --mat-full-pseudo-checkbox-selected-checkmark-color: white;
        --mat-full-pseudo-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);
        --mat-full-pseudo-checkbox-disabled-selected-checkmark-color: white;
        --mat-full-pseudo-checkbox-disabled-unselected-icon-color: #b0b0b0;
        --mat-full-pseudo-checkbox-disabled-selected-icon-color: #b0b0b0;
        --mat-minimal-pseudo-checkbox-selected-checkmark-color: #5dd9b5;
        --mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: #b0b0b0;
      }
      html {
        --mat-app-background-color: white;
        --mat-app-text-color: rgba(0, 0, 0, 0.87);
      }
      html {
        --mat-option-label-text-font: Roboto, sans-serif;
        --mat-option-label-text-line-height: 24px;
        --mat-option-label-text-size: 16px;
        --mat-option-label-text-tracking: normal;
        --mat-option-label-text-weight: 400;
      }
      html {
        --mat-optgroup-label-text-font: Roboto, sans-serif;
        --mat-optgroup-label-text-line-height: 24px;
        --mat-optgroup-label-text-size: 16px;
        --mat-optgroup-label-text-tracking: normal;
        --mat-optgroup-label-text-weight: 400;
      }
      html {
        --mdc-elevated-card-container-shape: 4px;
        --mdc-outlined-card-container-shape: 4px;
        --mdc-outlined-card-outline-width: 1px;
      }
      html {
        --mdc-elevated-card-container-color: #fafafa;
        --mdc-elevated-card-container-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
          0px 1px 1px 0px rgba(0, 0, 0, 0.14),
          0px 1px 3px 0px rgba(0, 0, 0, 0.12);
        --mdc-outlined-card-container-color: #fafafa;
        --mdc-outlined-card-outline-color: rgba(0, 0, 0, 0.12);
        --mdc-outlined-card-container-elevation: 0px 0px 0px 0px
            rgba(0, 0, 0, 0.2),
          0px 0px 0px 0px rgba(0, 0, 0, 0.14),
          0px 0px 0px 0px rgba(0, 0, 0, 0.12);
        --mat-card-subtitle-text-color: rgba(0, 0, 0, 0.54);
      }
      html {
        --mat-card-title-text-font: Roboto, sans-serif;
        --mat-card-title-text-line-height: 32px;
        --mat-card-title-text-size: 16px;
        --mat-card-title-text-tracking: normal;
        --mat-card-title-text-weight: 400;
        --mat-card-subtitle-text-font: Roboto, sans-serif;
        --mat-card-subtitle-text-line-height: 24px;
        --mat-card-subtitle-text-size: 14px;
        --mat-card-subtitle-text-tracking: normal;
        --mat-card-subtitle-text-weight: 500;
      }
      html {
        --mdc-linear-progress-active-indicator-height: 4px;
        --mdc-linear-progress-track-height: 4px;
        --mdc-linear-progress-track-shape: 0;
      }
      html {
        --mdc-plain-tooltip-container-shape: 4px;
        --mdc-plain-tooltip-supporting-text-line-height: 16px;
      }
      html {
        --mdc-plain-tooltip-container-color: #616161;
        --mdc-plain-tooltip-supporting-text-color: #fff;
      }
      html {
        --mdc-plain-tooltip-supporting-text-font: Roboto, sans-serif;
        --mdc-plain-tooltip-supporting-text-size: 12px;
        --mdc-plain-tooltip-supporting-text-weight: 400;
        --mdc-plain-tooltip-supporting-text-tracking: normal;
      }
      html {
        --mdc-filled-text-field-active-indicator-height: 1px;
        --mdc-filled-text-field-focus-active-indicator-height: 2px;
        --mdc-filled-text-field-container-shape: 4px;
        --mdc-outlined-text-field-outline-width: 1px;
        --mdc-outlined-text-field-focus-outline-width: 2px;
        --mdc-outlined-text-field-container-shape: 4px;
      }
      html {
        --mdc-filled-text-field-caret-color: #378a9f;
        --mdc-filled-text-field-focus-active-indicator-color: #378a9f;
        --mdc-filled-text-field-focus-label-text-color: rgba(
          55,
          138,
          159,
          0.87
        );
        --mdc-filled-text-field-container-color: #f0f0f0;
        --mdc-filled-text-field-disabled-container-color: whitesmoke;
        --mdc-filled-text-field-label-text-color: rgba(0, 0, 0, 0.6);
        --mdc-filled-text-field-hover-label-text-color: rgba(0, 0, 0, 0.6);
        --mdc-filled-text-field-disabled-label-text-color: rgba(0, 0, 0, 0.38);
        --mdc-filled-text-field-input-text-color: rgba(0, 0, 0, 0.87);
        --mdc-filled-text-field-disabled-input-text-color: rgba(0, 0, 0, 0.38);
        --mdc-filled-text-field-input-text-placeholder-color: rgba(
          0,
          0,
          0,
          0.6
        );
        --mdc-filled-text-field-error-hover-label-text-color: #f44336;
        --mdc-filled-text-field-error-focus-label-text-color: #f44336;
        --mdc-filled-text-field-error-label-text-color: #f44336;
        --mdc-filled-text-field-error-caret-color: #f44336;
        --mdc-filled-text-field-active-indicator-color: rgba(0, 0, 0, 0.42);
        --mdc-filled-text-field-disabled-active-indicator-color: rgba(
          0,
          0,
          0,
          0.06
        );
        --mdc-filled-text-field-hover-active-indicator-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mdc-filled-text-field-error-active-indicator-color: #f44336;
        --mdc-filled-text-field-error-focus-active-indicator-color: #f44336;
        --mdc-filled-text-field-error-hover-active-indicator-color: #f44336;
        --mdc-outlined-text-field-caret-color: #378a9f;
        --mdc-outlined-text-field-focus-outline-color: #378a9f;
        --mdc-outlined-text-field-focus-label-text-color: rgba(
          55,
          138,
          159,
          0.87
        );
        --mdc-outlined-text-field-label-text-color: rgba(0, 0, 0, 0.6);
        --mdc-outlined-text-field-hover-label-text-color: rgba(0, 0, 0, 0.6);
        --mdc-outlined-text-field-disabled-label-text-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mdc-outlined-text-field-input-text-color: rgba(0, 0, 0, 0.87);
        --mdc-outlined-text-field-disabled-input-text-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mdc-outlined-text-field-input-text-placeholder-color: rgba(
          0,
          0,
          0,
          0.6
        );
        --mdc-outlined-text-field-error-caret-color: #f44336;
        --mdc-outlined-text-field-error-focus-label-text-color: #f44336;
        --mdc-outlined-text-field-error-label-text-color: #f44336;
        --mdc-outlined-text-field-error-hover-label-text-color: #f44336;
        --mdc-outlined-text-field-outline-color: rgba(0, 0, 0, 0.38);
        --mdc-outlined-text-field-disabled-outline-color: rgba(0, 0, 0, 0.06);
        --mdc-outlined-text-field-hover-outline-color: rgba(0, 0, 0, 0.87);
        --mdc-outlined-text-field-error-focus-outline-color: #f44336;
        --mdc-outlined-text-field-error-hover-outline-color: #f44336;
        --mdc-outlined-text-field-error-outline-color: #f44336;
        --mat-form-field-focus-select-arrow-color: rgba(55, 138, 159, 0.87);
        --mat-form-field-disabled-input-text-placeholder-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mat-form-field-state-layer-color: rgba(0, 0, 0, 0.87);
        --mat-form-field-error-text-color: #f44336;
        --mat-form-field-select-option-text-color: inherit;
        --mat-form-field-select-disabled-option-text-color: GrayText;
        --mat-form-field-leading-icon-color: unset;
        --mat-form-field-disabled-leading-icon-color: unset;
        --mat-form-field-trailing-icon-color: unset;
        --mat-form-field-disabled-trailing-icon-color: unset;
        --mat-form-field-error-focus-trailing-icon-color: unset;
        --mat-form-field-error-hover-trailing-icon-color: unset;
        --mat-form-field-error-trailing-icon-color: unset;
        --mat-form-field-enabled-select-arrow-color: rgba(0, 0, 0, 0.54);
        --mat-form-field-disabled-select-arrow-color: rgba(0, 0, 0, 0.38);
        --mat-form-field-hover-state-layer-opacity: 0.04;
        --mat-form-field-focus-state-layer-opacity: 0.08;
      }
      html {
        --mat-form-field-container-height: 56px;
        --mat-form-field-filled-label-display: block;
        --mat-form-field-container-vertical-padding: 16px;
        --mat-form-field-filled-with-label-container-padding-top: 24px;
        --mat-form-field-filled-with-label-container-padding-bottom: 8px;
      }
      html {
        --mdc-filled-text-field-label-text-font: Roboto, sans-serif;
        --mdc-filled-text-field-label-text-size: 16px;
        --mdc-filled-text-field-label-text-tracking: normal;
        --mdc-filled-text-field-label-text-weight: 400;
        --mdc-outlined-text-field-label-text-font: Roboto, sans-serif;
        --mdc-outlined-text-field-label-text-size: 16px;
        --mdc-outlined-text-field-label-text-tracking: normal;
        --mdc-outlined-text-field-label-text-weight: 400;
        --mat-form-field-container-text-font: Roboto, sans-serif;
        --mat-form-field-container-text-line-height: 24px;
        --mat-form-field-container-text-size: 16px;
        --mat-form-field-container-text-tracking: normal;
        --mat-form-field-container-text-weight: 400;
        --mat-form-field-outlined-label-text-populated-size: 16px;
        --mat-form-field-subscript-text-font: Roboto, sans-serif;
        --mat-form-field-subscript-text-line-height: 20px;
        --mat-form-field-subscript-text-size: 12px;
        --mat-form-field-subscript-text-tracking: normal;
        --mat-form-field-subscript-text-weight: 400;
      }
      html {
        --mat-select-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-select-panel-background-color: #fafafa;
        --mat-select-enabled-trigger-text-color: rgba(0, 0, 0, 0.87);
        --mat-select-disabled-trigger-text-color: rgba(0, 0, 0, 0.38);
        --mat-select-placeholder-text-color: rgba(0, 0, 0, 0.6);
        --mat-select-enabled-arrow-color: rgba(0, 0, 0, 0.54);
        --mat-select-disabled-arrow-color: rgba(0, 0, 0, 0.38);
        --mat-select-focused-arrow-color: rgba(55, 138, 159, 0.87);
        --mat-select-invalid-arrow-color: rgba(244, 67, 54, 0.87);
      }
      html {
        --mat-select-arrow-transform: translateY(-8px);
      }
      html {
        --mat-select-trigger-text-font: Roboto, sans-serif;
        --mat-select-trigger-text-line-height: 24px;
        --mat-select-trigger-text-size: 16px;
        --mat-select-trigger-text-tracking: normal;
        --mat-select-trigger-text-weight: 400;
      }
      html {
        --mat-autocomplete-container-shape: 4px;
        --mat-autocomplete-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-autocomplete-background-color: #fafafa;
      }
      html {
        --mdc-dialog-container-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2),
          0px 24px 38px 3px rgba(0, 0, 0, 0.14),
          0px 9px 46px 8px rgba(0, 0, 0, 0.12);
        --mdc-dialog-container-shadow-color: #000;
        --mdc-dialog-container-shape: 4px;
        --mat-dialog-container-max-width: 80vw;
        --mat-dialog-container-small-max-width: 80vw;
        --mat-dialog-container-min-width: 0;
        --mat-dialog-actions-alignment: start;
        --mat-dialog-actions-padding: 8px;
        --mat-dialog-content-padding: 20px 24px;
        --mat-dialog-with-actions-content-padding: 20px 24px;
        --mat-dialog-headline-padding: 0 24px 9px;
      }
      html {
        --mdc-dialog-container-color: white;
        --mdc-dialog-subhead-color: rgba(0, 0, 0, 0.87);
        --mdc-dialog-supporting-text-color: rgba(0, 0, 0, 0.6);
      }
      html {
        --mdc-dialog-subhead-font: Roboto, sans-serif;
        --mdc-dialog-subhead-line-height: 32px;
        --mdc-dialog-subhead-size: 16px;
        --mdc-dialog-subhead-weight: 400;
        --mdc-dialog-subhead-tracking: normal;
        --mdc-dialog-supporting-text-font: Roboto, sans-serif;
        --mdc-dialog-supporting-text-line-height: 24px;
        --mdc-dialog-supporting-text-size: 16px;
        --mdc-dialog-supporting-text-weight: 400;
        --mdc-dialog-supporting-text-tracking: normal;
      }
      html {
        --mdc-switch-disabled-selected-icon-opacity: 0.38;
        --mdc-switch-disabled-track-opacity: 0.12;
        --mdc-switch-disabled-unselected-icon-opacity: 0.38;
        --mdc-switch-handle-height: 20px;
        --mdc-switch-handle-shape: 10px;
        --mdc-switch-handle-width: 20px;
        --mdc-switch-selected-icon-size: 18px;
        --mdc-switch-track-height: 14px;
        --mdc-switch-track-shape: 7px;
        --mdc-switch-track-width: 36px;
        --mdc-switch-unselected-icon-size: 18px;
        --mdc-switch-selected-focus-state-layer-opacity: 0.12;
        --mdc-switch-selected-hover-state-layer-opacity: 0.04;
        --mdc-switch-selected-pressed-state-layer-opacity: 0.1;
        --mdc-switch-unselected-focus-state-layer-opacity: 0.12;
        --mdc-switch-unselected-hover-state-layer-opacity: 0.04;
        --mdc-switch-unselected-pressed-state-layer-opacity: 0.1;
        --mat-switch-disabled-selected-handle-opacity: 0.38;
        --mat-switch-disabled-unselected-handle-opacity: 0.38;
        --mat-switch-unselected-handle-size: 20px;
        --mat-switch-selected-handle-size: 20px;
        --mat-switch-pressed-handle-size: 20px;
        --mat-switch-with-icon-handle-size: 20px;
        --mat-switch-selected-handle-horizontal-margin: 0;
        --mat-switch-selected-with-icon-handle-horizontal-margin: 0;
        --mat-switch-selected-pressed-handle-horizontal-margin: 0;
        --mat-switch-unselected-handle-horizontal-margin: 0;
        --mat-switch-unselected-with-icon-handle-horizontal-margin: 0;
        --mat-switch-unselected-pressed-handle-horizontal-margin: 0;
        --mat-switch-visible-track-opacity: 1;
        --mat-switch-hidden-track-opacity: 1;
        --mat-switch-visible-track-transition: transform 75ms 0ms
          cubic-bezier(0, 0, 0.2, 1);
        --mat-switch-hidden-track-transition: transform 75ms 0ms
          cubic-bezier(0.4, 0, 0.6, 1);
        --mat-switch-track-outline-width: 1px;
        --mat-switch-track-outline-color: transparent;
        --mat-switch-selected-track-outline-width: 1px;
        --mat-switch-disabled-unselected-track-outline-width: 1px;
        --mat-switch-disabled-unselected-track-outline-color: transparent;
      }
      html {
        --mdc-switch-selected-focus-state-layer-color: #47b3d3;
        --mdc-switch-selected-handle-color: #47b3d3;
        --mdc-switch-selected-hover-state-layer-color: #47b3d3;
        --mdc-switch-selected-pressed-state-layer-color: #47b3d3;
        --mdc-switch-selected-focus-handle-color: #2a6772;
        --mdc-switch-selected-hover-handle-color: #2a6772;
        --mdc-switch-selected-pressed-handle-color: #2a6772;
        --mdc-switch-selected-focus-track-color: #68d5f4;
        --mdc-switch-selected-hover-track-color: #68d5f4;
        --mdc-switch-selected-pressed-track-color: #68d5f4;
        --mdc-switch-selected-track-color: #68d5f4;
        --mdc-switch-disabled-selected-handle-color: #424242;
        --mdc-switch-disabled-selected-icon-color: #fff;
        --mdc-switch-disabled-selected-track-color: #424242;
        --mdc-switch-disabled-unselected-handle-color: #424242;
        --mdc-switch-disabled-unselected-icon-color: #fff;
        --mdc-switch-disabled-unselected-track-color: #424242;
        --mdc-switch-handle-surface-color: var(--mdc-theme-surface, #fff);
        --mdc-switch-handle-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
          0px 1px 1px 0px rgba(0, 0, 0, 0.14),
          0px 1px 3px 0px rgba(0, 0, 0, 0.12);
        --mdc-switch-handle-shadow-color: black;
        --mdc-switch-disabled-handle-elevation-shadow: 0px 0px 0px 0px
            rgba(0, 0, 0, 0.2),
          0px 0px 0px 0px rgba(0, 0, 0, 0.14),
          0px 0px 0px 0px rgba(0, 0, 0, 0.12);
        --mdc-switch-selected-icon-color: #fff;
        --mdc-switch-unselected-focus-handle-color: #212121;
        --mdc-switch-unselected-focus-state-layer-color: #424242;
        --mdc-switch-unselected-focus-track-color: #e0e0e0;
        --mdc-switch-unselected-handle-color: #616161;
        --mdc-switch-unselected-hover-handle-color: #212121;
        --mdc-switch-unselected-hover-state-layer-color: #424242;
        --mdc-switch-unselected-hover-track-color: #e0e0e0;
        --mdc-switch-unselected-icon-color: #fff;
        --mdc-switch-unselected-pressed-handle-color: #212121;
        --mdc-switch-unselected-pressed-state-layer-color: #424242;
        --mdc-switch-unselected-pressed-track-color: #e0e0e0;
        --mdc-switch-unselected-track-color: #e0e0e0;
        --mdc-switch-disabled-label-text-color: rgba(0, 0, 0, 0.38);
      }
      html {
        --mdc-switch-state-layer-size: 40px;
      }
      html {
        --mdc-radio-disabled-selected-icon-opacity: 0.38;
        --mdc-radio-disabled-unselected-icon-opacity: 0.38;
        --mdc-radio-state-layer-size: 40px;
      }
      html {
        --mdc-radio-state-layer-size: 40px;
        --mat-radio-touch-target-display: block;
      }
      html {
        --mat-slider-value-indicator-width: auto;
        --mat-slider-value-indicator-height: 32px;
        --mat-slider-value-indicator-caret-display: block;
        --mat-slider-value-indicator-border-radius: 4px;
        --mat-slider-value-indicator-padding: 0 12px;
        --mat-slider-value-indicator-text-transform: none;
        --mat-slider-value-indicator-container-transform: translateX(-50%);
        --mdc-slider-active-track-height: 6px;
        --mdc-slider-active-track-shape: 9999px;
        --mdc-slider-handle-height: 20px;
        --mdc-slider-handle-shape: 50%;
        --mdc-slider-handle-width: 20px;
        --mdc-slider-inactive-track-height: 4px;
        --mdc-slider-inactive-track-shape: 9999px;
        --mdc-slider-with-overlap-handle-outline-width: 1px;
        --mdc-slider-with-tick-marks-active-container-opacity: 0.6;
        --mdc-slider-with-tick-marks-container-shape: 50%;
        --mdc-slider-with-tick-marks-container-size: 2px;
        --mdc-slider-with-tick-marks-inactive-container-opacity: 0.6;
      }
      html {
        --mdc-slider-handle-color: #378a9f;
        --mdc-slider-focus-handle-color: #378a9f;
        --mdc-slider-hover-handle-color: #378a9f;
        --mdc-slider-active-track-color: #378a9f;
        --mdc-slider-inactive-track-color: #378a9f;
        --mdc-slider-with-tick-marks-inactive-container-color: #378a9f;
        --mdc-slider-with-tick-marks-active-container-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mdc-slider-disabled-active-track-color: #000;
        --mdc-slider-disabled-handle-color: #000;
        --mdc-slider-disabled-inactive-track-color: #000;
        --mdc-slider-label-container-color: #000;
        --mdc-slider-label-label-text-color: #fff;
        --mdc-slider-with-overlap-handle-outline-color: #fff;
        --mdc-slider-with-tick-marks-disabled-container-color: #000;
        --mdc-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
          0px 1px 1px 0px rgba(0, 0, 0, 0.14),
          0px 1px 3px 0px rgba(0, 0, 0, 0.12);
        --mat-slider-ripple-color: #378a9f;
        --mat-slider-hover-state-layer-color: rgba(55, 138, 159, 0.05);
        --mat-slider-focus-state-layer-color: rgba(55, 138, 159, 0.2);
        --mat-slider-value-indicator-opacity: 0.6;
      }
      html {
        --mdc-slider-label-label-text-font: Roboto, sans-serif;
        --mdc-slider-label-label-text-size: 14px;
        --mdc-slider-label-label-text-line-height: 24px;
        --mdc-slider-label-label-text-tracking: normal;
        --mdc-slider-label-label-text-weight: 500;
      }
      html {
        --mat-menu-container-shape: 4px;
        --mat-menu-divider-bottom-spacing: 0;
        --mat-menu-divider-top-spacing: 0;
        --mat-menu-item-spacing: 16px;
        --mat-menu-item-icon-size: 24px;
        --mat-menu-item-leading-spacing: 16px;
        --mat-menu-item-trailing-spacing: 16px;
        --mat-menu-item-with-icon-leading-spacing: 16px;
        --mat-menu-item-with-icon-trailing-spacing: 16px;
      }
      html {
        --mat-menu-item-label-text-color: rgba(0, 0, 0, 0.87);
        --mat-menu-item-icon-color: rgba(0, 0, 0, 0.87);
        --mat-menu-item-hover-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-menu-item-focus-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-menu-container-color: #fafafa;
        --mat-menu-divider-color: rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-menu-item-label-text-font: Roboto, sans-serif;
        --mat-menu-item-label-text-size: 16px;
        --mat-menu-item-label-text-tracking: normal;
        --mat-menu-item-label-text-line-height: 24px;
        --mat-menu-item-label-text-weight: 400;
      }
      html {
        --mdc-list-list-item-container-shape: 0;
        --mdc-list-list-item-leading-avatar-shape: 50%;
        --mdc-list-list-item-container-color: transparent;
        --mdc-list-list-item-selected-container-color: transparent;
        --mdc-list-list-item-leading-avatar-color: transparent;
        --mdc-list-list-item-leading-icon-size: 24px;
        --mdc-list-list-item-leading-avatar-size: 40px;
        --mdc-list-list-item-trailing-icon-size: 24px;
        --mdc-list-list-item-disabled-state-layer-color: transparent;
        --mdc-list-list-item-disabled-state-layer-opacity: 0;
        --mdc-list-list-item-disabled-label-text-opacity: 0.38;
        --mdc-list-list-item-disabled-leading-icon-opacity: 0.38;
        --mdc-list-list-item-disabled-trailing-icon-opacity: 0.38;
        --mat-list-active-indicator-color: transparent;
        --mat-list-active-indicator-shape: 4px;
      }
      html {
        --mdc-list-list-item-label-text-color: rgba(0, 0, 0, 0.87);
        --mdc-list-list-item-supporting-text-color: rgba(0, 0, 0, 0.54);
        --mdc-list-list-item-leading-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-list-list-item-trailing-supporting-text-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mdc-list-list-item-trailing-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-list-list-item-selected-trailing-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-list-list-item-disabled-label-text-color: black;
        --mdc-list-list-item-disabled-leading-icon-color: black;
        --mdc-list-list-item-disabled-trailing-icon-color: black;
        --mdc-list-list-item-hover-label-text-color: rgba(0, 0, 0, 0.87);
        --mdc-list-list-item-hover-leading-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-list-list-item-hover-trailing-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-list-list-item-focus-label-text-color: rgba(0, 0, 0, 0.87);
        --mdc-list-list-item-hover-state-layer-color: black;
        --mdc-list-list-item-hover-state-layer-opacity: 0.04;
        --mdc-list-list-item-focus-state-layer-color: black;
        --mdc-list-list-item-focus-state-layer-opacity: 0.12;
      }
      html {
        --mdc-list-list-item-one-line-container-height: 48px;
        --mdc-list-list-item-two-line-container-height: 64px;
        --mdc-list-list-item-three-line-container-height: 88px;
        --mat-list-list-item-leading-icon-start-space: 16px;
        --mat-list-list-item-leading-icon-end-space: 32px;
      }
      html {
        --mdc-list-list-item-label-text-font: Roboto, sans-serif;
        --mdc-list-list-item-label-text-line-height: 24px;
        --mdc-list-list-item-label-text-size: 16px;
        --mdc-list-list-item-label-text-tracking: normal;
        --mdc-list-list-item-label-text-weight: 400;
        --mdc-list-list-item-supporting-text-font: Roboto, sans-serif;
        --mdc-list-list-item-supporting-text-line-height: 20px;
        --mdc-list-list-item-supporting-text-size: 14px;
        --mdc-list-list-item-supporting-text-tracking: normal;
        --mdc-list-list-item-supporting-text-weight: 400;
        --mdc-list-list-item-trailing-supporting-text-font: Roboto, sans-serif;
        --mdc-list-list-item-trailing-supporting-text-line-height: 20px;
        --mdc-list-list-item-trailing-supporting-text-size: 12px;
        --mdc-list-list-item-trailing-supporting-text-tracking: normal;
        --mdc-list-list-item-trailing-supporting-text-weight: 400;
      }
      html {
        --mat-paginator-container-text-color: rgba(0, 0, 0, 0.87);
        --mat-paginator-container-background-color: #fafafa;
        --mat-paginator-enabled-icon-color: rgba(0, 0, 0, 0.54);
        --mat-paginator-disabled-icon-color: rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-paginator-container-size: 56px;
        --mat-paginator-form-field-container-height: 40px;
        --mat-paginator-form-field-container-vertical-padding: 8px;
      }
      html {
        --mat-paginator-container-text-font: Roboto, sans-serif;
        --mat-paginator-container-text-line-height: 20px;
        --mat-paginator-container-text-size: 12px;
        --mat-paginator-container-text-tracking: normal;
        --mat-paginator-container-text-weight: 400;
        --mat-paginator-select-trigger-text-size: 12px;
      }
      html {
        --mdc-tab-indicator-active-indicator-height: 2px;
        --mdc-tab-indicator-active-indicator-shape: 0;
        --mdc-secondary-navigation-tab-container-height: 48px;
        --mat-tab-header-divider-color: transparent;
        --mat-tab-header-divider-height: 0;
      }
      html {
        --mdc-checkbox-disabled-selected-checkmark-color: #fff;
        --mdc-checkbox-selected-focus-state-layer-opacity: 0.16;
        --mdc-checkbox-selected-hover-state-layer-opacity: 0.04;
        --mdc-checkbox-selected-pressed-state-layer-opacity: 0.16;
        --mdc-checkbox-unselected-focus-state-layer-opacity: 0.16;
        --mdc-checkbox-unselected-hover-state-layer-opacity: 0.04;
        --mdc-checkbox-unselected-pressed-state-layer-opacity: 0.16;
      }
      html {
        --mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, 0.38);
        --mdc-checkbox-selected-checkmark-color: black;
        --mdc-checkbox-selected-focus-icon-color: #5dd9b5;
        --mdc-checkbox-selected-hover-icon-color: #5dd9b5;
        --mdc-checkbox-selected-icon-color: #5dd9b5;
        --mdc-checkbox-selected-pressed-icon-color: #5dd9b5;
        --mdc-checkbox-unselected-focus-icon-color: #212121;
        --mdc-checkbox-unselected-hover-icon-color: #212121;
        --mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);
        --mdc-checkbox-unselected-pressed-icon-color: rgba(0, 0, 0, 0.54);
        --mdc-checkbox-selected-focus-state-layer-color: #5dd9b5;
        --mdc-checkbox-selected-hover-state-layer-color: #5dd9b5;
        --mdc-checkbox-selected-pressed-state-layer-color: #5dd9b5;
        --mdc-checkbox-unselected-focus-state-layer-color: black;
        --mdc-checkbox-unselected-hover-state-layer-color: black;
        --mdc-checkbox-unselected-pressed-state-layer-color: black;
        --mat-checkbox-disabled-label-color: rgba(0, 0, 0, 0.38);
      }
      html {
        --mdc-checkbox-state-layer-size: 40px;
        --mat-checkbox-touch-target-display: block;
      }
      html {
        --mdc-text-button-container-shape: 4px;
        --mdc-text-button-keep-touch-target: false;
        --mdc-filled-button-container-shape: 4px;
        --mdc-filled-button-keep-touch-target: false;
        --mdc-protected-button-container-shape: 4px;
        --mdc-protected-button-keep-touch-target: false;
        --mdc-outlined-button-keep-touch-target: false;
        --mdc-outlined-button-outline-width: 1px;
        --mdc-outlined-button-container-shape: 4px;
        --mat-text-button-horizontal-padding: 8px;
        --mat-text-button-with-icon-horizontal-padding: 8px;
        --mat-text-button-icon-spacing: 8px;
        --mat-text-button-icon-offset: 0;
        --mat-filled-button-horizontal-padding: 16px;
        --mat-filled-button-icon-spacing: 8px;
        --mat-filled-button-icon-offset: -4px;
        --mat-protected-button-horizontal-padding: 16px;
        --mat-protected-button-icon-spacing: 8px;
        --mat-protected-button-icon-offset: -4px;
        --mat-outlined-button-horizontal-padding: 15px;
        --mat-outlined-button-icon-spacing: 8px;
        --mat-outlined-button-icon-offset: -4px;
      }
      html {
        --mdc-text-button-label-text-color: black;
        --mdc-text-button-disabled-label-text-color: rgba(0, 0, 0, 0.38);
        --mat-text-button-state-layer-color: black;
        --mat-text-button-disabled-state-layer-color: black;
        --mat-text-button-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-text-button-hover-state-layer-opacity: 0.04;
        --mat-text-button-focus-state-layer-opacity: 0.12;
        --mat-text-button-pressed-state-layer-opacity: 0.12;
        --mdc-filled-button-container-color: #fafafa;
        --mdc-filled-button-label-text-color: black;
        --mdc-filled-button-disabled-container-color: rgba(0, 0, 0, 0.12);
        --mdc-filled-button-disabled-label-text-color: rgba(0, 0, 0, 0.38);
        --mat-filled-button-state-layer-color: black;
        --mat-filled-button-disabled-state-layer-color: black;
        --mat-filled-button-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-filled-button-hover-state-layer-opacity: 0.04;
        --mat-filled-button-focus-state-layer-opacity: 0.12;
        --mat-filled-button-pressed-state-layer-opacity: 0.12;
        --mdc-protected-button-container-color: #fafafa;
        --mdc-protected-button-label-text-color: black;
        --mdc-protected-button-disabled-container-color: rgba(0, 0, 0, 0.12);
        --mdc-protected-button-disabled-label-text-color: rgba(0, 0, 0, 0.38);
        --mdc-protected-button-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
          0px 2px 2px 0px rgba(0, 0, 0, 0.14),
          0px 1px 5px 0px rgba(0, 0, 0, 0.12);
        --mdc-protected-button-disabled-container-elevation-shadow: 0px 0px 0px
            0px rgba(0, 0, 0, 0.2),
          0px 0px 0px 0px rgba(0, 0, 0, 0.14),
          0px 0px 0px 0px rgba(0, 0, 0, 0.12);
        --mdc-protected-button-focus-container-elevation-shadow: 0px 2px 4px -1px
            rgba(0, 0, 0, 0.2),
          0px 4px 5px 0px rgba(0, 0, 0, 0.14),
          0px 1px 10px 0px rgba(0, 0, 0, 0.12);
        --mdc-protected-button-hover-container-elevation-shadow: 0px 2px 4px -1px
            rgba(0, 0, 0, 0.2),
          0px 4px 5px 0px rgba(0, 0, 0, 0.14),
          0px 1px 10px 0px rgba(0, 0, 0, 0.12);
        --mdc-protected-button-pressed-container-elevation-shadow: 0px 5px 5px -3px
            rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-protected-button-container-shadow-color: #000;
        --mat-protected-button-state-layer-color: black;
        --mat-protected-button-disabled-state-layer-color: black;
        --mat-protected-button-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-protected-button-hover-state-layer-opacity: 0.04;
        --mat-protected-button-focus-state-layer-opacity: 0.12;
        --mat-protected-button-pressed-state-layer-opacity: 0.12;
        --mdc-outlined-button-disabled-outline-color: rgba(0, 0, 0, 0.12);
        --mdc-outlined-button-disabled-label-text-color: rgba(0, 0, 0, 0.38);
        --mdc-outlined-button-label-text-color: black;
        --mdc-outlined-button-outline-color: rgba(0, 0, 0, 0.12);
        --mat-outlined-button-state-layer-color: black;
        --mat-outlined-button-disabled-state-layer-color: black;
        --mat-outlined-button-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-outlined-button-hover-state-layer-opacity: 0.04;
        --mat-outlined-button-focus-state-layer-opacity: 0.12;
        --mat-outlined-button-pressed-state-layer-opacity: 0.12;
      }
      html {
        --mdc-text-button-container-height: 36px;
        --mdc-filled-button-container-height: 36px;
        --mdc-outlined-button-container-height: 36px;
        --mdc-protected-button-container-height: 36px;
        --mat-text-button-touch-target-display: block;
        --mat-filled-button-touch-target-display: block;
        --mat-protected-button-touch-target-display: block;
        --mat-outlined-button-touch-target-display: block;
      }
      html {
        --mdc-text-button-label-text-font: Roboto, sans-serif;
        --mdc-text-button-label-text-size: 14px;
        --mdc-text-button-label-text-tracking: normal;
        --mdc-text-button-label-text-weight: 500;
        --mdc-text-button-label-text-transform: none;
        --mdc-filled-button-label-text-font: Roboto, sans-serif;
        --mdc-filled-button-label-text-size: 14px;
        --mdc-filled-button-label-text-tracking: normal;
        --mdc-filled-button-label-text-weight: 500;
        --mdc-filled-button-label-text-transform: none;
        --mdc-outlined-button-label-text-font: Roboto, sans-serif;
        --mdc-outlined-button-label-text-size: 14px;
        --mdc-outlined-button-label-text-tracking: normal;
        --mdc-outlined-button-label-text-weight: 500;
        --mdc-outlined-button-label-text-transform: none;
        --mdc-protected-button-label-text-font: Roboto, sans-serif;
        --mdc-protected-button-label-text-size: 14px;
        --mdc-protected-button-label-text-tracking: normal;
        --mdc-protected-button-label-text-weight: 500;
        --mdc-protected-button-label-text-transform: none;
      }
      html {
        --mdc-icon-button-icon-size: 24px;
      }
      html {
        --mdc-icon-button-icon-color: inherit;
        --mdc-icon-button-disabled-icon-color: rgba(0, 0, 0, 0.38);
        --mat-icon-button-state-layer-color: black;
        --mat-icon-button-disabled-state-layer-color: black;
        --mat-icon-button-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-icon-button-hover-state-layer-opacity: 0.04;
        --mat-icon-button-focus-state-layer-opacity: 0.12;
        --mat-icon-button-pressed-state-layer-opacity: 0.12;
      }
      html {
        --mat-icon-button-touch-target-display: block;
      }
      html {
        --mdc-fab-container-shape: 50%;
        --mdc-fab-icon-size: 24px;
        --mdc-fab-small-container-shape: 50%;
        --mdc-fab-small-icon-size: 24px;
        --mdc-extended-fab-container-height: 48px;
        --mdc-extended-fab-container-shape: 24px;
      }
      html {
        --mdc-fab-container-color: #fafafa;
        --mdc-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
          0px 6px 10px 0px rgba(0, 0, 0, 0.14),
          0px 1px 18px 0px rgba(0, 0, 0, 0.12);
        --mdc-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2),
          0px 12px 17px 2px rgba(0, 0, 0, 0.14),
          0px 5px 22px 4px rgba(0, 0, 0, 0.12);
        --mdc-fab-container-shadow-color: #000;
        --mat-fab-foreground-color: black;
        --mat-fab-state-layer-color: black;
        --mat-fab-disabled-state-layer-color: black;
        --mat-fab-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-fab-hover-state-layer-opacity: 0.04;
        --mat-fab-focus-state-layer-opacity: 0.12;
        --mat-fab-pressed-state-layer-opacity: 0.12;
        --mat-fab-disabled-state-container-color: rgba(0, 0, 0, 0.12);
        --mat-fab-disabled-state-foreground-color: rgba(0, 0, 0, 0.38);
        --mdc-fab-small-container-color: #fafafa;
        --mdc-fab-small-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
          0px 6px 10px 0px rgba(0, 0, 0, 0.14),
          0px 1px 18px 0px rgba(0, 0, 0, 0.12);
        --mdc-fab-small-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-fab-small-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-fab-small-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2),
          0px 12px 17px 2px rgba(0, 0, 0, 0.14),
          0px 5px 22px 4px rgba(0, 0, 0, 0.12);
        --mdc-fab-small-container-shadow-color: #000;
        --mat-fab-small-foreground-color: black;
        --mat-fab-small-state-layer-color: black;
        --mat-fab-small-disabled-state-layer-color: black;
        --mat-fab-small-ripple-color: rgba(0, 0, 0, 0.1);
        --mat-fab-small-hover-state-layer-opacity: 0.04;
        --mat-fab-small-focus-state-layer-opacity: 0.12;
        --mat-fab-small-pressed-state-layer-opacity: 0.12;
        --mat-fab-small-disabled-state-container-color: rgba(0, 0, 0, 0.12);
        --mat-fab-small-disabled-state-foreground-color: rgba(0, 0, 0, 0.38);
        --mdc-extended-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
          0px 6px 10px 0px rgba(0, 0, 0, 0.14),
          0px 1px 18px 0px rgba(0, 0, 0, 0.12);
        --mdc-extended-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-extended-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
        --mdc-extended-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2),
          0px 12px 17px 2px rgba(0, 0, 0, 0.14),
          0px 5px 22px 4px rgba(0, 0, 0, 0.12);
        --mdc-extended-fab-container-shadow-color: #000;
      }
      html {
        --mat-fab-touch-target-display: block;
        --mat-fab-small-touch-target-display: block;
      }
      html {
        --mdc-extended-fab-label-text-font: Roboto, sans-serif;
        --mdc-extended-fab-label-text-size: 14px;
        --mdc-extended-fab-label-text-tracking: normal;
        --mdc-extended-fab-label-text-weight: 500;
      }
      html {
        --mdc-snackbar-container-shape: 4px;
      }
      html {
        --mdc-snackbar-container-color: #323232;
        --mdc-snackbar-supporting-text-color: rgba(250, 250, 250, 0.87);
        --mat-snack-bar-button-color: #5dd9b5;
      }
      html {
        --mdc-snackbar-supporting-text-font: Roboto, sans-serif;
        --mdc-snackbar-supporting-text-line-height: 20px;
        --mdc-snackbar-supporting-text-size: 14px;
        --mdc-snackbar-supporting-text-weight: 400;
      }
      html {
        --mat-table-row-item-outline-width: 1px;
      }
      html {
        --mat-table-background-color: #fafafa;
        --mat-table-header-headline-color: rgba(0, 0, 0, 0.87);
        --mat-table-row-item-label-text-color: rgba(0, 0, 0, 0.87);
        --mat-table-row-item-outline-color: rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-table-header-container-height: 56px;
        --mat-table-footer-container-height: 52px;
        --mat-table-row-item-container-height: 52px;
      }
      html {
        --mat-table-header-headline-font: Roboto, sans-serif;
        --mat-table-header-headline-line-height: 24px;
        --mat-table-header-headline-size: 14px;
        --mat-table-header-headline-weight: 500;
        --mat-table-header-headline-tracking: normal;
        --mat-table-row-item-label-text-font: Roboto, sans-serif;
        --mat-table-row-item-label-text-line-height: 20px;
        --mat-table-row-item-label-text-size: 14px;
        --mat-table-row-item-label-text-weight: 400;
        --mat-table-row-item-label-text-tracking: normal;
        --mat-table-footer-supporting-text-font: Roboto, sans-serif;
        --mat-table-footer-supporting-text-line-height: 20px;
        --mat-table-footer-supporting-text-size: 14px;
        --mat-table-footer-supporting-text-weight: 400;
        --mat-table-footer-supporting-text-tracking: normal;
      }
      html {
        --mdc-circular-progress-active-indicator-width: 4px;
        --mdc-circular-progress-size: 48px;
      }
      html {
        --mdc-circular-progress-active-indicator-color: #378a9f;
      }
      html {
        --mat-badge-container-shape: 50%;
        --mat-badge-container-size: unset;
        --mat-badge-small-size-container-size: unset;
        --mat-badge-large-size-container-size: unset;
        --mat-badge-legacy-container-size: 22px;
        --mat-badge-legacy-small-size-container-size: 16px;
        --mat-badge-legacy-large-size-container-size: 28px;
        --mat-badge-container-offset: -11px 0;
        --mat-badge-small-size-container-offset: -8px 0;
        --mat-badge-large-size-container-offset: -14px 0;
        --mat-badge-container-overlap-offset: -11px;
        --mat-badge-small-size-container-overlap-offset: -8px;
        --mat-badge-large-size-container-overlap-offset: -14px;
        --mat-badge-container-padding: 0;
        --mat-badge-small-size-container-padding: 0;
        --mat-badge-large-size-container-padding: 0;
      }
      html {
        --mat-badge-background-color: #378a9f;
        --mat-badge-text-color: rgba(0, 0, 0, 0.87);
        --mat-badge-disabled-state-background-color: #bdbdbd;
        --mat-badge-disabled-state-text-color: rgba(0, 0, 0, 0.38);
      }
      html {
        --mat-badge-text-font: Roboto, sans-serif;
        --mat-badge-text-size: 12px;
        --mat-badge-text-weight: 600;
        --mat-badge-small-size-text-size: 9px;
        --mat-badge-large-size-text-size: 24px;
      }
      html {
        --mat-bottom-sheet-container-shape: 4px;
      }
      html {
        --mat-bottom-sheet-container-text-color: rgba(0, 0, 0, 0.87);
        --mat-bottom-sheet-container-background-color: white;
      }
      html {
        --mat-bottom-sheet-container-text-font: Roboto, sans-serif;
        --mat-bottom-sheet-container-text-line-height: 20px;
        --mat-bottom-sheet-container-text-size: 14px;
        --mat-bottom-sheet-container-text-tracking: normal;
        --mat-bottom-sheet-container-text-weight: 400;
      }
      html {
        --mat-legacy-button-toggle-height: 36px;
        --mat-legacy-button-toggle-shape: 2px;
        --mat-legacy-button-toggle-focus-state-layer-opacity: 1;
        --mat-standard-button-toggle-shape: 4px;
        --mat-standard-button-toggle-hover-state-layer-opacity: 0.04;
        --mat-standard-button-toggle-focus-state-layer-opacity: 0.12;
      }
      html {
        --mat-legacy-button-toggle-text-color: rgba(0, 0, 0, 0.38);
        --mat-legacy-button-toggle-state-layer-color: rgba(0, 0, 0, 0.12);
        --mat-legacy-button-toggle-selected-state-text-color: rgba(
          0,
          0,
          0,
          0.54
        );
        --mat-legacy-button-toggle-selected-state-background-color: #e0e0e0;
        --mat-legacy-button-toggle-disabled-state-text-color: rgba(
          0,
          0,
          0,
          0.26
        );
        --mat-legacy-button-toggle-disabled-state-background-color: #eeeeee;
        --mat-legacy-button-toggle-disabled-selected-state-background-color: #bdbdbd;
        --mat-standard-button-toggle-text-color: rgba(0, 0, 0, 0.87);
        --mat-standard-button-toggle-background-color: #fafafa;
        --mat-standard-button-toggle-state-layer-color: black;
        --mat-standard-button-toggle-selected-state-background-color: #e0e0e0;
        --mat-standard-button-toggle-selected-state-text-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-standard-button-toggle-disabled-state-text-color: rgba(
          0,
          0,
          0,
          0.26
        );
        --mat-standard-button-toggle-disabled-state-background-color: #fafafa;
        --mat-standard-button-toggle-disabled-selected-state-text-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-standard-button-toggle-disabled-selected-state-background-color: #bdbdbd;
        --mat-standard-button-toggle-divider-color: gainsboro;
      }
      html {
        --mat-standard-button-toggle-height: 48px;
      }
      html {
        --mat-legacy-button-toggle-label-text-font: Roboto, sans-serif;
        --mat-legacy-button-toggle-label-text-line-height: 24px;
        --mat-legacy-button-toggle-label-text-size: 16px;
        --mat-legacy-button-toggle-label-text-tracking: normal;
        --mat-legacy-button-toggle-label-text-weight: 400;
        --mat-standard-button-toggle-label-text-font: Roboto, sans-serif;
        --mat-standard-button-toggle-label-text-line-height: 24px;
        --mat-standard-button-toggle-label-text-size: 16px;
        --mat-standard-button-toggle-label-text-tracking: normal;
        --mat-standard-button-toggle-label-text-weight: 400;
      }
      html {
        --mat-datepicker-calendar-container-shape: 4px;
        --mat-datepicker-calendar-container-touch-shape: 4px;
        --mat-datepicker-calendar-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
          0px 4px 5px 0px rgba(0, 0, 0, 0.14),
          0px 1px 10px 0px rgba(0, 0, 0, 0.12);
        --mat-datepicker-calendar-container-touch-elevation-shadow: 0px 11px
            15px -7px rgba(0, 0, 0, 0.2),
          0px 24px 38px 3px rgba(0, 0, 0, 0.14),
          0px 9px 46px 8px rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-datepicker-calendar-date-selected-state-text-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-datepicker-calendar-date-selected-state-background-color: #378a9f;
        --mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(
          55,
          138,
          159,
          0.4
        );
        --mat-datepicker-calendar-date-today-selected-state-outline-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-datepicker-calendar-date-focus-state-background-color: rgba(
          55,
          138,
          159,
          0.3
        );
        --mat-datepicker-calendar-date-hover-state-background-color: rgba(
          55,
          138,
          159,
          0.3
        );
        --mat-datepicker-toggle-active-state-icon-color: #378a9f;
        --mat-datepicker-calendar-date-in-range-state-background-color: rgba(
          55,
          138,
          159,
          0.2
        );
        --mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(
          249,
          171,
          0,
          0.2
        );
        --mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;
        --mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: #46a35e;
        --mat-datepicker-toggle-icon-color: rgba(0, 0, 0, 0.54);
        --mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, 0.54);
        --mat-datepicker-calendar-period-button-text-color: black;
        --mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, 0.54);
        --mat-datepicker-calendar-navigation-button-icon-color: rgba(
          0,
          0,
          0,
          0.54
        );
        --mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, 0.12);
        --mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, 0.54);
        --mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, 0.38);
        --mat-datepicker-calendar-date-today-disabled-state-outline-color: rgba(
          0,
          0,
          0,
          0.18
        );
        --mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, 0.87);
        --mat-datepicker-calendar-date-outline-color: transparent;
        --mat-datepicker-calendar-date-disabled-state-text-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mat-datepicker-calendar-date-preview-state-outline-color: rgba(
          0,
          0,
          0,
          0.24
        );
        --mat-datepicker-range-input-separator-color: rgba(0, 0, 0, 0.87);
        --mat-datepicker-range-input-disabled-state-separator-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mat-datepicker-range-input-disabled-state-text-color: rgba(
          0,
          0,
          0,
          0.38
        );
        --mat-datepicker-calendar-container-background-color: #fafafa;
        --mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, 0.87);
      }
      html {
        --mat-datepicker-calendar-text-font: Roboto, sans-serif;
        --mat-datepicker-calendar-text-size: 13px;
        --mat-datepicker-calendar-body-label-text-size: 14px;
        --mat-datepicker-calendar-body-label-text-weight: 500;
        --mat-datepicker-calendar-period-button-text-size: 14px;
        --mat-datepicker-calendar-period-button-text-weight: 500;
        --mat-datepicker-calendar-header-text-size: 11px;
        --mat-datepicker-calendar-header-text-weight: 400;
      }
      html {
        --mat-divider-width: 1px;
      }
      html {
        --mat-divider-color: rgba(0, 0, 0, 0.12);
      }
      html {
        --mat-expansion-container-shape: 4px;
        --mat-expansion-legacy-header-indicator-display: inline-block;
        --mat-expansion-header-indicator-display: none;
      }
      html {
        --mat-expansion-container-background-color: #fafafa;
        --mat-expansion-container-text-color: rgba(0, 0, 0, 0.87);
        --mat-expansion-actions-divider-color: rgba(0, 0, 0, 0.12);
        --mat-expansion-header-hover-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-expansion-header-focus-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-expansion-header-disabled-state-text-color: rgba(0, 0, 0, 0.26);
        --mat-expansion-header-text-color: rgba(0, 0, 0, 0.87);
        --mat-expansion-header-description-color: rgba(0, 0, 0, 0.54);
        --mat-expansion-header-indicator-color: rgba(0, 0, 0, 0.54);
      }
      html {
        --mat-expansion-header-collapsed-state-height: 48px;
        --mat-expansion-header-expanded-state-height: 64px;
      }
      html {
        --mat-expansion-header-text-font: Roboto, sans-serif;
        --mat-expansion-header-text-size: 14px;
        --mat-expansion-header-text-weight: 500;
        --mat-expansion-header-text-line-height: inherit;
        --mat-expansion-header-text-tracking: inherit;
        --mat-expansion-container-text-font: Roboto, sans-serif;
        --mat-expansion-container-text-line-height: 20px;
        --mat-expansion-container-text-size: 14px;
        --mat-expansion-container-text-tracking: normal;
        --mat-expansion-container-text-weight: 400;
      }
      html {
        --mat-grid-list-tile-header-primary-text-size: 14px;
        --mat-grid-list-tile-header-secondary-text-size: 12px;
        --mat-grid-list-tile-footer-primary-text-size: 14px;
        --mat-grid-list-tile-footer-secondary-text-size: 12px;
      }
      html {
        --mat-icon-color: inherit;
      }
      html {
        --mat-sidenav-container-shape: 0;
        --mat-sidenav-container-elevation-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.2),
          0px 16px 24px 2px rgba(0, 0, 0, 0.14),
          0px 6px 30px 5px rgba(0, 0, 0, 0.12);
        --mat-sidenav-container-width: auto;
      }
      html {
        --mat-sidenav-container-divider-color: rgba(0, 0, 0, 0.12);
        --mat-sidenav-container-background-color: white;
        --mat-sidenav-container-text-color: rgba(0, 0, 0, 0.87);
        --mat-sidenav-content-background-color: white;
        --mat-sidenav-content-text-color: rgba(0, 0, 0, 0.87);
        --mat-sidenav-scrim-color: rgba(5, 5, 5, 0.6);
      }
      html {
        --mat-stepper-header-icon-foreground-color: rgba(0, 0, 0, 0.87);
        --mat-stepper-header-selected-state-icon-background-color: #378a9f;
        --mat-stepper-header-selected-state-icon-foreground-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-stepper-header-done-state-icon-background-color: #378a9f;
        --mat-stepper-header-done-state-icon-foreground-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-stepper-header-edit-state-icon-background-color: #378a9f;
        --mat-stepper-header-edit-state-icon-foreground-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-stepper-container-color: #fafafa;
        --mat-stepper-line-color: rgba(0, 0, 0, 0.12);
        --mat-stepper-header-hover-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-stepper-header-focus-state-layer-color: rgba(0, 0, 0, 0.04);
        --mat-stepper-header-label-text-color: rgba(0, 0, 0, 0.54);
        --mat-stepper-header-optional-label-text-color: rgba(0, 0, 0, 0.54);
        --mat-stepper-header-selected-state-label-text-color: rgba(
          0,
          0,
          0,
          0.87
        );
        --mat-stepper-header-error-state-label-text-color: #f44336;
        --mat-stepper-header-icon-background-color: rgba(0, 0, 0, 0.54);
        --mat-stepper-header-error-state-icon-foreground-color: #f44336;
        --mat-stepper-header-error-state-icon-background-color: transparent;
      }
      html {
        --mat-stepper-header-height: 72px;
      }
      html {
        --mat-stepper-container-text-font: Roboto, sans-serif;
        --mat-stepper-header-label-text-font: Roboto, sans-serif;
        --mat-stepper-header-label-text-size: 14px;
        --mat-stepper-header-label-text-weight: 400;
        --mat-stepper-header-error-state-label-text-size: 16px;
        --mat-stepper-header-selected-state-label-text-size: 16px;
        --mat-stepper-header-selected-state-label-text-weight: 400;
      }
      html {
        --mat-sort-arrow-color: #737373;
      }
      html {
        --mat-toolbar-container-background-color: whitesmoke;
        --mat-toolbar-container-text-color: rgba(0, 0, 0, 0.87);
      }
      html {
        --mat-toolbar-standard-height: 64px;
        --mat-toolbar-mobile-height: 56px;
      }
      html {
        --mat-toolbar-title-text-font: Roboto, sans-serif;
        --mat-toolbar-title-text-line-height: 32px;
        --mat-toolbar-title-text-size: 16px;
        --mat-toolbar-title-text-tracking: normal;
        --mat-toolbar-title-text-weight: 400;
      }
      html {
        --mat-tree-container-background-color: #fafafa;
        --mat-tree-node-text-color: rgba(0, 0, 0, 0.87);
      }
      html {
        --mat-tree-node-min-height: 48px;
      }
      html {
        --mat-tree-node-text-font: Roboto, sans-serif;
        --mat-tree-node-text-size: 14px;
        --mat-tree-node-text-weight: 400;
      }
      body {
        margin: 0;
        height: 100%;
        font-family: Roboto, sans-serif;
        overscroll-behavior-y: contain;
        --mat-dialog-actions-alignment: end;
      }
      html {
        margin: 0;
        width: 100%;
        height: 100%;
        overscroll-behavior-y: contain;
      }
    </style>
    <link
      rel="stylesheet"
      href="login_files/styles.782e56bae9907d09.css"
      media="all"
      onload="this.media='all'"
    />
    <noscript
      ><link rel="stylesheet" href="/cswg/app/styles.782e56bae9907d09.css"
    /></noscript>
    <style type="text/css">
      @media print {
        .TridactylStatusIndicator {
          display: none !important;
        }
      }
    </style>
    <style>
      .application-container[_ngcontent-ng-c3118524757] {
        margin: 0;
        width: 100%;
        height: 100%;
      }
      .c3-sidenav[_ngcontent-ng-c3118524757] {
        width: 350px;
        max-width: 100vw;
      }
      .c3-searchnav[_ngcontent-ng-c3118524757] {
        width: 520px;
        max-width: 100vw;
      }
      .progress-bar[_ngcontent-ng-c3118524757] {
        height: 6px;
        position: absolute;
      }
    </style>
    <style>
      .mat-drawer-container {
        position: relative;
        z-index: 1;
        color: var(--mat-sidenav-content-text-color);
        background-color: var(--mat-sidenav-content-background-color);
        box-sizing: border-box;
        -webkit-overflow-scrolling: touch;
        display: block;
        overflow: hidden;
      }
      .mat-drawer-container[fullscreen] {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
      }
      .mat-drawer-container[fullscreen].mat-drawer-container-has-open {
        overflow: hidden;
      }
      .mat-drawer-container.mat-drawer-container-explicit-backdrop
        .mat-drawer-side {
        z-index: 3;
      }
      .mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,
      .mat-drawer-container.ng-animate-disabled .mat-drawer-content,
      .ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,
      .ng-animate-disabled .mat-drawer-container .mat-drawer-content {
        transition: none;
      }
      .mat-drawer-backdrop {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
        display: block;
        z-index: 3;
        visibility: hidden;
      }
      .mat-drawer-backdrop.mat-drawer-shown {
        visibility: visible;
        background-color: var(--mat-sidenav-scrim-color);
      }
      .mat-drawer-transition .mat-drawer-backdrop {
        transition-duration: 400ms;
        transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
        transition-property: background-color, visibility;
      }
      .cdk-high-contrast-active .mat-drawer-backdrop {
        opacity: 0.5;
      }
      .mat-drawer-content {
        position: relative;
        z-index: 1;
        display: block;
        height: 100%;
        overflow: auto;
      }
      .mat-drawer-transition .mat-drawer-content {
        transition-duration: 400ms;
        transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
        transition-property: transform, margin-left, margin-right;
      }
      .mat-drawer {
        position: relative;
        z-index: 4;
        color: var(--mat-sidenav-container-text-color);
        box-shadow: var(--mat-sidenav-container-elevation-shadow);
        background-color: var(--mat-sidenav-container-background-color);
        border-top-right-radius: var(--mat-sidenav-container-shape);
        border-bottom-right-radius: var(--mat-sidenav-container-shape);
        width: var(--mat-sidenav-container-width);
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        z-index: 3;
        outline: 0;
        box-sizing: border-box;
        overflow-y: auto;
        transform: translate3d(-100%, 0, 0);
      }
      .cdk-high-contrast-active .mat-drawer,
      .cdk-high-contrast-active [dir="rtl"] .mat-drawer.mat-drawer-end {
        border-right: solid 1px currentColor;
      }
      .cdk-high-contrast-active [dir="rtl"] .mat-drawer,
      .cdk-high-contrast-active .mat-drawer.mat-drawer-end {
        border-left: solid 1px currentColor;
        border-right: none;
      }
      .mat-drawer.mat-drawer-side {
        z-index: 2;
      }
      .mat-drawer.mat-drawer-end {
        right: 0;
        transform: translate3d(100%, 0, 0);
        border-top-left-radius: var(--mat-sidenav-container-shape);
        border-bottom-left-radius: var(--mat-sidenav-container-shape);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      [dir="rtl"] .mat-drawer {
        border-top-left-radius: var(--mat-sidenav-container-shape);
        border-bottom-left-radius: var(--mat-sidenav-container-shape);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        transform: translate3d(100%, 0, 0);
      }
      [dir="rtl"] .mat-drawer.mat-drawer-end {
        border-top-right-radius: var(--mat-sidenav-container-shape);
        border-bottom-right-radius: var(--mat-sidenav-container-shape);
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        left: 0;
        right: auto;
        transform: translate3d(-100%, 0, 0);
      }
      .mat-drawer[style*="visibility: hidden"] {
        display: none;
      }
      .mat-drawer-side {
        box-shadow: none;
        border-right-color: var(--mat-sidenav-container-divider-color);
        border-right-width: 1px;
        border-right-style: solid;
      }
      .mat-drawer-side.mat-drawer-end {
        border-left-color: var(--mat-sidenav-container-divider-color);
        border-left-width: 1px;
        border-left-style: solid;
        border-right: none;
      }
      [dir="rtl"] .mat-drawer-side {
        border-left-color: var(--mat-sidenav-container-divider-color);
        border-left-width: 1px;
        border-left-style: solid;
        border-right: none;
      }
      [dir="rtl"] .mat-drawer-side.mat-drawer-end {
        border-right-color: var(--mat-sidenav-container-divider-color);
        border-right-width: 1px;
        border-right-style: solid;
        border-left: none;
      }
      .mat-drawer-inner-container {
        width: 100%;
        height: 100%;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
      }
      .mat-sidenav-fixed {
        position: fixed;
      }
    </style>
    <style>
      .background[_ngcontent-ng-c2935415455] {
        background-repeat: no-repeat;
        background-color: transparent;
        background-origin: border-box;
        background-position: center;
        background-size: cover;
      }
      .img-cie-logo[_ngcontent-ng-c2935415455] {
        max-height: 150px;
        padding-left: 40px;
        padding-right: 40px;
        object-fit: scale-down;
      }
      .img-c3-powered[_ngcontent-ng-c2935415455] {
        width: 215px;
      }
      .logo-and-login[_ngcontent-ng-c2935415455] {
        overflow: auto;
        max-width: 432px;
      }
      @media only screen and (max-width: 600px) {
        .logo-and-login[_ngcontent-ng-c2935415455] {
          max-width: unset;
        }
        .background[_ngcontent-ng-c2935415455] {
          visibility: hidden;
          max-width: 0px;
        }
      }
      .login-content[_ngcontent-ng-c2935415455] {
        max-width: 272px;
        text-align: center;
      }
      .mat-divider[_ngcontent-ng-c2935415455] {
        width: 200px;
        padding-left: 40px;
        padding-right: 40px;
      }
      .main-button[_ngcontent-ng-c2935415455] {
        width: 248px;
      }
    </style>
    <style>
      .mat-divider {
        display: block;
        margin: 0;
        border-top-style: solid;
        border-top-color: var(--mat-divider-color);
        border-top-width: var(--mat-divider-width);
      }
      .mat-divider.mat-divider-vertical {
        border-top: 0;
        border-right-style: solid;
        border-right-color: var(--mat-divider-color);
        border-right-width: var(--mat-divider-width);
      }
      .mat-divider.mat-divider-inset {
        margin-left: 80px;
      }
      [dir="rtl"] .mat-divider.mat-divider-inset {
        margin-left: auto;
        margin-right: 80px;
      }
    </style>
    <style>
      .mdc-text-field {
        border-top-left-radius: 4px;
        border-top-left-radius: var(--mdc-shape-small, 4px);
        border-top-right-radius: 4px;
        border-top-right-radius: var(--mdc-shape-small, 4px);
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
        display: inline-flex;
        align-items: baseline;
        padding: 0 16px;
        position: relative;
        box-sizing: border-box;
        overflow: hidden;
        will-change: opacity, transform, color;
      }
      .mdc-text-field .mdc-floating-label {
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
      }
      .mdc-text-field__input {
        height: 28px;
        width: 100%;
        min-width: 0;
        border: none;
        border-radius: 0;
        background: none;
        appearance: none;
        padding: 0;
      }
      .mdc-text-field__input::-ms-clear {
        display: none;
      }
      .mdc-text-field__input::-webkit-calendar-picker-indicator {
        display: none;
      }
      .mdc-text-field__input:focus {
        outline: none;
      }
      .mdc-text-field__input:invalid {
        box-shadow: none;
      }
      @media all {
        .mdc-text-field__input::placeholder {
          opacity: 0;
        }
      }
      @media all {
        .mdc-text-field__input:-ms-input-placeholder {
          opacity: 0;
        }
      }
      @media all {
        .mdc-text-field--no-label .mdc-text-field__input::placeholder,
        .mdc-text-field--focused .mdc-text-field__input::placeholder {
          opacity: 1;
        }
      }
      @media all {
        .mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,
        .mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder {
          opacity: 1;
        }
      }
      .mdc-text-field__affix {
        height: 28px;
        opacity: 0;
        white-space: nowrap;
      }
      .mdc-text-field--label-floating .mdc-text-field__affix,
      .mdc-text-field--no-label .mdc-text-field__affix {
        opacity: 1;
      }
      @supports (-webkit-hyphens: none) {
        .mdc-text-field--outlined .mdc-text-field__affix {
          align-items: center;
          align-self: center;
          display: inline-flex;
          height: 100%;
        }
      }
      .mdc-text-field__affix--prefix {
        padding-left: 0;
        padding-right: 2px;
      }
      [dir="rtl"] .mdc-text-field__affix--prefix,
      .mdc-text-field__affix--prefix[dir="rtl"] {
        padding-left: 2px;
        padding-right: 0;
      }
      .mdc-text-field--end-aligned .mdc-text-field__affix--prefix {
        padding-left: 0;
        padding-right: 12px;
      }
      [dir="rtl"] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,
      .mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir="rtl"] {
        padding-left: 12px;
        padding-right: 0;
      }
      .mdc-text-field__affix--suffix {
        padding-left: 12px;
        padding-right: 0;
      }
      [dir="rtl"] .mdc-text-field__affix--suffix,
      .mdc-text-field__affix--suffix[dir="rtl"] {
        padding-left: 0;
        padding-right: 12px;
      }
      .mdc-text-field--end-aligned .mdc-text-field__affix--suffix {
        padding-left: 2px;
        padding-right: 0;
      }
      [dir="rtl"] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,
      .mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir="rtl"] {
        padding-left: 0;
        padding-right: 2px;
      }
      .mdc-text-field--filled {
        height: 56px;
      }
      .mdc-text-field--filled::before {
        display: inline-block;
        width: 0;
        height: 40px;
        content: "";
        vertical-align: 0;
      }
      .mdc-text-field--filled .mdc-floating-label {
        left: 16px;
        right: initial;
      }
      [dir="rtl"] .mdc-text-field--filled .mdc-floating-label,
      .mdc-text-field--filled .mdc-floating-label[dir="rtl"] {
        left: initial;
        right: 16px;
      }
      .mdc-text-field--filled .mdc-floating-label--float-above {
        transform: translateY(-106%) scale(0.75);
      }
      .mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input {
        height: 100%;
      }
      .mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label {
        display: none;
      }
      .mdc-text-field--filled.mdc-text-field--no-label::before {
        display: none;
      }
      @supports (-webkit-hyphens: none) {
        .mdc-text-field--filled.mdc-text-field--no-label
          .mdc-text-field__affix {
          align-items: center;
          align-self: center;
          display: inline-flex;
          height: 100%;
        }
      }
      .mdc-text-field--outlined {
        height: 56px;
        overflow: visible;
      }
      .mdc-text-field--outlined .mdc-floating-label--float-above {
        transform: translateY(-37.25px) scale(1);
      }
      .mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        transform: translateY(-34.75px) scale(0.75);
      }
      .mdc-text-field--outlined .mdc-floating-label--float-above {
        font-size: 0.75rem;
      }
      .mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        font-size: 1rem;
      }
      .mdc-text-field--outlined .mdc-text-field__input {
        height: 100%;
      }
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading {
        border-top-left-radius: 4px;
        border-top-left-radius: var(--mdc-shape-small, 4px);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 4px;
        border-bottom-left-radius: var(--mdc-shape-small, 4px);
      }
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading[dir="rtl"] {
        border-top-left-radius: 0;
        border-top-right-radius: 4px;
        border-top-right-radius: var(--mdc-shape-small, 4px);
        border-bottom-right-radius: 4px;
        border-bottom-right-radius: var(--mdc-shape-small, 4px);
        border-bottom-left-radius: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined
          .mdc-notched-outline
          .mdc-notched-outline__leading {
          width: max(12px, var(--mdc-shape-small, 4px));
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined
          .mdc-notched-outline
          .mdc-notched-outline__notch {
          max-width: calc(100% - max(12px, var(--mdc-shape-small, 4px)) * 2);
        }
      }
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-top-left-radius: 0;
        border-top-right-radius: 4px;
        border-top-right-radius: var(--mdc-shape-small, 4px);
        border-bottom-right-radius: 4px;
        border-bottom-right-radius: var(--mdc-shape-small, 4px);
        border-bottom-left-radius: 0;
      }
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing,
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing[dir="rtl"] {
        border-top-left-radius: 4px;
        border-top-left-radius: var(--mdc-shape-small, 4px);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 4px;
        border-bottom-left-radius: var(--mdc-shape-small, 4px);
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined {
          padding-left: max(16px, calc(var(--mdc-shape-small, 4px) + 4px));
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined {
          padding-right: max(16px, var(--mdc-shape-small, 4px));
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined + .mdc-text-field-helper-line {
          padding-left: max(16px, calc(var(--mdc-shape-small, 4px) + 4px));
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined + .mdc-text-field-helper-line {
          padding-right: max(16px, var(--mdc-shape-small, 4px));
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon {
        padding-left: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined.mdc-text-field--with-leading-icon {
          padding-right: max(16px, var(--mdc-shape-small, 4px));
        }
      }
      [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir="rtl"] {
        padding-right: 0;
      }
      @supports (top: max(0%)) {
        [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,
        .mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir="rtl"] {
          padding-left: max(16px, var(--mdc-shape-small, 4px));
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-trailing-icon {
        padding-right: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined.mdc-text-field--with-trailing-icon {
          padding-left: max(16px, calc(var(--mdc-shape-small, 4px) + 4px));
        }
      }
      [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,
      .mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir="rtl"] {
        padding-left: 0;
      }
      @supports (top: max(0%)) {
        [dir="rtl"]
          .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,
        .mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir="rtl"] {
          padding-right: max(16px, calc(var(--mdc-shape-small, 4px) + 4px));
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon {
        padding-left: 0;
        padding-right: 0;
      }
      .mdc-text-field--outlined
        .mdc-notched-outline--notched
        .mdc-notched-outline__notch {
        padding-top: 1px;
      }
      .mdc-text-field--outlined .mdc-floating-label {
        left: 4px;
        right: initial;
      }
      [dir="rtl"] .mdc-text-field--outlined .mdc-floating-label,
      .mdc-text-field--outlined .mdc-floating-label[dir="rtl"] {
        left: initial;
        right: 4px;
      }
      .mdc-text-field--outlined .mdc-text-field__input {
        display: flex;
        border: none !important;
        background-color: rgba(0, 0, 0, 0);
      }
      .mdc-text-field--outlined .mdc-notched-outline {
        z-index: 1;
      }
      .mdc-text-field--textarea {
        flex-direction: column;
        align-items: center;
        width: auto;
        height: auto;
        padding: 0;
      }
      .mdc-text-field--textarea .mdc-floating-label {
        top: 19px;
      }
      .mdc-text-field--textarea
        .mdc-floating-label:not(.mdc-floating-label--float-above) {
        transform: none;
      }
      .mdc-text-field--textarea .mdc-text-field__input {
        flex-grow: 1;
        height: auto;
        min-height: 1.5rem;
        overflow-x: hidden;
        overflow-y: auto;
        box-sizing: border-box;
        resize: none;
        padding: 0 16px;
      }
      .mdc-text-field--textarea.mdc-text-field--filled::before {
        display: none;
      }
      .mdc-text-field--textarea.mdc-text-field--filled
        .mdc-floating-label--float-above {
        transform: translateY(-10.25px) scale(0.75);
      }
      .mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input {
        margin-top: 23px;
        margin-bottom: 9px;
      }
      .mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label
        .mdc-text-field__input {
        margin-top: 16px;
        margin-bottom: 16px;
      }
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-notched-outline--notched
        .mdc-notched-outline__notch {
        padding-top: 0;
      }
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-floating-label--float-above {
        transform: translateY(-27.25px) scale(1);
      }
      .mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        transform: translateY(-24.75px) scale(0.75);
      }
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-floating-label--float-above {
        font-size: 0.75rem;
      }
      .mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        font-size: 1rem;
      }
      .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-text-field__input {
        margin-top: 16px;
        margin-bottom: 16px;
      }
      .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label {
        top: 18px;
      }
      .mdc-text-field--textarea.mdc-text-field--with-internal-counter
        .mdc-text-field__input {
        margin-bottom: 2px;
      }
      .mdc-text-field--textarea.mdc-text-field--with-internal-counter
        .mdc-text-field-character-counter {
        align-self: flex-end;
        padding: 0 16px;
      }
      .mdc-text-field--textarea.mdc-text-field--with-internal-counter
        .mdc-text-field-character-counter::after {
        display: inline-block;
        width: 0;
        height: 16px;
        content: "";
        vertical-align: -16px;
      }
      .mdc-text-field--textarea.mdc-text-field--with-internal-counter
        .mdc-text-field-character-counter::before {
        display: none;
      }
      .mdc-text-field__resizer {
        align-self: stretch;
        display: inline-flex;
        flex-direction: column;
        flex-grow: 1;
        max-height: 100%;
        max-width: 100%;
        min-height: 56px;
        min-width: fit-content;
        min-width: -moz-available;
        min-width: -webkit-fill-available;
        overflow: hidden;
        resize: both;
      }
      .mdc-text-field--filled .mdc-text-field__resizer {
        transform: translateY(-1px);
      }
      .mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,
      .mdc-text-field--filled
        .mdc-text-field__resizer
        .mdc-text-field-character-counter {
        transform: translateY(1px);
      }
      .mdc-text-field--outlined .mdc-text-field__resizer {
        transform: translateX(-1px) translateY(-1px);
      }
      [dir="rtl"] .mdc-text-field--outlined .mdc-text-field__resizer,
      .mdc-text-field--outlined .mdc-text-field__resizer[dir="rtl"] {
        transform: translateX(1px) translateY(-1px);
      }
      .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,
      .mdc-text-field--outlined
        .mdc-text-field__resizer
        .mdc-text-field-character-counter {
        transform: translateX(1px) translateY(1px);
      }
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-text-field__resizer
        .mdc-text-field__input,
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-text-field__resizer
        .mdc-text-field-character-counter,
      .mdc-text-field--outlined
        .mdc-text-field__resizer
        .mdc-text-field__input[dir="rtl"],
      .mdc-text-field--outlined
        .mdc-text-field__resizer
        .mdc-text-field-character-counter[dir="rtl"] {
        transform: translateX(-1px) translateY(1px);
      }
      .mdc-text-field--with-leading-icon {
        padding-left: 0;
        padding-right: 16px;
      }
      [dir="rtl"] .mdc-text-field--with-leading-icon,
      .mdc-text-field--with-leading-icon[dir="rtl"] {
        padding-left: 16px;
        padding-right: 0;
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--filled
        .mdc-floating-label {
        max-width: calc(100% - 48px);
        left: 48px;
        right: initial;
      }
      [dir="rtl"]
        .mdc-text-field--with-leading-icon.mdc-text-field--filled
        .mdc-floating-label,
      .mdc-text-field--with-leading-icon.mdc-text-field--filled
        .mdc-floating-label[dir="rtl"] {
        left: initial;
        right: 48px;
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--filled
        .mdc-floating-label--float-above {
        max-width: calc(100% / 0.75 - 64px / 0.75);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label {
        left: 36px;
        right: initial;
      }
      [dir="rtl"]
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label,
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label[dir="rtl"] {
        left: initial;
        right: 36px;
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        :not(.mdc-notched-outline--notched)
        .mdc-notched-outline__notch {
        max-width: calc(100% - 60px);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--float-above {
        transform: translateY(-37.25px) translateX(-32px) scale(1);
      }
      [dir="rtl"]
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--float-above,
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--float-above[dir="rtl"] {
        transform: translateY(-37.25px) translateX(32px) scale(1);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        transform: translateY(-34.75px) translateX(-32px) scale(0.75);
      }
      [dir="rtl"]
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      [dir="rtl"]
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above[dir="rtl"],
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above[dir="rtl"] {
        transform: translateY(-34.75px) translateX(32px) scale(0.75);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--float-above {
        font-size: 0.75rem;
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        font-size: 1rem;
      }
      .mdc-text-field--with-trailing-icon {
        padding-left: 16px;
        padding-right: 0;
      }
      [dir="rtl"] .mdc-text-field--with-trailing-icon,
      .mdc-text-field--with-trailing-icon[dir="rtl"] {
        padding-left: 0;
        padding-right: 16px;
      }
      .mdc-text-field--with-trailing-icon.mdc-text-field--filled
        .mdc-floating-label {
        max-width: calc(100% - 64px);
      }
      .mdc-text-field--with-trailing-icon.mdc-text-field--filled
        .mdc-floating-label--float-above {
        max-width: calc(100% / 0.75 - 64px / 0.75);
      }
      .mdc-text-field--with-trailing-icon.mdc-text-field--outlined
        :not(.mdc-notched-outline--notched)
        .mdc-notched-outline__notch {
        max-width: calc(100% - 60px);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon {
        padding-left: 0;
        padding-right: 0;
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled
        .mdc-floating-label {
        max-width: calc(100% - 96px);
      }
      .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled
        .mdc-floating-label--float-above {
        max-width: calc(100% / 0.75 - 96px / 0.75);
      }
      .mdc-text-field-helper-line {
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
      }
      .mdc-text-field + .mdc-text-field-helper-line {
        padding-right: 16px;
        padding-left: 16px;
      }
      .mdc-form-field > .mdc-text-field + label {
        align-self: flex-start;
      }
      .mdc-text-field--focused .mdc-notched-outline__leading,
      .mdc-text-field--focused .mdc-notched-outline__notch,
      .mdc-text-field--focused .mdc-notched-outline__trailing {
        border-width: 2px;
      }
      .mdc-text-field--focused
        + .mdc-text-field-helper-line
        .mdc-text-field-helper-text:not(
          .mdc-text-field-helper-text--validation-msg
        ) {
        opacity: 1;
      }
      .mdc-text-field--focused.mdc-text-field--outlined
        .mdc-notched-outline--notched
        .mdc-notched-outline__notch {
        padding-top: 2px;
      }
      .mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea
        .mdc-notched-outline--notched
        .mdc-notched-outline__notch {
        padding-top: 0;
      }
      .mdc-text-field--invalid
        + .mdc-text-field-helper-line
        .mdc-text-field-helper-text--validation-msg {
        opacity: 1;
      }
      .mdc-text-field--disabled {
        pointer-events: none;
      }
      @media screen and (forced-colors: active) {
        .mdc-text-field--disabled .mdc-text-field__input {
          background-color: Window;
        }
        .mdc-text-field--disabled .mdc-floating-label {
          z-index: 1;
        }
      }
      .mdc-text-field--disabled .mdc-floating-label {
        cursor: default;
      }
      .mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple {
        display: none;
      }
      .mdc-text-field--disabled .mdc-text-field__input {
        pointer-events: auto;
      }
      .mdc-text-field--end-aligned .mdc-text-field__input {
        text-align: right;
      }
      [dir="rtl"] .mdc-text-field--end-aligned .mdc-text-field__input,
      .mdc-text-field--end-aligned .mdc-text-field__input[dir="rtl"] {
        text-align: left;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__input,
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__affix,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__input,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__affix {
        direction: ltr;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__affix--prefix {
        padding-left: 0;
        padding-right: 2px;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__affix--suffix {
        padding-left: 12px;
        padding-right: 0;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__icon--leading {
        order: 1;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__affix--suffix {
        order: 2;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__input,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__input {
        order: 3;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__affix--prefix {
        order: 4;
      }
      [dir="rtl"] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,
      .mdc-text-field--ltr-text[dir="rtl"] .mdc-text-field__icon--trailing {
        order: 5;
      }
      [dir="rtl"]
        .mdc-text-field--ltr-text.mdc-text-field--end-aligned
        .mdc-text-field__input,
      .mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir="rtl"]
        .mdc-text-field__input {
        text-align: right;
      }
      [dir="rtl"]
        .mdc-text-field--ltr-text.mdc-text-field--end-aligned
        .mdc-text-field__affix--prefix,
      .mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir="rtl"]
        .mdc-text-field__affix--prefix {
        padding-right: 12px;
      }
      [dir="rtl"]
        .mdc-text-field--ltr-text.mdc-text-field--end-aligned
        .mdc-text-field__affix--suffix,
      .mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir="rtl"]
        .mdc-text-field__affix--suffix {
        padding-left: 2px;
      }
      .mdc-floating-label {
        position: absolute;
        left: 0;
        -webkit-transform-origin: left top;
        transform-origin: left top;
        line-height: 1.15rem;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: text;
        overflow: hidden;
        will-change: transform;
      }
      [dir="rtl"] .mdc-floating-label,
      .mdc-floating-label[dir="rtl"] {
        right: 0;
        left: auto;
        -webkit-transform-origin: right top;
        transform-origin: right top;
        text-align: right;
      }
      .mdc-floating-label--float-above {
        cursor: auto;
      }
      .mdc-floating-label--required:not(
          .mdc-floating-label--hide-required-marker
        )::after {
        margin-left: 1px;
        margin-right: 0px;
        content: "*";
      }
      [dir="rtl"]
        .mdc-floating-label--required:not(
          .mdc-floating-label--hide-required-marker
        )::after,
      .mdc-floating-label--required:not(
          .mdc-floating-label--hide-required-marker
        )[dir="rtl"]::after {
        margin-left: 0;
        margin-right: 1px;
      }
      .mdc-notched-outline {
        display: flex;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        box-sizing: border-box;
        width: 100%;
        max-width: 100%;
        height: 100%;
        text-align: left;
        pointer-events: none;
      }
      [dir="rtl"] .mdc-notched-outline,
      .mdc-notched-outline[dir="rtl"] {
        text-align: right;
      }
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        box-sizing: border-box;
        height: 100%;
        pointer-events: none;
      }
      .mdc-notched-outline__trailing {
        flex-grow: 1;
      }
      .mdc-notched-outline__notch {
        flex: 0 0 auto;
        width: auto;
      }
      .mdc-notched-outline .mdc-floating-label {
        display: inline-block;
        position: relative;
        max-width: 100%;
      }
      .mdc-notched-outline .mdc-floating-label--float-above {
        text-overflow: clip;
      }
      .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
        max-width: 133.3333333333%;
      }
      .mdc-notched-outline--notched .mdc-notched-outline__notch {
        padding-left: 0;
        padding-right: 8px;
        border-top: none;
      }
      [dir="rtl"] .mdc-notched-outline--notched .mdc-notched-outline__notch,
      .mdc-notched-outline--notched .mdc-notched-outline__notch[dir="rtl"] {
        padding-left: 8px;
        padding-right: 0;
      }
      .mdc-notched-outline--no-label .mdc-notched-outline__notch {
        display: none;
      }
      .mdc-line-ripple::before,
      .mdc-line-ripple::after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border-bottom-style: solid;
        content: "";
      }
      .mdc-line-ripple::before {
        z-index: 1;
      }
      .mdc-line-ripple::after {
        transform: scaleX(0);
        opacity: 0;
        z-index: 2;
      }
      .mdc-line-ripple--active::after {
        transform: scaleX(1);
        opacity: 1;
      }
      .mdc-line-ripple--deactivating::after {
        opacity: 0;
      }
      .mdc-floating-label--float-above {
        transform: translateY(-106%) scale(0.75);
      }
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-top: 1px solid;
        border-bottom: 1px solid;
      }
      .mdc-notched-outline__leading {
        border-left: 1px solid;
        border-right: none;
        width: 12px;
      }
      [dir="rtl"] .mdc-notched-outline__leading,
      .mdc-notched-outline__leading[dir="rtl"] {
        border-left: none;
        border-right: 1px solid;
      }
      .mdc-notched-outline__trailing {
        border-left: none;
        border-right: 1px solid;
      }
      [dir="rtl"] .mdc-notched-outline__trailing,
      .mdc-notched-outline__trailing[dir="rtl"] {
        border-left: 1px solid;
        border-right: none;
      }
      .mdc-notched-outline__notch {
        max-width: calc(100% - 12px * 2);
      }
      .mdc-line-ripple::before {
        border-bottom-width: 1px;
      }
      .mdc-line-ripple::after {
        border-bottom-width: 2px;
      }
      .mdc-text-field--filled {
        border-top-left-radius: var(--mdc-filled-text-field-container-shape);
        border-top-right-radius: var(--mdc-filled-text-field-container-shape);
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-text-field__input {
        caret-color: var(--mdc-filled-text-field-caret-color);
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-text-field__input {
        caret-color: var(--mdc-filled-text-field-error-caret-color);
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-text-field__input {
        color: var(--mdc-filled-text-field-input-text-color);
      }
      .mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input {
        color: var(--mdc-filled-text-field-disabled-input-text-color);
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-floating-label,
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-label-text-color);
      }
      .mdc-text-field--filled:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label,
      .mdc-text-field--filled:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-focus-label-text-color);
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-floating-label,
      .mdc-text-field--filled:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-hover-label-text-color);
      }
      .mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,
      .mdc-text-field--filled.mdc-text-field--disabled
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-disabled-label-text-color);
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-floating-label,
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-error-label-text-color);
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label,
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-error-focus-label-text-color);
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-floating-label,
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-floating-label--float-above {
        color: var(--mdc-filled-text-field-error-hover-label-text-color);
      }
      .mdc-text-field--filled .mdc-floating-label {
        font-family: var(--mdc-filled-text-field-label-text-font);
        font-size: var(--mdc-filled-text-field-label-text-size);
        font-weight: var(--mdc-filled-text-field-label-text-weight);
        letter-spacing: var(--mdc-filled-text-field-label-text-tracking);
      }
      @media all {
        .mdc-text-field--filled:not(.mdc-text-field--disabled)
          .mdc-text-field__input::placeholder {
          color: var(--mdc-filled-text-field-input-text-placeholder-color);
        }
      }
      @media all {
        .mdc-text-field--filled:not(.mdc-text-field--disabled)
          .mdc-text-field__input:-ms-input-placeholder {
          color: var(--mdc-filled-text-field-input-text-placeholder-color);
        }
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled) {
        background-color: var(--mdc-filled-text-field-container-color);
      }
      .mdc-text-field--filled.mdc-text-field--disabled {
        background-color: var(--mdc-filled-text-field-disabled-container-color);
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-line-ripple::before {
        border-bottom-color: var(
          --mdc-filled-text-field-active-indicator-color
        );
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-line-ripple::before {
        border-bottom-color: var(
          --mdc-filled-text-field-hover-active-indicator-color
        );
      }
      .mdc-text-field--filled:not(.mdc-text-field--disabled)
        .mdc-line-ripple::after {
        border-bottom-color: var(
          --mdc-filled-text-field-focus-active-indicator-color
        );
      }
      .mdc-text-field--filled.mdc-text-field--disabled
        .mdc-line-ripple::before {
        border-bottom-color: var(
          --mdc-filled-text-field-disabled-active-indicator-color
        );
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-line-ripple::before {
        border-bottom-color: var(
          --mdc-filled-text-field-error-active-indicator-color
        );
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-line-ripple::before {
        border-bottom-color: var(
          --mdc-filled-text-field-error-hover-active-indicator-color
        );
      }
      .mdc-text-field--filled.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-line-ripple::after {
        border-bottom-color: var(
          --mdc-filled-text-field-error-focus-active-indicator-color
        );
      }
      .mdc-text-field--filled .mdc-line-ripple::before {
        border-bottom-width: var(
          --mdc-filled-text-field-active-indicator-height
        );
      }
      .mdc-text-field--filled .mdc-line-ripple::after {
        border-bottom-width: var(
          --mdc-filled-text-field-focus-active-indicator-height
        );
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-text-field__input {
        caret-color: var(--mdc-outlined-text-field-caret-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-text-field__input {
        caret-color: var(--mdc-outlined-text-field-error-caret-color);
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-text-field__input {
        color: var(--mdc-outlined-text-field-input-text-color);
      }
      .mdc-text-field--outlined.mdc-text-field--disabled
        .mdc-text-field__input {
        color: var(--mdc-outlined-text-field-disabled-input-text-color);
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-floating-label,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-label-text-color);
      }
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label,
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-focus-label-text-color);
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-floating-label,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-hover-label-text-color);
      }
      .mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,
      .mdc-text-field--outlined.mdc-text-field--disabled
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-disabled-label-text-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-floating-label,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-error-label-text-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-error-focus-label-text-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-floating-label,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-floating-label--float-above {
        color: var(--mdc-outlined-text-field-error-hover-label-text-color);
      }
      .mdc-text-field--outlined .mdc-floating-label {
        font-family: var(--mdc-outlined-text-field-label-text-font);
        font-size: var(--mdc-outlined-text-field-label-text-size);
        font-weight: var(--mdc-outlined-text-field-label-text-weight);
        letter-spacing: var(--mdc-outlined-text-field-label-text-tracking);
      }
      @media all {
        .mdc-text-field--outlined:not(.mdc-text-field--disabled)
          .mdc-text-field__input::placeholder {
          color: var(--mdc-outlined-text-field-input-text-placeholder-color);
        }
      }
      @media all {
        .mdc-text-field--outlined:not(.mdc-text-field--disabled)
          .mdc-text-field__input:-ms-input-placeholder {
          color: var(--mdc-outlined-text-field-input-text-placeholder-color);
        }
      }
      .mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-floating-label--float-above {
        font-size: calc(0.75 * var(--mdc-outlined-text-field-label-text-size));
      }
      .mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded
        .mdc-floating-label--float-above,
      .mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        font-size: var(--mdc-outlined-text-field-label-text-size);
      }
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading {
        border-top-left-radius: var(--mdc-outlined-text-field-container-shape);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: var(
          --mdc-outlined-text-field-container-shape
        );
      }
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__leading[dir="rtl"] {
        border-top-left-radius: 0;
        border-top-right-radius: var(--mdc-outlined-text-field-container-shape);
        border-bottom-right-radius: var(
          --mdc-outlined-text-field-container-shape
        );
        border-bottom-left-radius: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined
          .mdc-notched-outline
          .mdc-notched-outline__leading {
          width: max(12px, var(--mdc-outlined-text-field-container-shape));
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined
          .mdc-notched-outline
          .mdc-notched-outline__notch {
          max-width: calc(
            100% - max(12px, var(--mdc-outlined-text-field-container-shape)) * 2
          );
        }
      }
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-top-left-radius: 0;
        border-top-right-radius: var(--mdc-outlined-text-field-container-shape);
        border-bottom-right-radius: var(
          --mdc-outlined-text-field-container-shape
        );
        border-bottom-left-radius: 0;
      }
      [dir="rtl"]
        .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing,
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__trailing[dir="rtl"] {
        border-top-left-radius: var(--mdc-outlined-text-field-container-shape);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: var(
          --mdc-outlined-text-field-container-shape
        );
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined {
          padding-left: max(
            16px,
            calc(var(--mdc-outlined-text-field-container-shape) + 4px)
          );
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined {
          padding-right: max(
            16px,
            var(--mdc-outlined-text-field-container-shape)
          );
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined + .mdc-text-field-helper-line {
          padding-left: max(
            16px,
            calc(var(--mdc-outlined-text-field-container-shape) + 4px)
          );
        }
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined + .mdc-text-field-helper-line {
          padding-right: max(
            16px,
            var(--mdc-outlined-text-field-container-shape)
          );
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon {
        padding-left: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined.mdc-text-field--with-leading-icon {
          padding-right: max(
            16px,
            var(--mdc-outlined-text-field-container-shape)
          );
        }
      }
      [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir="rtl"] {
        padding-right: 0;
      }
      @supports (top: max(0%)) {
        [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,
        .mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir="rtl"] {
          padding-left: max(
            16px,
            var(--mdc-outlined-text-field-container-shape)
          );
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-trailing-icon {
        padding-right: 0;
      }
      @supports (top: max(0%)) {
        .mdc-text-field--outlined.mdc-text-field--with-trailing-icon {
          padding-left: max(
            16px,
            calc(var(--mdc-outlined-text-field-container-shape) + 4px)
          );
        }
      }
      [dir="rtl"] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,
      .mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir="rtl"] {
        padding-left: 0;
      }
      @supports (top: max(0%)) {
        [dir="rtl"]
          .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,
        .mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir="rtl"] {
          padding-right: max(
            16px,
            calc(var(--mdc-outlined-text-field-container-shape) + 4px)
          );
        }
      }
      .mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon {
        padding-left: 0;
        padding-right: 0;
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-outline-color);
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-notched-outline
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
          .mdc-text-field--focused
        ):hover
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-hover-outline-color);
      }
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-focus-outline-color);
      }
      .mdc-text-field--outlined.mdc-text-field--disabled
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined.mdc-text-field--disabled
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined.mdc-text-field--disabled
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-disabled-outline-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        )
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-error-outline-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-notched-outline
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ):not(.mdc-text-field--focused):hover
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-error-hover-outline-color);
      }
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined.mdc-text-field--invalid:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline__trailing {
        border-color: var(--mdc-outlined-text-field-error-focus-outline-color);
      }
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined:not(.mdc-text-field--disabled)
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-width: var(--mdc-outlined-text-field-outline-width);
      }
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline
        .mdc-notched-outline__leading,
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline
        .mdc-notched-outline__notch,
      .mdc-text-field--outlined:not(
          .mdc-text-field--disabled
        ).mdc-text-field--focused
        .mdc-notched-outline
        .mdc-notched-outline__trailing {
        border-width: var(--mdc-outlined-text-field-focus-outline-width);
      }
      .mat-mdc-form-field-textarea-control {
        vertical-align: middle;
        resize: vertical;
        box-sizing: border-box;
        height: auto;
        margin: 0;
        padding: 0;
        border: none;
        overflow: auto;
      }
      .mat-mdc-form-field-input-control.mat-mdc-form-field-input-control {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font: inherit;
        letter-spacing: inherit;
        text-decoration: inherit;
        text-transform: inherit;
        border: none;
      }
      .mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        line-height: normal;
        pointer-events: all;
        will-change: auto;
      }
      .mat-mdc-form-field:not(.mat-form-field-disabled)
        .mat-mdc-floating-label.mdc-floating-label {
        cursor: inherit;
      }
      .mdc-text-field--no-label:not(.mdc-text-field--textarea)
        .mat-mdc-form-field-input-control.mdc-text-field__input,
      .mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {
        height: auto;
      }
      .mat-mdc-text-field-wrapper
        .mat-mdc-form-field-input-control.mdc-text-field__input[type="color"] {
        height: 23px;
      }
      .mat-mdc-text-field-wrapper {
        height: auto;
        flex: auto;
        will-change: auto;
      }
      .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper {
        padding-left: 0;
        --mat-mdc-form-field-label-offset-x: -16px;
      }
      .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
        padding-right: 0;
      }
      [dir="rtl"] .mat-mdc-text-field-wrapper {
        padding-left: 16px;
        padding-right: 16px;
      }
      [dir="rtl"]
        .mat-mdc-form-field-has-icon-suffix
        .mat-mdc-text-field-wrapper {
        padding-left: 0;
      }
      [dir="rtl"]
        .mat-mdc-form-field-has-icon-prefix
        .mat-mdc-text-field-wrapper {
        padding-right: 0;
      }
      .mat-form-field-disabled .mdc-text-field__input::placeholder {
        color: var(--mat-form-field-disabled-input-text-placeholder-color);
      }
      .mat-form-field-disabled .mdc-text-field__input::-moz-placeholder {
        color: var(--mat-form-field-disabled-input-text-placeholder-color);
      }
      .mat-form-field-disabled
        .mdc-text-field__input::-webkit-input-placeholder {
        color: var(--mat-form-field-disabled-input-text-placeholder-color);
      }
      .mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder {
        color: var(--mat-form-field-disabled-input-text-placeholder-color);
      }
      .mat-mdc-form-field-label-always-float
        .mdc-text-field__input::placeholder {
        transition-delay: 40ms;
        transition-duration: 110ms;
        opacity: 1;
      }
      .mat-mdc-text-field-wrapper
        .mat-mdc-form-field-infix
        .mat-mdc-floating-label {
        left: auto;
        right: auto;
      }
      .mat-mdc-text-field-wrapper.mdc-text-field--outlined
        .mdc-text-field__input {
        display: inline-block;
      }
      .mat-mdc-form-field
        .mat-mdc-text-field-wrapper.mdc-text-field
        .mdc-notched-outline__notch {
        padding-top: 0;
      }
      .mat-mdc-text-field-wrapper::before {
        content: none;
      }
      .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field
        .mdc-notched-outline__notch {
        border-left: 1px solid rgba(0, 0, 0, 0);
      }
      [dir="rtl"]
        .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field
        .mdc-notched-outline__notch {
        border-left: none;
        border-right: 1px solid rgba(0, 0, 0, 0);
      }
      .mat-mdc-form-field-infix {
        min-height: var(--mat-form-field-container-height);
        padding-top: var(
          --mat-form-field-filled-with-label-container-padding-top
        );
        padding-bottom: var(
          --mat-form-field-filled-with-label-container-padding-bottom
        );
      }
      .mdc-text-field--outlined .mat-mdc-form-field-infix,
      .mdc-text-field--no-label .mat-mdc-form-field-infix {
        padding-top: var(--mat-form-field-container-vertical-padding);
        padding-bottom: var(--mat-form-field-container-vertical-padding);
      }
      .mat-mdc-text-field-wrapper
        .mat-mdc-form-field-flex
        .mat-mdc-floating-label {
        top: calc(var(--mat-form-field-container-height) / 2);
      }
      .mdc-text-field--filled .mat-mdc-floating-label {
        display: var(--mat-form-field-filled-label-display, block);
      }
      .mat-mdc-text-field-wrapper.mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        --mat-mdc-form-field-label-transform: translateY(
            calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)
          )
          scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));
        transform: var(--mat-mdc-form-field-label-transform);
      }
      .mat-mdc-form-field-subscript-wrapper {
        box-sizing: border-box;
        width: 100%;
        position: relative;
      }
      .mat-mdc-form-field-hint-wrapper,
      .mat-mdc-form-field-error-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        padding: 0 16px;
      }
      .mat-mdc-form-field-subscript-dynamic-size
        .mat-mdc-form-field-hint-wrapper,
      .mat-mdc-form-field-subscript-dynamic-size
        .mat-mdc-form-field-error-wrapper {
        position: static;
      }
      .mat-mdc-form-field-bottom-align::before {
        content: "";
        display: inline-block;
        height: 16px;
      }
      .mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before {
        content: unset;
      }
      .mat-mdc-form-field-hint-end {
        order: 1;
      }
      .mat-mdc-form-field-hint-wrapper {
        display: flex;
      }
      .mat-mdc-form-field-hint-spacer {
        flex: 1 0 1em;
      }
      .mat-mdc-form-field-error {
        display: block;
        color: var(--mat-form-field-error-text-color);
      }
      .mat-mdc-form-field-subscript-wrapper,
      .mat-mdc-form-field-bottom-align::before {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-family: var(--mat-form-field-subscript-text-font);
        line-height: var(--mat-form-field-subscript-text-line-height);
        font-size: var(--mat-form-field-subscript-text-size);
        letter-spacing: var(--mat-form-field-subscript-text-tracking);
        font-weight: var(--mat-form-field-subscript-text-weight);
      }
      .mat-mdc-form-field-focus-overlay {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
        opacity: 0;
        pointer-events: none;
        background-color: var(--mat-form-field-state-layer-color);
      }
      .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay {
        opacity: var(--mat-form-field-hover-state-layer-opacity);
      }
      .mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay {
        opacity: var(--mat-form-field-focus-state-layer-opacity);
      }
      select.mat-mdc-form-field-input-control {
        -moz-appearance: none;
        -webkit-appearance: none;
        background-color: rgba(0, 0, 0, 0);
        display: inline-flex;
        box-sizing: border-box;
      }
      select.mat-mdc-form-field-input-control:not(:disabled) {
        cursor: pointer;
      }
      select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline)
        option {
        color: var(--mat-form-field-select-option-text-color);
      }
      select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline)
        option:disabled {
        color: var(--mat-form-field-select-disabled-option-text-color);
      }
      .mat-mdc-form-field-type-mat-native-select
        .mat-mdc-form-field-infix::after {
        content: "";
        width: 0;
        height: 0;
        border-left: 5px solid rgba(0, 0, 0, 0);
        border-right: 5px solid rgba(0, 0, 0, 0);
        border-top: 5px solid;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -2.5px;
        pointer-events: none;
        color: var(--mat-form-field-enabled-select-arrow-color);
      }
      [dir="rtl"]
        .mat-mdc-form-field-type-mat-native-select
        .mat-mdc-form-field-infix::after {
        right: auto;
        left: 0;
      }
      .mat-mdc-form-field-type-mat-native-select.mat-focused
        .mat-mdc-form-field-infix::after {
        color: var(--mat-form-field-focus-select-arrow-color);
      }
      .mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled
        .mat-mdc-form-field-infix::after {
        color: var(--mat-form-field-disabled-select-arrow-color);
      }
      .mat-mdc-form-field-type-mat-native-select
        .mat-mdc-form-field-input-control {
        padding-right: 15px;
      }
      [dir="rtl"]
        .mat-mdc-form-field-type-mat-native-select
        .mat-mdc-form-field-input-control {
        padding-right: 0;
        padding-left: 15px;
      }
      .cdk-high-contrast-active
        .mat-form-field-appearance-fill
        .mat-mdc-text-field-wrapper {
        outline: solid 1px;
      }
      .cdk-high-contrast-active
        .mat-form-field-appearance-fill.mat-form-field-disabled
        .mat-mdc-text-field-wrapper {
        outline-color: GrayText;
      }
      .cdk-high-contrast-active
        .mat-form-field-appearance-fill.mat-focused
        .mat-mdc-text-field-wrapper {
        outline: dashed 3px;
      }
      .cdk-high-contrast-active
        .mat-mdc-form-field.mat-focused
        .mdc-notched-outline {
        border: dashed 3px;
      }
      .mat-mdc-form-field-input-control[type="date"],
      .mat-mdc-form-field-input-control[type="datetime"],
      .mat-mdc-form-field-input-control[type="datetime-local"],
      .mat-mdc-form-field-input-control[type="month"],
      .mat-mdc-form-field-input-control[type="week"],
      .mat-mdc-form-field-input-control[type="time"] {
        line-height: 1;
      }
      .mat-mdc-form-field-input-control::-webkit-datetime-edit {
        line-height: 1;
        padding: 0;
        margin-bottom: -2px;
      }
      .mat-mdc-form-field {
        --mat-mdc-form-field-floating-label-scale: 0.75;
        display: inline-flex;
        flex-direction: column;
        min-width: 0;
        text-align: left;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-family: var(--mat-form-field-container-text-font);
        line-height: var(--mat-form-field-container-text-line-height);
        font-size: var(--mat-form-field-container-text-size);
        letter-spacing: var(--mat-form-field-container-text-tracking);
        font-weight: var(--mat-form-field-container-text-weight);
      }
      [dir="rtl"] .mat-mdc-form-field {
        text-align: right;
      }
      .mat-mdc-form-field
        .mdc-text-field--outlined
        .mdc-floating-label--float-above {
        font-size: calc(
          var(--mat-form-field-outlined-label-text-populated-size) *
            var(--mat-mdc-form-field-floating-label-scale)
        );
      }
      .mat-mdc-form-field
        .mdc-text-field--outlined
        .mdc-notched-outline--upgraded
        .mdc-floating-label--float-above {
        font-size: var(--mat-form-field-outlined-label-text-populated-size);
      }
      .mat-mdc-form-field-flex {
        display: inline-flex;
        align-items: baseline;
        box-sizing: border-box;
        width: 100%;
      }
      .mat-mdc-text-field-wrapper {
        width: 100%;
        z-index: 0;
      }
      .mat-mdc-form-field-icon-prefix,
      .mat-mdc-form-field-icon-suffix {
        align-self: center;
        line-height: 0;
        pointer-events: auto;
        position: relative;
        z-index: 1;
      }
      .mat-mdc-form-field-icon-prefix > .mat-icon,
      .mat-mdc-form-field-icon-suffix > .mat-icon {
        padding: 0 12px;
        box-sizing: content-box;
      }
      .mat-mdc-form-field-icon-prefix {
        color: var(--mat-form-field-leading-icon-color);
      }
      .mat-form-field-disabled .mat-mdc-form-field-icon-prefix {
        color: var(--mat-form-field-disabled-leading-icon-color);
      }
      .mat-mdc-form-field-icon-suffix {
        color: var(--mat-form-field-trailing-icon-color);
      }
      .mat-form-field-disabled .mat-mdc-form-field-icon-suffix {
        color: var(--mat-form-field-disabled-trailing-icon-color);
      }
      .mat-form-field-invalid .mat-mdc-form-field-icon-suffix {
        color: var(--mat-form-field-error-trailing-icon-color);
      }
      .mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled)
        .mat-mdc-text-field-wrapper:hover
        .mat-mdc-form-field-icon-suffix {
        color: var(--mat-form-field-error-hover-trailing-icon-color);
      }
      .mat-form-field-invalid.mat-focused
        .mat-mdc-text-field-wrapper
        .mat-mdc-form-field-icon-suffix {
        color: var(--mat-form-field-error-focus-trailing-icon-color);
      }
      .mat-mdc-form-field-icon-prefix,
      [dir="rtl"] .mat-mdc-form-field-icon-suffix {
        padding: 0 4px 0 0;
      }
      .mat-mdc-form-field-icon-suffix,
      [dir="rtl"] .mat-mdc-form-field-icon-prefix {
        padding: 0 0 0 4px;
      }
      .mat-mdc-form-field-subscript-wrapper .mat-icon,
      .mat-mdc-form-field label .mat-icon {
        width: 1em;
        height: 1em;
        font-size: inherit;
      }
      .mat-mdc-form-field-infix {
        flex: auto;
        min-width: 0;
        width: 180px;
        position: relative;
        box-sizing: border-box;
      }
      .mat-mdc-form-field .mdc-notched-outline__notch {
        margin-left: -1px;
        -webkit-clip-path: inset(-9em -999em -9em 1px);
        clip-path: inset(-9em -999em -9em 1px);
      }
      [dir="rtl"] .mat-mdc-form-field .mdc-notched-outline__notch {
        margin-left: 0;
        margin-right: -1px;
        -webkit-clip-path: inset(-9em 1px -9em -999em);
        clip-path: inset(-9em 1px -9em -999em);
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field__input {
        transition: opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      @media all {
        .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input::placeholder {
          transition: opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
      @media all {
        .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input:-ms-input-placeholder {
          transition: opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
      @media all {
        .mdc-text-field--no-label
          .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input::placeholder,
        .mdc-text-field--focused
          .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input::placeholder {
          transition-delay: 40ms;
          transition-duration: 110ms;
        }
      }
      @media all {
        .mdc-text-field--no-label
          .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input:-ms-input-placeholder,
        .mdc-text-field--focused
          .mat-mdc-form-field:not(.mat-form-field-no-animations)
          .mdc-text-field__input:-ms-input-placeholder {
          transition-delay: 40ms;
          transition-duration: 110ms;
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field__affix {
        transition: opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--filled.mdc-ripple-upgraded--background-focused
        .mdc-text-field__ripple::before,
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus
        .mdc-text-field__ripple::before {
        transition-duration: 75ms;
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--outlined
        .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-text-field-outlined
          250ms 1;
      }
      @keyframes mdc-floating-label-shake-float-above-text-field-outlined {
        0% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--textarea {
        transition: none;
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--textarea.mdc-text-field--filled
        .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-textarea-filled 250ms 1;
      }
      @keyframes mdc-floating-label-shake-float-above-textarea-filled {
        0% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px))
            scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px))
            scale(0.75);
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--textarea.mdc-text-field--outlined
        .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-textarea-outlined 250ms
          1;
      }
      @keyframes mdc-floating-label-shake-float-above-textarea-outlined {
        0% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px))
            scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px))
            scale(0.75);
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon
          250ms 1;
      }
      @keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon {
        0% {
          transform: translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
      }
      [dir="rtl"]
        .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined
        .mdc-floating-label--shake,
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir="rtl"]
        .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon
          250ms 1;
      }
      @keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl {
        0% {
          transform: translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - -32px))
            translateY(calc(0% - 34.75px)) scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px))
            scale(0.75);
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-floating-label {
        transition:
          transform 150ms cubic-bezier(0.4, 0, 0.2, 1),
          color 150ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      .mdc-floating-label--shake {
        animation: mdc-floating-label-shake-float-above-standard 250ms 1;
      }
      @keyframes mdc-floating-label-shake-float-above-standard {
        0% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 106%))
            scale(0.75);
        }
        33% {
          animation-timing-function: cubic-bezier(0.5, 0, 0.701732, 0.495819);
          transform: translateX(calc(4% - 0%)) translateY(calc(0% - 106%))
            scale(0.75);
        }
        66% {
          animation-timing-function: cubic-bezier(
            0.302435,
            0.381352,
            0.55,
            0.956352
          );
          transform: translateX(calc(-4% - 0%)) translateY(calc(0% - 106%))
            scale(0.75);
        }
        100% {
          transform: translateX(calc(0% - 0%)) translateY(calc(0% - 106%))
            scale(0.75);
        }
      }
      .mat-mdc-form-field:not(.mat-form-field-no-animations)
        .mdc-line-ripple::after {
        transition:
          transform 180ms cubic-bezier(0.4, 0, 0.2, 1),
          opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      .mdc-notched-outline .mdc-floating-label {
        max-width: calc(100% + 1px);
      }
      .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
        max-width: calc(133.3333333333% + 1px);
      }
    </style>
    <style>
      mat-icon,
      mat-icon.mat-primary,
      mat-icon.mat-accent,
      mat-icon.mat-warn {
        color: var(--mat-icon-color);
      }
      .mat-icon {
        -webkit-user-select: none;
        user-select: none;
        background-repeat: no-repeat;
        display: inline-block;
        fill: currentColor;
        height: 24px;
        width: 24px;
        overflow: hidden;
      }
      .mat-icon.mat-icon-inline {
        font-size: inherit;
        height: inherit;
        line-height: inherit;
        width: inherit;
      }
      .mat-icon.mat-ligature-font[fontIcon]::before {
        content: attr(fontIcon);
      }
      [dir="rtl"] .mat-icon-rtl-mirror {
        transform: scale(-1, 1);
      }
      .mat-form-field:not(.mat-form-field-appearance-legacy)
        .mat-form-field-prefix
        .mat-icon,
      .mat-form-field:not(.mat-form-field-appearance-legacy)
        .mat-form-field-suffix
        .mat-icon {
        display: block;
      }
      .mat-form-field:not(.mat-form-field-appearance-legacy)
        .mat-form-field-prefix
        .mat-icon-button
        .mat-icon,
      .mat-form-field:not(.mat-form-field-appearance-legacy)
        .mat-form-field-suffix
        .mat-icon-button
        .mat-icon {
        margin: auto;
      }
    </style>
    <style>
      .mdc-touch-target-wrapper {
        display: inline;
      }
      .mdc-elevation-overlay {
        position: absolute;
        border-radius: inherit;
        pointer-events: none;
        opacity: var(--mdc-elevation-overlay-opacity, 0);
        transition: opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      .mdc-button {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        min-width: 64px;
        border: none;
        outline: none;
        line-height: inherit;
        user-select: none;
        -webkit-appearance: none;
        overflow: visible;
        vertical-align: middle;
        background: rgba(0, 0, 0, 0);
      }
      .mdc-button .mdc-elevation-overlay {
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
      }
      .mdc-button::-moz-focus-inner {
        padding: 0;
        border: 0;
      }
      .mdc-button:active {
        outline: none;
      }
      .mdc-button:hover {
        cursor: pointer;
      }
      .mdc-button:disabled {
        cursor: default;
        pointer-events: none;
      }
      .mdc-button[hidden] {
        display: none;
      }
      .mdc-button .mdc-button__icon {
        margin-left: 0;
        margin-right: 8px;
        display: inline-block;
        position: relative;
        vertical-align: top;
      }
      [dir="rtl"] .mdc-button .mdc-button__icon,
      .mdc-button .mdc-button__icon[dir="rtl"] {
        margin-left: 8px;
        margin-right: 0;
      }
      .mdc-button .mdc-button__progress-indicator {
        font-size: 0;
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        line-height: initial;
      }
      .mdc-button .mdc-button__label {
        position: relative;
      }
      .mdc-button .mdc-button__focus-ring {
        pointer-events: none;
        border: 2px solid rgba(0, 0, 0, 0);
        border-radius: 6px;
        box-sizing: content-box;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        height: calc(100% + 4px);
        width: calc(100% + 4px);
        display: none;
      }
      @media screen and (forced-colors: active) {
        .mdc-button .mdc-button__focus-ring {
          border-color: CanvasText;
        }
      }
      .mdc-button .mdc-button__focus-ring::after {
        content: "";
        border: 2px solid rgba(0, 0, 0, 0);
        border-radius: 8px;
        display: block;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        height: calc(100% + 4px);
        width: calc(100% + 4px);
      }
      @media screen and (forced-colors: active) {
        .mdc-button .mdc-button__focus-ring::after {
          border-color: CanvasText;
        }
      }
      @media screen and (forced-colors: active) {
        .mdc-button.mdc-ripple-upgraded--background-focused
          .mdc-button__focus-ring,
        .mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring {
          display: block;
        }
      }
      .mdc-button .mdc-button__touch {
        position: absolute;
        top: 50%;
        height: 48px;
        left: 0;
        right: 0;
        transform: translateY(-50%);
      }
      .mdc-button__label + .mdc-button__icon {
        margin-left: 8px;
        margin-right: 0;
      }
      [dir="rtl"] .mdc-button__label + .mdc-button__icon,
      .mdc-button__label + .mdc-button__icon[dir="rtl"] {
        margin-left: 0;
        margin-right: 8px;
      }
      svg.mdc-button__icon {
        fill: currentColor;
      }
      .mdc-button--touch {
        margin-top: 6px;
        margin-bottom: 6px;
      }
      .mdc-button {
        padding: 0 8px 0 8px;
      }
      .mdc-button--unelevated {
        transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
        padding: 0 16px 0 16px;
      }
      .mdc-button--unelevated.mdc-button--icon-trailing {
        padding: 0 12px 0 16px;
      }
      .mdc-button--unelevated.mdc-button--icon-leading {
        padding: 0 16px 0 12px;
      }
      .mdc-button--raised {
        transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
        padding: 0 16px 0 16px;
      }
      .mdc-button--raised.mdc-button--icon-trailing {
        padding: 0 12px 0 16px;
      }
      .mdc-button--raised.mdc-button--icon-leading {
        padding: 0 16px 0 12px;
      }
      .mdc-button--outlined {
        border-style: solid;
        transition: border 280ms cubic-bezier(0.4, 0, 0.2, 1);
      }
      .mdc-button--outlined .mdc-button__ripple {
        border-style: solid;
        border-color: rgba(0, 0, 0, 0);
      }
      .mat-mdc-button {
        font-family: var(--mdc-text-button-label-text-font);
        font-size: var(--mdc-text-button-label-text-size);
        letter-spacing: var(--mdc-text-button-label-text-tracking);
        font-weight: var(--mdc-text-button-label-text-weight);
        text-transform: var(--mdc-text-button-label-text-transform);
        height: var(--mdc-text-button-container-height);
        border-radius: var(--mdc-text-button-container-shape);
        padding: 0 var(--mat-text-button-horizontal-padding, 8px);
      }
      .mat-mdc-button:not(:disabled) {
        color: var(--mdc-text-button-label-text-color);
      }
      .mat-mdc-button:disabled {
        color: var(--mdc-text-button-disabled-label-text-color);
      }
      .mat-mdc-button .mdc-button__ripple {
        border-radius: var(--mdc-text-button-container-shape);
      }
      .mat-mdc-button:has(.material-icons, mat-icon, [matButtonIcon]) {
        padding: 0 var(--mat-text-button-with-icon-horizontal-padding, 8px);
      }
      .mat-mdc-button > .mat-icon {
        margin-right: var(--mat-text-button-icon-spacing, 8px);
        margin-left: var(--mat-text-button-icon-offset, 0);
      }
      [dir="rtl"] .mat-mdc-button > .mat-icon {
        margin-right: var(--mat-text-button-icon-offset, 0);
        margin-left: var(--mat-text-button-icon-spacing, 8px);
      }
      .mat-mdc-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-text-button-icon-offset, 0);
        margin-left: var(--mat-text-button-icon-spacing, 8px);
      }
      [dir="rtl"] .mat-mdc-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-text-button-icon-spacing, 8px);
        margin-left: var(--mat-text-button-icon-offset, 0);
      }
      .mat-mdc-button .mat-ripple-element {
        background-color: var(--mat-text-button-ripple-color);
      }
      .mat-mdc-button .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-text-button-state-layer-color);
      }
      .mat-mdc-button.mat-mdc-button-disabled
        .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-text-button-disabled-state-layer-color);
      }
      .mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-text-button-hover-state-layer-opacity);
      }
      .mat-mdc-button.cdk-program-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-button.cdk-keyboard-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-button.mat-mdc-button-disabled-interactive:focus
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-text-button-focus-state-layer-opacity);
      }
      .mat-mdc-button:active .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-text-button-pressed-state-layer-opacity);
      }
      .mat-mdc-button .mat-mdc-button-touch-target {
        position: absolute;
        top: 50%;
        height: 48px;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        display: var(--mat-text-button-touch-target-display);
      }
      .mat-mdc-button[disabled],
      .mat-mdc-button.mat-mdc-button-disabled {
        cursor: default;
        pointer-events: none;
        color: var(--mdc-text-button-disabled-label-text-color);
      }
      .mat-mdc-button.mat-mdc-button-disabled-interactive {
        pointer-events: auto;
      }
      .mat-mdc-unelevated-button {
        font-family: var(--mdc-filled-button-label-text-font);
        font-size: var(--mdc-filled-button-label-text-size);
        letter-spacing: var(--mdc-filled-button-label-text-tracking);
        font-weight: var(--mdc-filled-button-label-text-weight);
        text-transform: var(--mdc-filled-button-label-text-transform);
        height: var(--mdc-filled-button-container-height);
        border-radius: var(--mdc-filled-button-container-shape);
        padding: 0 var(--mat-filled-button-horizontal-padding, 16px);
      }
      .mat-mdc-unelevated-button:not(:disabled) {
        background-color: var(--mdc-filled-button-container-color);
      }
      .mat-mdc-unelevated-button:disabled {
        background-color: var(--mdc-filled-button-disabled-container-color);
      }
      .mat-mdc-unelevated-button:not(:disabled) {
        color: var(--mdc-filled-button-label-text-color);
      }
      .mat-mdc-unelevated-button:disabled {
        color: var(--mdc-filled-button-disabled-label-text-color);
      }
      .mat-mdc-unelevated-button .mdc-button__ripple {
        border-radius: var(--mdc-filled-button-container-shape);
      }
      .mat-mdc-unelevated-button > .mat-icon {
        margin-right: var(--mat-filled-button-icon-spacing, 8px);
        margin-left: var(--mat-filled-button-icon-offset, -4px);
      }
      [dir="rtl"] .mat-mdc-unelevated-button > .mat-icon {
        margin-right: var(--mat-filled-button-icon-offset, -4px);
        margin-left: var(--mat-filled-button-icon-spacing, 8px);
      }
      .mat-mdc-unelevated-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-filled-button-icon-offset, -4px);
        margin-left: var(--mat-filled-button-icon-spacing, 8px);
      }
      [dir="rtl"] .mat-mdc-unelevated-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-filled-button-icon-spacing, 8px);
        margin-left: var(--mat-filled-button-icon-offset, -4px);
      }
      .mat-mdc-unelevated-button .mat-ripple-element {
        background-color: var(--mat-filled-button-ripple-color);
      }
      .mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-filled-button-state-layer-color);
      }
      .mat-mdc-unelevated-button.mat-mdc-button-disabled
        .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-filled-button-disabled-state-layer-color);
      }
      .mat-mdc-unelevated-button:hover
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-filled-button-hover-state-layer-opacity);
      }
      .mat-mdc-unelevated-button.cdk-program-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-unelevated-button.cdk-keyboard-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-filled-button-focus-state-layer-opacity);
      }
      .mat-mdc-unelevated-button:active
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-filled-button-pressed-state-layer-opacity);
      }
      .mat-mdc-unelevated-button .mat-mdc-button-touch-target {
        position: absolute;
        top: 50%;
        height: 48px;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        display: var(--mat-filled-button-touch-target-display);
      }
      .mat-mdc-unelevated-button[disabled],
      .mat-mdc-unelevated-button.mat-mdc-button-disabled {
        cursor: default;
        pointer-events: none;
        color: var(--mdc-filled-button-disabled-label-text-color);
        background-color: var(--mdc-filled-button-disabled-container-color);
      }
      .mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive {
        pointer-events: auto;
      }
      .mat-mdc-raised-button {
        font-family: var(--mdc-protected-button-label-text-font);
        font-size: var(--mdc-protected-button-label-text-size);
        letter-spacing: var(--mdc-protected-button-label-text-tracking);
        font-weight: var(--mdc-protected-button-label-text-weight);
        text-transform: var(--mdc-protected-button-label-text-transform);
        height: var(--mdc-protected-button-container-height);
        border-radius: var(--mdc-protected-button-container-shape);
        padding: 0 var(--mat-protected-button-horizontal-padding, 16px);
        box-shadow: var(--mdc-protected-button-container-elevation-shadow);
      }
      .mat-mdc-raised-button:not(:disabled) {
        background-color: var(--mdc-protected-button-container-color);
      }
      .mat-mdc-raised-button:disabled {
        background-color: var(--mdc-protected-button-disabled-container-color);
      }
      .mat-mdc-raised-button:not(:disabled) {
        color: var(--mdc-protected-button-label-text-color);
      }
      .mat-mdc-raised-button:disabled {
        color: var(--mdc-protected-button-disabled-label-text-color);
      }
      .mat-mdc-raised-button .mdc-button__ripple {
        border-radius: var(--mdc-protected-button-container-shape);
      }
      .mat-mdc-raised-button > .mat-icon {
        margin-right: var(--mat-protected-button-icon-spacing, 8px);
        margin-left: var(--mat-protected-button-icon-offset, -4px);
      }
      [dir="rtl"] .mat-mdc-raised-button > .mat-icon {
        margin-right: var(--mat-protected-button-icon-offset, -4px);
        margin-left: var(--mat-protected-button-icon-spacing, 8px);
      }
      .mat-mdc-raised-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-protected-button-icon-offset, -4px);
        margin-left: var(--mat-protected-button-icon-spacing, 8px);
      }
      [dir="rtl"] .mat-mdc-raised-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-protected-button-icon-spacing, 8px);
        margin-left: var(--mat-protected-button-icon-offset, -4px);
      }
      .mat-mdc-raised-button .mat-ripple-element {
        background-color: var(--mat-protected-button-ripple-color);
      }
      .mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-protected-button-state-layer-color);
      }
      .mat-mdc-raised-button.mat-mdc-button-disabled
        .mat-mdc-button-persistent-ripple::before {
        background-color: var(
          --mat-protected-button-disabled-state-layer-color
        );
      }
      .mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-protected-button-hover-state-layer-opacity);
      }
      .mat-mdc-raised-button.cdk-program-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-raised-button.cdk-keyboard-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-protected-button-focus-state-layer-opacity);
      }
      .mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-protected-button-pressed-state-layer-opacity);
      }
      .mat-mdc-raised-button .mat-mdc-button-touch-target {
        position: absolute;
        top: 50%;
        height: 48px;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        display: var(--mat-protected-button-touch-target-display);
      }
      .mat-mdc-raised-button:hover {
        box-shadow: var(
          --mdc-protected-button-hover-container-elevation-shadow
        );
      }
      .mat-mdc-raised-button:focus {
        box-shadow: var(
          --mdc-protected-button-focus-container-elevation-shadow
        );
      }
      .mat-mdc-raised-button:active,
      .mat-mdc-raised-button:focus:active {
        box-shadow: var(
          --mdc-protected-button-pressed-container-elevation-shadow
        );
      }
      .mat-mdc-raised-button[disabled],
      .mat-mdc-raised-button.mat-mdc-button-disabled {
        cursor: default;
        pointer-events: none;
        color: var(--mdc-protected-button-disabled-label-text-color);
        background-color: var(--mdc-protected-button-disabled-container-color);
      }
      .mat-mdc-raised-button[disabled].mat-mdc-button-disabled,
      .mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled {
        box-shadow: var(
          --mdc-protected-button-disabled-container-elevation-shadow
        );
      }
      .mat-mdc-raised-button.mat-mdc-button-disabled-interactive {
        pointer-events: auto;
      }
      .mat-mdc-outlined-button {
        font-family: var(--mdc-outlined-button-label-text-font);
        font-size: var(--mdc-outlined-button-label-text-size);
        letter-spacing: var(--mdc-outlined-button-label-text-tracking);
        font-weight: var(--mdc-outlined-button-label-text-weight);
        text-transform: var(--mdc-outlined-button-label-text-transform);
        height: var(--mdc-outlined-button-container-height);
        border-radius: var(--mdc-outlined-button-container-shape);
        padding: 0 15px 0 15px;
        border-width: var(--mdc-outlined-button-outline-width);
        padding: 0 var(--mat-outlined-button-horizontal-padding, 15px);
      }
      .mat-mdc-outlined-button:not(:disabled) {
        color: var(--mdc-outlined-button-label-text-color);
      }
      .mat-mdc-outlined-button:disabled {
        color: var(--mdc-outlined-button-disabled-label-text-color);
      }
      .mat-mdc-outlined-button .mdc-button__ripple {
        border-radius: var(--mdc-outlined-button-container-shape);
      }
      .mat-mdc-outlined-button:not(:disabled) {
        border-color: var(--mdc-outlined-button-outline-color);
      }
      .mat-mdc-outlined-button:disabled {
        border-color: var(--mdc-outlined-button-disabled-outline-color);
      }
      .mat-mdc-outlined-button.mdc-button--icon-trailing {
        padding: 0 11px 0 15px;
      }
      .mat-mdc-outlined-button.mdc-button--icon-leading {
        padding: 0 15px 0 11px;
      }
      .mat-mdc-outlined-button .mdc-button__ripple {
        top: -1px;
        left: -1px;
        bottom: -1px;
        right: -1px;
        border-width: var(--mdc-outlined-button-outline-width);
      }
      .mat-mdc-outlined-button .mdc-button__touch {
        left: calc(-1 * var(--mdc-outlined-button-outline-width));
        width: calc(100% + 2 * var(--mdc-outlined-button-outline-width));
      }
      .mat-mdc-outlined-button > .mat-icon {
        margin-right: var(--mat-outlined-button-icon-spacing, 8px);
        margin-left: var(--mat-outlined-button-icon-offset, -4px);
      }
      [dir="rtl"] .mat-mdc-outlined-button > .mat-icon {
        margin-right: var(--mat-outlined-button-icon-offset, -4px);
        margin-left: var(--mat-outlined-button-icon-spacing, 8px);
      }
      .mat-mdc-outlined-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-outlined-button-icon-offset, -4px);
        margin-left: var(--mat-outlined-button-icon-spacing, 8px);
      }
      [dir="rtl"] .mat-mdc-outlined-button .mdc-button__label + .mat-icon {
        margin-right: var(--mat-outlined-button-icon-spacing, 8px);
        margin-left: var(--mat-outlined-button-icon-offset, -4px);
      }
      .mat-mdc-outlined-button .mat-ripple-element {
        background-color: var(--mat-outlined-button-ripple-color);
      }
      .mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-outlined-button-state-layer-color);
      }
      .mat-mdc-outlined-button.mat-mdc-button-disabled
        .mat-mdc-button-persistent-ripple::before {
        background-color: var(--mat-outlined-button-disabled-state-layer-color);
      }
      .mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-outlined-button-hover-state-layer-opacity);
      }
      .mat-mdc-outlined-button.cdk-program-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-outlined-button.cdk-keyboard-focused
        .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-outlined-button-focus-state-layer-opacity);
      }
      .mat-mdc-outlined-button:active
        .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-outlined-button-pressed-state-layer-opacity);
      }
      .mat-mdc-outlined-button .mat-mdc-button-touch-target {
        position: absolute;
        top: 50%;
        height: 48px;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        display: var(--mat-outlined-button-touch-target-display);
      }
      .mat-mdc-outlined-button[disabled],
      .mat-mdc-outlined-button.mat-mdc-button-disabled {
        cursor: default;
        pointer-events: none;
        color: var(--mdc-outlined-button-disabled-label-text-color);
        border-color: var(--mdc-outlined-button-disabled-outline-color);
      }
      .mat-mdc-outlined-button.mat-mdc-button-disabled-interactive {
        pointer-events: auto;
      }
      .mat-mdc-button-base {
        text-decoration: none;
      }
      .mat-mdc-button,
      .mat-mdc-unelevated-button,
      .mat-mdc-raised-button,
      .mat-mdc-outlined-button {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      }
      .mat-mdc-button .mat-mdc-button-ripple,
      .mat-mdc-button .mat-mdc-button-persistent-ripple,
      .mat-mdc-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-unelevated-button .mat-mdc-button-ripple,
      .mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,
      .mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-raised-button .mat-mdc-button-ripple,
      .mat-mdc-raised-button .mat-mdc-button-persistent-ripple,
      .mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-outlined-button .mat-mdc-button-ripple,
      .mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,
      .mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
        pointer-events: none;
        border-radius: inherit;
      }
      .mat-mdc-button .mat-mdc-button-ripple,
      .mat-mdc-unelevated-button .mat-mdc-button-ripple,
      .mat-mdc-raised-button .mat-mdc-button-ripple,
      .mat-mdc-outlined-button .mat-mdc-button-ripple {
        overflow: hidden;
      }
      .mat-mdc-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,
      .mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before {
        content: "";
        opacity: 0;
      }
      .mat-mdc-button .mdc-button__label,
      .mat-mdc-unelevated-button .mdc-button__label,
      .mat-mdc-raised-button .mdc-button__label,
      .mat-mdc-outlined-button .mdc-button__label {
        z-index: 1;
      }
      .mat-mdc-button .mat-mdc-focus-indicator,
      .mat-mdc-unelevated-button .mat-mdc-focus-indicator,
      .mat-mdc-raised-button .mat-mdc-focus-indicator,
      .mat-mdc-outlined-button .mat-mdc-focus-indicator {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
      }
      .mat-mdc-button:focus .mat-mdc-focus-indicator::before,
      .mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,
      .mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,
      .mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before {
        content: "";
      }
      .mat-mdc-button._mat-animation-noopable,
      .mat-mdc-unelevated-button._mat-animation-noopable,
      .mat-mdc-raised-button._mat-animation-noopable,
      .mat-mdc-outlined-button._mat-animation-noopable {
        transition: none !important;
        animation: none !important;
      }
      .mat-mdc-button > .mat-icon,
      .mat-mdc-unelevated-button > .mat-icon,
      .mat-mdc-raised-button > .mat-icon,
      .mat-mdc-outlined-button > .mat-icon {
        display: inline-block;
        position: relative;
        vertical-align: top;
        font-size: 1.125rem;
        height: 1.125rem;
        width: 1.125rem;
      }
      .mat-mdc-outlined-button .mat-mdc-button-ripple,
      .mat-mdc-outlined-button .mdc-button__ripple {
        top: -1px;
        left: -1px;
        bottom: -1px;
        right: -1px;
        border-width: -1px;
      }
      .mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,
      .mat-mdc-raised-button .mat-mdc-focus-indicator::before {
        margin: calc(
          calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1
        );
      }
      .mat-mdc-outlined-button .mat-mdc-focus-indicator::before {
        margin: calc(
          calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px) * -1
        );
      }
    </style>
    <style>
      .cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),
      .cdk-high-contrast-active
        .mat-mdc-unelevated-button:not(.mdc-button--outlined),
      .cdk-high-contrast-active
        .mat-mdc-raised-button:not(.mdc-button--outlined),
      .cdk-high-contrast-active
        .mat-mdc-outlined-button:not(.mdc-button--outlined),
      .cdk-high-contrast-active .mat-mdc-icon-button {
        outline: solid 1px;
      }
    </style></head
  ><iframe
    class="cleanslate hidden"
    src="login_files/commandline.html"
    id="cmdline_iframe"
    loading="lazy"
    style="height: 0px !important"
  ></iframe>

  <body class="dx-device-desktop dx-device-generic">
    <c3-app _nghost-ng-c3118524757="" ng-version="17.3.12"
      ><div
        _ngcontent-ng-c3118524757=""
        class="application-container c3-layout-default c3-flex-layout-col c3-flex-layout-align-start-stretch c3-typography-condensed"
      >
        <!----><!----><mat-sidenav-container
          _ngcontent-ng-c3118524757=""
          autosize=""
          class="mat-drawer-container mat-sidenav-container c3-flex-1-1-auto"
          ><!----><!----><!----><mat-sidenav-content
            class="mat-drawer-content mat-sidenav-content"
            ><div
              _ngcontent-ng-c3118524757=""
              class="c3-flex-fill c3-flex-layout-col"
            >
              <!---->
              <div
                _ngcontent-ng-c3118524757=""
                class="overflow-auto c3-flex-fill"
              >
                <router-outlet _ngcontent-ng-c3118524757=""></router-outlet
                ><c3-login _nghost-ng-c2935415455="" class="ng-star-inserted"
                  ><div
                    _ngcontent-ng-c2935415455=""
                    class="c3-flex-fill c3-flex-layout-row c3-flex-layout-align-start-stretch"
                  >
                    <div
                      _ngcontent-ng-c2935415455=""
                      class="chromatic-ignore background c3-flex-1-1-auto"
                      style="
                        background-image: url(&quot;https://www.c3reservations.com/images/CSWG/CS_truck_and_trailer_for_C3_Yard_landing_page.jpg&quot;);
                      "
                    ></div>
                    <div
                      _ngcontent-ng-c2935415455=""
                      class="logo-and-login c3-layout-gap-column c3-flex-1-1-auto c3-flex-layout-col c3-flex-layout-align-space-around-stretch"
                    >
                      <img
                        _ngcontent-ng-c2935415455=""
                        class="img-cie-logo"
                        src="login_files/CSWG_bg.png"
                      />
                      <div
                        _ngcontent-ng-c2935415455=""
                        class="c3-layout-gap-column c3-flex-layout-col c3-flex-layout-align-space-between-center ng-star-inserted"
                      >
                        <div _ngcontent-ng-c2935415455="" class="login-content">
                          <div
                            _ngcontent-ng-c2935415455=""
                            class="c3-layout-gap-column c3-flex-layout-col c3-flex-layout-align-space-between-stretch"
                          >
                            <!----><!---->
                            <form
                              _ngcontent-ng-c2935415455=""
                              novalidate=""
                              class="c3-layout-gap-column c3-flex-layout-col c3-flex-layout-align-space-between-stretch ng-untouched ng-pristine ng-invalid ng-star-inserted"
                            >
                              <mat-form-field
                                _ngcontent-ng-c2935415455=""
                                class="mat-mdc-form-field ng-tns-c3736059725-0 mat-mdc-form-field-type-mat-input mat-mdc-form-field-has-icon-prefix mat-form-field-appearance-outline mat-form-field-hide-placeholder mat-primary ng-untouched ng-pristine ng-invalid ng-star-inserted"
                                ><!---->
                                <div
                                  class="mat-mdc-text-field-wrapper mdc-text-field ng-tns-c3736059725-0 mdc-text-field--outlined"
                                >
                                  <!---->
                                  <div
                                    class="mat-mdc-form-field-flex ng-tns-c3736059725-0"
                                  >
                                    <div
                                      matformfieldnotchedoutline=""
                                      class="mdc-notched-outline ng-tns-c3736059725-0 mdc-notched-outline--upgraded ng-star-inserted"
                                    >
                                      <div
                                        class="mdc-notched-outline__leading"
                                      ></div>
                                      <div class="mdc-notched-outline__notch">
                                        <label
                                          matformfieldfloatinglabel=""
                                          class="mdc-floating-label mat-mdc-floating-label ng-tns-c3736059725-0 ng-star-inserted"
                                          id="mat-mdc-form-field-label-0"
                                          for="mat-input-0"
                                          style="
                                            transform: var(
                                              --mat-mdc-form-field-label-transform,
                                              translateY(-50%)
                                                translateX(
                                                  calc(
                                                    1 *
                                                      (
                                                        52px +
                                                          var(
                                                            --mat-mdc-form-field-label-offset-x,
                                                            0px
                                                          )
                                                      )
                                                  )
                                                )
                                            );
                                          "
                                          ><mat-label
                                            _ngcontent-ng-c2935415455=""
                                            class="ng-tns-c3736059725-0"
                                            >User Name</mat-label
                                          ><span
                                            aria-hidden="true"
                                            class="mat-mdc-form-field-required-marker mdc-floating-label--required ng-tns-c3736059725-0 ng-star-inserted"
                                          ></span
                                          ><!----></label
                                        ><!----><!----><!---->
                                      </div>
                                      <div
                                        class="mdc-notched-outline__trailing"
                                      ></div>
                                    </div>
                                    <!---->
                                    <div
                                      class="mat-mdc-form-field-icon-prefix ng-tns-c3736059725-0 ng-star-inserted"
                                    >
                                      <mat-icon
                                        _ngcontent-ng-c2935415455=""
                                        role="img"
                                        maticonprefix=""
                                        class="mat-icon notranslate material-icons mat-ligature-font mat-icon-no-color ng-tns-c3736059725-0"
                                        aria-hidden="true"
                                        data-mat-icon-type="font"
                                        >person</mat-icon
                                      >
                                    </div>
                                    <!----><!---->
                                    <div
                                      class="mat-mdc-form-field-infix ng-tns-c3736059725-0"
                                    >
                                      <!----><input
                                        _ngcontent-ng-c2935415455=""
                                        matinput=""
                                        formcontrolname="userName"
                                        autocomplete="off"
                                        data-e2e="username"
                                        class="mat-mdc-input-element form-control ng-tns-c3736059725-0 mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine ng-invalid cdk-text-field-autofill-monitored"
                                        id="mat-input-0"
                                        required=""
                                        aria-required="true"
                                        name="off_484cfe65-5867-70bc-3479-08398b7d180b"
                                      />
                                    </div>
                                    <!----><!---->
                                  </div>
                                  <!---->
                                </div>
                                <div
                                  class="mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align ng-tns-c3736059725-0 mat-mdc-form-field-subscript-dynamic-size"
                                >
                                  <div
                                    class="mat-mdc-form-field-hint-wrapper ng-tns-c3736059725-0 ng-trigger ng-trigger-transitionMessages ng-star-inserted"
                                    style="
                                      opacity: 1;
                                      transform: translateY(0%);
                                    "
                                  >
                                    <!---->
                                    <div
                                      class="mat-mdc-form-field-hint-spacer ng-tns-c3736059725-0"
                                    ></div>
                                  </div>
                                  <!----><!---->
                                </div></mat-form-field
                              ><mat-form-field
                                _ngcontent-ng-c2935415455=""
                                class="mat-mdc-form-field ng-tns-c3736059725-1 mat-mdc-form-field-type-mat-input mat-mdc-form-field-has-icon-prefix mat-form-field-appearance-outline mat-form-field-hide-placeholder mat-primary ng-untouched ng-pristine ng-invalid ng-star-inserted"
                                ><!---->
                                <div
                                  class="mat-mdc-text-field-wrapper mdc-text-field ng-tns-c3736059725-1 mdc-text-field--outlined"
                                >
                                  <!---->
                                  <div
                                    class="mat-mdc-form-field-flex ng-tns-c3736059725-1"
                                  >
                                    <div
                                      matformfieldnotchedoutline=""
                                      class="mdc-notched-outline ng-tns-c3736059725-1 mdc-notched-outline--upgraded ng-star-inserted"
                                    >
                                      <div
                                        class="mdc-notched-outline__leading"
                                      ></div>
                                      <div class="mdc-notched-outline__notch">
                                        <label
                                          matformfieldfloatinglabel=""
                                          class="mdc-floating-label mat-mdc-floating-label ng-tns-c3736059725-1 ng-star-inserted"
                                          id="mat-mdc-form-field-label-2"
                                          for="mat-input-1"
                                          style="
                                            transform: var(
                                              --mat-mdc-form-field-label-transform,
                                              translateY(-50%)
                                                translateX(
                                                  calc(
                                                    1 *
                                                      (
                                                        52px +
                                                          var(
                                                            --mat-mdc-form-field-label-offset-x,
                                                            0px
                                                          )
                                                      )
                                                  )
                                                )
                                            );
                                          "
                                          ><mat-label
                                            _ngcontent-ng-c2935415455=""
                                            class="ng-tns-c3736059725-1"
                                            >Password</mat-label
                                          ><span
                                            aria-hidden="true"
                                            class="mat-mdc-form-field-required-marker mdc-floating-label--required ng-tns-c3736059725-1 ng-star-inserted"
                                          ></span
                                          ><!----></label
                                        ><!----><!----><!---->
                                      </div>
                                      <div
                                        class="mdc-notched-outline__trailing"
                                      ></div>
                                    </div>
                                    <!---->
                                    <div
                                      class="mat-mdc-form-field-icon-prefix ng-tns-c3736059725-1 ng-star-inserted"
                                    >
                                      <mat-icon
                                        _ngcontent-ng-c2935415455=""
                                        role="img"
                                        maticonprefix=""
                                        class="mat-icon notranslate material-icons mat-ligature-font mat-icon-no-color ng-tns-c3736059725-1"
                                        aria-hidden="true"
                                        data-mat-icon-type="font"
                                        >lock</mat-icon
                                      >
                                    </div>
                                    <!----><!---->
                                    <div
                                      class="mat-mdc-form-field-infix ng-tns-c3736059725-1"
                                    >
                                      <!----><input
                                        _ngcontent-ng-c2935415455=""
                                        matinput=""
                                        type="password"
                                        formcontrolname="password"
                                        autocomplete="off"
                                        data-e2e="password"
                                        class="mat-mdc-input-element form-control ng-tns-c3736059725-1 mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine ng-invalid cdk-text-field-autofill-monitored"
                                        id="mat-input-1"
                                        required=""
                                        aria-required="true"
                                        name="off_b9d39260-98ff-37c8-187d-1f208f3f7616"
                                      />
                                    </div>
                                    <!----><!---->
                                  </div>
                                  <!---->
                                </div>
                                <div
                                  class="mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align ng-tns-c3736059725-1 mat-mdc-form-field-subscript-dynamic-size"
                                >
                                  <div
                                    class="mat-mdc-form-field-hint-wrapper ng-tns-c3736059725-1 ng-trigger ng-trigger-transitionMessages ng-star-inserted"
                                    style="
                                      opacity: 1;
                                      transform: translateY(0%);
                                    "
                                  >
                                    <!---->
                                    <div
                                      class="mat-mdc-form-field-hint-spacer ng-tns-c3736059725-1"
                                    ></div>
                                  </div>
                                  <!----><!---->
                                </div></mat-form-field
                              ><button
                                _ngcontent-ng-c2935415455=""
                                type="submit"
                                mat-raised-button=""
                                data-e2e="login"
                                color="primary"
                                class="main-button mdc-button mdc-button--raised mat-mdc-raised-button mat-primary mat-mdc-button-base"
                                mat-ripple-loader-uninitialized=""
                                mat-ripple-loader-class-name="mat-mdc-button-ripple"
                              >
                                <span
                                  class="mat-mdc-button-persistent-ripple mdc-button__ripple"
                                ></span
                                ><span class="mdc-button__label"> LOGIN </span
                                ><span class="mat-mdc-focus-indicator"></span
                                ><span
                                  class="mat-mdc-button-touch-target"
                                ></span>
                              </button>
                            </form>
                            <!---->
                            <div
                              _ngcontent-ng-c2935415455=""
                              class="ng-star-inserted"
                            >
                              Or
                            </div>
                            <!----><button
                              _ngcontent-ng-c2935415455=""
                              mat-raised-button=""
                              class="main-button mdc-button mdc-button--raised mat-mdc-raised-button mat-unthemed mat-mdc-button-base ng-star-inserted"
                              mat-ripple-loader-uninitialized=""
                              mat-ripple-loader-class-name="mat-mdc-button-ripple"
                            >
                              <span
                                class="mat-mdc-button-persistent-ripple mdc-button__ripple"
                              ></span
                              ><span class="mdc-button__label"> CSWG User </span
                              ><span class="mat-mdc-focus-indicator"></span
                              ><span
                                class="mat-mdc-button-touch-target"
                              ></span></button
                            ><!----><!----><!---->
                          </div>
                        </div>
                        <div
                          _ngcontent-ng-c2935415455=""
                          class="c3-flex-layout-row c3-flex-layout-align-center-center"
                        >
                          <button
                            _ngcontent-ng-c2935415455=""
                            mat-button=""
                            data-e2e="resetPassword"
                            mat-ripple-loader-uninitialized=""
                            mat-ripple-loader-class-name="mat-mdc-button-ripple"
                            class="mdc-button mat-mdc-button mat-unthemed mat-mdc-button-base ng-star-inserted"
                          >
                            <span
                              class="mat-mdc-button-persistent-ripple mdc-button__ripple"
                            ></span
                            ><span class="mdc-button__label">
                              Forgot your password? </span
                            ><span class="mat-mdc-focus-indicator"></span
                            ><span
                              class="mat-mdc-button-touch-target"
                            ></span></button
                          ><!----><span
                            _ngcontent-ng-c2935415455=""
                            class="ng-star-inserted"
                            >|</span
                          ><!----><a
                            _ngcontent-ng-c2935415455=""
                            mat-button=""
                            target="_blank"
                            data-e2e="needHelp"
                            class="mat-mdc-tooltip-trigger mdc-button mat-mdc-button mat-unthemed mat-mdc-button-base ng-star-inserted"
                            mat-ripple-loader-uninitialized=""
                            mat-ripple-loader-class-name="mat-mdc-button-ripple"
                            href="https://c3supportdocuments.s3.us-east-1.amazonaws.com/C%26S+C3+Reservations+Training+11112024-+no+standing.pdf"
                            aria-describedby="cdk-describedby-message-ng-1-2"
                            cdk-describedby-host="ng-1"
                            ><span
                              class="mat-mdc-button-persistent-ripple mdc-button__ripple"
                            ></span
                            ><span class="mdc-button__label">Need help?</span
                            ><span class="mat-mdc-focus-indicator"></span
                            ><span
                              class="mat-mdc-button-touch-target"
                            ></span></a
                          ><!----><!---->
                        </div>
                      </div>
                      <!----><!---->
                      <div
                        _ngcontent-ng-c2935415455=""
                        class="c3-layout-gap-column c3-flex-layout-col c3-flex-layout-align-space-between-center"
                      >
                        <!----><!----><mat-divider
                          _ngcontent-ng-c2935415455=""
                          role="separator"
                          class="mat-divider mat-divider-horizontal"
                          aria-orientation="horizontal"
                        ></mat-divider
                        ><img
                          _ngcontent-ng-c2935415455=""
                          src="login_files/c3Powered.svg"
                          class="img-c3-powered"
                        />
                      </div>
                    </div></div></c3-login
                ><!----><router-outlet
                  _ngcontent-ng-c3118524757=""
                  name="details"
                ></router-outlet
                ><!---->
              </div>
            </div></mat-sidenav-content
          ><!----></mat-sidenav-container
        ><!---->
      </div></c3-app
    >
    <script
      src="login_files/runtime.ae3823deeeb3f491.js"
      type="module"
    ></script>
    <script
      src="login_files/polyfills.a152e2a187382597.js"
      type="module"
    ></script>
    <script src="login_files/main.977a608d24fe676d.js" type="module"></script>

    <span class="cleanslate TridactylStatusIndicator TridactylModenormal"
      >normal</span
    >
    <div
      class="cdk-live-announcer-element cdk-visually-hidden"
      aria-atomic="true"
      aria-live="polite"
      id="cdk-live-announcer-0"
    ></div>
    <div
      class="cdk-overlay-container c3-typography-condensed c3-layout-default"
    ></div>
    <div
      style="visibility: hidden"
      class="cdk-describedby-message-container cdk-visually-hidden"
    >
      <div id="cdk-describedby-message-ng-1-2" role="tooltip">
        Have a question? Need assistance? Want to offer a suggestion?
      </div>
    </div>
  </body>
</html>
