"""C3Reservations-specific data models."""

from typing import Optional

from pydantic import BaseModel

from integrations.scheduling.models import (
    SchedulingBaseRequest,
    SchedulingActionType,
    SchedulingPlatform,
    Credentials,
)


class C3ReservationsAppointmentData(BaseModel):
    """C3Reservations appointment data."""

    appointmentId: Optional[str] = ""
    appointmentTime: str
    carrierId: str
    dock: Optional[str] = ""
    duration: int
    loadId: Optional[str] = ""
    notes: Optional[str] = ""
    status: Optional[str] = ""


class C3ReservationsLoginRequest(SchedulingBaseRequest):
    """C3Reservations login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    credentials: Credentials


class C3ReservationsGetLoadTypesRequest(SchedulingBaseRequest):
    """C3Reservations-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class C3ReservationsGetOpenSlotsRequest(SchedulingBaseRequest):
    """C3Reservations-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class C3ReservationsGetWarehouseRequest(SchedulingBaseRequest):
    """C3Reservations-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class C3ReservationsCancelAppointmentRequest(SchedulingBaseRequest):
    """C3Reservations-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    appointmentId: str
    reason: Optional[str] = ""


class C3ReservationsGetAppointmentRequest(SchedulingBaseRequest):
    """C3Reservations-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    appointment: C3ReservationsAppointmentData


class C3ReservationsMakeAppointmentRequest(SchedulingBaseRequest):
    """C3Reservations-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    appointment: C3ReservationsAppointmentData


class C3ReservationsUpdateAppointmentRequest(SchedulingBaseRequest):
    """C3Reservations-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.C3RESERVATIONS

    appointment: C3ReservationsAppointmentData
    appointmentId: str
