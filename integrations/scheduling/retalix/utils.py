"""Retalix utilities."""

from typing import Optional

from selenium.webdriver import <PERSON><PERSON>hai<PERSON>
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement

from integrations.scheduling.retalix.models import RetalixWarehouseDetails
from utils.logging import logger
from utils.selenium import wait_for_element


def validate_po(
    driver: WebDriver, pro_id: str, warehouse: RetalixWarehouseDetails
) -> Optional[WebElement]:
    """Verify that the PO number is valid.

    Args:
        driver: WebDriver instance
        pro_id: PO number
        warehouse: Warehouse details

    Returns:
        WebElement if PO is valid, None otherwise
    """
    menu = wait_for_element(
        driver, (By.XPATH, "//span[contains(text(), 'Web Scheduling')]")
    )
    ActionChains(driver).move_to_element(menu).perform()

    appointments_link = wait_for_element(
        driver,
        (By.XPATH, "//li[contains(text(), 'Request An Appointment')]"),
    )
    appointments_link.click()

    from selenium.webdriver.support.ui import Select

    dropdown = Select(driver.find_element(By.ID, "Customers"))
    for option in dropdown.options:
        if warehouse.name.strip().lower() in option.text.strip().lower():
            dropdown.select_by_visible_text(option.text)
            break
    else:
        logger.error(f"No matching warehouse found for: {warehouse.name}")
        raise Exception(f"No matching warehouse found for: {warehouse.name}")

    po_input = driver.find_element(By.ID, "PONums")
    po_input.clear()
    po_input.send_keys(pro_id)

    add_button = driver.find_element(By.ID, "AddPOs")
    add_button.click()

    po_valid = wait_for_element(
        driver,
        (By.XPATH, f"//span[contains(@id, 'OrderNum') and text()='{pro_id}']"),
    )

    return po_valid


def traverse_detail_page(
    driver: WebDriver, warehouse: RetalixWarehouseDetails
) -> None:
    """Traverse the detail page to select the appropriate stop type.

    Args:
        driver: WebDriver instance
        warehouse: Warehouse details
    """
    next_button = driver.find_element(By.ID, "check")
    next_button.click()

    wait_for_element(driver, (By.ID, "CustomerName"))
    wait_for_element(driver, (By.ID, "LiveUnload"))

    stop_type = warehouse.stopType.lower()
    if stop_type == "pickup":
        live_radio = driver.find_element(By.ID, "LiveUnload")
        if live_radio.is_enabled():
            live_radio.click()
        else:
            raise RuntimeError(
                "Live Unload option is currently disabled. Cannot proceed with pickup."
            )
    elif stop_type == "dropoff":
        drop_radio = driver.find_element(By.ID, "Drop")
        if drop_radio.is_enabled():
            drop_radio.click()
        else:
            raise RuntimeError(
                "Drop option is currently disabled. Cannot proceed with dropoff."
            )
    else:
        raise ValueError(f"Unexpected stopType: {stop_type}")


def can_fetch_open_slots(driver: WebDriver) -> (bool, str):
    """Check if open slots can be fetched.

    Args:
        driver: WebDriver instance

    Returns:
        Tuple of bool and str. Bool indicates if open slots can be fetched. Str is the reason if False.
    """
    reserve_btn = driver.find_element(By.ID, "Reserve")
    manual_btn = driver.find_element(By.ID, "Manual")

    if not reserve_btn.is_enabled() and manual_btn.is_enabled():
        logger.info(
            "No open appointment slots available. Please request manually in your preferred time range."
        )
        return False, "Appointment is an request based appointment."

    elif reserve_btn.is_enabled():
        logger.info("Open slots available. Proceeding to fetch...")
        return True, ""

    elif not reserve_btn.is_enabled() and not manual_btn.is_enabled():
        logger.info(
            "Appointment is already being processed. Waiting for confirmation..."
        )
        return (
            False,
            "Appointment is already being processed. Please try again later.",
        )

    else:
        raise RuntimeError(
            "Unexpected button state. Please check DOM structure."
        )
