"""Retalix-specific data models."""

from datetime import datetime, timedelta, timezone
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, model_validator

from integrations.scheduling.models import (
    SchedulingBaseRequest,
    SchedulingActionType,
    SchedulingPlatform,
    Credentials,
    WarehouseDetails,
)
from models.base import BaseResponse


class RetalixWarehouseDetails(WarehouseDetails):
    """Retalix-specific warehouse details."""

    timezone: Optional[str] = ""
    addressLine1: Optional[str] = ""
    addressLine2: Optional[str] = ""


class RetalixAppointmentData(BaseModel):
    """Retalix appointment data."""

    appointmentId: Optional[str] = ""
    scheduledTime: Optional[str] = ""
    duration: int
    notes: Optional[str] = ""
    status: Optional[str] = ""
    warehouse: Optional[RetalixWarehouseDetails] = None
    extended: Optional[Dict[str, Any]] = None
    requested_appointment_date: Optional[str] = ""
    requested_appointment_time: Optional[str] = ""

    @model_validator(mode="after")
    def validate_appointment(cls, v):
        """Ensure valid date/time formats and required fields."""

        has_scheduled = bool(v.scheduledTime)
        has_requested = bool(
            v.requested_appointment_date and v.requested_appointment_time
        )

        if not has_scheduled and not has_requested:
            raise ValueError(
                "Either 'scheduledTime' or both 'requested_appointment_date' and 'requested_appointment_time' must be provided."
            )

        if v.scheduledTime:
            try:
                datetime.strptime(v.scheduledTime, "%Y-%m-%dT%H:%M:%S.%fZ")
            except ValueError:
                raise ValueError(
                    "Invalid 'scheduledTime' format. Expected: YYYY-MM-DDTHH:MM:SS.sssZ"
                )

        if v.requested_appointment_date:
            try:
                datetime.strptime(v.requested_appointment_date, "%m/%d/%Y")
            except ValueError:
                raise ValueError(
                    "Invalid 'requested_appointment_date' format. Expected: MM/DD/YYYY"
                )

        if v.requested_appointment_time:
            valid_times = {"Anytime", "Before Noon", "Noon - 6pm", "After 6pm"}
            if v.requested_appointment_time not in valid_times:
                raise ValueError(
                    f"Invalid 'requested_appointment_time'. Must be one of: {', '.join(valid_times)}"
                )

        return v


class RetalixGetOpenSlotsAppointmentData(BaseModel):
    """Retalix open slots appointment data."""

    notes: Optional[str] = ""
    status: Optional[str] = ""
    warehouse: Optional[RetalixWarehouseDetails] = None


class RetalixLoginRequest(SchedulingBaseRequest):
    """Retalix login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    credentials: Credentials


class RetalixGetLoadTypesRequest(SchedulingBaseRequest):
    """Retalix-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class RetalixGetOpenSlotsRequest(SchedulingBaseRequest):
    """Retalix-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    endDate: str
    proId: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    warehouse: Optional[RetalixWarehouseDetails] = None
    startDate: str

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that proId is provided."""
        if not self.proId:
            raise ValueError("proId must be provided")
        return self

    @model_validator(mode="after")
    def validate_warehouse_name(self):
        """Validate that name is provided."""
        if not self.warehouse.name:
            raise ValueError("Warehouse name must be provided")
        return self

    @model_validator(mode="after")
    def validate_dates(self):
        """Validate date fields and set defaults if not provided."""
        now = datetime.now()

        # Set default range if both missing
        if not self.startDate and not self.endDate:
            self.startDate = now.strftime("%Y-%m-%d")
            self.endDate = (now + timedelta(days=7)).strftime("%Y-%m-%d")
            return self

        # Require both or none
        if bool(self.startDate) != bool(self.endDate):
            raise ValueError(
                "Both startDate and endDate must be provided together"
            )

        try:
            start_date = datetime.strptime(self.startDate, "%Y-%m-%d")
            end_date = datetime.strptime(self.endDate, "%Y-%m-%d")

            if start_date < now.replace(
                hour=0, minute=0, second=0, microsecond=0
            ):
                raise ValueError("Start date cannot be in the past")

            if end_date < start_date:
                raise ValueError("End date must be on or after start date")

        except ValueError as e:
            if "does not match format" in str(
                e
            ) or "unconverted data remains" in str(e):
                raise ValueError("Date format must be YYYY-MM-DD")
            raise

        return self


class RetalixGetWarehouseRequest(SchedulingBaseRequest):
    """Retalix-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class RetalixCancelAppointmentRequest(SchedulingBaseRequest):
    """Retalix-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    proId: str
    warehouse: RetalixWarehouseDetails
    reason: Optional[str] = ""

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that proId is provided."""
        if not self.proId:
            raise ValueError("proId must be provided")
        return self

    @model_validator(mode="after")
    def validate_warehouse_name(self):
        """Validate that name is provided."""
        if not self.warehouse.name:
            raise ValueError("Warehouse name must be provided")
        return self


class RetalixGetAppointmentRequest(SchedulingBaseRequest):
    """Retalix-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    proId: str
    warehouse: RetalixWarehouseDetails

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that proId is provided."""
        if not self.proId:
            raise ValueError("proId must be provided")
        return self

    @model_validator(mode="after")
    def validate_warehouse_name(self):
        """Validate that name is provided."""
        if not self.warehouse.name:
            raise ValueError("Warehouse name must be provided")
        return self


class RetalixMakeAppointmentRequest(SchedulingBaseRequest):
    """Retalix-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    appointments: List[RetalixAppointmentData]


class RetalixUpdateAppointmentRequest(SchedulingBaseRequest):
    """Retalix-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    appointments: List[RetalixAppointmentData]
    appointmentId: str


class RetalixScheduleAppointmentRequest(SchedulingBaseRequest):
    """Retalix-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETALIX

    appointments: List[RetalixAppointmentData]

    @model_validator(mode="after")
    def validate_appointments(self):
        for idx, appointment in enumerate(self.appointments):
            if not appointment.pro_id:
                raise ValueError(
                    f"Appointment at index {idx} is missing 'pro_id'"
                )
            if not (appointment.warehouse and appointment.warehouse.name):
                raise ValueError(
                    f"Appointment at index {idx} is missing 'warehouse.name'"
                )
        return self


class RetalixGetOpenSlotsResponse(BaseResponse):
    """Retalix-specific GET open slots response."""

    appointments: Optional[List[RetalixGetOpenSlotsAppointmentData]] = None


class RetalixValidateAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.VALIDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    proId: str
    warehouse: Optional[RetalixWarehouseDetails] = None

    @model_validator(mode="after")
    def validate_warehouse_name(self):
        """Validate that name is provided."""
        if not self.warehouse.name:
            raise ValueError("Warehouse name must be provided")
        return self


class RetalixMakeAppointmentResponse(BaseResponse):
    """Response after updating an appointment."""

    appointments: Optional[List[RetalixAppointmentData]] = None


class RetalixUpdateAppointmentResponse(BaseResponse):
    """Response after updating an appointment."""

    appointments: Optional[List[RetalixAppointmentData]] = None


class RetalixScheduleAppointmentResponse(BaseResponse):
    """Response after scheduling an appointment."""

    appointments: Optional[List[RetalixAppointmentData]] = None


class RetalixGetAppointmentResponse(BaseResponse):
    """Response after getting an appointment."""

    appointments: Optional[List[RetalixAppointmentData]] = None
