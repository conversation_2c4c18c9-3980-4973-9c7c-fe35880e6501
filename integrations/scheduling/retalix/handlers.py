"""Retalix scheduling integration handlers."""

from . import api, scraper


SELENIUM_HANDLERS = {
    "GetWarehouse": scraper.get_warehouse,
    "GetOpenSlots": scraper.get_open_slots,
    "GetLoadTypes": scraper.get_load_types,
    "GetAppointment": scraper.get_appointment,
    "MakeAppointment": scraper.make_appointment,
    "UpdateAppointment": scraper.update_appointment,
    "CancelAppointment": scraper.cancel_appointment,
}

API_HANDLERS = {
    "GetWarehouse": api.not_implemented,
    "GetOpenSlots": api.not_implemented,
    "ValidateAppointment": api.not_implemented,
    "GetLoadTypes": api.not_implemented,
    "GetAppointment": api.not_implemented,
    "MakeAppointment": api.not_implemented,
    "UpdateAppointment": api.not_implemented,
    "CancelAppointment": api.not_implemented,
}
