"""Retalix Selenium scheduling implementation."""

from datetime import datetime
from typing import Optional, Type, Tuple
import functools

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from integrations.scheduling.retalix.utils import (
    validate_po,
    traverse_detail_page,
    can_fetch_open_slots,
)
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

from models.base import BaseResponse


from integrations.scheduling.models import (
    Credentials,
    Appointment,
    LoginResponse,
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    CancelAppointmentResponse,
    ValidateAppointmentResponse,
)


from integrations.scheduling.retalix.models import (
    RetalixLoginRequest,
    RetalixGetWarehouseRequest,
    RetalixGetOpenSlotsRequest,
    RetalixGetLoadTypesRequest,
    RetalixCancelAppointmentRequest,
    RetalixGetAppointmentRequest,
    RetalixMakeAppointmentRequest,
    RetalixUpdateAppointmentRequest,
    RetalixGetOpenSlotsRequest,
    RetalixGetOpenSlotsResponse,
    RetalixValidateAppointmentRequest,
    RetalixGetOpenSlotsAppointmentData,
    RetalixScheduleAppointmentRequest,
    RetalixAppointmentData,
    RetalixMakeAppointmentResponse,
    RetalixUpdateAppointmentResponse,
    RetalixScheduleAppointmentResponse,
    RetalixGetAppointmentResponse,
)

# retalix element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "Username"),
    "password_field": (By.ID, "Pass"),
    "login_button": (
        By.XPATH,
        "//div[@id='LoginDiv']//button[text()='Sign In']",
    ),
    "login_error": (
        By.ID,
        "lblInfo",
    ),
    "logged_in_indicator": (
        By.XPATH,
        "//h1[normalize-space()='Power Traffic']",
    ),
}


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting Retalix login process")

        wait_for_element(driver, LOCATORS["username_field"])
        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        logger.debug(f"Current Retalix URL: {driver.current_url}")
        logger.debug(f"retalix Page Source:\n{driver.page_source}\n")

        try:
            if is_element_present(driver, LOCATORS["login_error"], timeout=2):
                error_msg = get_element_text(driver, LOCATORS["login_error"])
                logger.error(f"Retalix Login failed: {error_msg}")
                return False, error_msg
        except Exception:
            # Silently continue if no error message found
            pass

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"Retalix Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("retalix", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            driver.get(PLATFORM_URLS["retalix"])

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["retalix"])

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(PLATFORM_URLS["retalix"])

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("retalix", credentials.username, new_cookies)
            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: RetalixLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to Retalix.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: RetalixGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from Retalix.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(ValidateAppointmentResponse)
def validate_appointment(
    request: RetalixValidateAppointmentRequest, driver: WebDriver
) -> BaseResponse:
    """Validate an appointment in Retalix.

    Args:
        request: Validate appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Validate appointment response
    """
    try:
        pro_id = request.proId
        warehouse = request.warehouse

        po_valid = validate_po(driver, pro_id, warehouse)

        if not po_valid:
            raise Exception(f"PO number {pro_id} is not valid.")

        return BaseResponse(
            success=True,
            message="PO number is valid",
        )
    except Exception as e:
        return BaseResponse(
            success=False,
            message="Failed to validate appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetOpenSlotsResponse)
def get_open_slots(
    request: RetalixGetOpenSlotsRequest, driver: WebDriver
) -> RetalixGetOpenSlotsResponse:
    """Get open appointment slots from Retalix.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        pro_id = request.proId
        warehouse = request.warehouse

        is_po_valid = validate_po(driver, pro_id, warehouse)
        if not is_po_valid:
            raise Exception(f"PO number {pro_id} is not valid.")

        traverse_detail_page(driver, warehouse)

        slots_available, reason = can_fetch_open_slots(driver)
        if not slots_available:
            return RetalixGetOpenSlotsResponse(
                success=False,
                message="Failed to fetch open slots",
                errors=[reason],
                appointments=[
                    RetalixGetOpenSlotsAppointmentData(
                        notes=reason, status="UNAVAILABLE", warehouse=warehouse
                    )
                ],
            )

        # TODO: Implement open slots fetching

        return RetalixGetOpenSlotsResponse(
            success=False,
            message="Not implemented yet",
            errors=["Open slots fetching not implemented"],
        )
    except Exception as e:
        return RetalixGetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: RetalixGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from Retalix.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: RetalixCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in Retalix.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        pro_id = request.proId
        warehouse = request.warehouse
        reason = request.reason

        is_po_valid = validate_po(driver, pro_id, warehouse)
        if not is_po_valid:
            raise Exception(f"PO number {pro_id} is not valid.")

        traverse_detail_page(driver, warehouse)

        cancel_button = driver.find_element(By.ID, "CancelAppt")
        if cancel_button.is_displayed() and cancel_button.is_enabled():
            cancel_button.click()

            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = driver.switch_to.alert
            alert.accept()

            wait_for_element(driver, (By.ID, "ManualText"))

            status_text = "Cancellation request added into the queue."
            try:
                rows = driver.find_elements(
                    By.CSS_SELECTOR, "#OrderList tr.bodySpacingText"
                )
                status_text = rows[1].find_elements(By.TAG_NAME, "td")[1].text
            except Exception as ex:
                pass

            return CancelAppointmentResponse(
                success=True,
                message=status_text,
                errors=[],
                proId=pro_id,
                warehouse=warehouse,
            )

        return CancelAppointmentResponse(
            success=False,
            message="Cancel button is not interactable.",
            errors=["Cancel button is not interactable."],
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(RetalixGetAppointmentResponse)
def get_appointment(
    request: RetalixGetAppointmentRequest, driver: WebDriver
) -> RetalixGetAppointmentResponse:
    """Get an appointment from Retalix.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        appointments = request.appointments
        all_appointments = []

        for appointment in appointments:
            pro_id = appointment.proId
            warehouse = appointment.warehouse

            is_po_valid = validate_po(driver, pro_id, warehouse)
            if not is_po_valid:
                raise Exception(f"PO number {pro_id} is not valid.")

            traverse_detail_page(driver, warehouse)

            try:
                panel = driver.find_element(By.ID, "panel1")
                # Scheduled appointment exists, extract info
                start_time = panel.find_element(
                    By.ID, "lblApptTime"
                ).text.strip()
                duration_text = panel.find_element(
                    By.ID, "lblDuration"
                ).text.strip()
                confirmation = panel.find_element(
                    By.ID, "lblConfirmnum"
                ).text.strip()

                dt_obj = datetime.strptime(start_time, "%m/%d/%Y %I:%M %p")
                iso_start_time = dt_obj.isoformat()

                # Extract number of minutes
                duration = int(duration_text.split()[0])

                appointment = RetalixAppointmentData(
                    appointmentId=confirmation,
                    scheduledTime=iso_start_time,
                    duration=duration,
                    status="SCHEDULED",
                    warehouse=warehouse,
                )

                all_appointments.append(appointment)

            except Exception as ex:
                # Appointment not scheduled
                appointment = RetalixAppointmentData(
                    appointmentId="",
                    scheduledTime="",
                    duration=0,
                    status="UNSCHEDULED",
                    warehouse=warehouse,
                )
                all_appointments.append(appointment)
                continue

        return RetalixGetAppointmentResponse(
            success=True,
            message="Successfully retrieved appointment",
            appointments=all_appointments,
        )

    except Exception as e:
        return RetalixGetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(RetalixMakeAppointmentResponse)
def make_appointment(
    request: RetalixMakeAppointmentRequest, driver: WebDriver
) -> RetalixMakeAppointmentResponse:
    """Create an appointment in Retalix.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        appointments = request.appointments
        all_appointments = []
        all_errors = []

        for appointment in appointments:
            failed_appointment = RetalixAppointmentData(
                appointmentId="",
                scheduledTime="",
                duration=0,
                status="UNSCHEDULED",
                warehouse=appointment.warehouse,
            )

            pro_id = appointment.proId
            warehouse = appointment.warehouse
            scheduled_time = appointment.scheduledTime

            try:
                driver.get(PLATFORM_URLS["retalix"])
                wait_for_page_load(driver)

                is_po_valid = validate_po(driver, pro_id, warehouse)
                if not is_po_valid:
                    raise Exception(f"PO number {pro_id} is not valid.")

                traverse_detail_page(driver, warehouse)

                slots_available, reason = can_fetch_open_slots(driver)
                if not slots_available:
                    failed_appointment.extended["reason"] = reason
                    failed_appointment.notes = reason
                    all_errors.append(reason)
                    all_appointments.append(failed_appointment)
                    continue

                # TODO: Implement appointment making

            except Exception as e:
                failed_appointment.notes = str(e)
                all_errors.append(str(e))
                all_appointments.append(failed_appointment)
                continue

        if not all_errors:
            return RetalixMakeAppointmentResponse(
                success=True,
                message="Successfully made appointments",
                appointments=all_appointments,
            )

        return RetalixMakeAppointmentResponse(
            success=False,
            message="All appointments were not successfully made.",
            errors=all_errors,
            appointments=all_appointments,
        )
    except Exception as e:
        return RetalixMakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(RetalixUpdateAppointmentResponse)
def update_appointment(
    request: RetalixUpdateAppointmentRequest, driver: WebDriver
) -> RetalixUpdateAppointmentResponse:
    """Update an appointment in Retalix.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        appointments = request.appointments
        all_appointments = []
        all_errors = []

        for appointment in appointments:
            failed_appointment = RetalixAppointmentData(
                appointmentId="",
                scheduledTime="",
                duration=0,
                status="UNSCHEDULED",
                warehouse=appointment.warehouse,
            )

            pro_id = appointment.proId
            warehouse = appointment.warehouse
            scheduled_time = appointment.scheduledTime

            try:
                driver.get(PLATFORM_URLS["retalix"])
                wait_for_page_load(driver)

                is_po_valid = validate_po(driver, pro_id, warehouse)
                if not is_po_valid:
                    raise Exception(f"PO number {pro_id} is not valid.")

                traverse_detail_page(driver, warehouse)

                slots_available, reason = can_fetch_open_slots(driver)
                if not slots_available:
                    failed_appointment.extended["reason"] = reason
                    failed_appointment.notes = reason
                    all_errors.append(reason)
                    all_appointments.append(failed_appointment)
                    continue

                # TODO: Implement appointment update

            except Exception as e:
                failed_appointment.notes = str(e)
                all_errors.append(str(e))
                all_appointments.append(failed_appointment)
                continue

        if not all_errors:
            return RetalixUpdateAppointmentResponse(
                success=True,
                message="Successfully made appointments",
                appointments=all_appointments,
            )
        return RetalixUpdateAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment updating not implemented"],
        )
    except Exception as e:
        return RetalixUpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(RetalixScheduleAppointmentResponse)
def schedule_appointment(
    request: RetalixScheduleAppointmentRequest, driver: WebDriver
) -> RetalixScheduleAppointmentResponse:
    """Schedule an appointment in Retalix.

    Args:
        request: Schedule appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Schedule appointment response
    """
    try:
        appointments = request.appointments
        all_appointments = []
        all_errors = []

        for appointment in appointments:
            failed_appointment = RetalixAppointmentData(
                appointmentId="",
                scheduledTime="",
                duration=0,
                status="UNSCHEDULED",
                warehouse=appointment.warehouse,
            )

            pro_id = appointment.proId
            warehouse = appointment.warehouse
            requested_appointment_date = appointment.requested_appointment_date
            requested_appointment_time = appointment.requested_appointment_time

            try:
                driver.get(PLATFORM_URLS["retalix"])
                wait_for_page_load(driver)

                is_po_valid = validate_po(driver, pro_id, warehouse)
                if not is_po_valid:
                    raise Exception(f"PO number {pro_id} is not valid.")

                traverse_detail_page(driver, warehouse)

                slots_available, reason = can_fetch_open_slots(driver)
                if slots_available:
                    failed_appointment.extended["reason"] = reason
                    failed_appointment.notes = reason
                    all_errors.append(reason)
                    all_appointments.append(failed_appointment)
                    continue

                # TODO: Implement appointment scheduling

            except Exception as e:
                failed_appointment.notes = str(e)
                all_errors.append(str(e))
                all_appointments.append(failed_appointment)
                continue

        if not all_errors:
            return RetalixScheduleAppointmentResponse(
                success=True,
                message="Successfully scheduled appointments",
                appointments=all_appointments,
            )
        return RetalixScheduleAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment updating not implemented"],
        )
    except Exception as e:
        return RetalixScheduleAppointmentResponse(
            success=False,
            message="Failed to schedule appointment",
            errors=[str(e)],
        )
