<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
    <HEAD>
        <title>NCR Web Scheduling - Change Orders</title>
        <meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
        <meta content="C#" name="CODE_LANGUAGE">
        <meta content="JavaScript" name="vs_defaultClientScript">
        <meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
        <LINK href="site_style.css" type="text/css" rel="stylesheet">
        <script language="javascript">
            var popUp;
            function OpenCalendar(idname) {
            	popUp = window.open('Calendar.aspx?formname=' + document.forms[0].name +
            		'&id=' + idname + '&selected=' + document.forms[0].elements[idname].value,
            		'popupcal',
            		'width=225,height=165,left=450,top=400');
            }

            function SetDate(formName, id, newDate) {
            	eval('var theform = document.' + formName + ';');
            	popUp.close();
            	theform.elements[id].value = newDate;
            }
        </script>
        <script language="javascript">
            function DisplayEntryForLoad()
            {
                var rddrop = document.getElementById("Drop");
                if (rddrop.checked == true) {
                    document.getElementById("liveReserve").style.display = "none";
                    document.getElementById("liveRequest").style.display = "none";
                    document.getElementById("or").style.display = "none";
                    document.getElementById("dropReserve").style.display = "";
                    document.getElementById("lblExpDate").disabled = false;
                    document.getElementById("ArrivalDate").disabled = false;
                    document.getElementById("calendar1").disabled = false;
                    document.getElementById("lblTrailer").disabled = false;
                    document.getElementById("Trailer").disabled = false;
                    document.getElementById("lblExpTime").disabled = false;
                    document.getElementById("ArrivalHour").disabled = false;
                    document.getElementById("lblColon").disabled = false;
                    document.getElementById("ArrivalMin").disabled = false;
                    document.getElementById("AMPM").disabled = false;
                    document.getElementById("lbDropHeader").disabled = false;
                    document.getElementById("DropText").disabled = false;
                    document.getElementById("ReserveDrop").disabled = false;
                    document.getElementById("ManualDrop").disabled = false;
                    document.getElementById("CancelDrop").disabled = false;
                }
                else
                {
                    document.getElementById("liveReserve").style.display = "";
                    document.getElementById("liveRequest").style.display = "";
                    document.getElementById("or").style.display = "";
                    document.getElementById("dropReserve").style.display = "none";
                    document.getElementById("dropReserve").style.top = document.getElementById("liveReserve").style.top;
                    document.getElementById("lbDropHeader").disabled = true;
                    document.getElementById("DropText").disabled = true;
                    document.getElementById("ReserveDrop").disabled = true;
                    document.getElementById("ManualDrop").disabled = true;
                    document.getElementById("CancelDrop").disabled = true;
                    document.getElementById("lblExpDate").disabled = true;
                    document.getElementById("ArrivalDate").disabled = true;
                    document.getElementById("calendar1").disabled = true;
                    document.getElementById("lblTrailer").disabled = true;
                    document.getElementById("Trailer").disabled = true;
                    document.getElementById("lblExpTime").disabled = true;
                    document.getElementById("ArrivalHour").disabled = true;
                    document.getElementById("lblColon").disabled = true;
                    document.getElementById("ArrivalMin").disabled = true;
                    document.getElementById("AMPM").disabled = true;
                    document.getElementById("ArrivalDate").innerText = "";
                    document.getElementById("ArrivalHour").selectedIndex = 0;
                    document.getElementById("ArrivalMin").selectedIndex = 0;
                    document.getElementById("AMPM").selectedIndex = 0;
                    document.getElementById("Trailer").innerText = "";
                    document.getElementById("lbAppointmentDates").disabled = false;
                    if (document.getElementById("Reserve").disabled == false)
                    {
                        if (document.getElementById("Appointments").innerHTML == "")
                        {
                            document.getElementById("Reserve").disabled = true;
                            document.getElementById("Cancel").disabled = true;
                        }
                        else
                        {
                            document.getElementById("AppointmentDate").disabled = false;
                            document.getElementById("Appointments").disabled = false;
                        }
                    }
                    document.getElementById("lbRequestedDate").disabled = false;
                    document.getElementById("DeliveryDate").disabled = false;
                    document.getElementById("RequestedDeliveryTime").disabled = false;
                    document.getElementById("lbRequestTimePreference").disabled = false;
                }
            }
        </script>
        <script language="javascript">
            var rmsg;
            function ValidateDeliveryDate() {
                if (document.PageForm.DeliveryDate.value.length > 0) {
                          rmsg = isDate(document.PageForm.DeliveryDate.value,'Delivery Date: ');
                          if (rmsg != "dateisvalid")
                          {
                              alert(rmsg);
                              return;
                          }
                          else
                          {
                              OpenCalendar('DeliveryDate');
                          }
                      }
                      else {
                          OpenCalendar('DeliveryDate');
                      }
            }

            function ValidateArrivalDate() {
                if (document.PageForm.ArrivalDate.value.length > 0) {
                          rmsg = isDate(document.PageForm.ArrivalDate.value,'Expected Arrival Date: ');
                          if (rmsg != "dateisvalid")
                          {
                              alert(rmsg);
                              return;
                          }
                          else
                          {
                              OpenCalendar('ArrivalDate');
                          }
                      }
                      else {
                          OpenCalendar('ArrivalDate');
                      }
            }
        </script>
        <script language="javascript">
            function textboxMultilineMaxNumber(txt,maxLen){
            try {
                if(txt.value.length > (maxLen-1))return false;
                }
                catch(e){
                }
            }
        </script>
        <script>
            function toggleForm(enabled) {
              for (var x=0;x<document.forms[0].length;x++) {
                document.forms[0].elements[x].disabled = !enabled;
              }
            }

            function handleKey(e) {
              if (e.keyCode == 13) { return false; }
            }

            function focusButton(e, useButton) {
              var bName = '';
              if (bName.length > 0) {
                if (document.PageForm.elements[''] != null) {
                  document.PageForm.elements[''].focus();
                }
              }
            }

            function handleFocus() {
              var focusName = "";

            var editCasesOrdinal = -1; // default the EditCasesOrdinal to not found
              for (var x=0;x<document.PageForm.elements.length;x++) {
                if (document.PageForm.elements[x].name.indexOf(':EditCases') > -1) {
               editCasesOrdinal = x; // assign the EditCasesOrdinal
             }
              }

              if (focusName.length == 0) {
                for (var x=0;x<document.PageForm.elements.length;x++) {
                  if (document.PageForm.elements[x].name.indexOf(':EditCases') > -1) {
                    document.PageForm.elements[x].focus();
                    document.PageForm.elements[x].select();
                  }
            if (editCasesOrdinal == -1) { // if the EditCases textbox is hidden (not found)
                    if (document.PageForm.elements[x].name.indexOf(':EditOrigin') > -1) { // if the EditOrigin textbox is visible (found)
                      document.PageForm.elements[x].focus();
                      document.PageForm.elements[x].select();
              }
                  }

                  if (document.PageForm.elements[x].name.indexOf(':btnSave') > -1) {
                    if (document.PageForm.elements[x].value == 'Save') {
                      document.PageForm.elements[x].tabOrder = 6;
                    }
                  }
                  if (document.PageForm.elements[x].name.indexOf(':btnCancel') > -1) {
                    if (document.PageForm.elements[x].value == 'Cancel') {
                      document.PageForm.elements[x].tabOrder = 7;
                    }
                  }
                }
              } else {
                document.PageForm[focusName].focus();
              }
            }
        </script>
        <!-- Date Validation functions -->
        <script>
            function y2k(number) { return (number < 1000) ? number + 1900 : number; }
            //Used to compare to dates and return whether the first is before, after, or the same
            function compareDates (value1, value2) {
            var date1, date2;
            var month1, month2;
            var year1, year2;

            var year1Length = value1.substring(value1.lastIndexOf ("/")+1, value1.length);
            if (year1Length.length == 2)
            {
             year1 = "20" + year1Length;
             year1 = parseInt(year1);
            }
            else
            {
             year1 = parseInt(year1Length);
            }

            var year2Length = value2.substring(value2.lastIndexOf ("/")+1, value2.length);
            if (year2Length.length == 2)
            {
             year2 = "20" + year2Length;
             year2 = parseInt(year2);
            }
            else
            {
             year2 = parseInt(year2Length);
            }

            if (value1.substring(0, 1) == 0) value1 = value1.substring(1, value1.length);
            month1 = parseInt(value1.substring (0, value1.indexOf ("/")));
            date1 = parseInt(value1.substring (value1.indexOf ("/")+1, value1.lastIndexOf ("/")));

            if (value2.substring(0, 1) == 0) value2 = value2.substring(1, value2.length);
            month2 = parseInt(value2.substring(0, value2.indexOf("/")));
            date2 = parseInt(value2.substring (value2.indexOf ("/")+1, value2.lastIndexOf ("/")));

            if (year1 > year2) return 1;
            else if (year1 < year2) return -1;
            else if (month1 > month2) return 1;
            else if (month1 < month2) return -1;
            else if (date1 > date2) return 1;
            else if (date1 < date2) return -1;
            else return 0;
            }
            /**
            * DHTML date validation script. Courtesy of SmartWebby.com (http://www.smartwebby.com/dhtml/)
            */
            // Declaring valid date character, minimum year and maximum year
            var dtCh= "/";
            var dtNow = new Date();
            var minYear = dtNow.getFullYear() - 2;
            var maxYear = dtNow.getFullYear() + 3;

            function isInteger(s){
            var i;
            for (i = 0; i < s.length; i++){
            // Check that current character is number.
            var c = s.charAt(i);
            if (((c < "0") || (c > "9"))) return false;
            }
            // All characters are numbers.
            return true;
            }

            function stripCharsInBag(s, bag){
            var i;
            var returnString = "";
            // Search through string's characters one by one.
            // If character is not in bag, append to returnString.
            for (i = 0; i < s.length; i++){
            var c = s.charAt(i);
            if (bag.indexOf(c) == -1) returnString += c;
            }
            return returnString;
            }

            function daysInFebruary (year){
            // February has 29 days in any year evenly divisible by four,
            // EXCEPT for centurial years which are not also divisible by 400.
            return (((year % 4 == 0) && ( (!(year % 100 == 0)) || (year % 400 == 0))) ? 29 : 28 );
            }
            function DaysArray(n) {
            for (var i = 1; i <= n; i++) {
            this[i] = 31
            if (i==4 || i==6 || i==9 || i==11) {this[i] = 30}
            if (i==2) {this[i] = 29}
            }
            return this
            }

            function isDate(dtStr,field){
            var daysInMonth = DaysArray(12)
            var pos1=dtStr.indexOf(dtCh)
            var pos2=dtStr.indexOf(dtCh,pos1+1)
            var strMonth=dtStr.substring(0,pos1)
            var strDay=dtStr.substring(pos1+1,pos2)
            var strYear=dtStr.substring(pos2+1)
            var retmsg = ''
            strYr=strYear
            if (strYr.length == 2)
            {
            strYear = "20" + strYear;
            strYr = "20" + strYr;
            }
            if (strDay.charAt(0)=="0" && strDay.length>1) strDay=strDay.substring(1)
            if (strMonth.charAt(0)=="0" && strMonth.length>1) strMonth=strMonth.substring(1)
            for (var i = 1; i <= 3; i++) {
            if (strYr.charAt(0)=="0" && strYr.length>1) strYr=strYr.substring(1)
            }
            month=parseInt(strMonth)
            day=parseInt(strDay)
            year=parseInt(strYr)
            if (pos1==-1 || pos2==-1){
            retmsg = "Invalid " + field + "The date format should be : mm/dd/yyyy\r\n";
            return retmsg
            }
            if (strMonth.length<1 || month<1 || month>12){
            retmsg = "Invalid " + field + "Please enter a valid month.\r\n";
            return retmsg
            }
            if (strDay.length<1 || day<1 || day>31 || (month==2 && day>daysInFebruary(year)) || day > daysInMonth[month]){
            retmsg = "Invalid " + field + "Please enter a valid day.\r\n";
            return retmsg
            }
            if (strYear.length != 4 || year==0 || year<minYear || year>maxYear){
            retmsg = "Invalid " + field + "Please enter a valid 4 digit year between "+minYear+" and "+maxYear +".\r\n";
            return retmsg
            }
            if (dtStr.indexOf(dtCh,pos2+1)!=-1 || isInteger(stripCharsInBag(dtStr, dtCh))==false){
            retmsg = "Invalid " + field + "Please enter a valid date.\r\n";
            return retmsg
            }
            return "dateisvalid"
            }

            function HTMLEncode(buf) {
               var ret;
               ret = escape(buf);
               ret = ret.replace(/\//g,"%2F");
               ret = ret.replace(/\?/g,"%3F");
               ret = ret.replace(/=/g,"%3D");
               ret = ret.replace(/&amp;/g,"%26");
               ret = ret.replace(/@/g,"%40");
               return ret;
             }

        </script>
        <script>
            function confirmSave() {
              if (false) {
                if (alert("You must save or cancel your changes for the current record before continuing.")) {
                  window.location = "createAppointment.aspx";
                }
              } else {
                if (document.getElementById("lblConfirmnum") != null)
                      window.location = "createAppointment.aspx?rs=1";
                else
                      window.location = "createAppointment.aspx";
              }
            }

            function confirmCancel() {
              if (false) {
                alert("You must save or cancel your changes for the current record before continuing.");
              }

              var agree=confirm("Are you sure you wish to cancel this appointment?");
              if (agree)
              {
                  document.RedirForm.ccApp.value = "true";
                  getData(true, 'ins');
              }
              else
              {
                  document.RedirForm.ccApp.value = "false";
                  return false;
              }
            }

            function confirmEditOther() {
              if (false) {
                if (alert("You must save or cancel your changes for the current record before continuing.")) {
                  return true;
                } else { return false; }
              }
            }

            function valForm(autoAppointment) {
              var valid = true;
              var msg = '';
              var cur = 2;
              var deliveryDate = 0;

            //Create a date
            var date = new Date();
            //Create a day in UTC time to represent today's date
            var todayDate = new Date(Date.UTC(y2k(date.getYear()),date.getMonth(),date.getDate(),date.getHours(),date.getMinutes(),date.getSeconds()));

            //Formats the date into dd/mm/yyyy
            var Month = (todayDate.getMonth() + 1) + "/";
            var Day = todayDate.getDate() + "/";
            var Year = todayDate.getFullYear();
            var today =  Month + Day + Year;

              if (false) {
                if (!alert("You must save or cancel your changes for the current record before continuing.")) {
                  return false;
                }
              }

              if (false) {
                while (getRawObject('OrderList__ctl' + cur + '_OrderNum') != null) {
                  if (getRawObject('OrderList__ctl' + cur + '_ErrorCode') != null) {
                    if (parseInt(getRawObject('OrderList__ctl' + cur + '_ErrorCode').value) > 0) {
                      if (getRawObject('OrderList__ctl' + cur + '_Origin') != null) {
                        if (getRawObject('OrderList__ctl' + cur + '_Origin').innerHTML.length == 0) {
                          valid = false;
                          msg += 'Click the [Edit] button next to order ' + getRawObject('OrderList__ctl' + cur + '_OrderNum').innerHTML + ' and enter origin.\r\n';
                        }
                      }
                      if (getRawObject('OrderList:_ctl' + cur + ':EditOrigin') != null) {
                        if (getRawObject('OrderList:_ctl' + cur + ':EditOrigin').value.length == 0) {
                          valid = false;
                          msg += 'Click the [Edit] button next to order ' + getRawObject('OrderList__ctl' + cur + '_OrderNum').innerHTML + ' and enter origin.\r\n';
                        }
                      }
                    }
                  }
                  cur++;
                }
              }

              cur = 2;
              if (false) {
                while (getRawObject('OrderList__ctl' + cur + '_OrderNum') != null) {
                  if (getRawObject('OrderList__ctl' + cur + '_ErrorCode') != null) {
                    if (parseInt(getRawObject('OrderList__ctl' + cur + '_ErrorCode').value) > 0) {
                      if (getRawObject('OrderList__ctl' + cur + '_PalletType') != null) {
                        if (getRawObject('OrderList__ctl' + cur + '_PalletType').innerHTML == "Not Assigned") {
                          valid = false;
                          msg += 'Click the [Edit] button next to order ' + getRawObject('OrderList__ctl' + cur + '_OrderNum').innerHTML + ' and select a pallet type.\r\n';
                        }
                      }
                      if (getRawObject('OrderList:_ctl' + cur + ':EditPalletType') != null) {
                        if (getRawObject('OrderList:_ctl' + cur + ':EditPalletType').value.length == 0) {
                          valid = false;
                          msg += 'Click the [Edit] button next to order ' + getRawObject('OrderList__ctl' + cur + '_OrderNum').innerHTML + ' and select a pallet type.\r\n';
                        }
                      }
                    }
                  }
                  cur++;
                }
              }

              if (!valid) {
                  alert(msg);
                  return valid;
              }

              if(document.getElementById("Drop") != null && document.PageForm.Drop.checked == true)
              {
                if (document.PageForm.ArrivalDate.value.length > 0)
                {
                  rmsg = isDate(document.PageForm.ArrivalDate.value,'Expected Arrival Date: ');
                  if (rmsg != "dateisvalid")
                  {
                    valid = false;
                    msg += 'The expected arrival date is invalid.\r\n';
                  }
                }
                else {
                  valid = false;
                  msg += 'Please enter the expected arrival date.\r\n';
                }
              }
              else if (autoAppointment) {
                if (getRawObject('Appointments').selectedIndex == -1) {
                  valid = false;
                  msg += 'Please select an appointment time from the list.\r\n';
                }
              }
              else {
                if (document.PageForm.DeliveryDate.value.length == 0) {
                  valid = false;
                  msg += "Please select a requested delivery date.\r\n";
                }
                else {
                    msg += isDate(document.PageForm.DeliveryDate.value,'Delivery Date: ');
                    if (msg != "dateisvalid") {
                        valid = false;
                    }
              else if (compareDates(document.PageForm.DeliveryDate.value, today) < 0 ) {
               msg = "Requested Delivery Date: Selected date cannot be before today.\r\n";
               valid = false;
              }
              else {
                        deliveryDate = document.PageForm.DeliveryDate.value;
                    }
                 }
              }

              if (!true) {
                valid = false;
                if (msg == "dateisvalid") {
                  msg = "";
                }
                msg += " " + 'Please revise this load to contain 1 or more valid orders.\r\n';
              }
              if (!valid) alert(msg);
              return valid;

            }

            function doReserve() {
              if (document.getElementById("Drop") != null) {
                  if ((document.PageForm.Drop.checked == true) && (document.getElementById("ArrivalDate").value == "")) {
                      alert("Expected Arrival Date is required.");
                      return false;
                  }
              }

              if (document.getElementById("LumperUnassigned") != null) {
                if (document.PageForm.LumperUnassigned.checked == true)
                {
                  alert("Please check if lumper is requested.");
                  return false;
                }
              }

              if (valForm(true)) getData(true, 'ins');
            }

            function doManual() {
              if ((document.PageForm.Comments.value == "") && (document.getElementById("lblConfirmnum") != null)) {
                  alert("Please enter the change request comments!");
                  return false;
              }

              if (document.getElementById("LumperUnassigned") != null) {
                if (document.PageForm.LumperUnassigned.checked == true)
                {
                  alert("Please check if lumper is requested.");
                  return false;
                }
              }

              if (document.getElementById("Drop") != null) {
                  if ((document.PageForm.Drop.checked == true) && (document.getElementById("ArrivalDate").value == "")) {
                      alert("Expected Arrival Date is required.");
                      return false;
                  }
              }
              if (valForm(false))
              {
                getManualData(true, 'ins');

               /* Note: Comments_TextChanged fires only when asp:buttons are clicked;
                  Since this is an <input> button, we *pass* the comments to manualAppointment.aspx
                  via document.manualForm.AppComments1 (an embedded, invisible <form>/<input>) */

             //  document.manualForm.AppComments1.value = document.PageForm.Comments.value;
             //  document.manualForm.AppDate.value = document.PageForm.DeliveryDate.value;
             // if (document.getElementById("LumperYes") != null) {
             //     if (document.PageForm.LumperYes.checked == true) document.manualForm.AppLumper.value = 1;
             //     else document.manualForm.AppLumper.value = 0;
             //  }
             //  else {
             //     document.manualForm.AppLumper.value = -1;
             //  }

             //  if (document.getElementById("Drop") != null) {
             //     if (document.PageForm.Drop.checked == true) {
             //         document.manualForm.AppDeliveryType.value = 1;
             //         document.manualForm.AppExpArrivalDate.value = document.getElementById("ArrivalDate").value;
             //         document.manualForm.AppExpArrivalTime.value = document.getElementById("ArrivalHour").value + ":" + document.getElementById("ArrivalMin").value + " " + document.getElementById("AMPM").value;
             //         document.manualForm.AppTrailer.value = document.getElementById("Trailer").value;
             //     }
             //     else
             //         document.manualForm.AppDeliveryType.value = 0;
             //  }
             //  else {
             //     document.manualForm.AppDeliveryType.value = -1;
             //  }
             //
             //  document.manualForm.submit();
              }
            }

        </script>
        <script>
            function getHTTPObject() {
              var xmlhttp;
              /*@cc_on
              @if (@_jscript_version >= 5)
                try {
                  xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e) {
                  try {
                    xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
                  } catch (E) {
                    xmlhttp = false;
                  }
                }
              @else
              xmlhttp = false;
              @end @*/
              if (!xmlhttp && typeof XMLHttpRequest != 'undefined') {
                try {
                  xmlhttp = new XMLHttpRequest();
                  if (xmlhttp.overrideMimeType) xmlhttp.overrideMimeType('text/xml');
                } catch (e) {
                  xmlhttp = false;
                }
              }
              return xmlhttp;
            }

            var http = getHTTPObject();
            var startTime;

            function getData(startAnimation, req) {
              http = getHTTPObject();
              toggleForm(false);

              if (startAnimation) {
                startTime = new Date();
                getRawObject("Animation").style.display = "";
                getRawObject("Response").innerHTML  = '';
              }

              var ilumper;
              var ideliverytype;
              var iexpdate;
              var iexptime;
              var itrailer;
              var icancel;

              if (document.getElementById("LumperYes") != null) {
                  if (document.PageForm["LumperYes"].checked == true)
                      ilumper = 1;
                  else if (document.PageForm["LumperNo"].checked == true)
                      ilumper = 0;
              }
              else
                  ilumper = -1;

              if (document.getElementById("Drop") != null) {
                  if (document.PageForm["Drop"].checked == true) {
                      ideliverytype = 1;
                      iexpdate = document.PageForm["ArrivalDate"].value;
                      iexptime = document.PageForm["ArrivalHour"].value + ":" + document.PageForm["ArrivalMin"].value + ' ' + document.PageForm["AMPM"].value;
                      itrailer = document.PageForm["Trailer"].value;
                  }
                  else if (document.PageForm["LiveUnload"].checked == true)
                      ideliverytype = 0;
              }
              else
                  ideliverytype = -1;

              if (req == "ins")
              {
                  if (document.RedirForm.ccApp.value == "true") {
                      if (document.getElementById("requestReview").checked == true)
                          icancel = 1;
                      else
                          icancel = 0;
                  }
                  else
                      icancel = -1;
              }

              if (ideliverytype == 1)
              {
                  http.open("GET", "scheduleApp.aspx?req=" + req + "&t=" +  iexptime
                      + "&d=" + getRawObject('DeliveryDate').value + "&uc=" + getRawObject('Comments').value + "&lp=" + ilumper + "&del=" + ideliverytype + "&ed=" + iexpdate + "&et=" + iexptime + "&tl=" + itrailer + "&cc=" + icancel, true);
              }
              else
              {
                  if (icancel == -1)
                  {
                      http.open("GET", "scheduleApp.aspx?req=" + req + "&t=" + getRawObject('Appointments').options[getRawObject('Appointments').selectedIndex].value
                          + "&d=" + getRawObject('DeliveryDate').value + "&uc=" + getRawObject('Comments').value + "&lp=" + ilumper + "&del=" + ideliverytype + "&ed=" + iexpdate + "&et=" + iexptime + "&tl=" + itrailer, true);
                  }
                  else
                  {
                      http.open("GET", "scheduleApp.aspx?req=" + req
                          + "&d=" + getRawObject('DeliveryDate').value + "&uc=" + getRawObject('Comments').value + "&lp=" + ilumper + "&del=" + ideliverytype + "&ed=" + iexpdate + "&et=" + iexptime + "&tl=" + itrailer + "&cc=" + icancel, true);
                  }
              }


              http.onreadystatechange = handleHttpResponse;
              http.send(null);
            }

            //Checks browser compatibility and returns the proper object
            function getRawObject(obj) {
            var theObj;
            if (typeof obj == "string")
            {
            if (document.getElementById) {
            theObj = document.getElementById(obj);
            } else if (document.all) {
            theObj = document.all(obj);
            } else if (document.layers) {
            theObj = seekLayer(document, obj);
            }
            } else {
            // pass through object reference
            theObj = obj;
            }
            return theObj;
            }

            function handleHttpResponse() {
              var now = new Date();
              var handled = false;
              if (http.readyState == 4) {
                if (http.responseText == "true") {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  getRawObject("Response").innerHTML = "";
                  if(getRawObject('Appointments').selectedIndex > -1)
                  {
                    document.RedirForm.AppTime.value = getRawObject('Appointments').options[getRawObject('Appointments').selectedIndex].value;
                  }

                  document.RedirForm.ConfirmRedir.value = "TRUE";
                  document.RedirForm.AppComments.value = document.PageForm.Comments.value;

                  if (false) alert(((now - startTime) / 1000) + ' secs');
                  document.RedirForm.submit();
                }

                if (http.responseText.indexOf("refresh") > -1) {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                  var index = http.responseText.indexOf("?Status=");
                  var message = "";
                  if (index > -1)
                  {
                    message = http.responseText.substr(index + 8);
                  }
                  alert('Error servicing appointment.  ' + message);
                  document.PageForm.submit();
                }

                if (http.responseText == "beginReserve") {
                  handled = true;
                  getData(false, 'val');
                  return;
                }

                if (http.responseText == "false") {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  getRawObject("Response").innerHTML  = "<p>Timeout...</p>";
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                }

                if (!handled) {
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  getRawObject("Response").innerHTML  = http.responseText;
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                }
              }
            }

            function getManualData(startAnimation, req) {
              http = getHTTPObject();
              toggleForm(false);
              var custName = 'Ben E. Keith Foods - Oklahoma City';
              var destCity = 'Oklahoma City';
              if (startAnimation) {
                startTime = new Date();
                getRawObject("Animation").style.display = "";
                getRawObject("Response").innerHTML  = "<br>Contacting " + custName + ", " + destCity;
              }

              var ilumper;
              var ideliverytype;
              var iexpdate;
              var iexptime;
              var itrailer;

              if (document.getElementById("LumperYes") != null) {
                if (document.PageForm["LumperYes"].checked == true)
                  ilumper = 1;
                else if (document.PageForm["LumperNo"].checked == true)
                  ilumper = 0;
                else
                  ilumper = -1;
              }
              else {
                ilumper = -1;
              }

              if (document.getElementById("Drop") != null) {
                  if (document.PageForm["Drop"].checked == true) {
                      ideliverytype = 1;
                      iexpdate = document.PageForm["ArrivalDate"].value;
                      iexptime = document.PageForm["ArrivalHour"].value + ":" + document.PageForm["ArrivalMin"].value + ' ' + document.PageForm["AMPM"].value;
                      itrailer = document.PageForm["Trailer"].value;
                  }
                  else if (document.PageForm["LiveUnload"].checked == true)
                      ideliverytype = 0;
              }
              else
                  ideliverytype = -1;

              if (ideliverytype == 1)
              {
                  http.open("GET", "scheduleApp.aspx?req=" + req + "&t=manual&d=" + getRawObject('DeliveryDate').value + "&lp=" + ilumper  + "&del=" + ideliverytype  + "&ed=" + iexpdate + "&et=" + iexptime + "&tl=" + itrailer + "&uc=" + HTMLEncode(document.PageForm.Comments.value), true);
              }
              else
              {
                  http.open("GET", "scheduleApp.aspx?req=" + req + "&t=manual&d=" + getRawObject('DeliveryDate').value + "&lp=" + ilumper  + "&uc=" + HTMLEncode(document.PageForm.Comments.value), true);
              }

              http.onreadystatechange = handleManualHttpResponse;
              http.send(null);
            }

            function handleManualHttpResponse() {
            var custName = 'Ben E. Keith Foods - Oklahoma City';
              var contName = 'Kerry Purcell';
              var contPhone = '(*************';
              var contEmail = '<EMAIL>';
              var now = new Date();
              var handled = false;

              if (http.readyState == 4) {
            if (http.responseText == "Could not validate session.")
            {
            document.RedirLogin.submit();
            }

                if (http.responseText == "true") {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  getRawObject("Response").innerHTML = "";
                  document.RedirForm.AppTime.value = "manual";
                  document.RedirForm.ConfirmRedir.value = "TRUE";
                  document.RedirForm.UpdateComments.value = document.PageForm.Comments.value;

                  if (false) alert(((now - startTime) / 1000) + ' secs');
                  document.RedirForm.submit();
                }

                if (http.responseText.indexOf("refresh") > -1) {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                  var index = http.responseText.indexOf("?Status=");
                  var message = "";
                  if (index > -1)
                  {
                    message = http.responseText.substr(index + 8);
                  }
                  alert('Error servicing appointment.  ' + message);
                }

                if (http.responseText == "beginReserve") {
                  handled = true;
                  getManualData(false, 'val');
                  return;
                }

                if (http.responseText == "false") {
                  handled = true;
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  //getRawObject("Response").innerHTML  = "<br>Error: " + custName + " appears to be off-line.  Please contact " + contName + ", " + contPhone + ", " + contEmail  + " to complete this request.";
                  getRawObject("Response").innerHTML  = "<br> " + custName + " Web Scheduling is currently busy. Please try your request again. Thank you.";
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                }

                if (!handled) {
                  toggleForm(true);
                  getRawObject("Animation").style.display = "none";
                  getRawObject("Response").innerHTML  = http.responseText;
                  if (false) alert(((now - startTime) / 1000) + ' secs');
                }
              }
            }

        </script>
        <SCRIPT language="JavaScript">
            var n = (navigator.appName == "Netscape")

            var n4 = (document.layers) ? 1:0

            var ie = (navigator.appName == "Microsoft Internet Explorer")

            var ie4 = (document.all) ? 1:0

            var browserName = navigator.appName;

            var rolloversOK = 0;

            browserVer = parseInt(navigator.appVersion);

            if (n && browserVer >= 3) rolloversOK = 1;

            else if (ie && browserVer >= 4) rolloversOK = 1;

            //alert("RolloversOK = " + rolloversOK);



            if (rolloversOK==0)  {



            btn0=btn1=btn2=btn3="Hi There";

            btn0off=btn1off=btn2off=btn3off="Hi There";

            btn0on=btn1on=btn2on=btn3on="Hi There";



            }



            if (rolloversOK==1)



            if (document.images) {
            btn1= new Image();
            btn1.src = "images/btnHelp_off.gif";
            btn1on = new Image();
            btn1on.src = "images/btnHelp_on.gif";
            btn1off = new Image();
            btn1off.src = "images/btnHelp_off.gif";
            btn2= new Image();
            btn2.src = "images/btnFeedback_off.gif";
            btn2on = new Image();
            btn2on.src = "images/btnFeedback_on.gif";
            btn2off = new Image();
            btn2off.src = "images/btnFeedback_off.gif";
            btn3= new Image();
            btn3.src = "images/btnAccount_off.gif";
            btn3on = new Image();
            btn3on.src = "images/btnAccount_on.gif";
            btn3off = new Image();
            btn3off.src = "images/btnAccount_off.gif";
            btn4= new Image();
            btn4.src = "images/btnLogout_off.gif";
            btn4on = new Image();
            btn4on.src = "images/btnLogout_on.gif";
            btn4off = new Image();
            btn4off.src = "images/btnLogout_off.gif";
            btn0= new Image();
            btn0.src = "images/btnHome_off.gif";
            btn0on = new Image();
            btn0on.src = "images/btnHome_on.gif";
            btn0off = new Image();
            btn0off.src = "images/btnHome_off.gif";
            btn5= new Image();
            btn5.src = "images/btnAdd_off.gif";
            btn5on = new Image();
            btn5on.src = "images/btnAdd_on.gif";
            btn5off = new Image();
            btn5off.src = "images/btnAdd_off.gif";
            btn6= new Image();
            btn6.src = "images/btnView_off.gif";
            btn6on = new Image();
            btn6on.src = "images/btnView_on.gif";
            btn6off = new Image();
            btn6off.src = "images/btnView_off.gif";
            btn7= new Image();
            btn7.src = "images/btnHow_off.gif";
            btn7on = new Image();
            btn7on.src = "images/btnHow_on.gif";
            btn7off = new Image();
            btn7off.src = "images/btnHow_off.gif";
            btn8= new Image();
            btn8.src = "images/btnRequest_off.gif";
            btn8on = new Image();
            btn8on.src = "images/btnRequest_on.gif";
            btn8off = new Image();
            btn8off.src = "images/btnRequest_off.gif";
            }



            function setImage(ff, gg) {



            if (rolloversOK==1)



            if (document.images ) {



            document.images[ff].src = gg.src;



              }



            }



            // -->

        </SCRIPT>
        <style type="text/css">
            #requestReview
            {
            width: 24px;
            }
            .style1
            {
            margin: 0px;
            border: 0px solid #FFFFFF;
            background-color: #FFFFFF;
            height: 22px;
            }
            .style2
            {
            width: 750px;
            height: 26px;
            }
        </style>
        <!-- GREENSCREEN -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <link type="text/css" href="greenscreen/css/main.css" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700,400italic,100,100italic,300,300italic,700italic,900italic,900&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
        <script src="greenscreen/javascript/jquery.min.js"></script>
        <script src="greenscreen/javascript/greenscreen.js"></script>
        <script>
            $(document).ready(function() {
            	greenscreen.init();
            	greenscreen.renderLogin();
            });
        </script>
    </HEAD>
    <body bgColor="#ffffff" onload="handleFocus()">
        <!-- GREENSCREEN -->
        <header class="color_2 background">
            <div class="logo tablet_only">
                <div class="image"><img src="greenscreen/images/logo.png"></div>
                <div class="slogan">Power Traffic</div>
            </div>
            <div class="actions">
                <section class="tablet_only">
                    <div class="button without_border" title="Home"><span class="icon-home" onclick="window.location='home.aspx'"></span></div>
                    <div class="button without_border" title="Help"><span class="icon-help" onclick="greenscreen.help_pdf()"></span></div>
                    <div class="button without_border" title="Feedback"><span class="icon-mail" onclick="window.location='mailto:<EMAIL>?subject=Feedback'"></span></div>
                    <div class="button without_border" title="My Account"><span class="icon-user" onclick="window.location='account.aspx'"></span></div>
                    <div class="button without_border" title="Logout"><span class="icon-log-out" onclick="window.location='logout.aspx'"></span></div>
                </section>
            </div>
            <div class="titles">
                <!--<h1>Orders <span class="icon-chevron-thin-right"></span> Order #12345</h1>
                    <h3>Store #1200</h3>-->
            </div>
        </header>
        <menu class="dark_4 background">
            <ul>
                <li class="tendering" onclick="window.location='carTenderingLoads.aspx'"><span class="icon-bell"></span> Web Tendering</li>
                <li class="scheduling">
                    <span onclick="window.location='createAppointment.aspx'"><span class="icon-calendar"></span> Web Scheduling</span>
                    <ul class="dark_4 background">
                        <li onclick="window.location='createAppointment.aspx'">Request An Appointment</li>
                        <li onclick="window.location='viewAppointments.aspx'">View My Current Appointments</li>
                        <li onclick="window.location='customers.aspx'">Add scheduling for a company</li>
                        <li onclick="window.location='faq.aspx'">How do I use web scheduling?</li>
                    </ul>
                </li>
            </ul>
        </menu>
        <div class="content">
            <form name="PageForm" method="post" action="scheduleAppointment.aspx" id="PageForm">
                <input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" />
                <input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" />
                <input type="hidden" name="__LASTFOCUS" id="__LASTFOCUS" value="" />
                <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" />
                <script type="text/javascript">
                    <!--
                    var theForm = document.forms['PageForm'];
                    if (!theForm) {
                        theForm = document.PageForm;
                    }
                    function __doPostBack(eventTarget, eventArgument) {
                        if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                            theForm.__EVENTTARGET.value = eventTarget;
                            theForm.__EVENTARGUMENT.value = eventArgument;
                            theForm.submit();
                        }
                    }
                    // -->
                </script>
                <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="1E67EEBA" />
                <input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEWYgL6gpKTDQK48aaPAQL/8MSMAgK91ZedBQLP/JO2CwKT1sLuAgKS8pqTBwKE+aiMBQKMm+WyBwL8uLtYAvfS8LgEAv3W+bwKAry1tPsEAuWo7PgGAuaW04gIAo6d/a4BAu2ByLQDAo7p948PAqWBwZYEAqWBzZYEAqWByZYEAqWB9ZYEAqWB8ZYEAqWB/ZYEAqWB+ZYEAqWB5ZYEAqWBoZUEAqWBrZUEArqBwZYEArqBzZYEArqByZYEAoTP/dIDAoTP8dIDAoTP9dIDAoTPydIDAoTPzdIDAoTPwdIDAoTPxdIDAoTP2dIDAoTPndEDAoTPkdEDApvP/dIDApvP8dIDApvP9dIDApvPydIDApvPzdIDApvPwdIDApvPxdIDApvP2dIDApvPndEDApvPkdEDAprP/dIDAprP8dIDAprP9dIDAprPydIDAprPzdIDAprPwdIDAprPxdIDAprP2dIDAprPndEDAprPkdEDApnP/dIDApnP8dIDApnP9dIDApnPydIDApnPzdIDApnPwdIDApnPxdIDApnP2dIDApnPndEDApnPkdEDApjP/dIDApjP8dIDApjP9dIDApjPydIDApjPzdIDApjPwdIDApjPxdIDApjP2dIDApjPndEDApjPkdEDAp/P/dIDAp/P8dIDAp/P9dIDAp/PydIDAp/PzdIDAp/PwdIDAp/PxdIDAp/P2dIDAp/PndEDAp/PkdEDAobV0NEPAqnV0NEPAqvz/dUDAvDhjekEAqjO4tYJAuqM+rgJAoPO7YQJCuYXM3E6bxDhASLLnoEnqC25gjM=" />
                <!--
                    <table class="navBackground2" cellSpacing="0" cellPadding="0" width="100%" border="0">
                    	<tr>
                    		<td vAlign="top">
                    			<table cellSpacing="0" cellPadding="0" width="760" border="0">
                    				<tr>
                    					<td colSpan="7"><IMG height="94" alt="NCR Web Scheduling" src="images/headerScheduling.jpg" width="760"></td>
                    				</tr>
                    				<tr>
                    					<td style="height: 27px"><a href="help/InstructionsForUsingNCRWebScheduling.pdf" onmouseover="setImage('btn1',btn1on)" onmouseout="setImage('btn1',btn1off)" target="_blank"><img src="images/btnHelp_off.gif" width="81" height="27" alt="Web Scheduling Help" border="0"	name="btn1"></a></td>
                    					<td style="height: 27px"><a href="mailto:<EMAIL>?subject=Feedback" onmouseover="setImage('btn2',btn2on)" onmouseout="setImage('btn2',btn2off)"><img src="images/btnFeedback_off.gif" width="104" height="27" alt="Feedback" border="0" name="btn2"></a></td>
                    					<td style="height: 27px"><a href="account.aspx" onmouseover="setImage('btn3',btn3on)" onmouseout="setImage('btn3',btn3off)"><img src="images/btnAccount_off.gif" width="109" height="27" alt="My Account" border="0" name="btn3"></a></td>
                    					<td style="height: 27px"><a href="logout.aspx" onmouseover="setImage('btn4',btn4on)" onmouseout="setImage('btn4',btn4off)"><img src="images/btnLogout_off.gif" alt="Logoff" Width="96" Height="27" border="0" name="btn4"></a></td>
                    					<td style="height: 27px"><a href="home.aspx" onmouseover="setImage('btn0',btn0on)" onmouseout="setImage('btn0',btn0off)"><img src="images/btnHome_off.gif" width="81" height="27" alt="Home" border="0" name="btn0"></a></td>
                    					<td><img src="images/GrayStripe.gif" width="104" height="27" alt="" border="0" /></td>
                    					<td style="width: 188px; height: 27px;"><img src="images/navTopSpacer.gif" width="200" height="27" alt=""></td>
                    				</tr>
                    			</table>
                    			<table cellSpacing="0" cellPadding="0" width="760" border="0">
                    				<tr>
                    					<td style="width: 162px"><A onmouseover="setImage('btn8',btn8on)" onmouseout="setImage('btn8',btn8off)" href="createAppointment.aspx"><IMG height="45" alt="Request An Appointment" src="images/btnRequest_off.gif" width="161"
                    								border="0" name="btn8"></A></td>
                    					<td style="width: 189px"><A onmouseover="setImage('btn6',btn6on)" onmouseout="setImage('btn6',btn6off)" href="viewAppointments.aspx"><IMG height="45" alt="View My Current Appointments" src="images/btnView_off.gif" width="188"
                    								border="0" name="btn6"></A></td>
                    					<td><A onmouseover="setImage('btn5',btn5on)" onmouseout="setImage('btn5',btn5off)" href="customers.aspx"><IMG height="45" alt="Add web scheduling for a company" src="images/btnAdd_off.gif"
                    								width="217" border="0" name="btn5"></A></td>
                    					<td style="width: 235px"><A onmouseover="setImage('btn7',btn7on)" onmouseout="setImage('btn7',btn7off)" href="faq.aspx"><IMG height="45" alt="How do I use web scheduling?" src="images/btnHow_off.gif" width="194"
                    								border="0" name="btn7"></A></td>
                    				</tr>
                    			</table>
                    		</td>
                    	</tr>
                    	<tr>
                    		<td vAlign="top"><IMG height="40" alt="Create An Appointment For A New Load" src="images/headerCreateAppointment.gif"
                    				width="760px"><BR>
                    			<IMG height="35" alt="Change Orders" src="images/orderProcessStep2.gif" width="760px"></td>
                    	</tr>
                    </table>
                    -->
                <table cellSpacing="0" cellPadding="0" width="100%" bgColor="#ffffff" border="0">
                    <TBODY>
                        <tr>
                            <td vAlign="top">
                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td width="760">
                                            <table class="bodySpacing2" cellSpacing="0" cellPadding="0" width="760" border="0">
                                                <TR style="height:25px">
                                                    <TD width="250px">
                                                        <p>Company</p>
                                                    </TD>
                                                    <TD>
                                                        <p><span id="CustomerName">Ben E. Keith Foods - Oklahoma City - Ben E. Keith OKC (Oklahoma City, OK)</span></p>
                                                    </TD>
                                                </TR>
                                                <TR style="height:25px">
                                                    <TD width="250px">
                                                        <p>Company Contact</p>
                                                    </TD>
                                                    <TD>
                                                        <p><span id="CustomerContact">Kerry Purcell; (*************; <a href='mailto:'<EMAIL>'><EMAIL></a></span></p>
                                                    </TD>
                                                </TR>
                                                <TR>
                                                    <TD width="250px">
                                                        <input id="Revise" onclick="confirmSave()" type="button" value="Add/Remove Orders">
                                                    </TD>
                                                    <TD>
                                                        &nbsp;
                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    </TD>
                                                </TR>
                                                <TR style="height:5px">
                                                    <!-- <TD width="250px">
                                                        &nbsp;</TD>
                                                        <TD>
                                                        &nbsp;</TD> -->
                                                </TR>
                                            </table>
                                            <table class="bodySpacing2" cellSpacing="0" cellPadding="0" width="760" border="0">
                                                <tr style="height:25px">
                                                    <td vAlign="top">
                                                        <p>Note: Use the Edit button in the first column to modify the PO information. Any column with <font color="red">*</font> is a required field.</p>
                                                    </td>
                                                </tr>
                                                <tr style="height:25px">
                                                    <td vAlign="top" style="width: 750px">
                                                        <p>Orders On This Load:</p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td vAlign="top" style="width: 750px">
                                                        <table cellspacing="0" rules="all" border="1" id="OrderList" style="width:760px;border-collapse:collapse;">
                                                            <tr class="bodytext" align="center" valign="middle">
                                                                <td>Orders On This Load:</td>
                                                                <td>PO #</td>
                                                                <td>Order Date</td>
                                                                <td>Due Date</td>
                                                                <td>Cases</td>
                                                                <td>Weight</td>
                                                                <td>Pallets</td>
                                                                <td>Cube</td>
                                                                <td style="width:200px;">Company</td>
                                                                <td style="width:150px;">
                                                                    <span id="OrderList__ctl1_OriginHeader">Origin (City, State)</span>
                                                                </td>
                                                                <td style="width:200px;">
                                                                    <span id="OrderList__ctl1_PalletTypeHeader">Pallet Type</span>
                                                                </td>
                                                            </tr>
                                                            <tr class="bodytext" align="center" valign="middle">
                                                                <td>
                                                                    <input type="submit" name="OrderList:_ctl2:btnEdit" value="Edit" id="OrderList__ctl2_btnEdit" />
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderNum">620967</span>
                                                                    <input name="OrderList:_ctl2:ErrorCode" type="hidden" id="OrderList__ctl2_ErrorCode" value="-1" />
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderDate"></span>
                                                                    <span id="OrderList__ctl2_ErrorText">Order Already Scheduled</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_DueDate">6/25/2025</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderedCases">1481.00</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderedWeight">21255.00</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderedPallets">38.00</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_OrderedCube">2518.00</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_VendorName" style="width:100px;">LINEAGE REDISTRIBUTION / LARGE MOVE</span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_Origin"></span>
                                                                </td>
                                                                <td>
                                                                    <span id="OrderList__ctl2_PalletType">Not Assigned</span>
                                                                </td>
                                                            </tr>
                                                            <tr class="bodytext" align="center" valign="middle">
                                                                <td>&nbsp;</td>
                                                                <td>
                                                                </td>
                                                                <td>
                                                                </td>
                                                                <td>
                                                                </td>
                                                                <td>
                                                                    Total
                                                                    0.00
                                                                </td>
                                                                <td>
                                                                    Total
                                                                    0.00
                                                                </td>
                                                                <td>
                                                                    Total
                                                                    0.00
                                                                </td>
                                                                <td>
                                                                    Total
                                                                    0.00
                                                                </td>
                                                                <td>
                                                                </td>
                                                                <td>
                                                                </td>
                                                                <td>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <P><span id="lbError" style="color:Red;font-weight:bold;width:723px;">Order '620967' is already scheduled as below:
                                                            </span>
                                                        </P>
                                                        <div id="panel1" style="width:750px;">
                                                            <span id="lbApptTime" class="bodytext">Appointment Start Time:</span>&nbsp;
                                                            <span id="lblApptTime" class="bodytext" style="font-weight:bold;width:200px;">6/30/2025 5:00 AM</span>
                                                            &nbsp; &nbsp; &nbsp;
                                                            <span id="lbDuration" class="bodytext">Duration:</span>&nbsp;
                                                            <span id="lblDuration" class="bodytext" style="font-weight:bold;width:100px;">120 minutes</span>
                                                            &nbsp; &nbsp; &nbsp;
                                                            <span id="lbConfirmnum" class="bodytext">Confirmation#:</span>&nbsp;
                                                            <span id="lblConfirmnum" class="bodytext" style="font-weight:bold;width:80px;">384964</span><br />
                                                            <table>
                                                                <tr>
                                                                    <td><span id="lbCancelAppt" class="bodytext" style="color:Red;font-weight:bold;">Do you wish to cancel this appointment?</span>&nbsp;&nbsp;&nbsp;&nbsp;</td>
                                                                    <td><input id="CancelAppt" onclick="confirmCancel()" type="button" value="Cancel Appointment" /></td>
                                                                    <td><span class="bodytext" disabled="disabled" style="width:140px;"><input id="requestReview" type="checkbox" name="requestReview" checked="checked" disabled="disabled" /><label for="requestReview">  Require For Review</label></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                                                                    <td><span id="lbOR" class="bodytext" style="color:Red;font-weight:bold;">OR</span></td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <p><span id="Messages" style="color:Red;font-weight:bold;">Please select a new appointment to request change.</span></p>
                                                        <p><span id="DeliveryType" class="bodytext" style="font-weight:bold;">Please select an appointment type: </span>
                                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="bodytext"><input id="LiveUnload" type="radio" name="DeliveryType" value="LiveUnload" checked="checked" onclick="javascript:DisplayEntryForLoad();" /><label for="LiveUnload">Live</label></span>
                                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="bodytext" disabled="disabled"><input id="Drop" type="radio" name="DeliveryType" value="Drop" disabled="disabled" onclick="javascript:DisplayEntryForLoad();" /><label for="Drop">Drop</label></span>
                                                        </p>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table class="bodySpacing2" cellSpacing="0" cellPadding="0" width="760" border="0" height="280">
                                                <tr>
                                                    <td width="380" valign=top>
                                                        <table id="liveReserve" class="outlinedTable" width="100%" height="270">
                                                            <TR>
                                                                <TD class="outlinedTableCell" colspan=2>
                                                                    <p><span id="lbAppointmentHeader" style="color:Gray;font-weight:bold;">Make a live appointment now</span></p>
                                                                    <hr />
                                                                </TD>
                                                            </TR>
                                                            <tr>
                                                                <td class="outlinedTableCell">
                                                                    <p><span id="lbAppointmentDates" style="color:Gray;">Available Appointment Date</span> &nbsp;<select name="AppointmentDate" onchange="javascript:setTimeout('__doPostBack(\'AppointmentDate\',\'\')', 0)" language="javascript" id="AppointmentDate" disabled="disabled">
                                                                        </select>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="outlinedTableCell" vAlign="top"><select size="4" name="Appointments" id="Appointments" disabled="disabled" style="height:68px;width:380px;">
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="outlinedTableCell">
                                                                    <p><span id="AppointmentsText" style="color:Gray;font-weight:bold;">
                                                                        Note: Available appointments are time sensitive. Avoiding delays once you
                                                                        have reached this step will ensure the best chance of reservation of your
                                                                        preferred appointment.</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <TR>
                                                                <TD class="outlinedTableCell" colspan=2>
                                                                    <input name="Reserve" type="button" id="Reserve" onclick="doReserve()" value="Reserve Appointment" title="Reserve the selected appointment" disabled="disabled" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type="submit" name="Cancel" value="Cancel" id="Cancel" /><br />
                                                                </TD>
                                                            </TR>
                                                        </table>
                                                    </td>
                                                    <td id="or" width="70" valign=top align=center>- OR -</td>
                                                    <td width="310" valign=top>
                                                        <table id="liveRequest" class="outlinedTable" width="100%" height="270">
                                                            <TR>
                                                                <TD class="outlinedTableCell">
                                                                    <p><span id="lbRequestHeader" style="font-weight:bold;">Submit a request for review</span></p>
                                                                    <hr />
                                                                </TD>
                                                            </TR>
                                                            <TR>
                                                                <TD class="outlinedTableCell" valign=middle>
                                                                    <p>
                                                                        <span id="lbRequestedDate">Requested Appointment Date</span> &nbsp;<input name="DeliveryDate" type="text" id="DeliveryDate" style="width:75px;" />&nbsp; <A onclick="javascript:ValidateDeliveryDate();"><IMG src="images/cal.gif" border="0"></A><br />
                                                                        <span id="lbRequestTimePreference">Requested Appointment Time</span> &nbsp;
                                                                        <select name="RequestedDeliveryTime" onchange="javascript:setTimeout('__doPostBack(\'RequestedDeliveryTime\',\'\')', 0)" language="javascript" id="RequestedDeliveryTime">
                                                                            <option selected="selected" value="Anytime">Anytime</option>
                                                                            <option value="Before Noon">Before Noon</option>
                                                                            <option value="Noon - 6pm">Noon - 6pm</option>
                                                                            <option value="After 6pm">After 6pm</option>
                                                                        </select>
                                                                    </p>
                                                                </TD>
                                                            </TR>
                                                            <tr>
                                                                <td class="outlinedTableCell">
                                                                    <p><span id="lbRequestText" style="font-weight:bold;">
                                                                        Note: Your request will be placed in an appointment queue. Once your appointment has been scheduled, you will be notified by
                                                                        email of the appointment time.<br /><font color="red">Your request may be rejected without including reason in comments box below.</font></span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <TR>
                                                                <TD class="style1">
                                                                    <input name="Manual" type="button" id="Manual" onclick="doManual()" value="Request Appointment" title="Request an appointment using the requested appointment date and time" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input type="submit" name="CancelRequest" value="Cancel" id="CancelRequest" /><br />
                                                                </TD>
                                                            </TR>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr style="height:10px">
                                                    <td colspan="3" width="760px">
                                                        <table id="dropReserve" class="outlinedTable" width="100%" height="270">
                                                            <TR>
                                                                <TD class="outlinedTableCell" colspan="2">
                                                                    <p><span id="lbDropHeader" style="font-weight:bold;">Make a drop appointment now</span></p>
                                                                    <hr />
                                                                </TD>
                                                            </TR>
                                                            <tr>
                                                                <td class="outlinedTableCell" vAlign="top">
                                                                    <p>
                                                                        <span id="lblExpDate">Expected Arrival Date </span>
                                                                        <input name="ArrivalDate" type="text" id="ArrivalDate" class="bodytext" style="width:85px;" />
                                                                        &nbsp;<A onclick="javascript:ValidateArrivalDate();"><IMG id="calendar1" src="images/cal.gif" border="0"></A><br />
                                                                        <span id="lblExpTime">Expected Arrival Time </span>
                                                                        <select name="ArrivalHour" id="ArrivalHour" style="width:50px;">
                                                                            <option value="00">00</option>
                                                                            <option value="01">01</option>
                                                                            <option value="02">02</option>
                                                                            <option value="03">03</option>
                                                                            <option value="04">04</option>
                                                                            <option value="05">05</option>
                                                                            <option value="06">06</option>
                                                                            <option value="07">07</option>
                                                                            <option value="08">08</option>
                                                                            <option value="09">09</option>
                                                                            <option value="10">10</option>
                                                                            <option value="11">11</option>
                                                                            <option value="12">12</option>
                                                                        </select>
                                                                        &nbsp;<span id="lblColon">:</span>&nbsp;
                                                                        <select name="ArrivalMin" id="ArrivalMin" style="width:50px;">
                                                                            <option value="00">00</option>
                                                                            <option value="01">01</option>
                                                                            <option value="02">02</option>
                                                                            <option value="03">03</option>
                                                                            <option value="04">04</option>
                                                                            <option value="05">05</option>
                                                                            <option value="06">06</option>
                                                                            <option value="07">07</option>
                                                                            <option value="08">08</option>
                                                                            <option value="09">09</option>
                                                                            <option value="10">10</option>
                                                                            <option value="11">11</option>
                                                                            <option value="12">12</option>
                                                                            <option value="13">13</option>
                                                                            <option value="14">14</option>
                                                                            <option value="15">15</option>
                                                                            <option value="16">16</option>
                                                                            <option value="17">17</option>
                                                                            <option value="18">18</option>
                                                                            <option value="19">19</option>
                                                                            <option value="20">20</option>
                                                                            <option value="21">21</option>
                                                                            <option value="22">22</option>
                                                                            <option value="23">23</option>
                                                                            <option value="24">24</option>
                                                                            <option value="25">25</option>
                                                                            <option value="26">26</option>
                                                                            <option value="27">27</option>
                                                                            <option value="28">28</option>
                                                                            <option value="29">29</option>
                                                                            <option value="30">30</option>
                                                                            <option value="31">31</option>
                                                                            <option value="32">32</option>
                                                                            <option value="33">33</option>
                                                                            <option value="34">34</option>
                                                                            <option value="35">35</option>
                                                                            <option value="36">36</option>
                                                                            <option value="37">37</option>
                                                                            <option value="38">38</option>
                                                                            <option value="39">39</option>
                                                                            <option value="40">40</option>
                                                                            <option value="41">41</option>
                                                                            <option value="42">42</option>
                                                                            <option value="43">43</option>
                                                                            <option value="44">44</option>
                                                                            <option value="45">45</option>
                                                                            <option value="46">46</option>
                                                                            <option value="47">47</option>
                                                                            <option value="48">48</option>
                                                                            <option value="49">49</option>
                                                                            <option value="50">50</option>
                                                                            <option value="51">51</option>
                                                                            <option value="52">52</option>
                                                                            <option value="53">53</option>
                                                                            <option value="54">54</option>
                                                                            <option value="55">55</option>
                                                                            <option value="56">56</option>
                                                                            <option value="57">57</option>
                                                                            <option value="58">58</option>
                                                                            <option value="59">59</option>
                                                                        </select>
                                                                        &nbsp;&nbsp;
                                                                        <select name="AMPM" id="AMPM" style="width:50px;">
                                                                            <option value="AM">AM</option>
                                                                            <option value="PM">PM</option>
                                                                        </select>
                                                                        &nbsp;<br />
                                                                        <span id="lblTrailer">Trailer Number </span>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <input name="Trailer" type="text" maxlength="15" id="Trailer" class="bodytext" style="width:109px;" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="outlinedTableCell">
                                                                    <p><span id="DropText" style="font-weight:bold;">
                                                                        Note: Select the expected arrival date and time, using Reserve this appointment to make an reservation for the
                                                                        preferred appointment. Or using Request appointment to submit the request for review of the preferred appointment. Once your
                                                                        appointment is scheduled, you will be notified by the email of the appointment time.</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <TR>
                                                                <TD class="outlinedTableCell" colspan=2>
                                                                    <input name="ReserveDrop" type="button" id="ReserveDrop" onclick="doReserve()" value="Reserve this appointment" disabled="disabled" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="ManualDrop" type="button" id="ManualDrop" onclick="doManual()" value="Request Appointment" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                    <input type="submit" name="CancelDrop" value="Cancel" id="CancelDrop" /><br />
                                                                </TD>
                                                            </TR>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table class="outlinedTable" cellSpacing="0" cellPadding="0" width="760" border="0">
                                                <tr>
                                                    <td vAlign="bottom" style="width: 760px">
                                                        <div class="bodytext" id="Response"></div>
                                                        <IMG id="Animation" style="DISPLAY: none" src="images/status_anim.gif">
                                                        <p style="margin-left:3px; margin-right:3px; margin-top:3px; margin-bottom:-2px;"><span id="lblComments" style="font-weight:bold;">Enter any change comments here.</span></p>
                                                        <hr style="margin-left: 3px; margin-right:3px" />
                                                        <P><textarea name="Comments" id="Comments" onkeypress="return textboxMultilineMaxNumber(this,150)" style="height:60px;width:100%;"></textarea>
                                                        </P>
                                                        <br>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td width="3%">&nbsp;&nbsp;</td>
                                        <td valign="top" height="100%" style="width: 226px">
                                            <br />
                                            <table width="90%" height="200px" cellpadding="8" cellspacing="0" border="0" class="bodySpacingText">
                                                <tr>
                                                    <td>
                                                    </td>
                                                </tr>
                                            </table>
                                </table>
                            </td>
                        </tr>
                    </TBODY>
                </table>
                <script type="text/javascript">
                    <!--
                    document.getElementById('dropReserve').style.display='none';// -->
                </script>
            </form>
            <!--
                <table cellSpacing="0" cellPadding="0" width="100%" border="0">
                	<tr>
                		<td class="footTopBack"><IMG height="39" src="home/footerTop.gif" width="760"></td>
                	</tr>
                	<tr>
                		<td vAlign="top" bgColor="#000000">
                			<table cellSpacing="0" cellPadding="0" width="760" bgColor="#000000" border="0">
                				<tr>
                					<td align="center"><br>
                						<p class="footer">
                							<A class="special" href="mailto:<EMAIL>?subject=Feedback">Feedback</A>
                							| <A class="special" href="account.aspx">My Account</A>
                							| <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a><br>
                							<br>
                						</p>
                						<p class="footer"><A class="special" href="customers.aspx">Add Web Scheduling For A
                								Company</A> | <A class="special" href="viewAppointments.aspx">View My Current
                								Appointments</A> | <A class="special" href="faq.aspx">How Do I Use Web
                								Scheduling?</A><br>
                							<br>
                						</p>
                					</td>
                				</tr>
                			</table>
                		</td>
                	</tr>
                	<tr>
                		<td class="footBtmBack"><IMG height="43" src="home/footerBottom.gif" width="760"></td>
                	</tr>
                </table>
                -->
            <form id="" name="manualForm" action="manualAppointment.aspx" method="post">
                <input type="hidden" name="AppComments1">
                <input type="hidden" name="AppDate">
                <input type="hidden" name="AppLumper">
                <input type="hidden" name="AppDeliveryType">
                <input type="hidden" name="AppExpArrivalDate">
                <input type="hidden" name="AppExpArrivalTime">
                <input type="hidden" name="AppTrailer">
            </form>
            <form id="" name="RedirForm" action="confirmAppointment.aspx" method="post">
                <input type="hidden" name="AppTime">
                <input type="hidden" name="AppComments">
                <input type="hidden" value="FALSE" name="ConfirmRedir">
                <input type="hidden" name="UpdateComments">
                <input type="hidden" name="ccApp">
            </form>
            <Table>
                <tr>
                    <td>
                    </td>
                </tr>
            </Table>
        </div>
        </div>
        <!-- GREENSCREEN -->
        <footer class="color_0 background">
            Copyright &copy; 2021 NCR Corp. All Rights Reserved |
            <a href="mailto:<EMAIL>?subject=Feedback" class="special">Feedback</a> |
            <a href="account.aspx" class="special">My Account</a> <!-- | -->
            <!-- <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a> -->
        </footer>
    </body>
</HTML>