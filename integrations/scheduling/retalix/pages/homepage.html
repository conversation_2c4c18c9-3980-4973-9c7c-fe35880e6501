<!DOCTYPE HTML>
<!--<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >-->
<HTML>
    <HEAD>
        <title>NCR Web Scheduling - Home</title>
        <LINK href="site_style.css" type="text/css" rel="stylesheet">
        <SCRIPT language="JavaScript">
            var n = (navigator.appName == "Netscape")

            var n4 = (document.layers) ? 1:0

            var ie = (navigator.appName == "Microsoft Internet Explorer")

            var ie4 = (document.all) ? 1:0

            var browserName = navigator.appName;

            var rolloversOK = 0;

            browserVer = parseInt(navigator.appVersion);

            if (n && browserVer >= 3) rolloversOK = 1;

            else if (ie && browserVer >= 4) rolloversOK = 1;

            //alert("RolloversOK = " + rolloversOK);



            if (rolloversOK==0)  {



            btn0=btn1=btn2=btn3="Hi There";

            btn0off=btn1off=btn2off=btn3off="Hi There";

            btn0on=btn1on=btn2on=btn3on="Hi There";



            }



            if (rolloversOK==1)



            if (document.images) {
            btn0= new Image();
            btn0.src = "images/btnHome_off.gif";
            btn0on = new Image();
            btn0on.src = "images/btnHome_on.gif";
            btn0off = new Image();
            btn0off.src = "images/btnHome_off.gif";
            btn1= new Image();
            btn1.src = "images/btnHelp_off.gif";
            btn1on = new Image();
            btn1on.src = "images/btnHelp_on.gif";
            btn1off = new Image();
            btn1off.src = "images/btnHelp_off.gif";
            btn2= new Image();
            btn2.src = "images/btnFeedback_off.gif";
            btn2on = new Image();
            btn2on.src = "images/btnFeedback_on.gif";
            btn2off = new Image();
            btn2off.src = "images/btnFeedback_off.gif";
            btn3= new Image();
            btn3.src = "images/btnAccount_off.gif";
            btn3on = new Image();
            btn3on.src = "images/btnAccount_on.gif";
            btn3off = new Image();
            btn3off.src = "images/btnAccount_off.gif";
            btn4= new Image();
            btn4.src = "images/btnLogout_off.gif";
            btn4on = new Image();
            btn4on.src = "images/btnLogout_on.gif";
            btn4off = new Image();
            btn4off.src = "images/btnLogout_off.gif";


            }



            function setImage(ff, gg) {



            if (rolloversOK==1)



            if (document.images ) {



            document.images[ff].src = gg.src;



              }



            }



            // -->

        </SCRIPT>
        <style type="text/css">
            .hidden{
            visibility: hidden;
            }
            .show{
            visibility: visible;
            }
        </style>
        <!-- GREENSCREEN -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <link type="text/css" href="greenscreen/css/main.css" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700,400italic,100,100italic,300,300italic,700italic,900italic,900&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
        <script src="greenscreen/javascript/jquery.min.js"></script>
        <script src="greenscreen/javascript/greenscreen.js"></script>
        <script>
            $(document).ready(function() {
            	greenscreen.init();
            	greenscreen.renderLogin();
            });
        </script>
    </HEAD>
    <body>
        <form name="PageForm" method="post" action="home.aspx" id="PageForm">
            <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwULLTE3MDUyNTEwMjUPZBYCZg9kFgZmDw8WAh4HVmlzaWJsZWdkZAIED2QWAgIBDw8WAh8AaGRkAgUPPCsACwBkZELc3ekbP7Z4kchA1sFwsiy5JID+" />
            <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="8D0E13E6" />
            <!-- GREENSCREEN -->
            <header class="color_2 background">
                <div class="logo tablet_only">
                    <div class="image"><img src="greenscreen/images/logo.png"></div>
                    <div class="slogan">Power Traffic</div>
                </div>
                <div class="actions">
                    <section class="tablet_only">
                        <div class="button without_border" title="Home"><span class="icon-home" onclick="window.location='home.aspx'"></span></div>
                        <div class="button without_border" title="Help"><span class="icon-help" onclick="greenscreen.help_pdf()"></span></div>
                        <div class="button without_border" title="Feedback"><span class="icon-mail" onclick="window.location='mailto:<EMAIL>?subject=Feedback'"></span></div>
                        <div class="button without_border" title="My Account"><span class="icon-user" onclick="window.location='account.aspx'"></span></div>
                        <div class="button without_border" title="Logout"><span class="icon-log-out" onclick="window.location='logout.aspx'"></span></div>
                    </section>
                </div>
                <div class="titles">
                    <!--<h1>Orders <span class="icon-chevron-thin-right"></span> Order #12345</h1>
                        <h3>Store #1200</h3>-->
                </div>
            </header>
            <menu class="dark_4 background">
                <ul>
                    <li class="tendering" onclick="window.location='carTenderingLoads.aspx'"><span class="icon-bell"></span> Web Tendering</li>
                    <li class="scheduling">
                        <span onclick="window.location='createAppointment.aspx'"><span class="icon-calendar"></span> Web Scheduling</span>
                        <ul class="dark_4 background">
                            <li onclick="window.location='createAppointment.aspx'">Request An Appointment</li>
                            <li onclick="window.location='viewAppointments.aspx'">View My Current Appointments</li>
                            <li onclick="window.location='customers.aspx'">Add scheduling for a company</li>
                            <li onclick="window.location='faq.aspx'">How do I use web scheduling?</li>
                        </ul>
                    </li>
                </ul>
            </menu>
            <h1>Power Traffic</h1>
            <div class="content">
                <!--<table width="100%" cellpadding="0" cellspacing="0" border="0" class="navBackground">
                    <tr>
                    	<td valign="top">
                    		<table width="760" cellpadding="0" cellspacing="0" border="0">
                    			<tr>
                    				<td colspan="7"><img src="images/headerSchedulingAndTendering.jpg" width="760" height="94" alt="NCR Web Scheduling"></td>
                    			</tr>
                    			<tr>
                    				<td><a href="help/InstructionsForUsingNCRWebScheduling.pdf" onmouseover="setImage('btn1',btn1on)" onmouseout="setImage('btn1',btn1off)" target="_blank"><img src="images/btnHelp_off.gif" width="81" height="27" alt="Help" border="0" name="btn1"></a></td>
                    				<td><a href="mailto:<EMAIL>?subject=Feedback" onmouseover="setImage('btn2',btn2on)" onmouseout="setImage('btn2',btn2off)"><img src="images/btnFeedback_off.gif" width="104" height="27" alt="Feedback" border="0" name="btn2"></a></td>
                    				<td><a href="account.aspx" onmouseover="setImage('btn3',btn3on)" onmouseout="setImage('btn3',btn3off)"><img src="images/btnAccount_off.gif" width="109" height="27" alt="My Account" border="0" name="btn3"></a></td>
                    				<td><a href="logout.aspx" onmouseover="setImage('btn4',btn4on)" onmouseout="setImage('btn4',btn4off)"><img src="images/btnLogout_off.gif" alt="Logoff" Width="96" Height="27" border="0" name="btn4"></a></td>
                    				<td><a href="home.aspx" onmouseover="setImage('btn0',btn0on)" onmouseout="setImage('btn0',btn0off)"><img src="images/btnHome_off.gif" width="81" height="27" alt="Home" border="0" name="btn0"></a></td>
                    				<td><img src="images/GrayStripe.gif" width="104" height="27" alt="" border="0" /></td>
                    				<td><img src="images/navTopSpacer.gif" width="200" height="27"></td>
                    			</tr>
                    		</table>
                    	</td>
                    </tr>
                    <tr>
                    	<td valign="top"><img src="home/headerMyServices.gif" width="760" height="40" alt="My Services"></td>
                    </tr>
                    </table>-->
                <div class="home_description">Please choose an option from the above main menu.</div>
                <div class="links_indicator" style="display:none">
                    <div id="PanelTendering" class="dashboardPanel">
                        <a href="carTenderingLoads.aspx"><span id="lblWebtendering">Web Tendering</span></a>
                    </div>
                    &nbsp;
                    <div id="PanelScheduling" class="dashboardPanel">
                        <a href="createAppointment.aspx">Web Scheduling</a>
                    </div>
                </div>
                <div class="hidden_buttons_container">
                    &nbsp;
                </div>
                <!--
                    <table cellSpacing="0" cellPadding="0" border="0" width="100%" bgcolor="#ffffff">
                    	<tr>
                    		<td valign="top">
                    			<table width="760" cellpadding="0" cellspacing="0" border="0" class="bodySpacing2">
                               <tr>
                                 <td valign="top" style="height: 25px">


                                  </td>
                               </tr>
                               <tr>
                                 <td valign="top" style="height: 25px">
                                   <b><font color="red">
                                     <marquee behavior="slide" direction="left" style="width: 650px; display: none;">
                                     <span>Change web scheduling appointment request is available now. Click <a href="newFeature.aspx" target="_blank"><font color="blue">here</font></a> to see detail.</span></marquee>
                                   </font></b>
                                 </td>
                    				</tr>
                    				<tr>
                    					<td valign="top"></td>
                    				</tr>
                    			</table>
                    		</td>
                    	</tr>
                    	<tr>
                    		<td valign="top"><img src="images/transpix.gif" width="715" height="80"></td>
                    	</tr>
                    </table>
                    -->
                <!--<table width="100%" cellpadding="0" cellspacing="0" border="0">
                    <tr>
                    	<td class="footTopBack"><img src="home/footerTop.gif" width="760" height="39"></td>
                    </tr>
                    <tr>
                    	<td valign="top" bgcolor="#000000">
                    		<table width="760" cellpadding="0" cellspacing="0" border="0" bgcolor="#000000">
                    			<tr>
                    				<td align="center"><br>
                    					<p class="footer">
                    						<a href="mailto:<EMAIL>?subject=Feedback" class="special">Feedback</a>
                    						| <a href="account.aspx" class="special">My Account</a>
                    						| <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a><br>
                    						<br>
                    					</p>
                    				</td>
                    			</tr>
                    		</table>
                    	</td>
                    </tr>
                    <tr>
                    	<td class="footBtmBack"><img src="home/footerBottom.gif" width="760" height="43"></td>
                    </tr>
                    </table>
                    -->
            </div>
        </form>
        <!-- GREENSCREEN -->
        <footer class="color_0 background">
            Copyright &copy; 2021 NCR Corp. All Rights Reserved |
            <a href="mailto:<EMAIL>?subject=Feedback" class="special">Feedback</a> |
            <a href="account.aspx" class="special">My Account</a> <!-- | -->
            <!-- <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a> -->
        </footer>
    </body>
</HTML>