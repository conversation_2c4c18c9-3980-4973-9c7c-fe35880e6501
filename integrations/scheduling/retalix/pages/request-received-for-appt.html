<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
    <HEAD>
        <title>NCR Web Scheduling - Request Received</title>
        <META http-equiv="Content-Type" content="text/html; charset=windows-1252">
        <meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
        <meta content="C#" name="CODE_LANGUAGE">
        <meta content="JavaScript" name="vs_defaultClientScript">
        <meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
        <LINK href="site_style.css" type="text/css" rel="stylesheet">
        <SCRIPT language="JavaScript">
            var n = (navigator.appName == "Netscape")

            var n4 = (document.layers) ? 1:0

            var ie = (navigator.appName == "Microsoft Internet Explorer")

            var ie4 = (document.all) ? 1:0

            var browserName = navigator.appName;

            var rolloversOK = 0;

            browserVer = parseInt(navigator.appVersion);

            if (n && browserVer >= 3) rolloversOK = 1;

            else if (ie && browserVer >= 4) rolloversOK = 1;

            //alert("RolloversOK = " + rolloversOK);



            if (rolloversOK==0)  {



            btn0=btn1=btn2=btn3="Hi There";

            btn0off=btn1off=btn2off=btn3off="Hi There";

            btn0on=btn1on=btn2on=btn3on="Hi There";



            }



            if (rolloversOK==1)



            if (document.images) {
            btn1= new Image();
            btn1.src = "images/btnHelp_off.gif";
            btn1on = new Image();
            btn1on.src = "images/btnHelp_on.gif";
            btn1off = new Image();
            btn1off.src = "images/btnHelp_off.gif";
            btn2= new Image();
            btn2.src = "images/btnFeedback_off.gif";
            btn2on = new Image();
            btn2on.src = "images/btnFeedback_on.gif";
            btn2off = new Image();
            btn2off.src = "images/btnFeedback_off.gif";
            btn3= new Image();
            btn3.src = "images/btnAccount_off.gif";
            btn3on = new Image();
            btn3on.src = "images/btnAccount_on.gif";
            btn3off = new Image();
            btn3off.src = "images/btnAccount_off.gif";
            btn4= new Image();
            btn4.src = "images/btnLogout_off.gif";
            btn4on = new Image();
            btn4on.src = "images/btnLogout_on.gif";
            btn4off = new Image();
            btn4off.src = "images/btnLogout_off.gif";
            btn0= new Image();
            btn0.src = "images/btnHome_off.gif";
            btn0on = new Image();
            btn0on.src = "images/btnHome_on.gif";
            btn0off = new Image();
            btn0off.src = "images/btnHome_off.gif";
            btn5= new Image();
            btn5.src = "images/btnAdd_off.gif";
            btn5on = new Image();
            btn5on.src = "images/btnAdd_on.gif";
            btn5off = new Image();
            btn5off.src = "images/btnAdd_off.gif";
            btn6= new Image();
            btn6.src = "images/btnView_off.gif";
            btn6on = new Image();
            btn6on.src = "images/btnView_on.gif";
            btn6off = new Image();
            btn6off.src = "images/btnView_off.gif";
            btn7= new Image();
            btn7.src = "images/btnHow_off.gif";
            btn7on = new Image();
            btn7on.src = "images/btnHow_on.gif";
            btn7off = new Image();
            btn7off.src = "images/btnHow_off.gif";
            btn8= new Image();
            btn8.src = "images/btnRequest_off.gif";
            btn8on = new Image();
            btn8on.src = "images/btnRequest_on.gif";
            btn8off = new Image();
            btn8off.src = "images/btnRequest_off.gif";
            btn9= new Image();
            btn9.src = "images/btnReferrals_off.gif";
            btn9on = new Image();
            btn9on.src = "images/btnReferrals_on.gif";
            btn9off = new Image();
            btn9off.src = "images/btnReferrals_off.gif";

            }



            function setImage(ff, gg) {



            if (rolloversOK==1)



            if (document.images ) {



            document.images[ff].src = gg.src;



              }



            }

            function createNew() {
            	window.location = "createAppointment.aspx";
            }



            // -->

        </SCRIPT>
        <!-- GREENSCREEN -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <link type="text/css" href="greenscreen/css/main.css" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700,400italic,100,100italic,300,300italic,700italic,900italic,900&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
        <script src="greenscreen/javascript/jquery.min.js"></script>
        <script src="greenscreen/javascript/greenscreen.js"></script>
        <script>
            $(document).ready(function() {
            	greenscreen.init();
            	greenscreen.renderLogin();
            });
        </script>
    </HEAD>
    <body>
        <!-- GREENSCREEN -->
        <header class="color_2 background">
            <div class="logo tablet_only">
                <div class="image"><img src="greenscreen/images/logo.png"></div>
                <div class="slogan">Power Traffic</div>
            </div>
            <div class="actions">
                <section class="tablet_only">
                    <div class="button without_border" title="Home"><span class="icon-home" onclick="window.location='home.aspx'"></span></div>
                    <div class="button without_border" title="Help"><span class="icon-help" onclick="greenscreen.help_pdf()"></span></div>
                    <div class="button without_border" title="Feedback"><span class="icon-mail" onclick="window.location='mailto:<EMAIL>?subject=Feedback'"></span></div>
                    <div class="button without_border" title="My Account"><span class="icon-user" onclick="window.location='account.aspx'"></span></div>
                    <div class="button without_border" title="Logout"><span class="icon-log-out" onclick="window.location='logout.aspx'"></span></div>
                </section>
            </div>
            <div class="titles">
                <!--<h1>Orders <span class="icon-chevron-thin-right"></span> Order #12345</h1>
                    <h3>Store #1200</h3>-->
            </div>
        </header>
        <menu class="dark_4 background">
            <ul>
                <li class="tendering" onclick="window.location='carTenderingLoads.aspx'"><span class="icon-bell"></span> Web Tendering</li>
                <li class="scheduling">
                    <span onclick="window.location='createAppointment.aspx'"><span class="icon-calendar"></span> Web Scheduling</span>
                    <ul class="dark_4 background">
                        <li onclick="window.location='createAppointment.aspx'">Request An Appointment</li>
                        <li onclick="window.location='viewAppointments.aspx'">View My Current Appointments</li>
                        <li onclick="window.location='customers.aspx'">Add scheduling for a company</li>
                        <li onclick="window.location='faq.aspx'">How do I use web scheduling?</li>
                    </ul>
                </li>
            </ul>
        </menu>
        <div class="content">
            <form name="PageForm" method="post" action="confirmAppointment.aspx" id="PageForm">
                <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwULLTIxMzAzNTM3OTcPFgIeFUN1c3RvbWVyTWVzc2FnZUJvcmRlcgUBMBYCZg9kFg5mDw8WAh4HVmlzaWJsZWdkZAIDDw8WAh8BaGRkAgQPDxYCHwFoZGQCBQ88KwALAQAPFggeCERhdGFLZXlzFgAeC18hSXRlbUNvdW50AgEeCVBhZ2VDb3VudAIBHhVfIURhdGFTb3VyY2VJdGVtQ291bnQCAWQWAmYPZBYCAgEPZBYCZg9kFhICAQ8WAh8BaBYCAgEPZBYCZg8VAQYzODQ5NjRkAgIPFQIiQmVuIEUuIEtlaXRoIEZvb2RzIC0gT2tsYWhvbWEgQ2l0eWFLZXJyeSBQdXJjZWxsOyg0MDUpIDc1My03NzcwOzxhIGhyZWY9J21haWx0bzprbHB1cmNlbGxAYmVuZWtlaXRoLmNvbSc+a2xwdXJjZWxsQGJlbmVrZWl0aC5jb208L2E+ZAIDDxYCHwFoFgICAQ9kFgJmDxUBAGQCBQ8WAh8BaBYCAgEPZBYCZg8VAQIsIGQCBw8PFgIeBFRleHQFGlJlcXVlc3RlZCBBcHBvaW50bWVudCBEYXRlZGQCCQ8PFgIfBgUVTW9uZGF5LCBKdW5lIDMwLCAyMDI1ZGQCCw8PFgIfBmVkZAINDw8WAh8GBRZBcHBvaW50bWVudCBUeXBlOiBMaXZlZGQCFQ8WAh8BaBYCAgEPZBYCZg8VAQEwZAIGDzwrAAsBAA8WCh8EAgEeClNob3dGb290ZXJnHwIWAQK7qY05HwMCAR8FAgFkFgJmD2QWBAIBD2QWFGYPZBYCAgEPDxYCHwYFBjYyMDk2N2RkAgEPZBYEAgEPDxYCHwYFCTYvMTEvMjAyNWRkAgMPDxYCHwZlZGQCAg9kFgICAQ8PFgIfBgUJNi8yNS8yMDI1ZGQCAw9kFgICAQ8PFgIfBgUIMSw0ODEuMDBkZAIED2QWAgIBDw8WAh8GBQkyMSwyNTUuMDBkZAIFD2QWAgIBDw8WAh8GBQUzOC4wMGRkAgYPZBYCAgEPDxYCHwYFCDIsNTE4LjAwZGQCBw9kFgICAQ8PFgIfBgUjTElORUFHRSBSRURJU1RSSUJVVElPTiAvIExBUkdFIE1PVkVkZAIID2QWAgIBDw8WAh8GZWRkAgkPZBYCAgEPDxYCHwYFDE5vdCBBc3NpZ25lZGRkAgIPZBYIAgMPZBYCZg8VAQgxLDQ4MS4wMGQCBA9kFgJmDxUBCTIxLDI1NS4wMGQCBQ9kFgJmDxUBBTM4LjAwZAIGD2QWAmYPFQEIMiw1MTguMDBkAgcPDxYCHwFoZGQCCA8PFgQfBmUfAWhkZGTB/LcKupp5R8sK5BcaX710mX7Cbw==" />
                <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="E6DF9696" />
                <input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEWAgKQ4tKxDwL/8MSMAkSv84LdQ9I2JQ5G+6vWfJqFw1W6" />
                <!--
                    <table width="100%" cellpadding="0" cellspacing="0" border="0" class="navBackground2">
                    	<tr>
                    		<td valign="top">
                    			<table width="760" cellpadding="0" cellspacing="0" border="0">
                    				<tr>
                    					<td colspan="7"><img src="images/headerScheduling.jpg" width="760" height="94" alt="NCR Web Scheduling"></td>
                    				</tr>
                    				<tr>
                    					<td><a href="help/InstructionsForUsingNCRWebScheduling.pdf" onmouseover="setImage('btn1',btn1on)" onmouseout="setImage('btn1',btn1off)" target="_blank"><img src="images/btnHelp_off.gif" width="81" height="27" alt="Web Scheduling Help" border="0" name="btn1"></a></td>
                    					<td><a href="mailto:<EMAIL>?subject=Feedback" onmouseover="setImage('btn2',btn2on)" onmouseout="setImage('btn2',btn2off)"><img src="images/btnFeedback_off.gif" width="104" height="27" alt="Feedback" border="0" name="btn2"></a></td>
                    					<td><a href="account.aspx" onmouseover="setImage('btn3',btn3on)" onmouseout="setImage('btn3',btn3off)"><img src="images/btnAccount_off.gif" width="109" height="27" alt="My Account" border="0" name="btn3"></a></td>
                    					<td><a href="logout.aspx" onmouseover="setImage('btn4',btn4on)" onmouseout="setImage('btn4',btn4off)"><img src="images/btnLogout_off.gif" alt="Logoff" Width="96" Height="27" border="0" name="btn4"></a></td>
                    					<td><a href="home.aspx" onmouseover="setImage('btn0',btn0on)" onmouseout="setImage('btn0',btn0off)"><img src="images/btnHome_off.gif" width="81" height="27" alt="Home" border="0" name="btn0"></a></td>
                    					<td><img src="images/GrayStripe.gif" width="104" height="27" alt="" border="0" /></td>
                    					<td><img src="images/navTopSpacer.gif" width="200" height="27" alt=""></td>
                    				</tr>
                    			</table>
                    			<table width="760" cellpadding="0" cellspacing="0" border="0">
                    				<tr>
                    					<td><a href="createAppointment.aspx" onmouseover="setImage('btn8',btn8on)" onmouseout="setImage('btn8',btn8off)"><img src="images/btnRequest_off.gif" width="161" height="45" alt="Request An Appointment"
                    								border="0" name="btn8"></a></td>
                    					<td><a href="viewAppointments.aspx" onmouseover="setImage('btn6',btn6on)" onmouseout="setImage('btn6',btn6off)"><img src="images/btnView_off.gif" width="188" height="45" alt="View My Current Appointments"
                    								border="0" name="btn6"></a></td>
                    					<td><a href="customers.aspx" onmouseover="setImage('btn5',btn5on)" onmouseout="setImage('btn5',btn5off)"><img src="images/btnAdd_off.gif" width="217" height="45" alt="Add web scheduling for a customer"
                    								border="0" name="btn5"></a></td>
                    					<td><a href="faq.aspx" onmouseover="setImage('btn7',btn7on)" onmouseout="setImage('btn7',btn7off)"><img src="images/btnHow_off.gif" width="194" height="45" alt="How do I use web scheduling?"
                    								border="0" name="btn7"></a></td>
                    				</tr>
                    			</table>
                    		</td>
                    	</tr>
                    	<tr>
                    		<td valign="top"><img src="images/headerCreateAppointment.gif" width="760" height="40" alt="Create An Appointment For A New Load"><BR>
                    			<IMG height="35" alt="Request Received" src="images/orderProcessStep4.gif" width="760"></td>
                    	</tr>
                    </table>
                    -->
                <table cellSpacing="0" cellPadding="0" border="0" width="100%" bgcolor="#ffffff">
                    <tr>
                        <td valign="top">
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td width="760">
                                        <table width="760" cellpadding="0" cellspacing="0" border="0" class="bodySpacingText">
                                            <tr>
                                                <td valign="top">
                                                    <p>
                                                        <span id="ManualText">
                                                        <b>Your request has been placed in the appointment queue.<br>
                                                        Once your appointment has been scheduled, you will be notified by
                                                        email of the appointment time.</b><br>
                                                        </span>
                                                    <P><B>Appointment Summary</B></P>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="760" cellpadding="0" cellspacing="0" border="0" class="bodySpacingText">
                                            <tr>
                                                <td valign="top">
                                                    <table class="bodySpacingText" cellspacing="0" rules="all" border="1" id="AppInfo" style="width:760px;border-collapse:collapse;">
                                                        <tr>
                                                            <td>&nbsp;</td>
                                                        </tr>
                                                        <tr align="center" valign="middle">
                                                            <td>
                                                                <table width="760" cellpadding="0" cellspacing="0" border="0" class="bodySpacingText">
                                                                    <tr>
                                                                        <td>
                                                                            <p>Company</p>
                                                                        </td>
                                                                        <td>
                                                                            <p>Ben E. Keith Foods - Oklahoma City</p>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p>Company Contact</p>
                                                                        </td>
                                                                        <td>
                                                                            <p>Kerry Purcell;(*************;<a href='mailto:<EMAIL>'><EMAIL></a></p>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td><b>
                                                                            <span id="AppInfo__ctl2_TimeLabel">Requested Appointment Date</span></b>
                                                                        </td>
                                                                        <td>
                                                                            <span id="AppInfo__ctl2_TimeVal">Monday, June 30, 2025</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            <span id="AppInfo__ctl2_Lumper" style="color:Blue;font-weight:bold;"></span>&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            <span id="AppInfo__ctl2_DeliveryType" style="color:Blue;font-weight:bold;">Appointment Type: Live</span>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>&nbsp;</td>
                                                                        <td>
                                                                            <span id="AppInfo__ctl2_ExpArrivalDate" style="color:Blue;"></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            <span id="AppInfo__ctl2_ExpArrivalTime" style="color:Blue;"></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            <span id="AppInfo__ctl2_Trailer" style="color:Blue;"></span>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="760" cellpadding="0" cellspacing="0" border="0" class="bodySpacingText">
                                            <tr>
                                                <td valign="top">
                                                    <table class="bodySpacingText" cellspacing="0" rules="all" border="1" id="OrderList" style="width:760px;border-collapse:collapse;">
                                                        <tr class="bodySpacingText" align="center" valign="middle">
                                                            <td>PO #</td>
                                                            <td>Order Date</td>
                                                            <td>Due Date</td>
                                                            <td>Cases</td>
                                                            <td>Weight</td>
                                                            <td>Pallets</td>
                                                            <td>Cube</td>
                                                            <td>Vendor</td>
                                                            <td>Origin (City, State)</td>
                                                            <td>Pallet Type</td>
                                                        </tr>
                                                        <tr class="bodySpacingText" align="center" valign="middle">
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderNum">620967</span>
                                                                <input name="OrderList:_ctl2:ErrorCode" type="hidden" id="OrderList__ctl2_ErrorCode" value="1" />
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderDate">6/11/2025</span>
                                                                <span id="OrderList__ctl2_ErrorText"></span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_DueDate">6/25/2025</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderedCases">1,481.00</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderedWeight">21,255.00</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderedPallets">38.00</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_OrderedCube">2,518.00</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_VendorName">LINEAGE REDISTRIBUTION / LARGE MOVE</span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_Origin"></span>
                                                            </td>
                                                            <td>
                                                                <span id="OrderList__ctl2_PalletType">Not Assigned</span>
                                                            </td>
                                                        </tr>
                                                        <tr class="bodySpacingText" align="center" valign="middle">
                                                            <td>
                                                            </td>
                                                            <td>
                                                            </td>
                                                            <td>
                                                            </td>
                                                            <td>
                                                                Total
                                                                1,481.00
                                                            </td>
                                                            <td>
                                                                Total
                                                                21,255.00
                                                            </td>
                                                            <td>
                                                                Total
                                                                38.00
                                                            </td>
                                                            <td>
                                                                Total
                                                                2,518.00
                                                            </td>
                                                            <td>
                                                            </td>
                                                            <td>
                                                            </td>
                                                            <td>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td width="3%">&nbsp;&nbsp;</td>
                                    <td valign="top" height="100%" style="width: 226px">
                                        <br />
                                        <table width="90%" height="200px" cellpadding="8" cellspacing="0" border="0" class="bodySpacingText">
                                            <tr>
                                                <td>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <div class="bodySpacingText">To view your current requests, click 'View My Current
                                Appointments'
                            </div>
                            <br>
                            <p><input type="button" onclick="window.print()" value="Print">&nbsp; <input type="button" onclick="createNew()" value="Request An Appointment">
                            </p>
                            <!--
                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                	<tr>
                                		<td class="footTopBack"><img src="home/footerTop.gif" width="760" height="39"></td>
                                	</tr>
                                	<tr>
                                		<td valign="top" bgcolor="#000000">
                                			<table width="760" cellpadding="0" cellspacing="0" border="0" bgcolor="#000000">
                                				<tr>
                                					<td align="center"><br>
                                						<p class="footer">
                                							<a href="mailto:<EMAIL>?subject=Feedback" class="special">Feedback</a>
                                							| <a href="account.aspx" class="special">My Account</a>
                                							| <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a><br>
                                							<br>
                                						</p>
                                						<p class="footer"><a href="customers.aspx" class="special">Add Web Scheduling For A
                                								Company</a> | <a href="viewAppointments.aspx" class="special">View My Current
                                								Appointments</a> | <a href="faq.aspx" class="special">How Do I Use Web
                                								Scheduling?</a><br>
                                							<br>
                                						</p>
                                					</td>
                                				</tr>
                                			</table>
                                		</td>
                                	</tr>
                                	<tr>
                                		<td class="footBtmBack"><img src="home/footerBottom.gif" width="760" height="43"></td>
                                	</tr>
                                </table>
                                -->
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <!-- GREENSCREEN -->
        <footer class="color_0 background">
            Copyright &copy; 2021 NCR Corp. All Rights Reserved |
            <a href="mailto:<EMAIL>?subject=Feedback" class="special">Feedback</a> |
            <a href="account.aspx" class="special">My Account</a> <!-- | -->
            <!-- <a href="newFeature.aspx" class="special" target="_blank"><font color=red>What's New?</font></a> -->
        </footer>
    </body>
</HTML>