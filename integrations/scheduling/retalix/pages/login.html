<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <title>NCR Web Scheduling & Tendering</title>
        <link href="site_style.css" type="text/css" rel="stylesheet" />
        <script type="text/javascript">
            function SetTimeZoneOffset() {
            	// create Date object for current location
            	d = new Date();
            	localOffset = d.getTimezoneOffset() / -60.0;
            	//document.cookie = "LocalOffsetHours=" + localOffset + ";path=/";
            	document.getElementById("hiddenTimeZone").value = localOffset;
            	return;
            }
        </script>
        <!-- GREENSCREEN -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <link type="text/css" href="greenscreen/css/main.css" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700,400italic,100,100italic,300,300italic,700italic,900italic,900&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
        <script src="greenscreen/javascript/jquery.min.js"></script>
        <script src="greenscreen/javascript/greenscreen.js"></script>
        <script>
            $(document).ready(function() {
            	greenscreen.init();
            	greenscreen.renderLogin();
            });
        </script>
    </head>
    <body onload="SetTimeZoneOffset();document.PageForm.Username.focus();" class="login_page" style="background-color:#ffffff;">
        <form name="PageForm" method="post" action="default.aspx" id="PageForm">
            <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwUJNTgyMTIwOTg2D2QWAgIDD2QWAgILDw8WAh4EVGV4dGVkZBgBBR5fX0NvbnRyb2xzUmVxdWlyZVBvc3RCYWNrS2V5X18WAgUFTG9naW4FCGxiRm9yZ290lujy+rsfy1EtEiw/maHhtYLxVPQ=" />
            <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="CA0B0334" />
            <input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEWBgLTrePwDwLj2/DDDAKPruq2CAKbnbKyBwLvz/GACgLLkLOVB3g7oLVzUqNC1DuI6UiCLxO2Ki3V" />
            <input type="hidden" name="hiddenTimeZone" id="hiddenTimeZone" />
            <div class="app-login-screen">
                <!--<div class="post-it-note" style="background-color:rebeccapurple;color:white;font-weight:bold;position:absolute;left:800px;top:400px;">Notice Alert<br />This site has changed the email <NAME_EMAIL> to <EMAIL></div> -->
                <div class="app-login-container">
                    <div class="app-logo">
                        <img src="greenscreen/images/logo.png">
                        <div class="app-logo-slogan">Power Traffic</div>
                    </div>
                    <app-login-form id="app-login-form">
                        <div class="app-login-form-container">
                            <div class="app-login-form form-fields">
                                <div class="field-container" id="UsernameDiv">
                                    <div class="field-title">Username</div>
                                    <div class="field">
                                        <input name="Username" type="text" maxlength="100" id="Username" />
                                        <!--<input type="text" name="username" id="username" ng-model="AppLoginForm.username" placeholder="Username (test)" title="Use &quot;test&quot;"required>-->
                                    </div>
                                </div>
                                <div class="field-container" id="PasswordDiv">
                                    <div class="field-title">Password</div>
                                    <div class="field">
                                        <input name="Pass" type="password" maxlength="17" id="Pass" />
                                        <!--<input type="password" name="password" id="password" ng-model="AppLoginForm.password" placeholder="Password (123456)" title="Use &quot;123456&quot;" required>-->
                                    </div>
                                </div>
                                <div class="actions-container">
                                    <div class="app-button-native" id="LoginDiv">
                                        <!--<button>Sign In</button>-->
                                        <input type="image" name="Login" id="Login" src="Images/login2.gif" alt="Enter" border="0" />
                                        <!--
                                            <input type="image" name="lbForgot" id="lbForgot" src="images/reset.gif" alt="RESET" border="0" />
                                            -->
                                    </div>
                                    <div class="app-forgot-password" id="ForgotPasswordDiv">
                                        <span>Forgot your password?</span><br><a href="forgotPassword.aspx">Please reset it here</a>
                                        <br><br>
                                        <span>Forgot your username?<br>Please contact your customer/vendor</span>
                                    </div>
                                    <div class="app-register" id="NewUserDiv">
                                        <a href="register.aspx">New user? Click here</a>
                                    </div>
                                    <div><span id="lblInfo" style="color:#B50010;font-weight:normal;"></span></div>
                                </div>
                            </div>
                        </div>
                    </app-login-form>
                </div>
            </div>
            <div id="lblCompatibilityViewWarning" style="position:relative; margin-top:225px; text-align:center; color:white; background-color:#B50010; font-size:medium; display:none; width:540px;" Font-Bold="false">
                <br />You are running Internet Explorer in Compatibility View.
                <br />To use NCRpowertraffic.com, turn off compatibility view.
                <br />&nbsp;<br />
            </div>
        </form>
    </body>
    <script type="text/javascript">
        var agentStr = navigator.userAgent;
        if ((agentStr.indexOf('Trident/7.0') > -1) ||
        	(agentStr.indexOf('Trident/6.0') > -1) ||
        	(agentStr.indexOf('Trident/5.0') > -1) ||
        	(agentStr.indexOf('Trident/4.0') > -1)) { // if IE

          if (agentStr.indexOf('MSIE 7.0') > -1) { // if Compatibility View is on

               // hide controls
               if (document.getElementById('UsernameDiv')) {
                 document.getElementById('UsernameDiv').style.display = 'none';
               }
               if (document.getElementById('PasswordDiv')) {
                 document.getElementById('PasswordDiv').style.display = 'none';
               }
               if (document.getElementById('LoginDiv')) {
                 document.getElementById('LoginDiv').style.display = 'none';
               }
               if (document.getElementById('ForgotPasswordDiv')) {
                 document.getElementById('ForgotPasswordDiv').style.display = 'none';
               }
               if (document.getElementById('NewUserDiv')) {
                 document.getElementById('NewUserDiv').style.display = 'none';
               }

               // show warning message
        	if (document.getElementById('lblCompatibilityViewWarning')) {
                 var warning = document.getElementById('lblCompatibilityViewWarning').style;
                 if (warning.display == 'none') {
                   warning.display = 'block';
                 }
               }
             }
        }
    </script>
</html>