"""RetailLink-specific data models."""

from typing import Optional

from pydantic import BaseModel

from integrations.scheduling.models import (
    SchedulingBaseRequest,
    SchedulingActionType,
    SchedulingPlatform,
    Credentials,
)


class RetailLinkAppointmentData(BaseModel):
    """RetailLink appointment data."""

    appointmentId: Optional[str] = ""
    appointmentTime: str
    carrierId: str
    dock: Optional[str] = ""
    duration: int
    loadId: Optional[str] = ""
    notes: Optional[str] = ""
    status: Optional[str] = ""


class RetailLinkLoginRequest(SchedulingBaseRequest):
    """RetailLink login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    credentials: Credentials


class RetailLinkGetLoadTypesRequest(SchedulingBaseRequest):
    """RetailLink-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class RetailLinkGetOpenSlotsRequest(SchedulingBaseRequest):
    """RetailLink-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class RetailLinkGetWarehouseRequest(SchedulingBaseRequest):
    """RetailLink-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class RetailLinkCancelAppointmentRequest(SchedulingBaseRequest):
    """RetailLink-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    appointmentId: str
    reason: Optional[str] = ""


class RetailLinkGetAppointmentRequest(SchedulingBaseRequest):
    """RetailLink-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    appointment: RetailLinkAppointmentData


class RetailLinkMakeAppointmentRequest(SchedulingBaseRequest):
    """RetailLink-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    appointment: RetailLinkAppointmentData


class RetailLinkUpdateAppointmentRequest(SchedulingBaseRequest):
    """RetailLink-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.RETAILLINK

    appointment: RetailLinkAppointmentData
    appointmentId: str
