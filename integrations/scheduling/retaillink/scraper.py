"""RetailLink Selenium scheduling implementation."""

from typing import Optional, Type, Tuple
import functools

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

from models.base import BaseResponse


from integrations.scheduling.models import (
    Credentials,
    Appointment,
    LoginResponse,
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    CancelAppointmentResponse,
    GetAppointmentResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
)


from integrations.scheduling.retaillink.models import (
    RetailLinkLoginRequest,
    RetailLinkGetWarehouseRequest,
    RetailLinkGetOpenSlotsRequest,
    RetailLinkGetLoadTypesRequest,
    RetailLinkCancelAppointmentRequest,
    RetailLinkGetAppointmentRequest,
    RetailLinkMakeAppointmentRequest,
    RetailLinkUpdateAppointmentRequest,
)

# RetailLink element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.CSS_SELECTOR, "input[data-automation-id='uname']"),
    "password_field": (By.CSS_SELECTOR, "input[data-automation-id='pwd']"),
    "login_button": (By.CSS_SELECTOR, "button[data-automation-id='loginBtn']"),
    "login_error": (
        By.CSS_SELECTOR,
        "div[data-automation-id='alert-element'] .alert__message___wvlXn",
    ),
    "logged_in_indicator": (By.ID, "<TBD>"),
}


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting RetailLink login process")

        wait_for_element(driver, LOCATORS["username_field"])
        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        logger.debug(f"Current RetailLink URL: {driver.current_url}")
        logger.debug(f"RetailLink Page Source:\n{driver.page_source}\n")

        try:
            if is_element_present(driver, LOCATORS["login_error"], timeout=2):
                error_msg = get_element_text(driver, LOCATORS["login_error"])
                logger.error(f"RetailLink Login failed: {error_msg}")
                return False, error_msg
        except Exception:
            # Silently continue if no error message found
            pass

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"RetailLink Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("retaillink", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            driver.get(PLATFORM_URLS["retaillink"])

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["retaillink"])

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(PLATFORM_URLS["retaillink"])

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("retaillink", credentials.username, new_cookies)
            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: RetailLinkLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to RetailLink.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: RetailLinkGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from RetailLink.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetOpenSlotsResponse)
def get_open_slots(
    request: RetailLinkGetOpenSlotsRequest, driver: WebDriver
) -> GetOpenSlotsResponse:
    """Get open appointment slots from RetailLink.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        return GetOpenSlotsResponse(
            success=False,
            message="Not implemented yet",
            errors=["Open slots fetching not implemented"],
        )
    except Exception as e:
        return GetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: RetailLinkGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from RetailLink.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: RetailLinkCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in RetailLink.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        return CancelAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment canceling not implemented"],
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetAppointmentResponse)
def get_appointment(
    request: RetailLinkGetAppointmentRequest, driver: WebDriver
) -> GetAppointmentResponse:
    """Get an appointment from RetailLink.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        return GetAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment fetching not implemented"],
        )
    except Exception as e:
        return GetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(MakeAppointmentResponse)
def make_appointment(
    request: RetailLinkMakeAppointmentRequest, driver: WebDriver
) -> MakeAppointmentResponse:
    """Create an appointment in RetailLink.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        return MakeAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment making not implemented"],
        )
    except Exception as e:
        return MakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(UpdateAppointmentResponse)
def update_appointment(
    request: RetailLinkUpdateAppointmentRequest, driver: WebDriver
) -> UpdateAppointmentResponse:
    """Update an appointment in RetailLink.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        return UpdateAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment updating not implemented"],
        )
    except Exception as e:
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )
