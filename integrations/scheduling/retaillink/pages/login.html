<!doctype html>
<html lang="en" class="TridactylThemeMidnight">
  <head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <title>Retail Link</title>
    <link
      rel="shortcut icon"
      type="image/png"
      href="https://i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6620b9135551c47b375531dcb5e0daa2.png"
    />
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link href="https://i5.walmartimages.com/" rel="dns-prefetch" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="index,follow" />
    <meta content="Retail Link login page to access Retail Link portal" />
    <script>
      window._wml = { defaultCdnHost: "i5.walmartimages.com" };
    </script>
    <script>
      (function (init) {
        if (!window._wml) window._wml = init || {};
        var cdn = window._wml.cdn || {};
        window._wml.cdn = cdn;
        cdn.update = function (data) {
          var md = data.md;
          for (var k in md) {
            if (this.md[k]) {
              console.error("CDN map already exist:", k);
            } else {
              this.md[k] = md[k];
            }
          }
        };
        cdn.map = function (f) {
          for (var k in this.md) {
            if (k.indexOf(f) >= 0) {
              return this.md[k];
            }
          }
          console.error("CDN map not found:", f);
        };
        if (!cdn.md) cdn.md = {};
      })();
      (function () {
        var native = window.performance || {};
        var wml = (window._wml = window._wml || {});
        var entries = [],
          start,
          now;
        if (native.now) {
          wml.hasPerfNow = function () {
            return true;
          };
          start = native.now();
          now = native.now.bind(native);
        } else {
          wml.hasPerfNow = function () {
            return false;
          };
          start = new Date().getTime();
          now = function now() {
            return new Date().getTime() - start;
          };
        }
        function newEntry(duration, type, name, startTime) {
          return {
            duration: duration,
            entryType: type,
            name: name,
            startTime: startTime,
          };
        }
        function findEntry(type, value, count) {
          var result = [];
          var found = 0;
          count = isNaN(count) ? entries.length : count;
          for (var i = 0; i < entries.length && found < count; i++) {
            if (entries[i][type] === value) {
              result.push(entries[i]);
              found++;
            }
          }
          return result;
        }
        function findEntriesByName(name, count) {
          return findEntry("name", name, count);
        }
        function findEntriesByType(type, count) {
          return findEntry("entryType", type, count);
        }
        function getNative(x) {
          return (
            x.mark &&
            x.measure &&
            x.getEntries &&
            x.getEntriesByType &&
            x.getEntriesByName &&
            x
          );
        }
        wml.perf = getNative(native) || {
          entries: entries,
          start: start,
          now: now,
          mark: function mark(name) {
            entries.push(newEntry(0, "mark", name, now()));
          },
          measure: function measure(name, mark1, mark2) {
            if (!name || !mark1 || !mark2) return;
            var x1 = findEntriesByName(mark1, 1)[0],
              x2 = findEntriesByName(mark2, 1)[0];
            if (x1 && x2) {
              entries.push(
                newEntry(
                  x2.startTime - x1.startTime,
                  "measure",
                  name,
                  x1.startTime,
                ),
              );
            }
          },
          getEntries: function getEntries() {
            return entries;
          },
          getEntriesByType: function getEntriesByType(type) {
            return findEntriesByType(type);
          },
          getEntriesByName: function getEntriesByName(name) {
            return findEntriesByName(name);
          },
          _feN: findEntriesByName,
          _feT: findEntriesByType,
        };
        wml.perf.mark("index-start");
      })();
    </script>
    <script id="tb-djs-wml-data" type="application/json">
      {
        "correlationId": "d226841a-007-195fd269c2dd3a,d226841a-007-195fd269c2d7a3,d226841a-007-195fd269c2d7a3",
        "ccm": {
          "falcon": {
            "clientConfig": {
              "rlsetPwdUrl": "ERROR_REMOVED_BY_DYNAMIC_DATArllogin.wal-mart.com/rl_security/setPassword.aspx",
              "enableMixPanel": "",
              "falconOptimize": "{\n\t\"rolloutEnabled\": true,\n\t\"rolloutPercent\": 1,\n\t\"cookieName\": \"selfReg\",\n\t\"variation\": \"ERROR_REMOVED_BY_DYNAMIC_DATArllogin.wal-mart.com/ElectronicAgreement/#/\",\n\t\"control\": \"/register\"\n}",
              "validateLogoutRedirect": "true",
              "mfaAppProvisionEnabled": "true",
              "enableSecurityQnA": "true",
              "mfaAppHelpUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAsupplierhelp.walmart.com/s/guide?article=*********#Symantic_App",
              "useCCMClient": "false",
              "checkRedirectURLDomain": "true",
              "getAgreementFromCCM": "false",
              "validateDomainRegex": "^http(s?):\\/\\/(([\\w\\d]+\\.){1}|([\\w\\d]+\\.){2}|([\\w\\d]+\\.){3})wal-mart\\.com(.*)$",
              "enableSecurityQnAUserList": "[ \"<EMAIL>\", \"<EMAIL>\"]",
              "isLoginPgViewNeeded": "false",
              "ssoMfaCountryCodes": "[\n  {\n    \"isoCode\": \"+1\",\n    \"isoCountryCode\": \"US\",\n    \"isoCountryName\": \"United States\",\n    \"maxLength\": 10,\n    \"minLength\": 10\n  },\n  {\n    \"isoCode\": \"+1\",\n    \"isoCountryCode\": \"CA\",\n    \"isoCountryName\": \"Canada\",\n    \"maxLength\": 10,\n    \"minLength\": 10\n  },\n  {\n    \"isoCode\": \"+52\",\n    \"isoCountryCode\": \"MX\",\n    \"isoCountryName\": \"Mexico\",\n    \"maxLength\": 10,\n    \"minLength\": 10\n  },\n  {\n    \"isoCode\": \"+56\",\n    \"isoCountryCode\": \"CL\",\n    \"isoCountryName\": \"Chile\",\n    \"maxLength\": 9,\n    \"minLength\": 9\n  }\n]",
              "turnOnUserIdApi": "true",
              "hidePwdReset": "true",
              "showInfoBanner": "false",
              "mixPanelToken": "d2304ba2d61180df521ddf751fcb59e5",
              "disableCaptcha": "false",
              "turnOnUserTypeApi": "true",
              "resetPwdOTPLength": "5",
              "serverLogClientList": "[]",
              "rlCreateAccountUrl": "ERROR_REMOVED_BY_DYNAMIC_DATArllogin.wal-mart.com/ElectronicAgreement/#/",
              "mfaHelpGuideUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAsupplierhelp.walmart.com/s/guide?article=*********",
              "rlClientId": "1b902bda-c72f-4c5a-8e9c-3630e679399f",
              "tmxConfig": "{\n  \"seller\": {\n    \"tmxUrl\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfw/4ff9c6c9-d9a0/k2-_66e7fff8-b096-45d0-8ec5-70e5787386e9.v1.js\",\n    \"orgId\": \"hgy2n0ks\",\n    \"profilingDomain\": \"ofxmryjd.walmart.com\",\n    \"loadTimeout\": 3000\n  }\n}",
              "supportTicketLink": "ERROR_REMOVED_BY_DYNAMIC_DATAsellerhelp.walmart.com/s/contact",
              "playStoreUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAplay.google.com/store/apps/details?id=com.verisign.mvip.main&hl=en",
              "callUserTypeForRL": "true",
              "migrationHelpGuideUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAsupplierhelp.walmart.com/s/guide?channel=Stores&article=*********",
              "accountRecoveryDocuments": "[\"Proof of ownership\", \"Proof of address\", \"Business Owner Identity Documents\"]",
              "hideForgotUserId": "false",
              "rlAgreementUrls": "{\n  \"en\": {\n    \"html\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfwrs/3d0839e4-92b2/k2-_993a2b73-0f9b-44df-9898-102e6c3851cc.v4.html\",\n    \"pdf\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfwrs/3d0839e4-d3a5/k2-_422aa41b-9d60-4dfa-b343-371dbd0ceaf0.v3.pdf\"\n  }\n}",
              "rlForgotUserIdUrl": "ERROR_REMOVED_BY_DYNAMIC_DATArllogin.wal-mart.com/ElectronicAgreement/#/Forgetuserid",
              "blockUnsupportedBrowsers": "false",
              "checkRedirectDomain": "true",
              "testClient": "testClient355",
              "hideAccessRetailLinkBtn": "false",
              "tmxRolloutPercent": "1",
              "partnerLevelConfig": "{\n  \"SELLER\": {\n    \"loginHelpLink\": \"ERROR_REMOVED_BY_DYNAMIC_DATAsellerhelp.walmart.com/seller/s/guide?language=en_US&article=*********\",\n    \"showAccountRecovery\": true,\n    \"showBackToLogin\": false,\n    \"preferredMfaChannel\": \"TEXT\",\n    \"allowedMfaChannels\": [\n      \"EMAIL\",\n      \"TOTP\",\n      \"TEXT\"\n    ]\n  },\n  \"SOLUTION_PROVIDER\": {\n    \"loginHelpLink\": \"ERROR_REMOVED_BY_DYNAMIC_DATAdeveloper.walmart.com/channelpartnerportal/ui/support\",\n    \"showAccountRecovery\": false,\n    \"showBackToLogin\": true,\n    \"preferredMfaChannel\": \"EMAIL\",\n    \"allowedMfaChannels\": [\n      \"EMAIL\",\n      \"TOTP\"\n    ]\n  },\n  \"WLS\": {\n    \"showAccountRecovery\": false,\n    \"showBackToLogin\": true,\n    \"preferredMfaChannel\": \"TEXT\",\n    \"allowedMfaChannels\": [\n      \"EMAIL\",\n      \"TOTP\",\n      \"TEXT\"\n    ]\n  }\n}",
              "mfaVipUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAvip.symantec.com/",
              "loginExternalUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAretaillink.login.wal-mart.com",
              "hideEditRLToEmail": "true",
              "selfRegistrationEnabled": "false",
              "agreementId": "85a556c1-88f7-4ba0-a756-5fe06aa361f4",
              "troubleLoggingLink": "ERROR_REMOVED_BY_DYNAMIC_DATAsellerhelp.walmart.com/seller/s/guide?article=*********",
              "validDomains": "[\"wal-mart.com\", \"walmart.com\", \"salesforce.com\", \"samsclub.com\", \"riversand.com\", \"ibmcloud.com\", \"mercurygate.net\"]",
              "appStoreUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAapps.apple.com/app/vip-access-for-iphone/id307658513",
              "coiUrls": "{\n  \"en\": {\n    \"html\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfwrs/3d0839e4-c252/k2-_9f90916d-60f2-465e-aab1-43f176a73570.v3.html\",\n    \"pdf\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfwrs/3d0839e4-51c2/k2-_39f8d4e2-0661-43b7-9e72-ba738fb4ccba.v1.pdf\"\n  }\n}",
              "givePrecedenceToExpPwd": "true",
              "mfaResendTimer": "60",
              "privacyPolicyUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAcorporate.walmart.com/privacy-security/walmart-supplier-privacy-policy",
              "topBannerInfo": "{\n \"type\": \"INFO\",\n \"message\": \"Multi-Factor Authentication (MFA) will be enabled for all new and existing users starting 27 January 2025. Please refer to the <a target='_blank' href='ERROR_REMOVED_BY_DYNAMIC_DATAsupplierhelp.walmart.com/s/guide?article=*********'>article</a> for help with registration.\"\n}",
              "validateSSOLogout": "true",
              "token": "705567de98ce3827b0c4af0012dff66d",
              "ui.token": "705567de98ce3827b0c4af0012dff66d",
              "termsURL": "{\n  \"en\": {\n    \"html\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfw/63fd9f59-8ea9/k2-_68208f4e-c91b-4b72-bf0b-8cfeb360d5ac.v2.html\",\n \"pdf\": \"ERROR_REMOVED_BY_DYNAMIC_DATAi5.walmartimages.com/dfw/63fd9f59-69e7/k2-_3ce8f0b2-23b9-4c95-8cf8-2925a1210a83.v2.pdf\"\n  }\n}",
              "adminAgreementId": "4c468971-05f5-4dc7-9e7a-76060e6971e2",
              "rlCreateAssociateAccountUrl": "ERROR_REMOVED_BY_DYNAMIC_DATAwalmartglobal.service-now.com/wm_sp?id=sc_cat_item&sys_id=1a45e61e1bb18d50dae98621604bcb49&parent_id=4fc6e8efdb03efc0cb717f698c96194d",
              "turnOnIntl": "true",
              "addUserIdToSetPwd": "true",
              "resetPwdCaptchaLimit": "5",
              "iamMFAConfig": "{\n  \"otpExpiry\": 600,\n  \"resendTimeout\": 30,\n  \"failedOtpThreshold\": 5,\n  \"accountLockTimeout\": 24,\n  \"validOtpRegex\": \"^[0-9]{1,10}$\",\n  \"failedTotpThreshold\": 10\n}"
            }
          }
        },
        "uiConfig": { "webappPrefix": "" },
        "envInfo": {},
        "expoCookies": {}
      }
    </script>
    <script>
      !(function () {
        var e = {},
          n = 0,
          o = function (e) {
            var n = document.getElementsByTagName("script")[0];
            n.parentNode.insertBefore(e, n);
          },
          t = function (t, r, a) {
            var c;
            r &&
              "function" != typeof r &&
              ((a = r.context || a), (c = r.setup), (r = r.callback));
            var d,
              l,
              i = document.createElement("script"),
              f = !1,
              u = function () {
                f || ((f = !0), l(), r && r.call(a, d));
              },
              s = function () {
                (d = new Error(t || "EMPTY")), u();
              };
            if (!i.readyState || "async" in i)
              (l = function () {
                i.onload = i.onerror = null;
              }),
                (i.onerror = s),
                (i.onload = u),
                (i.async = !0),
                (i.charset = "utf-8"),
                c && c.call(a, i),
                (i.src = t),
                o(i);
            else {
              var y = n++,
                p = { loaded: !0, complete: !0 },
                m = !1;
              (l = function () {
                (i.onreadystatechange = i.onerror = null), (e[y] = void 0);
              }),
                (i.onreadystatechange = function () {
                  var e = i.readyState;
                  if (!d)
                    return (
                      !m && p[e] && ((m = !0), o(i)),
                      "loaded" === e && (i.children, "loading" === i.readyState)
                        ? s()
                        : void ("complete" === i.readyState && u())
                    );
                }),
                (i.onerror = s),
                (e[y] = i),
                c && c.call(a, i),
                (i.src = t);
            }
          };
        "object" == typeof exports && "object" == typeof module
          ? (module.exports = t)
          : "function" == typeof define && define.amd
            ? define([], function () {
                return t;
              })
            : (window._lload = t);
      })();
      (function () {
        var Dyn = (window._Dyn = function (selector) {
          try {
            this._data = JSON.parse(
              (document.querySelector(selector) || {}).innerHTML,
            );
          } catch (err) {
            this._data = {};
          }
        });
        Dyn.prototype.get = function (props) {
          props = props ? props.split(".") : [];
          var data = this._data;
          for (var x = 0; x < props.length; x++) {
            if (!data) return data;
            data = data[props[x]];
          }
          return data;
        };
        Dyn._cache = {};
        Dyn.create = function (elementId) {
          return (Dyn._cache[elementId] =
            Dyn._cache[elementId] || new Dyn(elementId));
        };
      })();
      (function (data, nameSpace) {
        var wml = window._wml;
        var sys = wml;
        if (!nameSpace) {
          nameSpace = "";
        } else {
          sys = wml["sys" + nameSpace] = {};
        }
        sys.correlationId = data.correlationId;
        sys.jwt = data.jwt;
        sys.envInfo = data.envInfo;
        wml["config" + nameSpace] = {
          ccm: data.ccm,
          ui: data.uiConfig,
          expoCookies: data.expoCookies,
        };
      })(window._Dyn.create("#tb-djs-wml-data").get(), "");
    </script>
    <script>
      window._wml.cdn.update({
        md: {
          "07ebb2553f3d98b9d45b072f8f4c3e8b.jpg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/07ebb2553f3d98b9d45b072f8f4c3e8b.jpg",
          "0fc477d5795185926e56c5419f85e479.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/0fc477d5795185926e56c5419f85e479.ttf",
          "16d8f643d4f1b65d73fc027d7426a25d.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/16d8f643d4f1b65d73fc027d7426a25d.svg",
          "1812c8940a09b7e8de827e9e19a63ecb.eot":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/1812c8940a09b7e8de827e9e19a63ecb.eot",
          "18362ffd3e6ea62e6e5b87b9bf29dd21.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/18362ffd3e6ea62e6e5b87b9bf29dd21.svg",
          "227f252257338f1662b125fba3928989.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/227f252257338f1662b125fba3928989.svg",
          "242c2c0ce882c15debe353839784f6f5.eot":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/242c2c0ce882c15debe353839784f6f5.eot",
          "284babb6d7c1c5ac63d8b59773f97f7e.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/284babb6d7c1c5ac63d8b59773f97f7e.svg",
          "3355023a8c95c3e4c729f72e4ebda731.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/3355023a8c95c3e4c729f72e4ebda731.svg",
          "44ef2bdebfabe79e54ab14238c5c6fc9.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/44ef2bdebfabe79e54ab14238c5c6fc9.svg",
          "47e23c799a6935d4a27b84e257a49c41.woff2":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/47e23c799a6935d4a27b84e257a49c41.woff2",
          "4f4eb24c2f05e3d1f74dd37ca08de8b0.eot":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/4f4eb24c2f05e3d1f74dd37ca08de8b0.eot",
          "5006a0af77421dde6448789bab7e8e6a.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/5006a0af77421dde6448789bab7e8e6a.svg",
          "55b8acfc0a102030c5ca967f6a3a15de.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/55b8acfc0a102030c5ca967f6a3a15de.svg",
          "5d77f1013a79195002a2d2bb2262642f.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/5d77f1013a79195002a2d2bb2262642f.svg",
          "5ec5639ac28de988904349f72f32ee32.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/5ec5639ac28de988904349f72f32ee32.svg",
          "613a91c65d23a30f780c91b18b2ebe1b.woff":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/613a91c65d23a30f780c91b18b2ebe1b.woff",
          "61c0c93a0d48b3d52a70e13d976e0dae.png":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/61c0c93a0d48b3d52a70e13d976e0dae.png",
          "6560fefbed8fc6693665874ad7aa5565.woff":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6560fefbed8fc6693665874ad7aa5565.woff",
          "65bb0a158ee1967292ee4d11079d45ae.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/65bb0a158ee1967292ee4d11079d45ae.ttf",
          "6620b9135551c47b375531dcb5e0daa2.png":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6620b9135551c47b375531dcb5e0daa2.png",
          "685f257ad154bc939f264015f132e391.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/685f257ad154bc939f264015f132e391.svg",
          "6ca1155bb6fc45f9559b37ce0a171d48.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6ca1155bb6fc45f9559b37ce0a171d48.svg",
          "6f47bcfc065790f02ed3cb8b51bef56f.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6f47bcfc065790f02ed3cb8b51bef56f.ttf",
          "6f957509b11d5ba9cfa1c4c9a33f0bab.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/6f957509b11d5ba9cfa1c4c9a33f0bab.svg",
          "73326ee81fee6423c9884465cba7e0f4.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/73326ee81fee6423c9884465cba7e0f4.svg",
          "74c03eafe5c45fc513859bd5fd65a442.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/74c03eafe5c45fc513859bd5fd65a442.svg",
          "76587a3d89b0c6760548d23d92fbd8c1.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/76587a3d89b0c6760548d23d92fbd8c1.ttf",
          "792e78cad1a3dccb29389202458fedcf.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/792e78cad1a3dccb29389202458fedcf.svg",
          "7b5924a33eced7bcd05ba9406e6b6900.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/7b5924a33eced7bcd05ba9406e6b6900.svg",
          "7d7305f4f59ce4ec70337b3838f5efca.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/7d7305f4f59ce4ec70337b3838f5efca.svg",
          "7ef50d19f3c54d53d94ed3ca6845b07e.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/7ef50d19f3c54d53d94ed3ca6845b07e.svg",
          "801d665da4b5bb8f7f49e679f1850b73.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/801d665da4b5bb8f7f49e679f1850b73.svg",
          "872be8fc1dfb84ee6610579591255d17.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/872be8fc1dfb84ee6610579591255d17.svg",
          "8cdb5df1d4b2d5f871a4cf92e291e124.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/8cdb5df1d4b2d5f871a4cf92e291e124.svg",
          "9614a96d2353d7ea446a9570ad4b42a3.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/9614a96d2353d7ea446a9570ad4b42a3.svg",
          "9d5125e3550d8616b04130c2bfe6c7a0.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/9d5125e3550d8616b04130c2bfe6c7a0.svg",
          "9d810e0ecaef65cd32bd695dbe23d4d7.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/9d810e0ecaef65cd32bd695dbe23d4d7.svg",
          "9fd5bbf5a816fc7cb9ab80d59e4f8929.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/9fd5bbf5a816fc7cb9ab80d59e4f8929.svg",
          "ac91d9c2261b19a84d735ad42cddc0e5.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/ac91d9c2261b19a84d735ad42cddc0e5.svg",
          "ada3bae54cd202d11ff8f4a26fb6e5ca.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/ada3bae54cd202d11ff8f4a26fb6e5ca.svg",
          "b6f972e912e6cc263c95a3b5d16c507a.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/b6f972e912e6cc263c95a3b5d16c507a.svg",
          "bc03e9340a228bf9371431c31016cf6a.png":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/bc03e9340a228bf9371431c31016cf6a.png",
          "c6b0b9f85490a4550a8f9fe762aef10c.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/c6b0b9f85490a4550a8f9fe762aef10c.svg",
          "d4e4f511295fca495c39c5f9cc5ae714.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/d4e4f511295fca495c39c5f9cc5ae714.svg",
          "d5c33e38a31f00067cae2cc984ae2a80.png":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/d5c33e38a31f00067cae2cc984ae2a80.png",
          "e0f0be7aed60fec9dab5324cf246296b.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e0f0be7aed60fec9dab5324cf246296b.ttf",
          "e1080bdf3a63ba5a1436e79b5c82780a.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e1080bdf3a63ba5a1436e79b5c82780a.svg",
          "e3d846d2d9e7603d706f7cbb59668e33.woff":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e3d846d2d9e7603d706f7cbb59668e33.woff",
          "e52778320b968c309cf09078406d6da4.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e52778320b968c309cf09078406d6da4.ttf",
          "e6681abf5d9800bcc0d1e73258588a12.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e6681abf5d9800bcc0d1e73258588a12.ttf",
          "e9d573c6573010272f327c43eb4aa69e.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/e9d573c6573010272f327c43eb4aa69e.svg",
          "ea6ab0c01f6ae74850217402a6a56569.woff2":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/ea6ab0c01f6ae74850217402a6a56569.woff2",
          "f01472de8c4e13cfc49765619f1f8f1d.ttf":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f01472de8c4e13cfc49765619f1f8f1d.ttf",
          "f115ab8df416a87b61bcf9ab13356e52.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f115ab8df416a87b61bcf9ab13356e52.svg",
          "f2580490543d3a76fc037ba1be459a48.woff2":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f2580490543d3a76fc037ba1be459a48.woff2",
          "f2b50c1bcd610185e95639bfd4961b7a.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f2b50c1bcd610185e95639bfd4961b7a.svg",
          "f558c0ce63c5debee1d6a3892e92e61e.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f558c0ce63c5debee1d6a3892e92e61e.svg",
          "f599262c1f960a67652822f72e2529fd.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/f599262c1f960a67652822f72e2529fd.svg",
          "fbd147a7bf7bded0fde5c79020ac0700.svg":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/fbd147a7bf7bded0fde5c79020ac0700.svg",
          "main.bundle.a5f6047192ce389e918d.js":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/main.bundle.a5f6047192ce389e918d.js",
          "main.style.a5f6047192ce389e918d.css":
            "//i5.walmartimages.com/dfw/63fd9f59-a662/866c309a-df5c-4419-ab99-9b5f3f2c4299/v1/main.style.a5f6047192ce389e918d.css",
        },
      });
      (function () {
        function rewriteObjStrings(obj, o, n) {
          var rgx;
          function r(d) {
            for (var k in d) {
              if (d[k]) {
                if (typeof d[k] === "object") {
                  r(d[k]);
                } else if (typeof d[k] === "string") {
                  if (d[k].indexOf(o) >= 0) {
                    d[k] = d[k].replace(rgx, n);
                  }
                }
              }
            }
          }
          if (obj && o && n) {
            rgx = new RegExp(o, "g");
            r(obj);
          }
        }
        if (
          !window._wml ||
          !document.head ||
          !document.head.getElementsByTagName
        ) {
          return false;
        }
        var wml = window._wml;
        var x = document.head.getElementsByTagName("script");
        if (x && x.length > 0) {
          for (var i = 0; i < x.length; i++) {
            if (x[i].getAttribute("id") === "cdnHint") {
              wml.cdnHost = x[i].getAttribute("src").split("/")[2];
              break;
            }
          }
        }
        if (wml.defaultCdnHost !== wml.cdnHost) {
          rewriteObjStrings(
            window.__WML_REDUX_INITIAL_STATE__,
            wml.defaultCdnHost,
            wml.cdnHost,
          );
          rewriteObjStrings(
            window.__REACT_RESOLVER_PAYLOAD__,
            wml.defaultCdnHost,
            wml.cdnHost,
          );
          rewriteObjStrings(
            wml.cdn && wml.cdn.md,
            wml.defaultCdnHost,
            wml.cdnHost,
          );
          rewriteObjStrings(
            wml.config && wml.config.ccm,
            wml.defaultCdnHost,
            wml.cdnHost,
          );
        }
        return true;
      })();
    </script>
    <script src="login_files/init.js" async="true" defer="true"></script>
    <!--

NOTE: no request.app.seoTags - was request.app.seoAsync awaited?

-->
    <title>Untitled Electrode Web Application</title>
    <meta property="og:type" content="Website" />
    <meta
      property="og:image"
      content="https://i5.walmartimages.com/dfw/63fd9f59-49ff/k2-_52fee322-2e60-452a-bccc-1e847f452a13.v1.png"
    />
    <meta property="og:site_name" content="Walmart.com" />
    <meta property="fb:app_id" content="105223049547814" />
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:image"
      content="https://i5.walmartimages.com/dfw/63fd9f59-49ff/k2-_52fee322-2e60-452a-bccc-1e847f452a13.v1.png"
    />
    <meta property="twitter:site" content="@walmart" />
    <meta property="og:title" content="Untitled Electrode Web Application" />
    <script>
      window._wml.seoTags = (function removeURIEncoding(obj) {
        if (!obj) {
          return obj;
        }
        var decoded = {};
        for (var source in obj) {
          var data = obj[source];
          if (typeof data === "string") {
            data = decodeURIComponent(data);
          } else if (data && typeof data === "object") {
            data = removeURIEncoding(data);
          }
          decoded[source] = data;
        }
        return decoded;
      })({
        presets: {
          "og:type": "Website",
          "og:image":
            "https%3A%2F%2Fi5.walmartimages.com%2Fdfw%2F63fd9f59-49ff%2Fk2-_52fee322-2e60-452a-bccc-1e847f452a13.v1.png",
          "og:site_name": "Walmart.com",
          "fb:app_id": "105223049547814",
          "twitter:card": "summary_large_image",
          "twitter:image":
            "https%3A%2F%2Fi5.walmartimages.com%2Fdfw%2F63fd9f59-49ff%2Fk2-_52fee322-2e60-452a-bccc-1e847f452a13.v1.png",
          "twitter:site": "%40walmart",
        },
        defaults: { title: "Untitled%20Electrode%20Web%20Application" },
      });
    </script>
    <script id="cdnHint" src="login_files/ft.js" defer="defer"></script>
    <link
      rel="stylesheet"
      id="bogel-font"
      href="login_files/BogleWeb_subset.css"
    />
    <link
      rel="stylesheet"
      href="login_files/main.style.a5f6047192ce389e918d.css"
    />
    <style type="text/css">
      @media print {
        .TridactylStatusIndicator {
          display: none !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="js-content" data-tb-dyn="false">
      <div class="page-container css-0">
        <div class="logo-container">
          <div class="logoBox">
            <img
              src="login_files/61c0c93a0d48b3d52a70e13d976e0dae.png"
              class="logo"
            />
          </div>
        </div>
        <div class="main-container">
          <div class="content-container">
            <div class="tab-content">
              <div class="login">
                <span class="page-header"><span>Log In</span></span>
                <form>
                  <div class="custom-input-field-container">
                    <span
                      class="input-field-wrapper__inputFieldWrapper___3syAG input-field-wrapper__inputFieldWrapperAuto___jdv-1 input-field"
                      ><label
                        for="uname"
                        class="label__label___3a5-y input-field-wrapper__inputFieldAuto___2ML5s input-field-wrapper__label___ZoaBg"
                        >User ID</label
                      ><span class="input-field-wrapper__tooltipWrapper___2Ns5u"
                        ><span
                          class="input-field-wrapper__inputFieldAuto___2ML5s"
                          ><input
                            data-automation-id="uname"
                            type="text"
                            class="form-control__formControl___3uDUX" /></span></span
                    ></span>
                  </div>
                  <div class="custom-input-field-password-container">
                    <span
                      class="input-field-wrapper__inputFieldWrapper___3syAG input-field-wrapper__inputFieldWrapperAuto___jdv-1 input-field"
                      ><label
                        for="pwd"
                        class="label__label___3a5-y input-field-wrapper__inputFieldAuto___2ML5s input-field-wrapper__label___ZoaBg"
                        >Password</label
                      ><span class="input-field-wrapper__tooltipWrapper___2Ns5u"
                        ><span
                          class="input-field-wrapper__inputFieldAuto___2ML5s"
                          ><input
                            data-automation-id="pwd"
                            type="password"
                            class="form-control__formControl___3uDUX"
                            value="" /></span></span></span
                    ><svg
                      fill="currentColor"
                      preserveAspectRatio="xMidYMid meet"
                      height="16"
                      width="16"
                      viewBox="0 0 13 13"
                      class="show-pwd-icon"
                      style="vertical-align: middle"
                    >
                      <g>
                        <path
                          d="M8.224 4.868A2.434 2.434 0 1 1 4.78 8.309a2.434 2.434 0 0 1 3.443-3.441zm-1.722 2.94a1.22 1.22 0 1 0 0-2.44 1.22 1.22 0 0 0 0 2.44z"
                        ></path>
                        <path
                          d="M6.502 1.351c3.19 0 6.484 3.533 6.484 5.238 0 1.704-3.295 5.237-6.484 5.237C3.314 11.826.02 8.293.02 6.59c0-1.705 3.295-5.238 6.483-5.238zM11.52 6.59C10.301 4.282 8.457 2.923 6.505 2.89c-1.957.032-3.802 1.39-5.02 3.698 1.218 2.306 3.061 3.664 5.017 3.697 1.957-.033 3.8-1.391 5.018-3.697z"
                        ></path>
                      </g>
                    </svg>
                  </div>
                  <button
                    data-automation-id="loginBtn"
                    disabled="disabled"
                    class="app-btn spin-wrapper spin-button spinner"
                  >
                    <span class="spin-button-children">LOG IN</span></button
                  ><span class="foot-note"
                    ><span
                      >Forgot your
                      <a
                        data-automation-id="forgotUserId"
                        href="https://retaillink.login.wal-mart.com/forgot-userid"
                        ><span>User ID</span></a
                      >
                      or
                      <a
                        data-automation-id="resetPwd"
                        href="https://retaillink.login.wal-mart.com/reset-password/?path=login"
                        ><span>Password</span></a
                      >?</span
                    ></span
                  >
                </form>
                <div class="row-block OrSeperator">
                  <hr />
                  <span>OR</span>
                  <hr />
                </div>
                <div class="center-content"><span> </span></div>
                <a
                  class="linkBtn center-content"
                  href="https://retaillink.login.wal-mart.com/register"
                  data-automation-id="create-account"
                  ><span>CREATE ACCOUNT</span></a
                >
              </div>
            </div>
          </div>
          <div class="footnote-container">
            <span class="footnote"
              ><a
                href="https://corporate.walmart.com/privacy-security/walmart-supplier-privacy-policy"
                class="alertLink"
                target="_blank"
                data-automation-id="privacylink.automationId"
                ><span>Privacy Policy</span></a
              ></span
            >
          </div>
          <div class="mobile-footnote-container">
            <span class="footnote"
              ><select class="alertLink" id="language-selector">
                <option value="zh">中文(ZH)</option>
                <option value="en" selected="selected">English(EN)</option>
                <option value="fr">Français(FR)</option>
                <option value="de">Deutsch(DE)</option>
                <option value="ja">日本語(JA)</option>
                <option value="ko">한국인(KO)</option>
                <option value="pt">Português(PT)</option>
                <option value="es">Español(ES)</option>
              </select></span
            ><span class="footnote"
              ><a
                href="https://corporate.walmart.com/privacy-security/walmart-supplier-privacy-policy"
                class="alertLink"
                target="_blank"
                data-automation-id="privacylink.automationId"
                ><span>Privacy</span></a
              ></span
            >
          </div>
        </div>
        <div class="TopNav TopNav-overlap">
          <div class="action-icon-wrapper pull-right">
            <div class="language-selector">
              <div class="custom-input-field-container">
                <span
                  class="input-field-wrapper__inputFieldWrapper___3syAG input-field-wrapper__inputFieldWrapperAuto___jdv-1"
                  ><span class="input-field-wrapper__tooltipWrapper___2Ns5u"
                    ><span class="input-field-wrapper__inputFieldAuto___2ML5s"
                      ><div
                        id="language"
                        class="dropdown__dropdown___eioh-"
                        data-automation-id=""
                      >
                        <div data-analytics-name="DropDownClick">
                          <div
                            data-automation-id="-value"
                            class="input-group__inputGroup___XrZcm input-group__iconOnly___HOLNU"
                          >
                            <div class="input-group__inputField___1K5il">
                              <div
                                class="form-control__formControl___3uDUX dropdown__dropdownInput___3eiLj"
                              >
                                <span class="dropdown__iconHolder___2lU-L"
                                  ><img
                                    src="login_files/16d8f643d4f1b65d73fc027d7426a25d.svg"
                                    class="dropdown__icon___dTuGL" /></span
                                ><span class="">English</span>
                              </div>
                            </div>
                            <span
                              class="input-group__inputSuffix___yhLDy dropdown__caret___TzJhz"
                              ><svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="7"
                                viewBox="0 0 12 7"
                              >
                                <path
                                  fill="#000"
                                  fill-opacity=".6"
                                  fill-rule="evenodd"
                                  d="M.097.6A.367.367 0 0 1 .025.217.332.332 0 0 1 .332 0h11.036c.135 0 .255.085.307.217a.367.367 0 0 1-.072.383L6.366 6.148a.701.701 0 0 1-1.032 0L.097.6z"
                                ></path></svg
                            ></span>
                          </div>
                        </div>
                        <div class="dropdown__dropdownContent___Ag6FN">
                          <div class="dropdown__options___3Cf7K">
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="中文"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/5ec5639ac28de988904349f72f32ee32.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >中文
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="English"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/16d8f643d4f1b65d73fc027d7426a25d.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >English
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="Français"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/55b8acfc0a102030c5ca967f6a3a15de.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >Français
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="Deutsch"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/d4e4f511295fca495c39c5f9cc5ae714.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >Deutsch
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="日本語"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/ada3bae54cd202d11ff8f4a26fb6e5ca.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >日本語
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="한국인"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/8cdb5df1d4b2d5f871a4cf92e291e124.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >한국인
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="Português"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/284babb6d7c1c5ac63d8b59773f97f7e.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >Português
                            </div>
                            <div
                              role="button"
                              class="dropdown-list-item__listItem___dVdP_ dropdown__option___3wsKy"
                              data-automation-id="Español"
                            >
                              <span
                                class="dropdown-list-item__iconHolder___3VEz7"
                                ><img
                                  src="login_files/792e78cad1a3dccb29389202458fedcf.svg"
                                  class="dropdown-list-item__icon___33YE3" /></span
                              >Español
                            </div>
                          </div>
                        </div>
                      </div></span
                    ></span
                  ></span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      window.__WML_REDUX_INITIAL_STATE__ = {
        i18nState: {
          locale: "en",
          msgs: {
            "error.DEFAULT":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_11277301":
              "This MFA credential is already registered to another account. Please use a unique credential.",
            "error.ERR_INT_DATA_04010008":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04010009":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04011100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04013002":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_04013003":
              "We're unable to find any information about your Retail Link account. Please try logging in again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04020003":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_04020012":
              "Your account has been locked for security reasons. Please contact the Retail Link Help Desk at ************ for more information",
            "error.ERR_INT_DATA_04021010":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04030004":
              "New password should not be the same as the last 5 passwords.",
            "error.ERR_INT_DATA_04030005":
              "The passcode provided is invalid or has expired. Please click {email}",
            "error.ERR_INT_DATA_04030010":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_04031010":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04031015":
              "Your new password matches one of your old passwords. Please try a different password.",
            "error.ERR_INT_DATA_04031500":
              "Please provide all the required fields.",
            "error.ERR_INT_DATA_04041001":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04041100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04043002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04051000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04051100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04053002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04061001": "Please provide a valid email.",
            "error.ERR_INT_DATA_04063002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04070003":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04081100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04090001":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_0410_1100":
              "It looks like this page is missing some information. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04111100":
              "It looks like this page is missing some information. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04121100":
              "It looks like this page is missing some information. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04130002":
              "The email address provided is already in use by another Retail Link User.",
            "error.ERR_INT_DATA_04141000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04150003":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04163001": "The passcode is invalid.",
            "error.ERR_INT_DATA_04171015":
              "Your new password matches one of your old passwords. Please provide a new password.",
            "error.ERR_INT_DATA_04181100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04183002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04191100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_0419302":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04200003":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04211100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04213002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04221000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04221100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04224050":
              "Please provide email or Retail Link Id.",
            "error.ERR_INT_DATA_04224055": "No data was found.",
            "error.ERR_INT_DATA_04231000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04231100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04234053":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_0425":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04250003":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04251010":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04251100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04261100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04263002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04264051":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04271100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04273002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04274052": "Please provide a Supplier Number.",
            "error.ERR_INT_DATA_04280002":
              "The user already exists on Retail Link.",
            "error.ERR_INT_DATA_04281000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04281001":
              "One or more of the required fields needs to be provided.",
            "error.ERR_INT_DATA_04281100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04283003":
              "The user already exists on Retail Link.",
            "error.ERR_INT_DATA_0429003":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04291001":
              "One or more of the required fields needs to be provided.",
            "error.ERR_INT_DATA_04291100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04294053": "User ID is required.",
            "error.ERR_INT_DATA_04301000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04301001":
              "One or more of the required fields need to be provided.",
            "error.ERR_INT_DATA_04301100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04304053":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_0431000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04311100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04321000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04321100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04323002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04331100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04333002":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_0434054":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04341000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04341100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04344054":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04351100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04361100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04370013":
              "Your password doesn't meet our approved standards.",
            "error.ERR_INT_DATA_04371100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04377161":
              "User name or password is incorrect.",
            "error.ERR_INT_DATA_04380014":
              "The verification code you entered is expired. Please click {resend}",
            "error.ERR_INT_DATA_04381100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04383001":
              "The verification code you entered is incorrect.",
            "error.ERR_INT_DATA_04387168":
              "Invalid session. Please log in again {link}",
            "error.ERR_INT_DATA_04391100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04397030":
              "Your email is already verified. Please login using your email and password.",
            "error.ERR_INT_DATA_04500003": "User ID does not exist",
            "error.ERR_INT_DATA_04503001": "Verification token is invalid",
            "error.ERR_INT_DATA_0450400":
              "Email already verified for your user ID",
            "error.ERR_INT_DATA_04633002":
              "Some unknown error occurred in Retail Link while processing the request",
            "error.ERR_INT_DATA_0463401": "Missing tokens",
            "error.ERR_INT_DATA_04637048": "User is an existing email User",
            "error.ERR_INT_DATA_04677054": "This email Id does not exist",
            "error.ERR_INT_DATA_04991000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_04991100":
              "We're unable to find any information about your Retail Link account. If the problem persists, please reach out to the Retail Link Help Desk at ************. ",
            "error.ERR_INT_DATA_04994053":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_DATA_11020001":
              "Required field is missing in request",
            "error.ERR_INT_DATA_11020002": "User already exists",
            "error.ERR_INT_DATA_11020004": "Employee field is missing",
            "error.ERR_INT_DATA_11020005":
              "Invalid data provided for User Account Creation. Please provide valid details.",
            "error.ERR_INT_DATA_11020011": "Password is missing in request",
            "error.ERR_INT_DATA_11020012": "Contact info is missing in request",
            "error.ERR_INT_DATA_11070001": "User ID does not exist",
            "error.ERR_INT_DATA_11070003":
              "Email already verified for your user ID",
            "error.ERR_INT_DATA_11080001": "LoginId is missing",
            "error.ERR_INT_DATA_11120001":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_11120002":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_11120003":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_11170002":
              "We couldn't find an account associated with the email provided. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_11170004": "Please provide a valid email.",
            "error.ERR_INT_DATA_11180001": "Required parameters are missing.",
            "error.ERR_INT_DATA_11180007":
              "Login id is missing in the QnA request",
            "error.ERR_INT_DATA_11180008": "User is not found in Kraken",
            "error.ERR_INT_DATA_11180009": "User has skipped the maximum times",
            "error.ERR_INT_DATA_11180010":
              "Security questions and answers missing",
            "error.ERR_INT_DATA_11180011": "Some of answers are empty/blank",
            "error.ERR_INT_DATA_11180012": "User not found in Kraken",
            "error.ERR_INT_DATA_11180013":
              "User security questions not present",
            "error.ERR_INT_DATA_11180014": "User QnA not setup properly",
            "error.ERR_INT_DATA_11180015":
              "You have reached maximum attempts to unlock your account.",
            "error.ERR_INT_DATA_11180035":
              "You have reached maximum attempts to unlock your account.",
            "error.ERR_INT_DATA_11180036":
              "MFA details is not present for the user.",
            "error.ERR_INT_DATA_11202001":
              "We couldn't find an account associated with the email provided. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.ERR_INT_DATA_11202002":
              "Your new password matches one of your old passwords. Please try a different password.",
            "error.ERR_INT_DATA_11202003":
              "The passcode provided is invalid or has expired. Please click {email}.",
            "error.ERR_INT_DATA_11202004":
              "The old password you entered is incorrect.",
            "error.ERR_INT_DATA_11202005":
              "Your account has been locked for security reasons. Please contact the Retail Link Help Desk at ************ for more information",
            "error.ERR_INT_DATA_11202006": "Verification token is invalid",
            "error.ERR_INT_DATA_11202009":
              "The specified password does not meet defined policy.",
            "error.ERR_INT_DATA_11260009":
              "Please provide a valid agreement ID.",
            "error.ERR_INT_DATA_11260010":
              "Please provide a valid agreement ID.",
            "error.ERR_INT_DATA_11720003":
              "This phone number is already registered, please utilize a unique phone number",
            "error.ERR_INT_DATA_11730008.phone":
              "This phone number is already registered. Please use a unique phone number.",
            "error.ERR_INT_DATA_11730008.token":
              "This credential ID is already registered. Please use a unique credential ID.",
            "error.ERR_INT_SYS_04291000":
              "It looks like something went wrong. Please wait a few minutes and then try again. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "error.ERR_INT_SYS_11240000": "Required parameters are missing.",
            "error.ERR_INT_SYS_11240001":
              "We're unable to find any information about your Retail Link account. Please reach out to the Retail Link Help Desk at ************ for assistance.",
            "error.gateway.timeout":
              "Gateway timeout error. Please try again later.",
            "error.inValid.params": "Invalid migration params",
            "error.invalid.token":
              "Your session has expired. Please log in again {link}",
            "error.invalid.token.mismatch":
              "Invalid session. Please log in again {link}",
            "error.sso.DEFAULT":
              "It looks like something went wrong. Please wait a few minutes and then try again.",
            "error.sso.exchangeForCode": "Error while exchanging for code",
            "error.sso.username":
              "Retail Link ID accounts are not supported on this application. Please use your Supplier Center account to log in. If you don't have a Supplier Center account, please click {supportLink} to get support.",
            "home.browser.alert1":
              "This application does not support your current browser. Please upgrade to one the following browsers: ",
            "home.browser.alert2": "Chrome: 49 and above",
            "home.browser.alert3": "Firefox: 50 and above",
            "home.browser.alert4": "Safari: 10 and above",
            "home.browser.alert5": "IE: 11 and above or IE Edge: 15 and above",
            "input.warning.nonlatin": "Please enter latin characters only",
            "mfa.accountRecovery.adminRequestedToReset":
              "Administrator has been notified!",
            "mfa.accountRecovery.askAdmin":
              "Ask admin to reset verification method",
            "mfa.accountRecovery.askAdminHeading":
              "Ask your administrator to reset your account",
            "mfa.accountRecovery.askAdminMsg1":
              "You can ask your administrator to reset your account access. This will require you to change your authentication method and password.",
            "mfa.accountRecovery.askAdminMsg2":
              "If you choose not to make these changes, you can check the",
            "mfa.accountRecovery.askAdminMsg3":
              "or try again in a few hours. Click here to",
            "mfa.accountRecovery.askAdminMsg4":
              "If none of the previous methods to gain access to your account have not worked for you, ",
            "mfa.accountRecovery.askAdminMsg5":
              "If none of the previous methods to gain access to your account have not worked for you, ",
            "mfa.accountRecovery.askSupportHeading":
              "Ask support to reset your account",
            "mfa.accountRecovery.askSupportMsg1":
              "If the previous methods to gain access to your account have not worked, you can open a ticket with partner support to reset your authentication method and password.",
            "mfa.accountRecovery.askSupportMsg2":
              "You will need to upload the following documents -",
            "mfa.accountRecovery.askSupportMsg3":
              "On clicking “Open a ticket”, you will be redirected to Seller Help. You can create a ticket by following this path: Support -&gt; Password or other access issue -&gt; OTP access issue.",
            "mfa.accountRecovery.askSupportMsg4":
              "If you choose not to provide this documentation, you can check the ",
            "mfa.accountRecovery.askSupportMsg5":
              "If none of the previous methods to gain access to your account have not worked for you, ",
            "mfa.accountRecovery.backScreen": "return to the previous screen.",
            "mfa.accountRecovery.backToOtp": "Enter Verification Code",
            "mfa.accountRecovery.continue": "Continue",
            "mfa.accountRecovery.gainAccessMessage":
              "Gain access to your account if the previous method didn’t work.",
            "mfa.accountRecovery.openATicket": "Open a ticket",
            "mfa.accountRecovery.resetAccount": "Reset your account",
            "mfa.accountRecovery.selectMethod": "Select another method",
            "mfa.accountRecovery.troubleLogging": "Trouble logging in?",
            "mfa.accountRecovery.troubleShootOptions":
              "troubleshooting options",
            "mfa.accountRecovery.tryAnotherWay": "Try another way",
            "mfa.accountRecovery.verificationProblem":
              "Having problems with verification?",
            "mfa.accountRecovery.backTo2FA":
              "Back to {deliveryChannel} verification",
            "mfa.app.btn": "CONTINUE",
            "mfa.app.header": "Symantec VIP Access App",
            "mfa.back.link": "Try another way to sign in",
            "mfa.credType.invalidConfirmCredType":
              "Credential ID does not match",
            "mfa.credType.invalidCredType": "Invalid Credential ID",
            "mfa.credType.msg":
              "Use the security code provided by the {method}.",
            "mfa.enabled.error":
              "You have already signed up for 2 Step Verification. Please go ahead to login.",
            "mfa.info.code":
              "You'll receive the verification code in a {method}. ",
            "mfa.info.symantec.china": " ",
            "mfa.info.help": " ",
            "mfa.setup": "MFA Setup",
            "mfa.manage": "Manage MFA",
            "mfa.info.rates": "Message and data rates may apply.",
            "mfa.invalid.code.error":
              "The code provided is invalid or has expired.",
            "mfa.kraken.error":
              "We couldn't find an account associated with the email provided. Please clear cache and cookies and retry. If the problem persists, please contact Retail Link Help Desk at ************ for further assistance.",
            "mfa.legend.title": "App Version",
            "mfa.manage.ext.info.0":
              "To edit the phone number or VIP Access app ID associated with your multi-factor authentication, contact your Site Admin.",
            "mfa.manage.ext.info.admin.0":
              "To edit the phone number or VIP Access app ID associated with your multi-factor authentication, contact the Helpdesk",
            "mfa.manage.ext.info.china": "China: 0755-2151-1388",
            "mfa.manage.ext.info.us": "US: (*************",
            "mfa.manage.info.0":
              "Edit/Sign up for 2 Step Verification from Walmart Internal Network",
            "mfa.manage.info.1":
              "You can add additional authentication methods or update existing ones {vipLink}",
            "mfa.mfaSetup.accountLocked.text.line1":
              "You’ve reached the maximum number of attempts.",
            "mfa.page.error":
              "An unexpected error has occurred. Please try again later.",
            "mfa.page.ext.info.1":
              "Enter the verification code we just sent you via text.",
            "mfa.page.ext.info.2":
              "Enter the security code provided by the Symantec VIP Access App.",
            "mfa.page.header": "Verify Your Identity",
            "mfa.page.info.0": "Select a method for verifying your identity",
            "mfa.page.info.1":
              "Enter the verification code we just sent you via voice call, text, or using your authenticator app.",
            "mfa.page.signup":
              "Sign up for 2 Step Verification from Walmart Internal Network {vipLink}",
            "mfa.provision.app-download":
              "Download the app for {vipAccessLink} or {playStoreLink} or {appStoreLink}.",
            "mfa.provision.app.store": "iPhone",
            "mfa.provision.credId": "Credential ID",
            "mfa.provision.credId.confirm": "Re-enter Credential ID",
            "mfa.provision.credId.confirm.label": "Re-enter your Credential ID",
            "mfa.provision.credId.label": "Enter your Credential ID",
            "mfa.provision.desktop": "Desktop",
            "mfa.provision.error":
              "We're unable to sign you up for 2 Step Verification. Please clear cache and cookies and retry. If the problem persists, please reach out to the Retail Link Help Desk at ************.",
            "mfa.provision.header": "Enable 2-Step Verification",
            "mfa.provision.instruction":
              "Please select an option to add an extra layer of security to your accounts",
            "mfa.provision.method": "Security Method",
            "mfa.provision.not.unique.error.PHONE":
              "This phone number is already registered, please utilize a unique phone number",
            "mfa.provision.not.unique.error.TOKEN":
              "This credential ID is already registered. Please use a unique credential ID.",
            "mfa.provision.phone": "Enter the mobile phone number",
            "mfa.provision.play.store": "Android",
            "mfa.provision.send.code.btn": "SEND VERIFICATION CODE",
            "mfa.provision.subheader":
              "Two-step verification adds an extra layer of security for your account.",
            "mfa.provision.tool.tip":
              "Multi-Factor Authentication App Credential ID is a unique identifier that Symantec VIP Access app provisions for a particular device. Click {helpLink} for detailed steps.",
            "mfa.provison.app.continue.btn": "CONTINUE",
            "mfa.resend.code": "Resend",
            "mfa.resend.code.success": "Code resent successfully.",
            "mfa.resend.link": "Re-send verification code",
            "mfa.send.code.error":
              "Error sending Code. Please click {resendLink} to send again.",
            "mfa.sms.btn": "Verify with Text Message",
            "mfa.sms.header": "Text Message",
            "mfa.twoStepSetup.accountSaferMsg":
              "Your account is safer than ever!",
            "mfa.twoStepSetup.android": "Android",
            "mfa.twoStepSetup.appStore": "App Store",
            "mfa.twoStepSetup.authApp": "Authenticator app",
            "mfa.twoStepSetup.authToolTipMsg":
              "An authenticator app provides a one-time, six-digit code that changes every 30 seconds. You input this code to access the application.",
            "mfa.twoStepSetup.checkSuccessScan":
              "Scan the QR code with your authenticator app and check to see if the app is successfully connected.",
            "mfa.twoStepSetup.desktopOSMsg":
              "For Windows, Mac and Linux desktops",
            "mfa.twoStepSetup.done": "Done",
            "mfa.twoStepSetup.downloadAppMsg":
              "Download one of these recommended apps (or use your existing authenticator) and then continue to the setup key.",
            "mfa.twoStepSetup.downloadAuthMsg":
              "Download {authApp} for {os} from the {name}",
            "mfa.twoStepSetup.downloadTwilio": "Download at Authy",
            "mfa.twoStepSetup.enterSetupKey":
              "Or enter setup key into your authenticator app",
            "mfa.twoStepSetup.existingPhoneNumber":
              "Existing phone number {existingMaskedNumber}",
            "mfa.twoStepSetup.forPhone": "For phone",
            "mfa.twoStepSetup.googleAuthenticator": "Google Authenticator",
            "mfa.twoStepSetup.googlePlay": "Google Play",
            "mfa.twoStepSetup.goToSC": "Continue",
            "mfa.twoStepSetup.goToSetup": "Go to setup key",
            "mfa.twoStepSetup.invalidPhoneMsg": "Phone Number is invalid",
            "mfa.twoStepSetup.ios": "iOS",
            "mfa.twoStepSetup.microsoftAuthenticator":
              "Microsoft Authenticator",
            "mfa.twoStepSetup.protectimusAuthenticator": "Protectimus",
            "mfa.twoStepSetup.recommended": "Recommended",
            "mfa.twoStepSetup.scanQrMsg": "Scan the QR code with your phone.",
            "mfa.twoStepSetup.scanToConnectMsg":
              "Scan to connect your authenticator app",
            "mfa.twoStepSetup.selectAChannel": "Select a channel",
            "mfa.twoStepSetup.selectAuthenticatorApp":
              "Select an authenticator app",
            "mfa.twoStepSetup.selectPhone":
              "Select a phone number to receive text messages",
            "mfa.twoStepSetup.setup2Step": "Set up 2-step verification",
            "mfa.twoStepSetup.setup2StepHeader": "You're set up!",
            "mfa.twoStepSetup.setup2StepMsg":
              "Your administrator requires that you have a second method of authentication besides your username and password. Please set up your second channel for access.",
            "mfa.twoStepSetup.setup2StepNewMsg":
              "Before you get into your new account, you need a second method of authentication besides your username and password to enter Seller Center. Please set up your second channel for access.",
            "mfa.twoStepSetup.setup2StepSubHeader":
              "Now let's protect your new account",
            "mfa.twoStepSetup.setUpSuccess": "You’re all set up",
            "mfa.twoStepSetup.textMessage": "Text message",
            "mfa.twoStepSetup.textMessageUs":
              "Text message ( US phone numbers only )",
            "mfa.twoStepSetup.twilio": "Authy by Twilio",
            "mfa.twoStepSetup.updatePhone": "Update your phone number",
            "mfa.twoStepVerification.accountLocked.close": "Go back to login",
            "mfa.twoStepVerification.accountLocked.header": "Account Locked",
            "mfa.twoStepVerification.accountLocked.text.line1":
              "You’ve reached the maximum number of sign-in attempts.",
            "mfa.twoStepVerification.accountLocked.text.line2":
              "You can try again in {tryAgainHours} hours.",
            "mfa.twoStepVerification.accountLockedTotp.text.line1":
              "You've reached the maximum number of attempts",
            "mfa.twoStepVerification.accountLockedTotp.text.line2":
              "Please try again after sometime",
            "mfa.twoStepVerification.authorize.fail":
              "Authorization failed. Please reach out to Seller Support.",
            "mfa.twoStepVerification.back": "Back",
            "mfa.twoStepVerification.backToLogin": "Back to login",
            "mfa.twoStepVerification.codeExpiryMessage":
              "This code will expire in {remainingTime}",
            "mfa.twoStepVerification.didNotReceiveCode": "Didn’t receive code?",
            "mfa.twoStepVerification.emailCodeMsg":
              "Enter the code from your email.",
            "mfa.twoStepVerification.enterNewTotpMessage":
              "Enter a new code supplied by your authenticator app",
            "mfa.twoStepVerification.enterOtpMessage":
              "Enter the code we sent to “{email}”",
            "mfa.twoStepVerification.enterTextMessage":
              "Enter the code just sent via text message to {phone}.",
            "mfa.twoStepVerification.enterTotpMessage":
              "Enter the code from your authenticator app",
            "mfa.twoStepVerification.header": "2-step verification",
            "mfa.twoStepVerification.incorrectOtpEntered":
              "The code you entered wasn’t valid. {attemptNumber}/{maxAttempts} attempts.",
            "mfa.twoStepVerification.incorrectTotpEntered":
              "The code you entered wasn’t valid.",
            "mfa.twoStepVerification.internalError":
              "An unexpected error has occurred. Please try again later.",
            "mfa.twoStepVerification.resendOtp": "Resend",
            "mfa.twoStepVerification.resendOtpSuccess": "New code sent.",
            "mfa.twoStepVerification.resendOtpWaitMessage":
              "Try again in {secondsToWait} seconds.",
            "mfa.twoStepVerification.submitOtpCode": "Continue",
            "mfa.twoStepVerification.vericationExpired.header": "Try Again",
            "mfa.twoStepVerification.vericationExpired.sendNewCode":
              "Send a new code",
            "mfa.twoStepVerification.vericationExpired.text":
              "Your verification code has expired.",
            "mfa.twoStepVerification.verificationCodeLabel":
              "Verification code",
            "mfa.ValidationRequirements": "Invalid code",
            "mfa.verification.btn": "Submit",
            "mfa.verification.header": "Verification code",
            "mfa.verify.email": "Verify",
            "mfa.voice.btn": "Verify with Voice call",
            "mfa.voice.header": "Voice call",
            "rl.migration.admin.userAgreementLabel":
              "I have read, understand, and agree to be bound by all terms and conditions of the Agreement. I represent and warrant that I have the authority to bind my company to the Agreement, and that the Agreement is a legally enforceable contract between my company (Supplier) and Walmart Inc.",
            "rl.migration.admin.userAgreementLinkLabel":
              "View Retail Link Agreement",
            "rl.migration.alreadycreated.msg":
              "Your account is already created. Please click &quot;Next&quot; to verify your account.",
            "rl.migration.back": "BACK",
            "rl.migration.complete": "Complete",
            "rl.migration.confirmEmail": "CONFIRM EMAIL",
            "rl.migration.confirmEmailDesc":
              "Your Supplier Center account will now be your new account to access Retail Link and Supplier Center.",
            "rl.migration.confirmPassword": "Confirm Password",
            "rl.migration.confirmPasswordError":
              "Please enter the same password.",
            "rl.migration.editEmail": "Edit Email",
            "rl.migration.email": "Email",
            "rl.migration.emailAlreadyRegistered":
              "Email is already registered.",
            "rl.migration.emailAlreadyUsed":
              "This email is already used by another account. Please contact the Retail Link Help Desk at ************ if you need assistance.",
            "rl.migration.emailUnverified":
              "Your email has not been verified. Please log in using your Retail Link ID and password to complete the verification step.",
            "rl.migration.emailverification.codeResendSuccess":
              "Email verification was sent succesfully!",
            "rl.migration.emailverification.header": "Verify Email",
            "rl.migration.emailverification.inputLabel": "Verification Code",
            "rl.migration.emailverification.message":
              "We sent you a verification code to {email}.  Please enter the verification code below.",
            "rl.migration.emailverification.resend": "Resend Code",
            "rl.migration.error.emailAlreadyVerified":
              "Your email is already verified.",
            "rl.migration.error.incorrectCode":
              "The verification code is not valid.",
            "rl.migration.error.wrongPassword":
              "The password you entered is incorrect.",
            "rl.migration.forgotpassword": "Forgot Supplier Center Password?",
            "rl.migration.forgotPwd.error":
              "Your User ID has been migrated to email. Please provide your email as your User ID to reset your password",
            "rl.migration.internalError":
              "Something went wrong. Please try again later.",
            "rl.migration.intro1.buttonLabel": "Get Started",
            "rl.migration.intro1.header": "Update User ID",
            "rl.migration.intro1.message":
              "We're updating the way you log in to Retail Link. You will be taken through a series of steps to provide us with an email, which will be your new User ID to log in to Retail Link. After completing these steps, your Retail Link ID will no longer be valid.",
            "rl.migration.intro1.step1":
              "Provide a new password for your account",
            "rl.migration.intro1.step2": "Verify your email",
            "rl.migration.intro1.step3":
              "Use your email as your new User ID to log in",
            "rl.migration.intro2.buttonLabel": "Get Started",
            "rl.migration.intro2.header": "Update User ID",
            "rl.migration.intro2.message":
              "We're updating the way you log in to Retail Link. You will be taken through a series of steps to provide us with an email, which will be your new User ID to log in to Retail Link. After completing these steps, your Retail Link ID will no longer be valid. ",
            "rl.migration.intro2.step1": "Provide a new email to log in",
            "rl.migration.intro2.step2": "Verify your email",
            "rl.migration.intro2.step3":
              "Use your email as your new User ID to log in",
            "rl.migration.intro3.buttonLabel": "Get Started",
            "rl.migration.intro3.header": "Merge Accounts",
            "rl.migration.intro3.message":
              "We're updating the way you log in to Retail Link. You will be taken through a series of steps to merge your Supplier Center and Retail Link accounts into one account.  After completing these steps, your Retail Link ID will no longer be valid.",
            "rl.migration.intro3.step1": "Confirm your email",
            "rl.migration.intro3.step2":
              "Validate you own your Supplier Center account by providing your password.",
            "rl.migration.intro3.step3":
              "Use your email as your new User ID to log in.",
            "rl.migration.intro4.buttonLabel": "Continue",
            "rl.migration.intro4.header": "Account Error",
            "rl.migration.intro4.message":
              "Your account has an unforeseen internal problem and cannot be migrated to Email. Please contact Retail Link Support at ************ for more information.",
            "rl.migration.logout": "Log out",
            "rl.migration.newEmail": "NEW EMAIL",
            "rl.migration.newEmailDesc":
              "Please provide a new email to to be used as your User ID.",
            "rl.migration.newPassword": "NEW PASSWORD",
            "rl.migration.newPasswordDesc":
              "Your email will now be used as your User ID. Please provide a password.",
            "rl.migration.newPasswordLabel": "New Password",
            "rl.migration.next": "NEXT",
            "rl.migration.notAvailable": "Not available",
            "rl.migration.password": "Password",
            "rl.migration.passwordRequirements":
              "Your password must contain between eight and thirty alphanumeric characters, one uppercase letter, and one number.",
            "rl.migration.rlId": "Retail Link ID",
            "rl.migration.supplierInfo":
              "Statement applies to all your U.S. Supplier Numbers: ",
            "rl.migration.userAgreement": "User Agreement",
            "rl.migration.userAgreementLabel":
              "I have read and accept the terms of use",
            "rl.migration.userAgreementLinkLabel":
              "View Terms of Use Agreement",
            "rl.migration.username": "User ID",
            "rl.migration.useThisEmail": "Use this email",
            "rl.migration.validateEmail": "Validate Email",
            "rl.migration.validateSCAccount":
              "VALIDATE SUPPLIER CENTER ACCOUNT",
            "rl.migration.validateSCAccountDesc":
              "Validate your account by entering your Supplier Center credentials below.",
            "rl.migration.ValidationRequirements": "Invalid validation code",
            "rl.migration.validEmail": "Please enter a valid email.",
            "rl.migration.verified.buttonLabel": "Log In With Email",
            "rl.migration.verified.header": "Complete",
            "rl.migration.verified.message":
              "Your email has been verified. Please sign in using your new User ID: {email}",
            "rl.migration.verify": "Verify",
            "rl.migration.verify.suppliercenter.msg":
              "Please click &quot;Next&quot; to validate your Supplier Center account.",
            "rl.migration.verifyEmail": "Verify Email",
            "security.popup.btn.unlock": "Unlock account",
            "security.popup.btn.wait": "Wait for 24 hours",
            "security.popup.header": "Account Locked.",
            "security.popup.subHeader1":
              "You have reached the maximum number of verification attempts and your account has been temporary locked.",
            "security.popup.subHeader2":
              "You can either wait for 24 hours for the account to be unlocked by itself or unlock it by answering security questions.",
            "security.popup.subHeader3":
              "You need to wait for 24 hours for the account to be unlocked by itself.",
            "seller.support.ownerShipAddress": "Proof of address",
            "seller.support.ownerShipDocuments":
              "Business Owner Identity Documents",
            "seller.support.ownerShipProof": "Proof of ownership",
            "sso.agreements.agree": "AGREE",
            "sso.agreements.btnText": "CANCEL",
            "sso.agreements.coiTitle": "Conflict of Interest",
            "sso.agreements.downloadPdf": "Download PDF",
            "sso.agreements.printBtnStr": "Print friendly version",
            "sso.agreements.rlAgreement": "Retail Link Agreement",
            "sso.agreements.rlAgreementTitle": "Retail Link Agreement",
            "sso.appstore.authorize": "Authorize",
            "sso.appstore.authorize.fail.seller":
              "Authorization failed. Please reach out to Seller Support.",
            "sso.appstore.authorize.fail.supplier":
              "Authorization failed. Please reach out to Supplier Support.",
            "sso.appstore.decline": "Decline",
            "sso.captcha.error":
              "An unexpected error ocurred while fetching captcha.",
            "sso.captcha.invalidCaptcha": "Invalid captcha. Please try again.",
            "sso.captcha.tokenError":
              "An unexpected error ocurred while fetching captcha token.",
            "sso.captcha.validated": "Captcha validated!",
            "sso.captcha.validationError": "Error validating captcha",
            "sso.changepwd.apiError":
              "An unexpected error occurred while changing your password. Please try again later.",
            "sso.changepwd.badPassword":
              "Your password must contain between eight and thirty alphanumeric characters, one uppercase letter, and one number.",
            "sso.changepwd.buttonLbl": "SUBMIT",
            "sso.changepwd.changeError":
              "An unexpected error occurred while changing your password. Please try again later.",
            "sso.changepwd.changepwd.title": "Change Password",
            "sso.changepwd.confirmPwd": "Confirm New Password",
            "sso.changepwd.currentPwd": "Current Password",
            "sso.changepwd.header":
              "Your password has expired. Please create a new password.",
            "sso.changepwd.invalidPassword":
              "Your password must contain between eight and thirty alphanumeric characters, one uppercase letter, and one number.",
            "sso.changepwd.missing": "The redirect URL is missing.",
            "sso.changepwd.newPwd": "New Password",
            "sso.changepwd.pwdMatch": "Passwords do not match",
            "sso.changepwd.pwdTooltip":
              "Your password must contain between eight and thirty alphanumeric characters, one uppercase letter, and one number.",
            "sso.changepwd.required": "This field is required.",
            "sso.changepwd.sameAsOldPassword":
              "Your new password matches one of your old passwords. Please try a different password.",
            "sso.changepwd.successHeader": "Password Updated Succesfully",
            "sso.changepwd.successLogin": "Log In",
            "sso.changepwd.successMessage":
              "Your password has been succesfully changed! Please log in after 5 minutes using your new password.",
            "sso.changepwd.successMsg": "Password Successfully Updated!",
            "sso.changepwd.title": "Expired Password",
            "sso.changepwd.wrongPassword":
              "The password you entered is incorrect.",
            "sso.createaccount.emailExists": "This email already exists.",
            "sso.createaccount.failed": "Account creation failed",
            "sso.createpassword.agreementLabel":
              "I have read and accept the Terms of Use",
            "sso.createpassword.agreementLink": "View Terms of Use",
            "sso.createpassword.agreementTitle": "Terms of Use",
            "sso.createpassword.apiError":
              "An unexpected errror occurred while creating your new password. Please try again later.",
            "sso.createpassword.confirmPassword": "Confirm Password",
            "sso.createpassword.description": "Create password for {uname}",
            "sso.createpassword.emailError":
              "An unexpected errror occurred while sending the email. Please click {email} to resend.",
            "sso.createpassword.emailLinkText": "Resend Code",
            "sso.createpassword.emailMessage":
              "We just sent you a new verification email.",
            "sso.createpassword.getAgreementsError":
              "An error occured while downloading the Agreements. Try again later.",
            "sso.createpassword.header": "Create Password",
            "sso.createpassword.paramError": "Required parameters are missing",
            "sso.createpassword.password": "Password",
            "sso.createpassword.setPassword": "SET PASSWORD",
            "sso.createpassword.successHeader":
              "Password Successfully Updated!",
            "sso.createpassword.successLogin": "Log In",
            "sso.createpassword.successMessage":
              "Your password has been successfully updated. Please log in using your new password.",
            "sso.createpassword.tokenError":
              "Your verification email has expired. Click {email} to resend an email with a new link.",
            "sso.emailVerified.error": "ERROR",
            "sso.emailVerified.error.msg":
              "Unable to verify your email as {errMsg}",
            "sso.emailVerified.msg":
              "Your email has been successfully verified. Sign in now.",
            "sso.emailVerified.signIn": "SIGN IN",
            "sso.emailVerified.success": "Success",
            "sso.errorParams.missing": "One or more parameters is missing.",
            "sso.forgotUserId": "Forgot User Id",
            "sso.forgotUserId.description":
              "Please enter the email associated with your account.",
            "sso.forgotUserId.email": "Email",
            "sso.forgotUserId.emailRequired": "Valid email is required.",
            "sso.forgotUserId.header": "Forgot User Id",
            "sso.forgotUserId.next": "NEXT",
            "sso.forgotUserId.return": "Return to Log In",
            "sso.incorrectPassword": "Invalid password",
            "sso.invalidRedirectUrl": "Invalid Redirect Url",
            "sso.invite.cancelled": "Your invitation was cancelled by Walmart.",
            "sso.invite.emailExists":
              "{email} already exists. Please log in using your email and password.",
            "sso.invite.error": "Error retrieving invite details",
            "sso.invite.expired":
              "Your invitation expired. Please contact the Retail Link Help Desk at ************ for further assistance.",
            "sso.invite.linkText": "here",
            "sso.invite.missing": "Missing invite ID",
            "sso.invite.notFound": "Your invitation was not found.",
            "sso.invite.submitted": "You invitation was already submitted.",
            "sso.invite.userExists":
              "It seems you already have an account with us. Please click {link} to log in.",
            "sso.login": "Log In",
            "sso.login.botEmptyToken":
              "This is a bot account that needs a valid bot token to login. Your bot token is empty. Please pass the bot token to login.",
            "sso.login.botExpired":
              "This is a bot account that needs a valid bot token to login. Your bot token has expired. Please reach out to your Site Admin to generate a new token.",
            "sso.login.botInvalid":
              "This is a bot account that needs a valid bot token to login. Your bot token is invalid. Please reach out to your Site Admin for the valid token.",
            "sso.login.contactAdmin":
              "Your password has expired. Please reset your password {link}.",
            "sso.login.createAssociateAccount": "CREATE ASSOCIATE ACCOUNT",
            "sso.login.dormant":
              "Your account has been locked for security reasons. Please reset your password {dormantLink} to unlock your account.",
            "sso.login.email": "Email",
            "sso.login.emailOrUserId": "Email or User ID",
            "sso.login.expPswd":
              "Your password has expired. Please change your password {ssoLink}. After resetting your password you can try again.",
            "sso.login.expPswd.intl":
              "Your password has expired. Please reset your password on your computer to log in to this application",
            "sso.login.footer.privacyPolicy": "Privacy Policy",
            "sso.login.footer.privacy": "Privacy",
            "sso.login.footerNoteLine1":
              "We recommend setting your monitor resolution to at least 1024 x 768",
            "sso.login.footerNoteLine2":
              "U.S. Users: Please use Chrome v49 or newer",
            "sso.login.footerNoteLine3":
              "International Users: Please use Internet Explorer",
            "sso.login.forgotmessage":
              "Forgot your {userIdLink} or {pswdLink}?",
            "sso.login.forgotPswd": "Forgot your {pswdLink}?",
            "sso.login.internalError":
              "An unexpected error has occurred. Please try again later.",
            "sso.login.internalOnExternal":
              "Retail Link access for associates is restricted on the external login page. Please click {internalUrl} to log in.",
            "sso.login.login": "LOG IN",
            "sso.login.needAccount": " ",
            "sso.login.newDesign":
              "Welcome to our new design! Find out more info {link1}.",
            "sso.login.noAccess":
              "You don't have access to Retail Link. Please contact the Retail Link Help Desk at ************ for more information.",
            "sso.login.noRlAccess":
              "You don't have access to Retail Link. To create an account please click {createAssociateAccountLink}",
            "sso.login.notSupported":
              "Current account is not supported on Cert",
            "sso.login.or": "OR",
            "sso.login.password": "Password",
            "sso.login.passwordError":
              "The User ID or password you entered is incorrect",
            "sso.login.passwordLow": "Password",
            "sso.login.passwordTooltip": "Enter Password",
            "sso.login.privacyCenter": "Privacy Center",
            "sso.login.rememberme": "Remember Me",
            "sso.login.rlAccessBlocked":
              "You are not part of the beta program. Please contact the Retail Link Help Desk at ************ for more information.",
            "sso.login.rlExpPswd":
              "Your password has expired. Please login to Retail Link {link} to change your password. After resetting your password you can login to your application",
            "sso.login.seller.adminOnly":
              "Access to the application is restricted to admins only.",
            "sso.login.seller.userDeclined":
              " Your application has been declined and access to your account is restricted.",
            "sso.login.seller.verificationPending":
              "We could not verify your email. Please check your inbox and verify your email to login.",
            "sso.login.setPwdLinkMsg": "here",
            "sso.login.support": "Support",
            "sso.login.userCompromised.seller":
              "Your account has been locked for security reasons. Please reset your password {ssoLink} to unlock your account.",
            "sso.login.userDeclined":
              "Your account has been declined by the administrator. Please contact the Retail Link Help Desk at ************ for more information.",
            "sso.login.userDisabled":
              "Your account has been locked for security reasons. Please contact the Retail Link Help Desk at ************ for more information",
            "sso.login.userLocked":
              "Your account has been locked for security reasons. Please reset your password {dormantLink} to unlock your account.",
            "sso.login.userLocked.seller":
              "Your account has been locked for security reasons. Please reset your password {ssoLink} to unlock your account.",
            "sso.login.username": "User ID",
            "sso.login.usernameError":
              "The User ID or password you entered is incorrect",
            "sso.login.usernameLow": "User ID",
            "sso.login.usernameTooltip":
              "Enter User ID registered with Walmart",
            "sso.login.welcome": "Welcome!",
            "sso.pwdTooltip":
              "Your password must contain between eight and thirty alphanumeric characters, one uppercase letter, and one number.",
            "sso.recoverusername": "Recover User ID",
            "sso.recoverusername.btn": "RECOVER USER ID",
            "sso.recoverusername.description":
              "Please enter the email address associated with your account to recover your User ID.",
            "sso.recoverusername.email": "Email",
            "sso.recoverusername.emailCantFind":
              "We couldn't find an account associated with the email provided. If the problem persists, please contact Retail Link Help Desk at ************ for further assistance.",
            "sso.recoverusername.emailRequired": "Email is required.",
            "sso.recoverusername.return": "Return to Log In",
            "sso.recoverusername.successHeader": "Email Sent!",
            "sso.recoverusername.successLogin": "LOG IN",
            "sso.recoverusername.successMessage":
              "Your User ID was sent to {email}",
            "sso.register": "Create Account",
            "sso.register.agreeTerms": "I have read and agree to the",
            "sso.register.alreadyHaveAccount": "Already have an account?",
            "sso.register.coiStmt": "Conflict of Interest Statement",
            "sso.register.companyName": "Company Name",
            "sso.register.country": "Country",
            "sso.register.createAccount": "Create Account",
            "sso.register.createAccountBtn": "CREATE ACCOUNT",
            "sso.register.createAccountExisting":
              "Create User Account for existing Supplier?",
            "sso.register.email": "Email Address",
            "sso.register.employeeOrParentCompany":
              "Employee of Parent Company/Sister Division/Subsidiary",
            "sso.register.employeeType": "Employee Type",
            "sso.register.fieldRequired": "This field is required.",
            "sso.register.firstName": "First Name",
            "sso.register.function": "Function",
            "sso.register.inValid": "Special characters not allowed",
            "sso.register.invalidFirstName": "Invalid First Name",
            "sso.register.invalidLastName": "Invalid Last Name",
            "sso.register.invalidPhoneNumber": "Invalid phone number",
            "sso.register.lastName": "Last Name",
            "sso.register.maxLen": "Answer should not exceed 30 characters",
            "sso.register.minLen": "Answer should be more than 2 characters",
            "sso.register.mobile": "Mobile",
            "sso.register.newToWalmart": "New to Walmart?",
            "sso.register.password": "Password",
            "sso.register.phone": "Phone Number",
            "sso.register.provideEmail": "A valid email address is required.",
            "sso.register.pwdDontMatch": "The passwords do not match.",
            "sso.register.repassword": "Confirm Password",
            "sso.register.supplierID": "Supplier ID",
            "sso.register.thirdparty": "Third Party",
            "sso.register.thirdpartyType": "Third Party Type",
            "sso.register.title": "Title",
            "sso.resetpwd": "Reset Password",
            "sso.resetpwd.apiError":
              "An unexpected error occured while setting your new password. Please try again later.",
            "sso.resetpwd.badPassword":
              "Your new password matches one of your old passwords. Please try a different password.",
            "sso.resetpwd.confirmnewpassword": "Confirm New Password",
            "sso.resetpwd.confirmpassworderror": "Password does not match",
            "sso.resetpwd.confirmpasswordisrequired":
              "Confirm new password is required.",
            "sso.resetpwd.continue": "CONTINUE",
            "sso.resetpwd.description":
              "Please enter the User ID associated with your account.",
            "sso.resetpwd.didntReceive": "Didn’t receive a passcode? {resend}",
            "sso.resetpwd.emailError":
              "An unexpected error occured while sending the email. Please try again later.",
            "sso.resetpwd.emailNotFound":
              "We couldn’t find an account associated with the User ID provided. If the problem persists, please contact the Retail Link Help Desk at ************ for further assistance.",
            "sso.resetpwd.hint": "Hint",
            "sso.resetpwd.hintRequired": "Hint Answer is required.",
            "sso.resetpwd.invalidUserID": "User ID is invalid",
            "sso.resetpwd.loginId": "User ID",
            "sso.resetpwd.loginIdisrequired": "User ID is required.",
            "sso.resetpwd.newpass":
              "Please enter a new password for {loginId}.",
            "sso.resetpwd.newpassword": "New Password",
            "sso.resetpwd.next": "NEXT",
            "sso.resetpwd.noHintQuestion":
              "Hint Question was not set up in Retail Link. Please reach out to the Retail Link Help Desk for assistance.",
            "sso.resetpwd.passcode": "Passcode",
            "sso.resetpwd.passcodeisrequired": "Passcode is required.",
            "sso.resetpwd.passwordisrequired": "Password is required.",
            "sso.resetpwd.pin": "Retail Link Pin",
            "sso.resetpwd.pinInfo":
              "If you did not set up a pin in Retail Link, please reach out to the Retail Link Help Desk for assistance.",
            "sso.resetpwd.pinRequired": "Pin is required.",
            "sso.resetpwd.resend": "Resend",
            "sso.resetpwd.reset": "Reset Password",
            "sso.resetpwd.resetBtn": "RESET PASSWORD",
            "sso.resetpwd.resetError":
              "An unexpected error occurred while resetting your password. Please try again later.",
            "sso.resetpwd.return": "Return to Log In",
            "sso.resetpwd.successHeader": "Password Updated Succesfully",
            "sso.resetpwd.successLogin": "Log In",
            "sso.resetpwd.successMessage":
              "Your password has been succesfully changed! Please log in using your new password.",
            "sso.resetpwd.tendigit": "A passcode was sent to {loginId}.",
            "sso.resetpwd.type": "Enter passcode",
            "sso.security.answer": "Answer",
            "sso.security.answerPlaceholder": "Enter your answer here",
            "sso.security.failed": "Error occured while savind the data.",
            "sso.security.fieldRequired": "This field is required",
            "sso.security.fill.in.security.questions":
              "Fill in Security questions",
            "sso.security.header": "Security Questions",
            "sso.security.informationHeaderForRegister":
              "Select three security questions below. These questions will help us verify your identity to unlock your MFA.",
            "sso.security.informationHeaderForValidate":
              "Please answer the below security questions to verify your identity and unlock your MFA.",
            "sso.security.pop.data":
              "You have chosen to skip the security questions for now. However, it is mandatory to setup the security questions for your account to ensure protection.",
            "sso.security.question0": "Question 1",
            "sso.security.question1": "Question 2",
            "sso.security.question2": "Question 3",
            "sso.security.questionPlaceholder": "Choose a question",
            "sso.security.reset": "Reset",
            "sso.security.save": "Save",
            "sso.security.save.success":
              "Security Question setup is complete. You're being redirected to the redirect url page",
            "sso.security.skip": "Skip for now",
            "sso.security.skip.continue": "Continue",
            "sso.security.skip.success": "Security question skip successfully.",
            "sso.security.subHeader": "Skips remaining: ",
            "sso.security.verification.attemptsRemaining":
              "Attempts Remaining : {remainingAttempts} ",
            "sso.security.verification.btn": "Submit",
            "sso.security.verification.failed":
              " Some of your answers are incorrect. Please verify your answers",
            "sso.security.verification.maxAttempt":
              "You have reached maximum attempts to unlock your account.",
            "sso.security.verification.success":
              "Your identity has been verified successfully and your account is unlocked. You're being redirected to the MFA page.",
          },
          intlEnabled: true,
        },
        loginState: {
          loggedIn: false,
          validating: false,
          disableLogin: true,
          username: "",
          loginLoading: true,
          mutipleAdminDetails: {},
          noBrowserSupport: false,
          fullBrowserSupport: true,
          blockUnsupportedBrowsers: false,
          internalUrl: "rl.login.wal-mart.com",
          rlExtUrl: "https://retaillink.wal-mart.com/home",
          rlIntUrl: "https://rl.homeoffice.wal-mart.com/rl_portal",
          pfUrl: "https://pfedprod.wal-mart.com",
          externalUrl: "https://retaillink.login.wal-mart.com",
          checkRedirectURLDomain: true,
          checkRedirectDomain: true,
          urlList: [
            "wal-mart.com",
            "walmart.com",
            "salesforce.com",
            "samsclub.com",
            "riversand.com",
            "ibmcloud.com",
            "mercurygate.net",
          ],
          showServerLogClientList: [],
          hideAccessRetailLinkBtn: false,
          resetPwdUrl: {
            supplier: "https://partner.walmart.com/forgotPassword",
            seller: "https://seller.walmart.com/forgotPassword",
            "seller-ca": "https://seller.walmart.ca/forgotPassword",
          },
          isUrlExternal: true,
          caseMgtSupportUrl: "https://supplierhelp.walmart.com/s/contact",
          disableAutoABR: true,
          enablePerimeterXCaptchaFor: {
            "1653e6c0-191f-45ae-a099-ca849b103cfa": "LUMINATE",
            "4b9b9151-24df-4911-9ed8-9a2bf281e5b0": "LUMINATE",
            "0674a520-bee9-403f-beb5-26993b351df6": "LUMINATE",
            "28f5122b-a7de-4bbf-90bc-778b4db4c5f2": "LUMINATE",
            "4c35f5ff-311c-43be-9aef-f493c68a7b22": "LUMINATE",
            "4d4c9ee0-c646-4729-8b7f-53ed1e7b4bb0": "LUMINATE",
            "826e8230-6fb4-4943-9190-41e82b401516": "LUMINATE",
            "9db1af7b-5d78-4370-83fc-99968badc1fa": "LUMINATE",
            "c22e4a2b-43d4-45ee-aff3-35f89671c926": "LUMINATE",
            "f220a998-e34e-47f5-ac75-712a7ffd63bd": "LUMINATE",
          },
          pxCustomCss: {
            LUMINATE: {
              view: {
                fillColor: "#F0EEF6",
                backgroundColor: "#ffffff",
                borderColor: "#3c228a",
                borderRadius: 30,
                borderWidth: 1,
                textColor: "#3c228a",
                texSize: 16,
                width: "275px",
                height: "40px",
                targetColor: "#3c228a",
                textFont: "Bogle-Regular",
                checkmarkThickness: "4px",
                checkmarkHeight: "20px",
                checkmarkWidth: "8px",
                animation: true,
              },
            },
          },
          showBanner: false,
          bannerInfo: {
            type: "INFO",
            message:
              "Multi-Factor Authentication (MFA) will be enabled for all new and existing users starting 27 January 2025. Please refer to the <a target='_blank' href='https://supplierhelp.walmart.com/s/guide?article=*********'>article</a> for help with registration.",
          },
          rlClientId: "1b902bda-c72f-4c5a-8e9c-3630e679399f",
          privacyLink:
            "https://corporate.walmart.com/privacy-security/walmart-supplier-privacy-policy",
          isLoginPgViewNeeded: false,
          turnOnUserTypeApi: true,
          callUserTypeForRL: true,
          troubleLoggingLink:
            "https://sellerhelp.walmart.com/seller/s/guide?article=*********",
          partnerLevelConfig: {
            SELLER: {
              loginHelpLink:
                "https://sellerhelp.walmart.com/seller/s/guide?language=en_US&article=*********",
              showAccountRecovery: true,
              showBackToLogin: false,
              preferredMfaChannel: "TEXT",
              allowedMfaChannels: ["EMAIL", "TOTP", "TEXT"],
            },
            SOLUTION_PROVIDER: {
              loginHelpLink:
                "https://developer.walmart.com/channelpartnerportal/ui/support",
              showAccountRecovery: false,
              showBackToLogin: true,
              preferredMfaChannel: "EMAIL",
              allowedMfaChannels: ["EMAIL", "TOTP"],
            },
            WLS: {
              showAccountRecovery: false,
              showBackToLogin: true,
              preferredMfaChannel: "TEXT",
              allowedMfaChannels: ["EMAIL", "TOTP", "TEXT"],
            },
          },
          supportTicketLink: "https://sellerhelp.walmart.com/s/contact",
          addUserIdToSetPwd: true,
          tmxConfig:
            '{\n  "seller": {\n    "tmxUrl": "https://i5.walmartimages.com/dfw/4ff9c6c9-d9a0/k2-_66e7fff8-b096-45d0-8ec5-70e5787386e9.v1.js",\n    "orgId": "hgy2n0ks",\n    "profilingDomain": "ofxmryjd.walmart.com",\n    "loadTimeout": 3000\n  }\n}',
          mfaHelpGuideUrl: {
            main: "https://supplierhelp.walmart.com/s/guide?article=*********",
            manageMFA:
              "https://supplierhelp.walmart.com/s/guide?article=*********#Symantic_App",
          },
          hideForgotUserId: false,
          validateLogoutRedirect: true,
          validateSSOLogout: true,
          validateDomainRegex:
            "^http(s?):\\/\\/(([\\w\\d]+\\.){1}|([\\w\\d]+\\.){2}|([\\w\\d]+\\.){3})wal-mart\\.com(.*)$",
          ssoMfaCountryCodes: [
            {
              isoCode: "+1",
              isoCountryCode: "US",
              isoCountryName: "United States",
              maxLength: 10,
              minLength: 10,
            },
            {
              isoCode: "+1",
              isoCountryCode: "CA",
              isoCountryName: "Canada",
              maxLength: 10,
              minLength: 10,
            },
            {
              isoCode: "+52",
              isoCountryCode: "MX",
              isoCountryName: "Mexico",
              maxLength: 10,
              minLength: 10,
            },
            {
              isoCode: "+56",
              isoCountryCode: "CL",
              isoCountryName: "Chile",
              maxLength: 9,
              minLength: 9,
            },
          ],
          useRlResetApis: true,
          resetPwdCaptchaLimit: 5,
          pingfedConfiguration: {
            url: "https://pfedprod.wal-mart.com/as/authorization.oauth2",
            clientId: "ef97689b-7c0a-46c7-9f08-80b4f9a356f2",
            codeChallengeMethod: "S256",
            grantType: "authorization_code",
            falconScope: "openid profile full",
            codeChallengeLowerLimit: 1e43,
            codeChallengeHigherLimit: 1e126,
            tokenApiUrl: "https://pfedprod.wal-mart.com/as/token.oauth2",
            responseType: "code",
          },
        },
        regState: {
          disableCreate: true,
          firstName: { value: "", required: true, error: "" },
          lastName: { value: "", required: true, error: "" },
          email: { value: "", required: true, error: "" },
          phone: { value: "", required: true, error: "" },
          password: { value: "", required: true, error: "" },
          reenterPassword: { value: "", required: true, error: "" },
          isInvited: false,
          error: null,
          captchaDisabled: false,
          captchaValidated: false,
          creatingAccount: false,
          createdAccount: false,
          fetchingInvite: false,
          selfRegistrationEnabled: false,
          rlsetPswdLink:
            "https://rllogin.wal-mart.com/rl_security/setPassword.aspx",
          forgotRlUserId:
            "https://rllogin.wal-mart.com/ElectronicAgreement/#/Forgetuserid",
          turnOnUserIdApi: true,
          agreementUrls: {
            rlAgreement: {
              en: {
                html: "https://i5.walmartimages.com/dfwrs/3d0839e4-92b2/k2-_993a2b73-0f9b-44df-9898-102e6c3851cc.v4.html",
                pdf: "https://i5.walmartimages.com/dfwrs/3d0839e4-d3a5/k2-_422aa41b-9d60-4dfa-b343-371dbd0ceaf0.v3.pdf",
              },
            },
            coi: {
              en: {
                html: "https://i5.walmartimages.com/dfwrs/3d0839e4-c252/k2-_9f90916d-60f2-465e-aab1-43f176a73570.v3.html",
                pdf: "https://i5.walmartimages.com/dfwrs/3d0839e4-51c2/k2-_39f8d4e2-0661-43b7-9e72-ba738fb4ccba.v1.pdf",
              },
            },
            createAccount:
              "https://rllogin.wal-mart.com/ElectronicAgreement/#/",
            createAssociateAccount:
              "https://walmartglobal.service-now.com/wm_sp?id=sc_cat_item&sys_id=1a45e61e1bb18d50dae98621604bcb49&parent_id=4fc6e8efdb03efc0cb717f698c96194d",
            termsOfUse: {
              en: {
                html: "https://i5.walmartimages.com/dfw/63fd9f59-8ea9/k2-_68208f4e-c91b-4b72-bf0b-8cfeb360d5ac.v2.html",
                pdf: "https://i5.walmartimages.com/dfw/63fd9f59-69e7/k2-_3ce8f0b2-23b9-4c95-8cf8-2925a1210a83.v2.pdf",
              },
            },
          },
          agreementId: "85a556c1-88f7-4ba0-a756-5fe06aa361f4",
          adminAgreementId: "4c468971-05f5-4dc7-9e7a-76060e6971e2",
          getAgreementFromCCM: false,
          hideEditRLToEmail: true,
          falconOptimize: {
            rolloutEnabled: true,
            rolloutPercent: 1,
            cookieName: "selfReg",
            variation: "https://rllogin.wal-mart.com/ElectronicAgreement/#/",
            control: "/register",
          },
          iamTenantId: "YumaSupplierExperience_ROOT",
          givePrecedenceToExpPwd: true,
        },
        userRecoveryState: {
          recoverySuccess: false,
          recoveryError: "",
          spinner: false,
        },
        captchaState: {
          captchaId: null,
          captchaToken: null,
          captchaStatus: null,
          captchaValidated: false,
          fetching: false,
          validating: false,
          validationStatus: null,
        },
        rlmState: {
          userStatus: 0,
          emailId: null,
          userInfo: {},
          isRLEmailNotAvailable: false,
          isEmailUnique: true,
          isEmailUniqueError: "",
          migrationError: "",
          currentMigrationView: 0,
          emailVerified: false,
          newEmailId: null,
          newUserStatus: null,
          successMsgId: "",
          showGlobalSpinner: false,
          stepSuccess: null,
          helpGuideUrl:
            "https://supplierhelp.walmart.com/s/guide?channel=Stores&article=*********",
        },
        ssoState: [],
        mfaState: {
          showGlobalSpinner: false,
          stepSuccess: null,
          sms: [],
          voice: [],
          app: [],
          mfaError: "",
          type: "",
          credid: "",
          vipUrl: "https://vip.symantec.com/",
          mfaAppProvisionEnabled: true,
          playStoreUrl:
            "https://play.google.com/store/apps/details?id=com.verisign.mvip.main&hl=en",
          appStoreUrl:
            "https://apps.apple.com/app/vip-access-for-iphone/id307658513",
        },
        secRegState: {
          disableSave: true,
          uiModel: [
            {
              id: 0,
              qValue: "",
              aValue: "",
              disableAns: true,
              options: [],
              error: "",
            },
            {
              id: 1,
              qValue: "",
              aValue: "",
              disableAns: true,
              options: [],
              error: "",
            },
            {
              id: 2,
              qValue: "",
              aValue: "",
              disableAns: true,
              options: [],
              error: "",
            },
          ],
        },
      };
    </script>
    <span class="cleanslate TridactylStatusIndicator TridactylModenormal"
      >normal</span
    >
    <script src="login_files/main.bundle.a5f6047192ce389e918d.js"></script>
    <script>
      if (window.webappStart) webappStart();
    </script>
    <noscript>
      <h4>JavaScript is Disabled</h4>
      <p>Sorry, this webpage requires JavaScript to function correctly.</p>
      <p>Please enable JavaScript in your browser and reload the page.</p>
    </noscript>
  </body>
  <iframe
    class="cleanslate hidden"
    src="login_files/commandline.html"
    id="cmdline_iframe"
    loading="lazy"
    style="height: 0px !important"
  ></iframe>
</html>
