"""Costco-specific data models."""

from typing import Optional

from pydantic import BaseModel

from integrations.scheduling.models import (
    SchedulingBaseRequest,
    SchedulingActionType,
    SchedulingPlatform,
    Credentials,
)


class CostcoAppointmentData(BaseModel):
    """Costco appointment data."""

    appointmentId: Optional[str] = ""
    appointmentTime: str
    carrierId: str
    dock: Optional[str] = ""
    duration: int
    loadId: Optional[str] = ""
    notes: Optional[str] = ""
    status: Optional[str] = ""


class CostcoLoginRequest(SchedulingBaseRequest):
    """Costco login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    credentials: Credentials


class CostcoGetLoadTypesRequest(SchedulingBaseRequest):
    """Costco-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class CostcoGetOpenSlotsRequest(SchedulingBaseRequest):
    """Costco-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class CostcoGetWarehouseRequest(SchedulingBaseRequest):
    """Costco-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class CostcoCancelAppointmentRequest(SchedulingBaseRequest):
    """Costco-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    appointmentId: str
    reason: Optional[str] = ""


class CostcoGetAppointmentRequest(SchedulingBaseRequest):
    """Costco-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    appointment: CostcoAppointmentData


class CostcoMakeAppointmentRequest(SchedulingBaseRequest):
    """Costco-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    appointment: CostcoAppointmentData


class CostcoUpdateAppointmentRequest(SchedulingBaseRequest):
    """Costco-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.COSTCO

    appointment: CostcoAppointmentData
    appointmentId: str
