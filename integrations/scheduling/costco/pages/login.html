<html
  lang="en-us"
  xmlns:htmldb="http://htmldb.oracle.com"
  class="TridactylThemeMidnight"
>
  <head>
    <meta http-equiv="x-ua-compatible" content="IE=edge" />
    <title>Appointment Login</title>
    <link rel="stylesheet" href="login_files/theme_4_0.css" type="text/css" />
    <!--[if IE
      ]><link rel="stylesheet" href="/i/themes/theme_20/ie.css" type="text/css"
    /><![endif]-->
    <link rel="stylesheet" href="login_files/Core.min.css" type="text/css" />
    <link
      rel="stylesheet"
      href="login_files/Theme-Standard.min.css"
      type="text/css"
    />

    <link rel="stylesheet" href="login_files/5.0.min.css" type="text/css" />

    <script>
      var apex_img_dir = "/i/",
        htmldb_Img_Dir = apex_img_dir;
    </script>
    <script src="login_files/desktop_all.min.js"></script>
    <script src="login_files/wwv_flow.js_messages"></script>
    <script src="login_files/legacy_pre18.min.js"></script>
    <script src="login_files/legacy_18.min.js"></script>
    <script src="login_files/jquery-migrate-3.3.0.min.js"></script>

    <script type="text/javascript">
      var throttleInterval;
      function throttleCounter() {
        try {
          var throttleElement = $x("apex_login_throttle_sec");
          throttleInterval = setInterval(function () {
            stopThrottleCounter();
          }, 1000);
        } catch (err) {}
      }

      function stopThrottleCounter() {
        var throttleElement = $x("apex_login_throttle_sec");

        if (throttleElement.innerHTML == 1) {
          clearInterval(throttleInterval);
          $x_Hide("apex_login_throttle_div");
        } else
          throttleElement.innerHTML = Number(throttleElement.innerHTML) - 1;
      }

      function chk_password() {
        var v_uname = $v("P1001_USERNAME");
        var v_uname_new = v_uname.replace(/&/g, "~");
        var v_pwd = $x("P1001_PASSWORD").value;
        var get = new htmldb_Get(
          null,
          html_GetElement("pFlowId").value,
          "APPLICATION_PROCESS=CHK_LOGIN_PASSWORD_NEW",
          $v("pFlowStepId"),
        );
        get.addParam("x01", v_uname);
        get.addParam("x02", v_pwd);
        var gReturn = get.get();
        //alert('1gReturn'+gReturn);
        gReturn = gReturn.substring(0, 1);
        //alert('2gReturn'+gReturn);
        if (gReturn == 0) {
          doSubmit("LOGIN");
        } else if (gReturn == 2) {
          alert("Username and Password does not match.");
        } else if (gReturn == 1) {
          url =
            "f?p=201:249:9728113860698::::P249_USERNAME,P249_ITEM,P249_TYPE:" +
            v_uname_new +
            ",0,T";
          w = window.open(
            url,
            "New1",
            "Scrollbars=1,resizable=1,top=20,left=20,width=630,height=270",
          );
        } else if (gReturn == 3) {
          url =
            "f?p=201:249:9728113860698::::P249_USERNAME,P249_ITEM,P249_TYPE:" +
            v_uname_new +
            ",0,P";
          w = window.open(
            url,
            "New1",
            "Scrollbars=1,resizable=1,top=20,left=20,width=630,height=270",
          );
        } else if (gReturn == 7) {
          url =
            "f?p=201:249:9728113860698::::P249_USERNAME,P249_ITEM,P249_TYPE:" +
            v_uname_new +
            ",0,P";
          w = window.open(
            url,
            "New1",
            "Scrollbars=1,resizable=1,top=20,left=20,width=630,height=270",
          );
        } else if (gReturn == 5 || gReturn == 4) {
          var vLabel;
          if (gReturn == 4) {
            vLabel = "Current Password";
          } else {
            vLabel = "Temporary Password";
          }

          //  url = 'f?p=201:464:9728113860698::::P464_USERNAME,P464_PASSWORD,P464_LABEL,P464_ITEM,P464_FROM_PAGE:'+v_uname_new+','+v_pwd+','+vLabel+',0,1001';
          url =
            "f?p=201:464:9728113860698::::P464_USERNAME,P464_LABEL,P464_ITEM,P464_FROM_PAGE:" +
            v_uname_new +
            "," +
            vLabel +
            ",0,1001";
          w = window.open(
            url,
            "New1",
            "Scrollbars=1,resizable=1,top=20,left=20,width=730,height=400",
          );
        }
      }

      function vendorregistration() {
        doSubmit("REGISTRATIONV");
      }

      function detect_browser() {
        var nVer = navigator.appVersion;
        var nAgt = navigator.userAgent;
        var browserName = navigator.appName;
        var fullVersion = "" + parseFloat(navigator.appVersion);
        var majorVersion = parseInt(navigator.appVersion, 10);
        var nameOffset, verOffset, ix;
        //console.log('browserName  ' + nAgt  + '<br>' +nAgt.indexOf("Edge") );
        //console.log( nAgt.substring(nameOffset,verOffset) );
        // In Opera, the true version is after "Opera" or after "Version"
        if ((verOffset = nAgt.indexOf("Opera")) != -1) {
          browserName = "Opera";
          fullVersion = nAgt.substring(verOffset + 6);
          if ((verOffset = nAgt.indexOf("Version")) != -1)
            fullVersion = nAgt.substring(verOffset + 8);
        }
        // In MSIE, the true version is after "MSIE" in userAgent
        else if ((verOffset = nAgt.indexOf("MSIE")) != -1) {
          browserName = "Microsoft Internet Explorer";
          fullVersion = nAgt.substring(verOffset + 5);
        }
        // In IE browser above 10
        else if (!window.ActiveXObject && "ActiveXObject" in window) {
          browserName = "Microsoft Internet Explorer";
          // fullVersion = nAgt.substring(verOffset+5);
          var rv = nAgt.indexOf("rv:");
          fullVersion = nAgt.substring(rv + 3, nAgt.indexOf(".", rv));
        } else if ((verOffset = nAgt.indexOf("Edge")) != -1) {
          browserName = "Microsoft Edge";
          fullVersion = nAgt.substring(verOffset + 5);
        }

        // In Chrome, the true version is after "Chrome"
        else if ((verOffset = nAgt.indexOf("Chrome")) != -1) {
          browserName = "Chrome";
          fullVersion = nAgt.substring(verOffset + 7);
        }
        // In Safari, the true version is after "Safari" or after "Version"
        else if ((verOffset = nAgt.indexOf("Safari")) != -1) {
          browserName = "Safari";
          fullVersion = nAgt.substring(verOffset + 7);
          if ((verOffset = nAgt.indexOf("Version")) != -1)
            fullVersion = nAgt.substring(verOffset + 8);
        }
        // In Firefox, the true version is after "Firefox"
        else if ((verOffset = nAgt.indexOf("Firefox")) != -1) {
          browserName = "Firefox";
          fullVersion = nAgt.substring(verOffset + 8);
        }
        // In most other browsers, "name/version" is at the end of userAgent
        else if (
          (nameOffset = nAgt.lastIndexOf(" ") + 1) <
          (verOffset = nAgt.lastIndexOf("/"))
        ) {
          browserName = nAgt.substring(nameOffset, verOffset);
          fullVersion = nAgt.substring(verOffset + 1);
          if (browserName.toLowerCase() == browserName.toUpperCase()) {
            browserName = navigator.appName;
          }
        }
        // trim the fullVersion string at semicolon/space if present
        if ((ix = fullVersion.indexOf(";")) != -1)
          fullVersion = fullVersion.substring(0, ix);
        if ((ix = fullVersion.indexOf(" ")) != -1)
          fullVersion = fullVersion.substring(0, ix);
        majorVersion = parseInt("" + fullVersion, 10);
        if (isNaN(majorVersion)) {
          fullVersion = "" + parseFloat(navigator.appVersion);
          majorVersion = parseInt(navigator.appVersion, 10);
        }
        //alert(browserName );
        var v_version =
          "Browser name  = " +
          browserName +
          "<br>" +
          "Full version  = " +
          fullVersion +
          "<br>" +
          "Major version = " +
          majorVersion +
          "<br>" +
          "navigator.appName = " +
          navigator.appName +
          "<br>" +
          "navigator.userAgent = " +
          navigator.userAgent +
          "<br>";
        $x("P1001_BROWSER").value = v_version;
        $x("P1001_BROWSER_NAME").value = browserName;
        $x("P1001_BROWER_VERSION").value = fullVersion;
        console.log(browserName);
      }

      function chk_prod_password() {
        var v_uname = $v("P1001_USERNAME");
        var v_uname_new = v_uname.replace(/&/g, "~");
        var v_pwd = $x("P1001_PASSWORD").value;
        var get = new htmldb_Get(
          null,
          html_GetElement("pFlowId").value,
          "APPLICATION_PROCESS=CHK_LOGIN_PASSWORD_NEW",
          $v("pFlowStepId"),
        );
        get.addParam("x01", v_uname);
        get.addParam("x02", v_pwd);
        var gReturn = get.get();
        gReturn = gReturn.substring(0, 1);
        if (gReturn == 0) {
          doSubmit("PRODCHECK");
        } else if (gReturn == 2) {
          alert(
            "Your password is required to update. Please update the password using costcotraffic.com.",
          );
        } else if (gReturn == 1) {
          alert(
            "Your password is required to update. Please update the password using costcotraffic.com.",
          );
        } else if (gReturn == 3) {
          alert(
            "Your password is required to update. Please update the password using costcotraffic.com.",
          );
        } else if (gReturn == 7) {
          alert(
            "Your password is required to update. Please update the password using costcotraffic.com.",
          );
        } else if (gReturn == 5 || gReturn == 4) {
          alert(
            "Your password is required to update. Please update the password using costcotraffic.com.",
          );
        }
      }
      function redirect_prod() {
        var v_uname = $v("P1001_USERNAME");
        if (v_uname == "") {
          alert("Please enter valid username.");
        } else {
          var get = new htmldb_Get(
            null,
            html_GetElement("pFlowId").value,
            "APPLICATION_PROCESS=CHK_USER_TYPE",
            $v("pFlowStepId"),
          );
          get.addParam("x01", v_uname);
          var gReturn = get.get();
          /* D Depot
      P Prepaid */
          if (gReturn == "E") {
            alert("Username does not exist. Please enter valid username.");
          } else if (gReturn == "D") {
            v_url =
              "https://www.costcotraffic.com/pls/apex/f?p=110:101:::::P101_HIDDEN,P101_USERNAME:1," +
              v_uname;
            location.replace(v_url);
          } else if (gReturn == "P") {
            v_url =
              "https://www.costcotraffic.com/pls/apex/f?p=110:1001:::::P1001_HIDDEN,P1001_USERNAME:1," +
              v_uname;
            location.replace(v_url);
          }
        }
      }
    </script>
    <style>
      a {
        color: hotpink;
      }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <style type="text/css">
      @media print {
        .TridactylStatusIndicator {
          display: none !important;
        }
      }
    </style></head
  ><iframe
    class="cleanslate hidden"
    src="login_files/commandline.html"
    id="cmdline_iframe"
    loading="lazy"
    style="height: 0px !important"
  ></iframe>
  <body style="background-color: rgb(234, 239, 245); touch-action: pan-y">
    <form
      action="wwv_flow.accept"
      method="post"
      name="wwv_flow"
      id="wwvFlowForm"
      data-oj-binding-provider="none"
      novalidate=""
      autocomplete="off"
    >
      <input type="hidden" name="p_flow_id" value="201" id="pFlowId" /><input
        type="hidden"
        name="p_flow_step_id"
        value="1001"
        id="pFlowStepId"
      /><input
        type="hidden"
        name="p_instance"
        value="9728113860698"
        id="pInstance"
      /><input
        type="hidden"
        name="p_page_submission_id"
        value="1762208536408498547896932818597302100"
        id="pPageSubmissionId"
      /><input type="hidden" name="p_request" value="" id="pRequest" /><input
        type="hidden"
        name="p_reload_on_submit"
        value="A"
        id="pReloadOnSubmit"
      /><input
        type="hidden"
        value="1762208536408498547896932818597302100"
        id="pSalt"
      />
      <table
        class="regionlayout"
        summary=""
        cellpadding="0"
        border="0"
        cellspacing="0"
        width="100%"
        role="presentation"
      >
        <tbody>
          <tr>
            <td valign="top">
              <style type="text/css">

                table.apexir_WORKSHEET_DATA th { padding:0px 0px 0px 0px}
                table.apexir_WORKSHEET_DATA td { padding:1px 5px}
                table.apexir_WORKSHEET_DATA td {border-right:1px solid #CCCCCC}
                table.apexir_WORKSHEET_DATA td {border-top:1px solid #CCCCCC}
                table.apexir_WORKSHEET_DATA td {font-size:11px;font-family:Arial;}
                // table.apexir_WORKSHEET_DATA td {background-color: #FFFFFF !important;} remove for shipment shading

                .t20ButtonHolder {
                    padding-top: 6px !important;
                }

                body {
                    background-color: #EAEFF5 !important;
                }

                .apexir_ACTION_MENU button:hover, .t20Button:hover, .apexir_ACTION_MENU button:focus, .t20Button:focus, .apexir-go-button:hover, .apexir-button:hover,
                .apexir-go-button:focus, .apexir-button:focus{
                    background-color: #0000FF !important;
                    border: 2px !important;
                    border-style: solid !important;
                    border-color:red !important;
                }
                .t20ButtonR:focus,.t20ButtonR:hover{
                    background-color: #FF0000 !important;
                    border: 2px !important;
                    border-style: solid !important;
                    border-color:navy !important;
                }

                .t20ButtonRC{
                      border-radius: 6px 6px 6px 6px !important;
                     background-color: #FF0000 !important;
                    border: 2px !important;
                    border-style: solid !important;
                    border-color:navy !important;
                   color: #FFFFFF !important;
                   font-weight: bold !important;
                   margin-left:340px;
                   font-size:15px;
                }
                .t20InlineError{
                    cursor: help;
                    font-weight: normal;
                    white-space: wrap;
                    color: #FF0000;
                }

                .t20Button{
                    background: none repeat scroll 0 0 #3872BF !important;
                    border-color: #144485 #144485 #0B274D !important;
                    border-image: none !important;
                    margin: 2px 5px 2px 5px !important;
                    border-radius: 6px 6px 6px 6px !important;
                    border-style: solid !important;
                    border-width: 2px !important;
                    color: #FFFFFF !important;
                    padding: 1px !important;
                    min-width:30px;
                }

                .t20ButtonAppt {
                 background:url('tmsappt/r/files/static/v160Y/ButtonAlternative3.gif') repeat scroll 0 0 transparent;
                 color:#FFFFFF !important;
                 font-weight:bold;
                 border-width: 0;
                    padding: 1px !important;
                    margin: 0.1px 0.1px 0.1px 0.1px !important;
                 cursor:pointer;
                 text-decoration:none !important;
                }

                .t20ButtonAppt_1 {
                 background:url('tmsappt/r/files/static/v160Y/ButtonAlternative3.gif') repeat scroll 0 0 transparent;
                 color:#FFFFFF !important;
                 font-weight:bold;
                 border-width: 0;
                    padding: 1px !important;
                    margin: 0.1px 0.1px 0.1px 0.1px !important;
                 cursor:pointer;
                 text-decoration:none !important;
                }


                .t20ButtonR {
                    background: none repeat scroll 0 0 #DC143C;
                    border-color: #FF0000 #FF0000 #0B274D !important;
                    border-image: none !important;
                    margin: 2px 5px 2px 5px !important;
                    border-radius: 6px 6px 6px 6px !important;
                    border-style: solid !important;
                    border-width: 2px !important;
                    color: #FFFFFF !important;
                    padding: 1px !important;
                    min-width:30px;
                    font-weight: bold !important;
                    text-decoration: none !important;
                    text-align: center !important;
                }

                .t20ButtonR1 {
                    background: none repeat scroll 0 0 #DC143C;
                    border-color: #FF0000 #FF0000 #0B274D !important;
                    border-image: none !important;
                    margin: 2px 5px 2px 5px !important;
                    border-radius: 6px 6px 6px 6px !important;
                    border-style: solid !important;
                    border-width: 2px !important;
                    color: #FFFFFF !important;
                    padding: 1px !important;
                    min-width:30px;
                    font-weight: bold !important;

                    text-decoration: none !important;
                    text-align: center !important;
                }

                .apexir-go-button, .apexir-button {
                    background: none repeat scroll 0 0 #3872BF !important;
                    border-color: #144485 #144485 #0B274D !important;
                    border-image: none !important;
                    margin: 2px 2px 2px 0 !important;
                    border-radius: 6px 6px 6px 6px !important;
                    border-style: solid !important;
                    border-width: 2px !important;
                    color: #FFFFFF !important;
                    min-width:30px;
                }

                .apexir-go-button span,.apexir-button span{
                    color: #FFFFFF !important;
                    font-weight:bold !important;
                }

                .sethover{
                    background-color: #0000FF !important;
                    border: 2px !important;
                    border-style: solid !important;
                    border-color:red !important;
                }

                .sethoverR{
                    background-color: #FF0000 !important;
                    border: 2px !important;
                    border-style: solid !important;
                    border-color:navy !important;
                }

                .apexir_ACTION_MENU button {
                    background: none repeat scroll 0 0 #3872BF;
                    border-color: #144485 #144485 #0B274D;
                    border-image: none;
                    border-style: solid;
                    border-radius: 6px 6px 6px 6px;
                    border-width: 2px;
                }

                .apexir_ACTION_MENU span {
                    color: #FFFFFF !important;
                    font-weight:bold !important;
                }


                .t20datav2 {
                    background: url('tmsappt/r/files/static/v160Y/purple.png') repeat scroll 0 0 transparent;
                    background-repeat: repeat-x;
                    border: 0 solid #A3BED8;
                    color: navy;
                    font-size: 9pt;
                    font-weight: bold;
                    padding: 1px 8px 10px;
                    text-align: left;
                    vertical-align: top;
                    white-space: nowrap;
                    width:1100px;
                }

                .t20datav2G {
                    background: url('tmsappt/r/files/static/v160Y/hdingBar-BG_V2-green.gif') repeat scroll 0 0 transparent;
                    background-repeat: repeat-x;
                    border: 0 solid #A3BED8;
                    color: navy;
                    font-size: 9pt;
                    font-weight: bold;
                    padding: 1px 8px 10px;
                    text-align: left;
                    vertical-align: top;
                    white-space: nowrap;
                    width:1100px;
                }
                .t20RegionHeaderBig {
                    vertical-align: center;
                    font-weight: normal;
                    font-size: 16pt;
                    font-weight: bold;
                    color: navy;
                    background-image: url('tmsappt/r/files/static/v160Y/hdingBar1-BG.gif');
                    background-repeat: repeat-x;
                    background-size: 10px;
                    text-align: left;
                    padding: 1px 8px;
                    white-space: nowrap;
                    border: 1px solid #a3bed8;
                }
                .radio_group{ white-space:nowrap; !important;}

                .t20OptionalLabel{color:#22316C !important;font-weight:bold;}
                .t20OptionalLabelR{color:#F10C0C !important;font-weight:bold;}

                .t20RegionHeader{color:navy;}
                table.vertical1 td.L {color:navy;font-weight:bold;}

                .blue-button {
                color: #ffffff;
                background-color: #1b8cdb;
                width: auto;
                padding: 7px;
                border-radius: 10px;
                border: none;
                text-decoration: none;
                }
                .blue-button1 {
                color: #ffffff;
                background-color: #1b8cdb;
                width: auto;
                padding: 7px;
                border-radius: 10px;
                border: none;
                text-decoration: none;
                }
                .green-button {
                color: #ffffff;
                background-color: #53c055;
                width: auto;
                padding: 7px;
                border-radius: 10px;
                border: none;
                text-decoration: none;
                }
                .green-button img{
                width: 18px;
                margin: -5px 5px -5px 0;
                }

                .v2R{font-weight:bold;color:black;white-space:nowrap;text-align:right;}
                .v2txt{
                    background: none repeat scroll 0 0 #EAEFF5;
                    border: 0 none;
                    color: #EAEFF5;
                    padding: 0;
                    }
              </style>

              <script>
                jQuery(function ($) {
                  $(".t20Button").hover(function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir-go-button").hover(function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir-button").hover(function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir_ACTION_MENU button").hover(function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".t20ButtonR").hover(function () {
                    $(this).toggleClass("sethoverR");
                  });

                  $(".t20Button").bind("focus focusout", function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir-go-button").bind("focus focusout", function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir-button").bind("focus focusout", function () {
                    $(this).toggleClass("sethover");
                  });

                  $(".apexir_ACTION_MENU button").bind(
                    "focus focusout",
                    function () {
                      $(this).toggleClass("sethover");
                    },
                  );

                  $(".t20ButtonR").bind("focus focusout", function () {
                    $(this).toggleClass("sethoverR");
                  });
                });

                function nvl(id, num) {
                  if ($v_IsEmpty(id)) return num;
                  else return parseFloat($v(id));
                }
                function setFocusV2(pThis) {
                  $x(pThis).focus();
                }

                function setButtonBrDotted(pThis) {
                  // $x(pThis).style.padding = "0px";
                  // $x(pThis).style.border = "solid red 2px" ;
                }
                function setButtonBrNone(pThis) {
                  //  $x(pThis).style.padding = "2px 2px 3px 2px";
                  // $x(pThis).style.border = "none" ;
                }

                function setButtonBrDottedR(pThis) {
                  // $x(pThis).style.padding = "0px";
                  // $x(pThis).style.border = "solid navy 2px" ;
                }

                function openMiles(url) {
                  w = open(
                    url,
                    "winLov",
                    "Scrollbars=1,resizable=1,width=1250,height=600,left=10,top=120",
                  );
                  if (w.opener == null) w.opener = self;
                  w.focus();
                }

                function openSMSRating(pThis) {
                  var get = new htmldb_Get(
                    null,
                    html_GetElement("pFlowId").value,
                    "APPLICATION_PROCESS=validate_dot_number",
                    $v("pFlowStepId"),
                  );
                  get.addParam("x01", pThis);
                  gReturn = get.get();

                  if (gReturn == 0)
                    alert("SMS Rating not found for DOT # :" + pThis);
                  else {
                    url = "f?p=201:182:9728113860698:::RP:P182_DOT:" + pThis;
                    w = open(
                      url,
                      "winLov",
                      "Scrollbars=1,resizable=1,width=550,height=300,left=200,top=200",
                    );
                    if (w.opener == null) w.opener = self;
                    w.focus();
                  }
                }

                function table_object(pId) {
                  this.id = pId;
                  this.interval = false;
                  this.call_refresh = function () {
                    $a_report(pId.substring(1), null, null, null);
                  };
                }
                function formatNumber_Deci(pThis) {
                  v_val = pThis.value;
                  no_of_dig = v_val.length;
                  v_return = "";
                  v_return1 = "";
                  v_val = v_val.split(".");
                  v_sign = "";

                  v_val1 = v_val[0];
                  v_len = v_val1.length;

                  for (i = 0; i < v_len; i++) {
                    v_c = "";
                    v_c = v_val1.substr(i, 1);

                    if (i == 0 && v_c == "-") v_sign = "-";

                    if (!isNaN(v_c)) v_return = v_return + v_c;
                  }

                  if (v_val[1] != "" && v_val[1] != undefined) {
                    v_val2 = v_val[1];
                    v_len = v_val2.length;
                    for (i = 0; i < v_len; i++) {
                      v_c = "";
                      v_c = v_val2.substr(i, 1);

                      if (!isNaN(v_c)) v_return1 = v_return1 + v_c;
                    }
                    pThis.value = v_return;
                    pThis.value = pThis.value + "." + v_return1;
                  } else pThis.value = v_return;

                  pThis.value = Math.round(pThis.value * 100) / 100;
                  //alert(pThis.value);

                  if (v_sign == "-") pThis.value = -1 * parseFloat(pThis.value);

                  var result = pThis.value;
                  result = result.toString().split(".");
                  if (result.length == 1) {
                    result[0] += ".00";
                    pThis.value = result[0];
                  } else if (result[1].length == 1) {
                    result[1] += "0";
                    pThis.value = result[0] + "." + result[1];
                  } else if (result[1].length == 2) {
                    //result[1] += '0';
                    pThis.value = result[0] + "." + result[1];
                  }
                  //adding commas to number
                  nStr = pThis.value;
                  nStr += "";
                  nStr = nStr.replace(/,/g, "");
                  x1 = nStr;
                  var rgx = /(\d+)(\d{3})/;
                  while (rgx.test(x1)) {
                    x1 = x1.replace(rgx, "$1" + "," + "$2");
                  }
                  s = new String(x1);
                  no_of_deci = "";
                  for (i = 1; i <= no_of_dig; i++) {
                    no_of_deci += "0";
                  }

                  if (s.indexOf(".") == -1 && no_of_dig != 0) {
                    // alert(s.indexOf('.'));
                    s += "." + no_of_deci;
                  }

                  pThis.value = s;
                  // }
                  //alert(s.toFixed(2));
                  //cal_charges();
                }

                function cal_charges() {
                  //alert('in cal charges');
                  var v_str1 = $x("P201_CUST_B_FEE").value;
                  var v_str2 = $v("P201_CUSTOMS_DUTY");
                  var v_str3 = $v("P201_HARBOR_MAINTENANCE_FEE");
                  var v_str4 = $v("P201_OCEAN_FREIGHT_CHARGES");

                  var v_cust_fee = v_str1.replace(/,/g, "");
                  var v_cust_duty = v_str2.replace(/,/g, "");
                  var v_har_fee = v_str3.replace(/,/g, "");
                  var v_ocean_charges = v_str4.replace(/,/g, "");

                  //alert('Charges'+v_cust_fee+','+v_cust_duty+','+v_har_fee+','+v_ocean_charges);

                  var get = new htmldb_Get(
                    null,
                    $v("pFlowId"),
                    "APPLICATION_PROCESS=calc_total",
                    $v("pFlowStepId"),
                  );
                  get.addParam("x01", v_cust_fee);
                  get.addParam("x02", v_cust_duty);
                  get.addParam("x03", v_har_fee);
                  get.addParam("x04", v_ocean_charges);

                  gReturn = get.get("XML");

                  //alert('gReturn '+gReturn);

                  if (gReturn) {
                    var l_Count = gReturn.getElementsByTagName("item").length;
                    for (var i = 0; i < l_Count; i++) {
                      var l_Opt_Xml = gReturn.getElementsByTagName("item")[i];
                      var l_ID = l_Opt_Xml.getAttribute("id");
                      var l_El = html_GetElement(l_ID);
                      if (l_Opt_Xml.firstChild) {
                        var l_Value = l_Opt_Xml.firstChild.nodeValue;
                      } else {
                        var l_Value = "";
                      }

                      if (l_El) {
                        if (l_El.tagName == "INPUT") {
                          l_El.value = l_Value;
                        } else if (
                          l_El.tagName == "SPAN" &&
                          l_El.className == "grabber"
                        ) {
                          l_El.parentNode.innerHTML = l_Value;
                          l_El.parentNode.id = l_ID;
                        } else {
                          l_El.innerHTML = l_Value;
                        }
                      }
                    }
                  }
                  get = null;
                }
                function validate_pickupsheet() {
                  var get = new htmldb_Get(
                    null,
                    html_GetElement("pFlowId").value,
                    "APPLICATION_PROCESS=app_validate_pickupsheet",
                    $v("pFlowStepId"),
                  );
                  get.addParam("x01", $x("P50_ID").value);

                  gReturn = get.get();

                  if (gReturn == 1) {
                    doSubmit("SENDEMAIL");
                  } else {
                    alert("Please assign the Delv. # to PO Lines");
                  }
                }

                function validateEmail(elem) {
                  //var emailExp = /^[\w\-\.\+\&]+\@[a-zA-Z0-9\.\-]+\.[a-zA-Z0-9]{2,}$/;
                  var emailExp =
                    /^[\w\-\.\+\&\']+\@[a-zA-Z0-9\.\-]+\.[a-zA-Z0-9]{2,}$/;
                  return emailExp.test(elem) ? true : false;
                }

                function valEmailsCommaSep(elem) {
                  if (elem.value != "") {
                    if (elem.value != " ") {
                      var result = elem.value.split(",");
                      for (var i = 0; i < result.length; i++) {
                        if (validateEmail(result[i]) == false) {
                          alert("Not a valid Email");
                          setItemFocus(elem);
                        }
                      }
                    } else {
                      alert("Please enter valid email address");
                      setItemFocus(elem);
                    }
                  }
                }

                function isNumericval(elem) {
                  if (elem.value != "") {
                    var numericExpression = /^[0-9\.]+$/;
                    if (elem.value.match(numericExpression)) return true;
                    else {
                      alert("Not a valid Number");
                      setItemFocus(elem);
                    }
                  }
                }

                function setItemFocus(elemId) {
                  checkFocus = false;
                  checkEl = elemId;
                  setTimeout(function () {
                    if (checkEl) checkEl.focus();
                    checkEl.select();
                  }, 100);
                  checkFocus = true;
                  return false;
                }

                function submitTab(pURL) {
                  $(".FCR").hide();
                  self.location = pURL;
                }
                function hideFCR() {
                  $(".FCR").hide();
                }

                function chat_popup() {
                  url = "f?p=201:194:9728113860698::NO::P194_PAGEID:194";
                  w = window.open(
                    url,
                    "New1",
                    "Scrollbars=1,resizable=1,top=600,left=900,width=500,height=400",
                  );
                }
                function open_map(pcity, pstate) {
                  var get = new htmldb_Get(
                    null,
                    $v("pFlowId"),
                    "APPLICATION_PROCESS=GET_LOCATION",
                    $v("pFlowStepId"),
                  );
                  get.addParam("x01", pcity);
                  get.addParam("x02", pstate);
                  var gReturn = get.get();
                  var result;
                  var v_long = "";
                  var v_lati = "";
                  if (gReturn != "0") {
                    result = gReturn.split("~");
                    v_long = result[0];
                    v_lati = result[1];
                    var url =
                      "f?p=201:266:9728113860698::::P266_LATITUDE,P266_LONGITUDE:" +
                      v_lati +
                      "," +
                      v_long;
                    w = open(
                      url,
                      "winLov",
                      "Scrollbars=1,width=730,height=1000,left=10,top=120",
                    );
                    if (w.opener == null) w.opener = self;
                    w.focus();
                  } else alert("Not valid location");
                }
              </script>
              <script>
                function numberVal_Deci(pThis) {
                  try {
                    v_val = pThis.value;
                  } catch (err) {
                    v_val = "0";
                  }
                  v_return = "";
                  v_val = v_val.split(".");

                  v_str = v_val[0];
                  v_deci = v_val[1];
                  v_len = v_str.length;

                  for (i = 0; i < v_len; i++) {
                    v_c = "";
                    v_c = v_str.substr(i, 1);

                    if (!isNaN(v_c)) v_return = v_return + v_c;
                  }
                  if (v_len > 0 && v_val[1] != undefined) {
                    v_return = v_return + "." + v_deci;
                  }
                  try {
                    pThis.value = v_return;
                  } catch (err) {}
                }
              </script>
              <script>
                function formatNumber_Deci_new(pThis) {
                  try {
                    v_val = pThis.value;
                    no_of_dig = v_val.length;
                    v_return = "";
                    v_return1 = "";
                    v_val = v_val.split(".");
                    v_sign = "";

                    v_val1 = v_val[0];
                    v_len = v_val1.length;

                    for (i = 0; i < v_len; i++) {
                      v_c = "";
                      v_c = v_val1.substr(i, 1);

                      if (i == 0 && v_c == "-") v_sign = "-";

                      if (!isNaN(v_c)) v_return = v_return + v_c;
                    }

                    if (v_val[1] != "" && v_val[1] != undefined) {
                      v_val2 = v_val[1];
                      v_len = v_val2.length;
                      for (i = 0; i < v_len; i++) {
                        v_c = "";
                        v_c = v_val2.substr(i, 1);

                        if (!isNaN(v_c)) v_return1 = v_return1 + v_c;
                      }
                      pThis.value = v_return;
                      pThis.value = pThis.value + "." + v_return1;
                    } else pThis.value = v_return;

                    pThis.value = Math.round(pThis.value * 100) / 100;

                    if (v_sign == "-")
                      pThis.value = -1 * parseFloat(pThis.value);

                    var result = pThis.value;
                    result = result.toString().split(".");
                    if (result.length == 1) {
                      result[0] += ".00";
                      pThis.value = result[0];
                    } else if (result[1].length == 1) {
                      result[1] += "0";
                      pThis.value = result[0] + "." + result[1];
                    } else if (result[1].length == 2) {
                      //result[1] += '0';
                      pThis.value = result[0] + "." + result[1];
                    }
                    //adding commas to number
                    nStr = pThis.value;
                    nStr += "";
                    nStr = nStr.replace(/,/g, "");
                    x1 = nStr;
                    var rgx = /(\d+)(\d{3})/;
                    while (rgx.test(x1)) {
                      x1 = x1.replace(rgx, "$1" + "," + "$2");
                    }
                    s = new String(x1);
                    no_of_deci = "";
                    for (i = 1; i <= no_of_dig; i++) {
                      no_of_deci += "0";
                    }

                    if (s.indexOf(".") == -1 && no_of_dig != 0) {
                      // alert(s.indexOf('.'));
                      s += "." + no_of_deci;
                    }
                    pThis.value = s;
                    // }

                    //cal_charges();
                  } catch (err) {
                    alert("Exception");
                  }
                }
                function transit_map(p_audit_id) {
                  var url =
                    "f?p=201:316:9728113860698::::P316_audit_id:" + p_audit_id;
                  w = open(
                    url,
                    "winLov",
                    "Scrollbars=1,width=930,height=600,left=10,top=120",
                  );
                  if (w.opener == null) w.opener = self;
                  w.focus();
                }
              </script>
            </td>
          </tr>
          <tr>
            <td valign="top">
              <script type="text/javascript">
                function setItemFocus(elemId) {
                  checkFocus = false;
                  checkEl = elemId;
                  setTimeout(function () {
                    if (checkEl) checkEl.focus();
                    checkEl.select();
                  }, 100);
                  checkFocus = true;
                  return false;
                }
                function numberVal(pThis) {
                  v_val = pThis.value;
                  v_return = "";
                  v_val = v_val.split(".");
                  v_val = v_val[0];
                  v_len = v_val.length;
                  for (i = 0; i < v_len; i++) {
                    v_c = "";
                    v_c = v_val.substr(i, 1);

                    if (!isNaN(v_c)) v_return = v_return + v_c;
                  }
                  return v_return;
                }
                function setDateFormat(elemId) {
                  var elem = $x(elemId).value;
                  if (elem != "") {
                    elem = elem.replace(/[-.\/]/g, "");
                    if (numberVal(elemId) == elem) {
                      if (elem.length == 6) {
                        if (
                          elem.substring(0, 2) < 1 ||
                          elem.substring(0, 2) > 12
                        ) {
                          alert("Invalid value for Month");
                          $x(elemId).value = "";
                          setItemFocus(elemId);
                        } else if (
                          elem.substring(2, 4) < 1 ||
                          elem.substring(2, 4) > 31
                        ) {
                          alert("Invalid value for Day");
                          $x(elemId).value = "";
                          setItemFocus(elemId);
                        } else {
                          elem =
                            elem.substring(0, 2) +
                            "/" +
                            elem.substring(2, 4) +
                            "/" +
                            elem.substring(4);
                          $x(elemId).value = elem;
                        }
                      } else if (elem.length == 8) {
                        if (
                          elem.substring(0, 2) < 1 ||
                          elem.substring(0, 2) > 12
                        ) {
                          alert("Invalid value for Month");
                          $x(elemId).value = "";
                          setItemFocus(elemId);
                        } else if (
                          elem.substring(2, 4) < 1 ||
                          elem.substring(2, 4) > 31
                        ) {
                          alert("Invalid value for Day");
                          $x(elemId).value = "";
                          setItemFocus(elemId);
                        } else {
                          elem =
                            elem.substring(0, 2) +
                            "/" +
                            elem.substring(2, 4) +
                            "/" +
                            elem.substring(6);
                          $x(elemId).value = elem;
                        }
                      } else {
                        alert(
                          "Invalid date format." +
                            "\n" +
                            "Please enter MMDDYY format.",
                        );
                        $x(elemId).value = "";
                        setItemFocus(elemId);
                      }
                    } else {
                      alert("Invalid Date Format");
                      $x(elemId).value = "";
                      setItemFocus(elemId);
                    }
                  }
                }
              </script>
              <script>
                function emailValidator(elem) {
                  var emailExp =
                    /^[\w\-\.\+]+\@[a-zA-Z0-9\.\-]+\.[a-zA-Z0-9]{2,}$/;

                  if (elem.value != "") {
                    if (elem.value.match(emailExp)) {
                      return true;
                    } else {
                      alert("Not a valid Email");
                      checkFocus = false;
                      checkEl = elem;
                      setTimeout(function () {
                        if (checkEl) checkEl.focus();
                        checkEl.select();
                      }, 100);
                      checkFocus = true;
                      return false;
                    }
                  }
                }
                function numberVal1(pThis) {
                  v_val = pThis.value;
                  v_return = "";
                  v_val = v_val.split(".");
                  v_sign = "";

                  v_str = v_val[0];
                  v_deci = v_val[1];
                  v_len = v_str.length;

                  for (i = 0; i < v_len; i++) {
                    v_c = "";
                    v_c = v_str.substr(i, 1);

                    if (i == 0 && v_c == "-") v_sign = "-";

                    if (!isNaN(v_c)) v_return = v_return + v_c;
                  }

                  if (v_val[1] != undefined) {
                    v_len1 = v_deci.length;
                    v_returndeci = "";
                    for (i = 0; i < v_len1; i++) {
                      v_c = "";
                      v_c = v_deci.substr(i, 1);

                      if (!isNaN(v_c)) v_returndeci = v_returndeci + v_c;
                    }

                    if (v_len > 0 && !isNaN(v_returndeci)) {
                      v_return = v_return + "." + v_returndeci;
                    }
                  }

                  pThis.value = v_return;
                  pThis.value = Math.round(pThis.value * 100) / 100;

                  //alert(pThis.value);

                  if (v_sign == "-") pThis.value = -1 * parseFloat(pThis.value);

                  var result = pThis.value;
                  result = result.toString().split(".");
                  if (result.length == 1) {
                    result[0] += ".00";
                    pThis.value = result[0];
                  } else if (result[1].length == 1) {
                    result[1] += "0";
                    pThis.value = result[0] + "." + result[1];
                  }
                }
                function isNumericval(elem) {
                  if (elem.value != "") {
                    var numericExpression = /^[0-9\.]+$/;
                    if (elem.value.match(numericExpression)) return true;
                    else {
                      alert("Not a valid Number");
                      setItemFocus(elem);
                    }
                  }
                }
              </script>

              <script>
                function formatPhoneSet(pThis) {
                  var str = pThis.value;
                  var pNum = str.replace(/\(/g, "");
                  pNum = pNum.replace(/\)/g, "");
                  pNum = pNum.replace(/ /g, "");
                  pNum = pNum.replace(/\-/g, "");

                  if (pNum != "") {
                    var numericExpression = /^[0-9]+$/;
                    if (pNum.match(numericExpression)) {
                      var pL = pNum.length;
                      if (pL == 10) {
                        a = "(" + pNum.substring(0, 3) + ")";
                        b = pNum.substring(3, 6);
                        c = pNum.substring(6, 10);
                        pNum = a + " " + b + "-" + c;
                        pThis.value = pNum;
                        return true;
                      } else {
                        alert("Not a valid Number");
                        checkFocus = false;
                        checkEl = pThis;
                        setTimeout(function () {
                          if (checkEl) checkEl.focus();
                          checkEl.select();
                        }, 100);
                        checkFocus = true;
                        return false;
                        pThis.value = "";
                        setItemFocus(pThis);
                      }
                      return true;
                    } else {
                      alert("Not a valid Number");
                      checkFocus = false;
                      checkEl = pThis;
                      setTimeout(function () {
                        if (checkEl) checkEl.focus();
                        checkEl.select();
                      }, 100);
                      checkFocus = true;
                      return false;
                      pThis.value = "";
                      setItemFocus(pThis);
                    }
                  }
                }
                function logOutCostco() {
                  var get = new htmldb_Get(
                    null,
                    html_GetElement("pFlowId").value,
                    "APPLICATION_PROCESS=DELETEUNVERIFIEDPO",
                    $v("pFlowStepId"),
                  );
                  get.get();
                  var v_page = "";
                  if (v_page == 2001) window.location = "f?p=201:2001";
                  else window.location = "f?p=201:1001";
                }
              </script>
              <style type="text/css">
                body {
                  touch-action: pan-x pan-y pinch-zoom !important;
                }
              </style>
            </td>
          </tr>
        </tbody>
      </table>
      <div id="t20PageHeader" style="display: none">
        <table border="0" cellpadding="0" cellspacing="0" summary="">
          <tbody>
            <tr>
              <td id="t20Logo" valign="top">
                <span width="250px" height="60px"></span><br />
              </td>
              <td id="t20HeaderMiddle" valign="top" width="100%"><br /></td>
              <td id="t20NavBar" valign="top"><br /></td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="t20BreadCrumbsLeft">
        <br /><br />
        <div align="center">
          <a
            href="https://www.costco.com/online-offers.html?COSTID=Traffic_MVM_hero"
            target="_blank"
            ><img
              src="login_files/d_25w06127_march_em_hero_mvm_cover.webp"
              style="width: 1000px" /></a
          ><br />
          <img align="middle" src="login_files/costco_appt_home_logo.jpg" />
        </div>
        <script>
          $("#t20PageHeader").hide();
        </script>
        <center>
          <br /><br />
          <img align="middle" src="login_files/costco-logo-trans.png" />
          <br /><br />
          <span style="color: blue; font-weight: bold"
            >Please Enter Username and Password</span
          ><br /><br />
          <table
            id="apex_layout_823972762187715017"
            border="0"
            class="formlayout"
            role="presentation"
          >
            <tbody>
              <tr>
                <td align="right">
                  <label
                    for="P1001_USERNAME"
                    id="P1001_USERNAME_LABEL"
                    tabindex="999"
                    ><span class="t20OptionalLabel FTR">Username</span></label
                  >
                </td>
                <td colspan="2" align="left">
                  <input
                    type="text"
                    id="P1001_USERNAME"
                    name="P1001_USERNAME"
                    class="text_field apex-item-text"
                    size="40"
                    maxlength="100"
                    data-trim-spaces="NONE"
                  />
                </td>
              </tr>
              <tr>
                <td align="right">
                  <label
                    for="P1001_PASSWORD"
                    id="P1001_PASSWORD_LABEL"
                    tabindex="999"
                    ><span class="t20OptionalLabel FTR">Password</span></label
                  >
                </td>
                <td align="left">
                  <input
                    type="password"
                    name="P1001_PASSWORD"
                    size="40"
                    maxlength="100"
                    value=""
                    id="P1001_PASSWORD"
                    class="password apex-item-text"
                    autocomplete="off"
                    onkeydown="if (event.keyCode == 13) chk_password();"
                  />&nbsp;&nbsp;
                </td>
                <td align="left">
                  <a
                    href="javascript:chk_password();"
                    id="P1001_LOGIN_BUTTON"
                    title="Login Button"
                    ><img
                      src="login_files/Longin.png"
                      alt="Login Button"
                      border="#"
                      width="70px"
                      height="36px"
                  /></a>
                </td>
                <input
                  type="hidden"
                  id="P1001_LOGIN"
                  name="P1001_LOGIN"
                  value=""
                /><input
                  type="hidden"
                  id="P1001_HIDDEN"
                  name="P1001_HIDDEN"
                  value="0"
                /><input
                  type="hidden"
                  id="P1001_HIDDEN1"
                  name="P1001_HIDDEN1"
                  value=""
                />
              </tr>
            </tbody>
          </table>
          <table id="P1001_X" class="formlayout" role="presentation">
            <tbody>
              <tr>
                <td nowrap="nowrap" width="0px;" align="right">
                  <label for="P1001_X1" id="P1001_X1_LABEL" tabindex="999"
                    ><span class="t20OptionalLabel FTR">&nbsp;</span></label
                  >
                </td>
                <td align="left" valign="middle">
                  <span
                    id="P1001_X1"
                    class="display_only apex-item-display-only"
                    data-escape="true"
                  ></span>
                </td>
              </tr>
            </tbody>
          </table>
          <table
            id="apex_layout_823972762187715017"
            border="0"
            class="formlayout"
            role="presentation"
          >
            <tbody>
              <tr>
                <td align="center">
                  <a
                    href="javascript:apex.submit(%7Brequest:'REGISTRATIONV'%7D);"
                    id="P1001_VENDOR_REGISTRATION"
                    title="New Vendor Registration"
                    ><img
                      src="login_files/New-Vendor-Reg.png"
                      alt="New Vendor Registration"
                      border="#"
                      width="241px"
                      height="36px"
                  /></a>
                </td>
                <td align="left">
                  <a
                    href="javascript:apex.submit(%7Brequest:'REGISTRATION'%7D);"
                    id="P1001_REGISTRATION"
                    title="New Carrier Registration"
                    ><img
                      src="login_files/New-Carrier-Reg.png"
                      alt="New Carrier Registration"
                      border="#"
                      width="241px"
                      height="36px" /></a
                  ><input
                    type="hidden"
                    id="P1001_PWD"
                    name="P1001_PWD"
                    value=""
                  /><input
                    type="hidden"
                    id="P1001_BROWSER"
                    name="P1001_BROWSER"
                    value="Browser name  = Firefox&lt;br&gt;Full version  = 134.0&lt;br&gt;Major version = 134&lt;br&gt;navigator.appName = Netscape&lt;br&gt;navigator.userAgent = Mozilla/5.0 (X11; Linux x86_64; rv:134.0) Gecko/20100101 Firefox/134.0&lt;br&gt;"
                  /><input
                    type="hidden"
                    id="P1001_BROWSER_NAME"
                    name="P1001_BROWSER_NAME"
                    value="Firefox"
                  /><input
                    type="hidden"
                    id="P1001_BROWER_VERSION"
                    name="P1001_BROWER_VERSION"
                    value="134.0"
                  /><input
                    type="hidden"
                    id="P1001_KEYWORD"
                    name="P1001_KEYWORD"
                    value="costco depot appointment registration"
                  />
                </td>
              </tr>
            </tbody>
          </table>
          <script>
            if ($v("P1001_HIDDEN1") != 1 && $v("P1001_HIDDEN") != 1)
              setItemFocus(P1001_USERNAME);
          </script>
        </center>
        <table
          class="t20Region t20RegionwithoutTitle"
          id="R856447165300183472"
          border="0"
          cellpadding="0"
          cellspacing="0"
          summary=""
          align="center"
          style="padding: 15px"
        >
          <tbody id="R856447165300183472_body">
            <tr>
              <td class="t20ButtonHolder"></td>
            </tr>
            <tr>
              <td class="t20RegionBody">
                <center>
                  <table
                    id="apex_layout_856447165300183472"
                    border="0"
                    class="formlayout"
                    role="presentation"
                  >
                    <tbody>
                      <tr>
                        <td align="left" valign="middle">
                          <input
                            type="button"
                            value="Cliquez ici pour version française"
                            onclick="apex.navigation.redirect('f?p=201:2001::::::');"
                            id="P1001_REDIRECT"
                            class=""
                            style="
                              width: 210px;
                              background-color: #eaeff5;
                              border: 0px;
                              font-weight: bold;
                              text-decoration: underline;
                              color: blue;
                            "
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table
                    id="apex_layout_856447165300183472"
                    border="0"
                    class="formlayout"
                    role="presentation"
                  >
                    <tbody>
                      <tr>
                        <td align="left">
                          <input
                            type="button"
                            value="Forgot Password?"
                            onclick="apex.submit({request:'Go'});"
                            id="P1001_FORGOT_PASSWORD"
                            class=""
                            style="
                              width: 150px;
                              background-color: #eaeff5;
                              border: 0px;
                              font-weight: bold;
                              font-size: 15px;
                              text-decoration: underline;
                              color: blue;
                            "
                          />
                        </td>
                        <td align="left">
                          <input
                            type="button"
                            value="Forgot Username?"
                            onclick="apex.submit({request:'Go1'});"
                            id="P1001_FORGOT_USERNAME"
                            class=""
                            style="
                              width: 150px;
                              background-color: #eaeff5;
                              border: 0px;
                              font-weight: bold;
                              font-size: 15px;
                              text-decoration: underline;
                              color: blue;
                            "
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <a
                    href="https://appointments.cwtraffic.com/apex/tmsappt/r/files/static/v160Y/Costco%20Appointment%20System%20-%20Registration%20Details.pdf"
                    target="_blank"
                  >
                    <u style="color: Blue"
                      ><b
                        >Costco Appointment System - Registration Details</b
                      ></u
                    >
                  </a>

                  <br /><br /><br />
                  <a
                    href="https://appointments.cwtraffic.com/apex/tmsappt/r/files/static/v160Y/Costco%20Appointment%20System%20-%20Registration%20FAQ.pdf"
                    target="_blank"
                  >
                    <u style="color: Blue"
                      ><b>Costco Appointment System - Registration FAQ</b></u
                    >
                  </a>
                </center>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <table
        border="0"
        cellpadding="0"
        cellspacing="0"
        summary=""
        id="t20PageBody"
        height="70%"
        align="center"
        width="400"
      >
        <tbody>
          <tr>
            <td
              width="100%"
              valign="top"
              height="100%"
              id="t20ContentBody"
              align="center"
            >
              <div id="t20Messages">
                <span
                  id="APEX_SUCCESS_MESSAGE"
                  data-template-id="122385487362011820_S"
                  class="apex-page-success u-hidden"
                ></span
                ><span
                  id="APEX_ERROR_MESSAGE"
                  data-template-id="122385487362011820_E"
                  class="apex-page-error u-hidden"
                ></span>
              </div>
              <div id="t20ContentMiddle"></div>
            </td>
            <td valign="top" width="200" id="t20ContentRight"><br /></td>
          </tr>
        </tbody>
      </table>
      <script>
        /*$('.dhtmlMenu').children().css('color','#FFFFFF');
$('.apexir-go-button').children().css('color','#FFFFFF');*/
        $(".apexir_SAVED_REPORTS")
          .find("label")
          .css({ color: "#22316C", "font-weight": "bolder" });
        $(".apexir_ROW_SELECT_LIST")
          .find("label")
          .css({ color: "#22316C", "font-weight": "bolder" });
      </script>
      <table
        border="0"
        cellpadding="0"
        cellspacing="0"
        summary=""
        id="t20PageFooter"
        width="100%"
      >
        <tbody>
          <tr>
            <td id="t20Left" valign="top">
              <span id="t20UserPrompt"></span><br />
            </td>
            <td id="t20Center" valign="top"></td>
            <td id="t20Right" valign="top">
              <span id="t20Customize"></span><br />
            </td>
          </tr>
        </tbody>
      </table>
      <br class="t20Break" />
      <input type="hidden" id="pPageFormRegionChecksums" value="[]" />
      <input type="hidden" id="pPageItemsRowVersion" value="" /><input
        type="hidden"
        id="pPageItemsProtected"
        value="/zhGYOmWUQCPGyY4BXvankXO-WnVjzrF0W3IRtUWRGifk0B0PFPrKf8hSFIUcJ05-og8fkkuLvMy-fDdqThKYQA"
      />
    </form>

    <script type="text/javascript">
      apex.jQuery(function () {
        apex.page.init(this, function () {
          apex.jQuery.when
            .apply(apex.jQuery, apex.page.loadingDeferreds)
            .done(function () {
              try {
                (function () {
                  var lTimeoutField = document.getElementById(
                      "apex_login_throttle_sec",
                    ),
                    lTimeout = lTimeoutField ? +lTimeoutField.innerHTML : 0;
                  if (lTimeout) {
                    var lTimer = window.setInterval(function () {
                      if (lTimeout > 0) {
                        lTimeoutField.innerHTML = lTimeout;
                        lTimeout--;
                      } else {
                        window.clearInterval(lTimer);
                        var lDiv = document.getElementById(
                          "apex_login_throttle_div",
                        );
                        if (lDiv) {
                          lDiv.parentNode.removeChild(lDiv);
                          return true;
                        }
                      }
                    }, 1000);
                  }
                })();

                throttleCounter();
                detect_browser();
              } finally {
                apex.event.trigger(apex.gPageContext$, "apexreadyend");
              }
            });
        });
      });
    </script>

    <span class="cleanslate TridactylStatusIndicator TridactylModeinsert"
      >insert</span
    >
  </body>
</html>
