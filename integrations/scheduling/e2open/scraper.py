"""E2open Selenium scheduling implementation."""

from datetime import datetime, timedelta
from dateutil.parser import isoparse
from typing import Optional, Type, Tuple
import asyncio
import functools
import time

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select
from selenium.common.exceptions import (
    InvalidSessionIdException,
    NoSuchWindowException,
    SessionNotCreatedException,
    StaleElementReferenceException,
    TimeoutException,
    WebDriverException,
)

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from integrations.scheduling.e2open.utils import (
    _validate_slots,
    _collect_slots,
    nav_to_3rd_party_appt,
    set_date,
    _find_matches,
    _validate_make_appointment,
    handle_alerts,
    fill_3rd_party_form,
    _match_warehouse,
    parse_location,
    nav_to_search,
    fill_search_form,
    switch_popup,
    parse_location_3rd_party,
    fetch_stop_type,
    close_other_windows,
)
from session import add_cookies, with_driver
from utils.aws_utils.s3.s3_uploader import S3Uploader
from utils.aws_utils.s3.html_snapshot_service import (
    snapshot_and_upload_webpage,
)
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

from models.base import BaseResponse


from integrations.scheduling.models import (
    Appointment,
    CancelAppointmentResponse,
    Credentials,
    GetAppointmentResponse,
    GetLoadTypesResponse,
    GetWarehouseResponse,
    LoginResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
    ValidateAppointmentResponse,
)


from integrations.scheduling.e2open.models import (
    E2openCancelAppointmentRequest,
    E2openGetAppointmentRequest,
    E2openGetLoadTypesRequest,
    E2openGetOpenSlotsRequest,
    E2openGetOpenSlotsResponse,
    E2openGetWarehouseRequest,
    E2openLoginRequest,
    E2openMakeAppointmentRequest,
    E2openOpenSlotsAppointmentData,
    E2openUpdateAppointmentRequest,
    E2openValidateAppointmentRequest,
)

# E2open element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "userID"),
    "password_field": (By.ID, "password"),
    "login_button": (By.ID, "userSubmit"),
    "remember_me": (By.ID, "rememberMe"),
    "user_login_tab": (By.CSS_SELECTOR, "[data-tab-id='userLogin']"),
    "login_error": (By.CSS_SELECTOR, ".footer.msg.text-block-5"),
    "logged_in_indicator": (By.ID, "headerUserItem"),
    "cookie_button_locator": (By.ID, "accept-button"),
    # Base URLs and endpoints
    "base_url": "https://na-app.tms.e2open.com",
    "appointment_search_endpoint": "/apptschedule/carrierappointmentsearch.do",
    "pro_id_input": (By.NAME, "proNumString"),
    "search_button": (By.ID, "searchButton"),
    # Re-schedule and schedule appointment buttons have same IDs
    "reschedule_live_appt_link1": (By.ID, "test-liveappt0stop0"),
    "reschedule_live_appt_link2": (By.ID, "test-liveappt0stop1"),
}


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except TimeoutException:
        logger.debug("Not logged in: Login indicator not found")
        return False
    except (NoSuchWindowException, StaleElementReferenceException) as e:
        logger.warning(f"Browser state error during login check: {str(e)}")
        return False
    except (SessionNotCreatedException, InvalidSessionIdException) as e:
        logger.warning(f"Session error during login check: {str(e)}")
        return False
    except WebDriverException as e:
        logger.warning(f"WebDriver error during login check: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during login check: {str(e)}")
        return False


def check_cookie_notice(driver: WebDriver) -> None:
    """Check if the cookie notice is present and accept it if so.

    Args:
        driver: WebDriver instance
    """
    try:
        if is_element_present(
            driver, LOCATORS["cookie_button_locator"], timeout=2
        ):
            safe_click(driver, LOCATORS["cookie_button_locator"])
            wait_for_page_load(driver)
    except TimeoutException:
        logger.debug("No cookie notice found within timeout period")
        return True
    except WebDriverException as e:
        logger.warning(f"Failed to handle cookie notice: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error handling cookie notice: {str(e)}")
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting E2open login process")

        wait_for_element(driver, LOCATORS["user_login_tab"])
        user_tab = driver.find_element(*LOCATORS["user_login_tab"])
        if "selected" not in user_tab.get_attribute("class"):
            safe_click(driver, LOCATORS["user_login_tab"])

        wait_for_element(driver, LOCATORS["username_field"])
        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        remember_me = driver.find_element(*LOCATORS["remember_me"])
        if not remember_me.is_selected():
            safe_click(driver, LOCATORS["remember_me"])

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        logger.debug(f"Current E2open URL: {driver.current_url}")

        try:
            if is_element_present(driver, LOCATORS["login_error"], timeout=2):
                error_msg = get_element_text(driver, LOCATORS["login_error"])
                logger.error(f"E2open Login failed: {error_msg}")
                return False, error_msg
        except Exception:
            # Silently continue if no error message found
            pass

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"E2open Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("e2open", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            driver.get(PLATFORM_URLS["e2open"])

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["e2open"])

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")

                driver.delete_all_cookies()

                logger.info("Deleted all cookies")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(PLATFORM_URLS["e2open"])

        check_cookie_notice(driver)

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("e2open", credentials.username, new_cookies)

            main_window = driver.current_window_handle
            close_other_windows(driver, main_window)
            logger.info("Closed additional browser windows")

            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: E2openLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to E2open.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: E2openGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from E2open.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(ValidateAppointmentResponse)
def validate_appointment(
    request: E2openValidateAppointmentRequest, driver: WebDriver
) -> ValidateAppointmentResponse:
    """Validate an appointment in E2open.

    Args:
        request: Validate appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Validate appointment response
    """
    try:
        pro_id = request.proId
        request_type = request.requestType
        company_name = request.companyName
        operation = request.operation
        target_warehouse = request.warehouse
        zip_code = target_warehouse.zipCode if target_warehouse else None

        if not pro_id:
            asyncio.create_task(
                snapshot_and_upload_webpage(
                    request=request,
                    current_url=driver.current_url,
                    html_source=driver.page_source,
                    function_name="validate_appointment",
                    page_name="parsing_api_request",
                    status_code=400,
                    s3_uploader=S3Uploader(),
                )
            )

            return ValidateAppointmentResponse(
                success=False,
                message="PRO ID is required",
                errors=["PRO ID is required"],
            )

        appointments = []

        if request_type == "normal":
            nav_to_search(driver)

            fill_search_form(driver, pro_id)

            matching_rows_result = _find_matches(
                driver, target_warehouse, pro_id, None
            )
            if not matching_rows_result.success:
                asyncio.create_task(
                    snapshot_and_upload_webpage(
                        request=request,
                        current_url=driver.current_url,
                        html_source=driver.page_source,
                        function_name="validate_appointment",
                        page_name="search_form_normal_appointment",
                        status_code=500,
                        s3_uploader=S3Uploader(),
                    )
                )

                return ValidateAppointmentResponse(
                    success=False,
                    message=matching_rows_result.message,
                    errors=matching_rows_result.errors,
                )

            driver.implicitly_wait(1)

            # Process each row to extract appointment details
            for row_data in matching_rows_result.matchingRows:
                row = row_data.row
                warehouse_details = row_data.warehouseDetails

                # Check if this is an FCFS appointment
                is_fcfs = False
                try:
                    row.find_element(
                        By.XPATH,
                        ".//input[@type='checkbox' and @name[contains(., 'FCFS')]]",
                    )
                    is_fcfs = True
                except Exception:
                    pass

                # Get appointment status
                status = "UNSCHEDULED"
                appointment_id = "-"
                scheduled_time = "-"

                # Check for confirmed appointments
                try:
                    appt_div = row.find_element(
                        By.XPATH, ".//div[contains(@id, 'apptdiv')]"
                    )
                    if "CONFIRMED" in appt_div.text:
                        status = "CONFIRMED"

                        # Extract appointment date/time
                        date_time_text = (
                            appt_div.text.strip().split("\n")[0].strip()
                        )
                        scheduled_time = date_time_text

                        # Try to find appointment reference
                        try:
                            appt_ref_input = row.find_element(
                                By.XPATH,
                                ".//input[contains(@name, 'apptReference')]",
                            )
                            appointment_id = (
                                appt_ref_input.get_attribute("value") or "-"
                            )
                        except Exception:
                            pass
                except Exception:
                    # No confirmed appointment
                    pass

                # Create appointment object
                appointment = Appointment(
                    appointmentId=appointment_id,
                    scheduledTime=scheduled_time,
                    duration=60,  # Default duration
                    status=status,
                    warehouse=warehouse_details,
                    extended={"isFcfs": is_fcfs},
                )

                appointments.append(appointment)

            driver.implicitly_wait(10)
        elif request_type == "3rd_party":
            stop_types = fetch_stop_type(target_warehouse)

            for stop_type in stop_types:
                try:
                    nav_to_3rd_party_appt(driver)
                    fill_3rd_party_form(
                        driver,
                        company_name,
                        pro_id,
                        zip_code,
                        stop_type,
                        operation,
                    )

                    select_rows = driver.find_elements(
                        By.XPATH,
                        """
                        //table[contains(@id, 'results') or .//th]
                        //tr[
                            .//td[contains(@rowspan, '2')]
                            and (
                                .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                or
                                .//input[
                                    @type='checkbox'
                                ]
                            )
                        ]
                        """,
                    )

                    for i in range(len(select_rows)):
                        select_rows = driver.find_elements(
                            By.XPATH,
                            """
                            //table[contains(@id, 'results') or .//th]
                            //tr[
                                .//td[contains(@rowspan, '2')]
                                and (
                                    .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                    or
                                    .//input[
                                        @type='checkbox'
                                    ]
                                )
                            ]
                            """,
                        )

                        if i >= len(select_rows):
                            break

                        row = select_rows[i]

                        location_cells = row.find_elements(By.XPATH, "./td[1]")
                        location_text = (
                            location_cells[0].text.strip()
                            if location_cells
                            else "Unknown location"
                        )
                        warehouse_details = parse_location_3rd_party(
                            location_text
                        )
                        warehouse_details.stopType = stop_type

                        try:
                            # Check if Manage Live Appointment dropdown exists in the row
                            driver.implicitly_wait(1)
                            row.find_element(
                                By.XPATH,
                                ".//table//select[.//option[contains(text(), 'Manage Live Appointment')]]",
                            )
                            driver.implicitly_wait(10)
                            logger.info(
                                "Found Manage Live Appointment dropdown"
                            )

                            select_element = row.find_element(
                                By.XPATH, ".//select"
                            )

                            go_buttons = row.find_elements(
                                By.XPATH, ".//button[contains(text(), 'Go')]"
                            )
                            if not go_buttons:
                                go_buttons = row.find_elements(
                                    By.XPATH, ".//button"
                                )

                            if not go_buttons:
                                logger.warning(
                                    f"No Go button found in row {i + 1}"
                                )
                                continue

                            go_button = go_buttons[0]

                            select = Select(select_element)

                            select.select_by_visible_text(
                                "Manage Live Appointment"
                            )

                            safe_click(driver, go_button)

                        except Exception as e:
                            driver.implicitly_wait(10)
                            logger.error(
                                f"Manage Live Appointment option not found, using Appoint selected as live"
                            )

                            checkbox = row.find_element(
                                By.XPATH, ".//input[@type='checkbox']"
                            )
                            checkbox.click()

                            appoint_button = driver.find_element(
                                By.XPATH,
                                "//button[normalize-space(text())='Appoint Selected as Live']",
                            )
                            appoint_button.click()

                        wait_for_page_load(driver)

                        label_cells = driver.find_elements(
                            By.XPATH,
                            "//td[contains(text(), 'Current Appt') or contains(text(), 'Appt. Status')]",
                        )

                        if len(label_cells) == 2:
                            x1 = (
                                label_cells[0]
                                .find_elements(
                                    By.XPATH, "./following-sibling::td[1]"
                                )[0]
                                .text.strip()
                            )
                            try:
                                date_str, time_str = x1.split(" ")
                                month, day, year = date_str.split("/")

                                if len(year) == 2:
                                    year = f"20{year}"

                                iso_datetime = f"{year}-{month.zfill(2)}-{day.zfill(2)}T{time_str}:00"
                            except Exception as e:
                                logger.error(
                                    f"Error parsing appointment date/time: {str(e)}"
                                )
                                iso_datetime = x1

                            status = (
                                label_cells[1]
                                .find_elements(
                                    By.XPATH, "./following-sibling::td[1]"
                                )[0]
                                .text.strip()
                            )
                        else:
                            iso_datetime = ""
                            status = "UNCONFIRMED"

                        if iso_datetime.strip() == "--":
                            iso_datetime = ""

                        if status.strip() == "--":
                            status = "UNCONFIRMED"

                        appointment = Appointment(
                            appointmentId="-",
                            scheduledTime=iso_datetime,
                            duration=60,  # Default duration
                            status=status,
                            warehouse=warehouse_details,
                            extended={"isFcfs": False},
                        )

                        appointments.append(appointment)

                        if i < len(select_rows) - 1:
                            nav_to_3rd_party_appt(driver)
                            fill_3rd_party_form(
                                driver,
                                company_name,
                                pro_id,
                                zip_code,
                                stop_type,
                                operation,
                            )

                            wait_for_page_load(driver)
                except Exception as ex:
                    return ValidateAppointmentResponse(
                        success=False,
                        message="Failed to validate appointment",
                        errors=[f"{str(ex)}"],
                    )
        else:
            return ValidateAppointmentResponse(
                success=False,
                message="Invalid request type",
                errors=["Invalid request type"],
            )

        return ValidateAppointmentResponse(
            success=True,
            message="Appointment validation successful",
            appointments=appointments,
        )
    except Exception as e:
        return ValidateAppointmentResponse(
            success=False,
            message="Failed to validate appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(E2openGetOpenSlotsResponse)
def get_open_slots(
    request: E2openGetOpenSlotsRequest, driver: WebDriver
) -> E2openGetOpenSlotsResponse:
    """Get open appointment slots from E2open.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response with slots organized by warehouse
    """
    try:
        start_date = (
            isoparse(request.startDate)
            if request.startDate
            else datetime.now()
        )

        end_date = (
            isoparse(request.endDate)
            if request.endDate
            else start_date + timedelta(days=7)
        )

        request.startDate = start_date.strftime("%Y-%m-%d")
        request.endDate = end_date.strftime("%Y-%m-%d")

        validation_result = _validate_slots(request)
        if not validation_result.success:
            asyncio.create_task(
                snapshot_and_upload_webpage(
                    request=request,
                    current_url=driver.current_url,
                    html_source=driver.page_source,
                    function_name="get_open_slots",
                    page_name="validate_slots",
                    status_code=500,
                    s3_uploader=S3Uploader(),
                )
            )

            return E2openGetOpenSlotsResponse(
                success=False,
                message=validation_result.message,
                errors=[validation_result.message],
            )

        start_date_obj = validation_result.startDate
        end_date_obj = validation_result.endDate

        pro_id = request.proId
        request_type = request.requestType
        company_name = request.companyName
        operation = request.operation

        target_warehouse = request.warehouse
        zip_code = target_warehouse.zipCode if target_warehouse else None

        # all_open_slots = []
        appointments = []

        if request_type == "normal":
            nav_to_search(driver)
            fill_search_form(driver, pro_id)

            main_window = driver.current_window_handle

            # Find the appropriate appointment link based on request type
            matching_rows_result = _find_matches(
                driver, target_warehouse, pro_id
            )
            if not matching_rows_result.success:
                asyncio.create_task(
                    snapshot_and_upload_webpage(
                        request=request,
                        current_url=driver.current_url,
                        html_source=driver.page_source,
                        function_name="get_open_slots",
                        page_name="search_form_normal_appointment",
                        status_code=500,
                        s3_uploader=S3Uploader(),
                    )
                )

                return E2openGetOpenSlotsResponse(
                    success=False,
                    message=matching_rows_result.message,
                    errors=matching_rows_result.errors,
                )

            for matching_row in matching_rows_result.matchingRows:
                try:
                    driver.implicitly_wait(1)
                    appointment_link_locator = matching_row.row.find_element(
                        By.XPATH,
                        ".//a[contains(text(), 'Reschedule Live Appt') or contains(text(), 'Schedule Live Appt')]",
                    )

                except Exception:
                    err_msg = (
                        "No open slots available for the respective warehouse"
                    )

                    appointments.append(
                        E2openOpenSlotsAppointmentData(
                            notes=err_msg,
                            status="UNAVAILABLE",
                            warehouse=matching_row.warehouseDetails,
                        )
                    )
                    driver.implicitly_wait(10)
                    continue

                finally:
                    driver.implicitly_wait(10)

                safe_click(driver, appointment_link_locator)

                if not switch_popup(driver, main_window):
                    asyncio.create_task(
                        snapshot_and_upload_webpage(
                            request=request,
                            current_url=driver.current_url,
                            html_source=driver.page_source,
                            function_name="get_open_slots",
                            page_name="switch_popup_normal_appointment",
                            status_code=500,
                            s3_uploader=S3Uploader(),
                        )
                    )

                    return E2openGetOpenSlotsResponse(
                        success=False,
                        message="Failed to switch to appointment scheduling popup",
                        errors=[
                            "Failed to switch to appointment scheduling popup"
                        ],
                    )

                wait_for_page_load(driver)

                # Collect open slots for the date range
                warehouse = _collect_slots(
                    driver,
                    start_date_obj,
                    end_date_obj,
                    matching_row.warehouseDetails,
                )

                if not warehouse.openSlots:
                    err_msg = (
                        "No open slots available for the respective warehouse"
                    )

                    appointments.append(
                        E2openOpenSlotsAppointmentData(
                            notes=err_msg,
                            status="UNAVAILABLE",
                            warehouse=warehouse,
                        )
                    )
                else:
                    appointments.append(
                        E2openOpenSlotsAppointmentData(
                            notes="Open slots available for the respective warehouse.",
                            status="AVAILABLE",
                            warehouse=warehouse,
                        )
                    )

                # Close the popup window
                if main_window:
                    close_other_windows(driver, main_window)

        elif request_type == "3rd_party":

            stop_types = fetch_stop_type(target_warehouse)

            for stop_type in stop_types:
                nav_to_3rd_party_appt(driver)
                fill_3rd_party_form(
                    driver,
                    company_name,
                    pro_id,
                    zip_code,
                    stop_type,
                    operation,
                )

                select_rows = driver.find_elements(
                    By.XPATH,
                    """
                    //table[contains(@id, 'results') or .//th]
                    //tr[
                        .//td[contains(@rowspan, '2')]
                        and (
                            .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                            or
                            .//input[
                                @type='checkbox'
                            ]
                        )
                    ]
                    """,
                )

                for i in range(len(select_rows)):
                    # Handling stale element not found issue
                    select_rows = driver.find_elements(
                        By.XPATH,
                        """
                        //table[contains(@id, 'results') or .//th]
                        //tr[
                            .//td[contains(@rowspan, '2')]
                            and (
                                .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                or
                                .//input[
                                    @type='checkbox'
                                ]
                            )
                        ]
                        """,
                    )

                    if i >= len(select_rows):
                        break

                    row = select_rows[i]

                    location_cells = row.find_elements(By.XPATH, "./td[1]")
                    location_text = (
                        location_cells[0].text.strip()
                        if location_cells
                        else "Unknown location"
                    )
                    warehouse_details = parse_location_3rd_party(location_text)
                    warehouse_details.stopType = stop_type

                    try:
                        # Check if Manage Live Appointment dropdown exists in the row
                        driver.implicitly_wait(1)
                        row.find_element(
                            By.XPATH,
                            ".//table//select[.//option[contains(text(), 'Manage Live Appointment')]]",
                        )
                        driver.implicitly_wait(10)
                        logger.info("Found Manage Live Appointment dropdown")

                        select_element = row.find_element(
                            By.XPATH, ".//select"
                        )

                        go_buttons = row.find_elements(
                            By.XPATH, ".//button[contains(text(), 'Go')]"
                        )
                        if not go_buttons:
                            go_buttons = row.find_elements(
                                By.XPATH, ".//button"
                            )

                        if not go_buttons:
                            logger.warning(
                                f"No Go button found in row {i + 1}"
                            )
                            continue

                        go_button = go_buttons[0]

                        select = Select(select_element)

                        select.select_by_visible_text(
                            "Manage Live Appointment"
                        )

                        safe_click(driver, go_button)

                    except Exception as e:
                        driver.implicitly_wait(10)
                        logger.error(
                            f"Manage Live Appointment option not found, using Appoint selected as live"
                        )

                        checkbox = row.find_element(
                            By.XPATH, ".//input[@type='checkbox']"
                        )
                        checkbox.click()

                        appoint_button = driver.find_element(
                            By.XPATH,
                            "//button[normalize-space(text())='Appoint Selected as Live']",
                        )
                        appoint_button.click()

                    wait_for_page_load(driver)

                    warehouse = _collect_slots(
                        driver,
                        start_date_obj,
                        end_date_obj,
                        warehouse_details,
                        request_type,
                    )

                    if not warehouse.openSlots:
                        err_msg = "No open slots available for the respective warehouse"

                        appointments.append(
                            E2openOpenSlotsAppointmentData(
                                notes=err_msg,
                                status="UNAVAILABLE",
                                warehouse=warehouse_details,
                            )
                        )
                    else:
                        appointments.append(
                            E2openOpenSlotsAppointmentData(
                                notes="Open slots available for the respective warehouse.",
                                status="AVAILABLE",
                                warehouse=warehouse,
                            )
                        )

                    if i < len(select_rows) - 1:
                        nav_to_3rd_party_appt(driver)
                        fill_3rd_party_form(
                            driver,
                            company_name,
                            pro_id,
                            zip_code,
                            stop_type,
                            operation,
                        )

                        wait_for_page_load(driver)

        else:
            return E2openGetOpenSlotsResponse(
                success=False,
                message="Invalid request type",
                errors=["Invalid request type"],
            )

        return E2openGetOpenSlotsResponse(
            success=True,
            message="Successfully retrieved open slots",
            appointments=appointments,
        )
    except Exception as e:
        return E2openGetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: E2openGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from E2open.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: E2openCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in E2open.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:

        pro_id = request.proId
        target_warehouse = request.warehouse
        zip_code = target_warehouse.zipCode if target_warehouse else None

        company_name = request.companyName
        operation = request.operation
        request_type = request.requestType

        matching_rows_result = None

        if request_type == "normal":
            nav_to_search(driver)
            fill_search_form(driver, pro_id)
            main_window = driver.current_window_handle

            matching_rows_result = _find_matches(
                driver, target_warehouse, pro_id
            )
            if not matching_rows_result.success:
                return CancelAppointmentResponse(
                    success=False,
                    message=matching_rows_result["message"],
                    errors=matching_rows_result["errors"],
                )

            first_match = matching_rows_result.matchingRows[0]
            warehouse_matched = True
            row = first_match.row
            warehouse_details = first_match.warehouseDetails

            driver.implicitly_wait(0)

            cancel_links = row.find_elements(
                By.XPATH, ".//a[contains(text(), 'Cancel Appt')]"
            )

            if cancel_links:
                cancel_link = cancel_links[0]
            else:
                start_date_input = row.find_element(
                    By.XPATH,
                    ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointStartDateTime')]",
                )
                start_time_input = row.find_element(
                    By.XPATH,
                    ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointStartDateTime')]",
                )

                start_date_input.clear()
                start_time_input.clear()

                end_date_input = row.find_element(
                    By.XPATH,
                    ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointEndDateTime')]",
                )
                end_time_input = row.find_element(
                    By.XPATH,
                    ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointEndDateTime')]",
                )

                end_date_input.clear()
                end_time_input.clear()

                # Submit the form
                submit_button = driver.find_element(
                    By.XPATH, "//button[contains(text(), 'Save')]"
                )
                safe_click(driver, submit_button)

                wait_for_page_load(driver)
                # Add a short delay to ensure the make appointment request is fully processed by the server
                time.sleep(1)

                alert_status, alert_message = handle_alerts(driver)

                return CancelAppointmentResponse(
                    success=True,
                    message="Appointment canceled successfully",
                    proId=pro_id,
                    warehouse=warehouse_details,
                    alertMessage=alert_message,
                )

            driver.implicitly_wait(10)

            safe_click(driver, cancel_link)

        elif request_type == "3rd_party":
            if not target_warehouse.stopType:
                return CancelAppointmentResponse(
                    success=False,
                    message="Stop type is required for 3rd party appointments",
                    errors=[
                        "Stop type is required for 3rd party appointments"
                    ],
                )

            nav_to_3rd_party_appt(driver)

            main_window = driver.current_window_handle

            fill_3rd_party_form(
                driver,
                company_name,
                pro_id,
                zip_code,
                target_warehouse.stopType,
                operation,
            )

            select_rows = driver.find_elements(
                By.XPATH,
                f"//table[contains(@id, 'results') or .//th]//tr[.//select//option[contains(text(), 'Manage Live Appointment')]]",
            )

            warehouse_matched = False
            for i, row in enumerate(select_rows):
                select_element = row.find_element(By.XPATH, ".//select")

                go_buttons = row.find_elements(
                    By.XPATH, ".//button[contains(text(), 'Go')]"
                )
                if not go_buttons:
                    go_buttons = row.find_elements(By.XPATH, ".//button")

                if not go_buttons:
                    logger.warning(f"No Go button found in row {i + 1}")
                    continue

                go_button = go_buttons[0]

                location_cells = row.find_elements(By.XPATH, "./td[1]")
                location_text = (
                    location_cells[0].text.strip()
                    if location_cells
                    else "Unknown location"
                )
                warehouse_details = parse_location_3rd_party(location_text)

                warehouse_details.stopType = target_warehouse.stopType

                match = _match_warehouse(warehouse_details, target_warehouse)
                if not match:
                    continue

                warehouse_matched = True
                select = Select(select_element)
                select.select_by_visible_text("Cancel Appointment")
                safe_click(driver, go_button)

                break
        else:
            return CancelAppointmentResponse(
                success=False,
                message="Invalid request type",
                errors=["Invalid request type"],
            )

        if not warehouse_matched:
            return CancelAppointmentResponse(
                success=False,
                message="No matching warehouse found for cancellation",
                errors=["No matching warehouse found for cancellation"],
            )

        if not switch_popup(driver, main_window):
            return CancelAppointmentResponse(
                success=False,
                message="Failed to switch to appointment canceling popup",
                errors=["Failed to switch to appointment canceling popup"],
            )

        wait_for_page_load(driver)

        if request_type == "normal":
            comments_textarea = driver.find_element(By.NAME, "comments")
        else:
            comments_textarea = driver.find_element(By.NAME, "comment")

        cancellation_message = request.reason or "No reason provided"
        safe_send_keys(driver, comments_textarea, cancellation_message)

        if request_type == "normal":
            continue_button = driver.find_element(
                By.XPATH,
                "//button[@id='test-actionBtn' and contains(text(), 'Continue')]",
            )
        else:
            continue_button = driver.find_element(By.NAME, "submitbut")
        safe_click(driver, continue_button)

        # Add a short delay to ensure the cancellation request is
        # fully processed by the server
        time.sleep(1)

        alert_status, alert_message = handle_alerts(driver)

        return CancelAppointmentResponse(
            success=True,
            message="Appointment canceled successfully",
            proId=pro_id,
            warehouse=(warehouse_details if matching_rows_result else None),
            alertMessage=alert_message,
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetAppointmentResponse)
def get_appointment(
    request: E2openGetAppointmentRequest, driver: WebDriver
) -> GetAppointmentResponse:
    """Get an appointment from E2open.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        pro_id = request.proId
        request_type = request.requestType
        company_name = request.companyName
        operation = request.operation
        target_warehouse = request.warehouse
        zip_code = target_warehouse.zipCode if target_warehouse else None

        if not pro_id:
            return GetAppointmentResponse(
                success=False,
                message="PRO ID is required",
                errors=["PRO ID is required"],
            )

        # Extract appointment details from the table
        appointments = []

        if request_type == "normal":
            # Navigate to appointment search page
            nav_to_search(driver)

            # Fill the appointment search form with the PRO ID
            fill_search_form(driver, pro_id)

            # Wait for search results to load
            wait_for_element(driver, LOCATORS["reschedule_live_appt_link1"])

            # Find all appointment rows (both pickup and dropoff)
            rows = driver.find_elements(By.CSS_SELECTOR, "tr.resultrow1")

            for i, row in enumerate(rows):
                # Skip the last rows which contain PRO details, not appointments
                if i >= len(rows) - 3:
                    break

                # Check if this is a stop row (has stop type)
                stop_type_elements = row.find_elements(
                    By.CSS_SELECTOR, "td strong"
                )
                if not stop_type_elements:
                    continue

                stop_type_text = stop_type_elements[0].text.strip()
                if stop_type_text not in ["Pick", "Drop"]:
                    continue

                # Get appointment type
                appointment_type = (
                    "pickup" if stop_type_text == "Pick" else "dropoff"
                )

                # Get location
                if appointment_type == "pickup":
                    location_element = row.find_elements(
                        By.CSS_SELECTOR, "td"
                    )[3]
                else:
                    location_element = row.find_elements(
                        By.CSS_SELECTOR, "td"
                    )[2]

                location = location_element.text.strip()
                row_warehouse_details = parse_location(location)
                row_warehouse_details.stopType = appointment_type

                driver.implicitly_wait(0)
                try:
                    # Get appointment date and time
                    appt_div = row.find_element(
                        By.CSS_SELECTOR, "div[id^='apptdiv']"
                    )
                    appt_span = appt_div.find_element(By.CSS_SELECTOR, "span")
                    appt_datetime_text = (
                        appt_span.text.strip().split("\n")[0].strip()
                    )
                except Exception as e:
                    logger.error(
                        f"Error extracting appointment date/time: {str(e)}"
                    )
                    continue
                finally:
                    driver.implicitly_wait(10)

                # Check if appointment is confirmed
                status_elements = appt_span.find_elements(
                    By.CSS_SELECTOR, "span.success-highling"
                )
                status = (
                    status_elements[0].text.strip()
                    if status_elements
                    else "UNCONFIRMED"
                )

                # Parse the appointment date and time
                try:
                    # Handle the date format (MM/DD/YYYY HH:MM)
                    date_str, time_str = appt_datetime_text.split(" ")
                    month, day, year = date_str.split("/")

                    # Ensure year is 4 digits
                    if len(year) == 2:
                        year = f"20{year}"

                    # Convert to ISO format
                    iso_datetime = (
                        f"{year}-{month.zfill(2)}-{day.zfill(2)}T{time_str}:00"
                    )

                    appointment = Appointment(
                        appointmentId="-",
                        scheduledTime=iso_datetime,
                        duration=60,
                        warehouse=row_warehouse_details,
                        status=status,
                        notes="",
                        extended={
                            "proId": pro_id,
                        },
                    )
                    appointments.append(appointment)
                except Exception as e:
                    logger.error(
                        f"Error parsing appointment date/time: {str(e)}"
                    )

        elif request_type == "3rd_party":
            stop_types = fetch_stop_type(target_warehouse)

            for stop_type in stop_types:
                nav_to_3rd_party_appt(driver)
                try:
                    fill_3rd_party_form(
                        driver,
                        company_name,
                        pro_id,
                        zip_code,
                        stop_type,
                        operation,
                    )

                    select_rows = driver.find_elements(
                        By.XPATH,
                        """
                        //table[contains(@id, 'results') or .//th]
                        //tr[
                            .//td[contains(@rowspan, '2')]
                            and (
                                .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                or
                                .//input[
                                    @type='checkbox'
                                ]
                            )
                        ]
                        """,
                    )

                    for i in range(len(select_rows)):
                        select_rows = driver.find_elements(
                            By.XPATH,
                            """
                            //table[contains(@id, 'results') or .//th]
                            //tr[
                                .//td[contains(@rowspan, '2')]
                                and (
                                    .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                    or
                                    .//input[
                                        @type='checkbox'
                                    ]
                                )
                            ]
                            """,
                        )

                        if i >= len(select_rows):
                            break

                        row = select_rows[i]

                        location_cells = row.find_elements(By.XPATH, "./td[1]")
                        location_text = (
                            location_cells[0].text.strip()
                            if location_cells
                            else "Unknown location"
                        )
                        warehouse_details = parse_location_3rd_party(
                            location_text
                        )
                        warehouse_details.stopType = stop_type

                        try:
                            # Check if Manage Live Appointment dropdown exists in the row
                            driver.implicitly_wait(1)
                            row.find_element(
                                By.XPATH,
                                ".//table//select[.//option[contains(text(), 'Manage Live Appointment')]]",
                            )
                            driver.implicitly_wait(10)
                            logger.info(
                                "Found Manage Live Appointment dropdown"
                            )

                            select_element = row.find_element(
                                By.XPATH, ".//select"
                            )

                            go_buttons = row.find_elements(
                                By.XPATH, ".//button[contains(text(), 'Go')]"
                            )
                            if not go_buttons:
                                go_buttons = row.find_elements(
                                    By.XPATH, ".//button"
                                )

                            if not go_buttons:
                                logger.warning(
                                    f"No Go button found in row {i + 1}"
                                )
                                continue

                            go_button = go_buttons[0]

                            select = Select(select_element)

                            select.select_by_visible_text(
                                "Manage Live Appointment"
                            )

                            safe_click(driver, go_button)

                        except Exception as e:
                            driver.implicitly_wait(10)
                            logger.error(
                                f"Manage Live Appointment option not found, using Appoint selected as live"
                            )

                            checkbox = row.find_element(
                                By.XPATH, ".//input[@type='checkbox']"
                            )
                            checkbox.click()

                            appoint_button = driver.find_element(
                                By.XPATH,
                                "//button[normalize-space(text())='Appoint Selected as Live']",
                            )
                            appoint_button.click()

                        wait_for_page_load(driver)

                        label_cells = driver.find_elements(
                            By.XPATH,
                            "//td[contains(text(), 'Current Appt') or contains(text(), 'Appt. Status')]",
                        )

                        if len(label_cells) == 2:
                            x1 = (
                                label_cells[0]
                                .find_elements(
                                    By.XPATH, "./following-sibling::td[1]"
                                )[0]
                                .text.strip()
                            )
                            try:
                                date_str, time_str = x1.split(" ")
                                month, day, year = date_str.split("/")

                                if len(year) == 2:
                                    year = f"20{year}"

                                iso_datetime = f"{year}-{month.zfill(2)}-{day.zfill(2)}T{time_str}:00"
                            except Exception as e:
                                logger.error(
                                    f"Error parsing appointment date/time: {str(e)}"
                                )
                                iso_datetime = x1

                            x2 = (
                                label_cells[1]
                                .find_elements(
                                    By.XPATH, "./following-sibling::td[1]"
                                )[0]
                                .text.strip()
                            )
                        else:
                            iso_datetime = ""
                            x2 = "UNCONFIRMED"

                        if iso_datetime.strip() == "--":
                            iso_datetime = ""

                        if x2.strip() == "--":
                            x2 = "UNCONFIRMED"

                        appointment = Appointment(
                            appointmentId="-",
                            scheduledTime=iso_datetime,
                            duration=60,
                            warehouse=warehouse_details,
                            status=x2,
                            notes="",
                            extended={
                                "proId": pro_id,
                            },
                        )
                        appointments.append(appointment)

                        if i < len(select_rows) - 1:
                            nav_to_3rd_party_appt(driver)
                            fill_3rd_party_form(
                                driver,
                                company_name,
                                pro_id,
                                zip_code,
                                stop_type,
                                operation,
                            )

                            wait_for_page_load(driver)
                except Exception as ex:
                    return GetAppointmentResponse(
                        success=False,
                        message="Failed to validate appointment",
                        errors=[f"{str(ex)}"],
                    )
        else:
            return GetAppointmentResponse(
                success=False,
                message="Invalid request type",
                errors=["Invalid request type"],
            )

        if not appointments:
            return GetAppointmentResponse(
                success=False,
                message=f"No appointments found for PRO ID {pro_id}",
                errors=[f"No appointments found for PRO ID {pro_id}"],
            )

        return GetAppointmentResponse(
            success=True,
            message=f"Successfully retrieved {len(appointments)} appointments for PRO ID {pro_id}",
            appointments=appointments,
        )
    except Exception as e:
        return GetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(MakeAppointmentResponse)
def make_appointment(
    request: E2openMakeAppointmentRequest, driver: WebDriver
) -> MakeAppointmentResponse:
    """Create an appointment in E2open.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        all_appointments = []
        all_errors = []
        overall_success = True

        request_type = request.requestType
        company_name = request.companyName
        operation = request.operation

        main_window = None

        for appointment in request.appointments:
            try:
                validation_result = _validate_make_appointment(appointment)

                target_warehouse = appointment.warehouse
                zip_code = (
                    target_warehouse.zipCode if target_warehouse else None
                )

                failed_appointment = Appointment(
                    appointmentId="",
                    scheduledTime="",
                    duration=0,
                    status="UNSCHEDULED",
                    warehouse=target_warehouse,
                )

                created_appointment = None

                if not validation_result.success:
                    failed_appointment.notes = validation_result.message
                    all_appointments.append(failed_appointment)
                    all_errors.append(validation_result.message)

                    overall_success = False
                    continue

                pro_id = appointment.proId

                main_window = None
                popup_window = None

                if request_type == "normal":
                    warehouse_details = None
                    nav_to_search(driver)

                    fill_search_form(driver, pro_id)
                    main_window = driver.current_window_handle

                    matching_rows_result = _find_matches(
                        driver, target_warehouse, pro_id
                    )
                    if not matching_rows_result.success:
                        failed_appointment.notes = (
                            "No appointment found for the mentioned warehouse."
                        )
                        all_appointments.append(failed_appointment)

                        all_errors.append(
                            "No appointment found for the mentioned warehouse."
                        )

                        overall_success = False
                        continue

                    first_match = matching_rows_result.matchingRows[0]
                    failed_appointment.warehouse = first_match.warehouseDetails
                    warehouse_details = first_match.warehouseDetails

                    row = first_match.row

                    driver.implicitly_wait(0)

                    appointment_links = row.find_elements(
                        By.XPATH,
                        ".//a[contains(text(), 'Reschedule Live Appt')]",
                    )

                    if not appointment_links:
                        appointment_links = row.find_elements(
                            By.XPATH,
                            ".//a[contains(text(), 'Schedule Live Appt')]",
                        )

                    if appointment_links:
                        appointment_link = appointment_links[0]
                    else:
                        # Direct appointment entry flow
                        appointment_datetime = datetime.strptime(
                            appointment.appointmentTime, "%Y-%m-%dT%H:%M:%S"
                        )

                        date_str = appointment_datetime.strftime("%m/%d/%Y")
                        time_str = appointment_datetime.strftime("%H:%M")

                        # Find date/time inputs
                        start_date_input = row.find_element(
                            By.XPATH,
                            ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointStartDateTime')]",
                        )
                        start_time_input = row.find_element(
                            By.XPATH,
                            ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointStartDateTime')]",
                        )

                        start_date_input.clear()
                        start_date_input.send_keys(date_str)
                        start_time_input.clear()
                        start_time_input.send_keys(time_str)

                        end_date_input = row.find_element(
                            By.XPATH,
                            ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointEndDateTime')]",
                        )
                        end_time_input = row.find_element(
                            By.XPATH,
                            ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointEndDateTime')]",
                        )

                        end_date_input.clear()
                        end_date_input.send_keys(date_str)
                        end_time_input.clear()
                        end_time_input.send_keys(time_str)

                        # Submit the form
                        submit_button = driver.find_element(
                            By.XPATH, "//button[contains(text(), 'Save')]"
                        )
                        safe_click(driver, submit_button)

                        alert_status, alert_message = handle_alerts(driver)

                        wait_for_page_load(driver)

                        # Add a short delay to ensure the make appointment request is fully processed by the server
                        time.sleep(1)

                        # Add to results
                        created_appointment = Appointment(
                            appointmentId="-",
                            scheduledTime=appointment.appointmentTime,
                            duration=60,
                            status="SCHEDULED",
                            notes=alert_message,
                            warehouse=row.warehouseDetails,
                        )

                        all_appointments.append(created_appointment)

                        continue

                    driver.implicitly_wait(10)

                    if appointment_link:
                        safe_click(driver, appointment_link)
                    else:
                        appointment_link_locator = LOCATORS[
                            "reschedule_live_appt_link2"
                        ]
                        safe_click(driver, appointment_link_locator)

                    if not switch_popup(driver, main_window):
                        failed_appointment.notes = (
                            "Failed to switch to appointment scheduling popup."
                        )
                        all_appointments.append(failed_appointment)

                        all_errors.append(
                            "Failed to switch to appointment scheduling popup."
                        )

                        overall_success = False
                        continue

                    popup_window = driver.current_window_handle
                    wait_for_page_load(driver)

                elif request_type == "3rd_party":
                    matching_rows_result = None

                    nav_to_3rd_party_appt(driver)
                    fill_3rd_party_form(
                        driver,
                        company_name,
                        pro_id,
                        zip_code,
                        target_warehouse.stopType,
                        operation,
                    )

                    select_rows = driver.find_elements(
                        By.XPATH,
                        """
                        //table[contains(@id, 'results') or .//th]
                        //tr[
                            .//td[contains(@rowspan, '2')]
                            and (
                                .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                or
                                .//input[
                                    @type='checkbox'
                                ]
                            )
                        ]
                        """,
                    )

                    if not select_rows:
                        raise Exception(f"No select rows found")

                    warehouse_matched = False
                    for row in select_rows:
                        location_cells = row.find_elements(By.XPATH, "./td[1]")
                        location_text = (
                            location_cells[0].text.strip()
                            if location_cells
                            else "Unknown location"
                        )
                        warehouse_details = parse_location_3rd_party(
                            location_text
                        )
                        warehouse_details.stopType = target_warehouse.stopType

                        if not _match_warehouse(
                            warehouse_details, target_warehouse
                        ):
                            continue
                        else:
                            warehouse_matched = True
                            break

                    if not warehouse_matched:
                        failed_appointment.notes = "No matching warehouse found for the specified stop type."
                        all_appointments.append(failed_appointment)

                        all_errors.append(failed_appointment.notes)

                        overall_success = False
                        continue

                    try:
                        driver.implicitly_wait(1)
                        row.find_element(
                            By.XPATH,
                            ".//table//select[.//option[contains(text(), 'Manage Live Appointment')]]",
                        )
                        driver.implicitly_wait(10)

                        select_element = row.find_element(
                            By.XPATH, ".//select"
                        )

                        if not select_element:
                            raise Exception(f"No select element found")

                        go_buttons = row.find_elements(
                            By.XPATH,
                            ".//button[contains(text(), 'Go')]",
                        )
                        if not go_buttons:
                            go_buttons = row.find_elements(
                                By.XPATH, ".//button"
                            )

                        if not go_buttons:
                            raise Exception(f"No Go button found in row")

                        go_button = go_buttons[0]

                        select = Select(select_element)
                        select.select_by_visible_text(
                            "Manage Live Appointment"
                        )
                        safe_click(driver, go_button)

                    except Exception:
                        try:
                            driver.implicitly_wait(10)

                            checkbox = row.find_element(
                                By.XPATH, ".//input[@type='checkbox']"
                            )
                            checkbox.click()

                            appoint_button = driver.find_element(
                                By.XPATH,
                                "//button[normalize-space(text())='Appoint Selected as Live']",
                            )
                            safe_click(driver, appoint_button)
                        except Exception as ex:
                            failed_appointment.notes = (
                                "Failed to make appointment live."
                            )
                            all_appointments.append(failed_appointment)

                            all_errors.append(
                                f"Failed to make appointment live: {str(ex)}"
                            )

                            overall_success = False
                            continue

                else:
                    return MakeAppointmentResponse(
                        success=False,
                        message="Invalid request type",
                        errors=["Invalid request type"],
                    )

                wait_for_page_load(driver)

                appointment_time = appointment.appointmentTime
                appointment_datetime = datetime.strptime(
                    appointment_time, "%Y-%m-%dT%H:%M:%S"
                )
                appointment_slot = appointment_datetime.strftime("%H:00")

                if request_type == "normal":
                    set_date(driver, appointment_datetime.strftime("%m/%d/%Y"))
                else:
                    set_date(
                        driver,
                        appointment_datetime.strftime("%m/%d/%Y"),
                        date_button_locator=(
                            By.ID,
                            "test-show-open-appts-button",
                        ),
                    )

                slot_elements = driver.find_elements(
                    By.CSS_SELECTOR, "input[name='startSlotNum']"
                )
                slot_found = False
                for slot_element in slot_elements:
                    label_element = slot_element.find_element(
                        By.XPATH, "./parent::label"
                    )
                    time_text = label_element.text.strip()

                    if time_text == appointment_slot:
                        logger.info(f"Found matching slot: {time_text}")
                        safe_click(driver, slot_element)
                        slot_found = True
                        break

                if not slot_found:
                    if main_window:
                        close_other_windows(driver, main_window)

                    failed_appointment.notes = f"No appointment slots available for {appointment_time}"
                    all_appointments.append(failed_appointment)

                    all_errors.append(
                        "No appointment slots available for the specified time."
                    )

                    overall_success = False
                    continue

                if request_type == "3rd_party":
                    attribute_value_mapping = [
                        {
                            "locator": (By.NAME, "containerNumber"),
                            "value": appointment.containerId,
                        },
                        {
                            "locator": (By.NAME, "trailerNumber"),
                            "value": appointment.trailerId,
                        },
                        {
                            "locator": (By.NAME, "driverName"),
                            "value": appointment.driver,
                        },
                        {
                            "locator": (By.NAME, "vehicleNumber"),
                            "value": appointment.vehicleNumber,
                        },
                        {
                            "locator": (By.NAME, "comment"),
                            "value": appointment.notes,
                        },
                        {
                            "locator": (By.NAME, "scac"),
                            "value": (
                                appointment.scac
                                if appointment.scac
                                else "Scheduled through automated flow."
                            ),
                        },
                    ]
                else:
                    attribute_value_mapping = [
                        {
                            "locator": (By.NAME, "containerNumber"),
                            "value": appointment.containerId,
                        },
                        {
                            "locator": (By.NAME, "trailerNumber"),
                            "value": appointment.trailerId,
                        },
                        {
                            "locator": (By.ID, "trailerLicense"),
                            "value": appointment.trailerLicense,
                        },
                        {
                            "locator": (By.ID, "driverName"),
                            "value": appointment.driver,
                        },
                        {
                            "locator": (By.ID, "vehicleNumber"),
                            "value": appointment.vehicleNumber,
                        },
                        {
                            "locator": (By.ID, "vehicleLicense"),
                            "value": appointment.vehicleLicense,
                        },
                        {
                            "locator": (By.NAME, "comment"),
                            "value": appointment.notes,
                        },
                    ]

                for attribute in attribute_value_mapping:
                    if attribute["value"]:
                        try:
                            element = driver.find_element(
                                *attribute["locator"]
                            )
                            safe_send_keys(driver, element, attribute["value"])
                        except Exception as e:
                            logger.warning(
                                f"Failed to set {attribute['locator']}: {str(e)}"
                            )

                if request_type == "normal":
                    submit_button = driver.find_element(
                        By.XPATH,
                        "//button[@name='submitbut' and contains(text(), 'Submit Request')]",
                    )
                else:
                    submit_button = driver.find_element(
                        By.XPATH,
                        "//button[contains(text(), 'Submit Request')]",
                    )

                safe_click(driver, submit_button)
                wait_for_page_load(driver)

                # Add a short delay to ensure the make appointment request is fully processed by the server
                time.sleep(1)

                alert_status, alert_message = handle_alerts(
                    driver, popup_window
                )

                # Add to results
                created_appointment = Appointment(
                    appointmentId=appointment_slot,
                    scheduledTime=appointment_time,
                    duration=60,
                    status="SCHEDULED",
                    notes=alert_message,
                    warehouse=warehouse_details,
                )

                all_appointments.append(created_appointment)

                # Close the popup window and return to main window
                if main_window:
                    close_other_windows(driver, main_window)

            except Exception as e:
                logger.error(f"Error processing appointment: {str(e)}")

                error_msg = str(e)
                if "Alert Text" in error_msg:
                    error_msg = error_msg.split("\n")[0]

                failed_appointment.notes = (
                    f"Error while creating appointment: {error_msg}."
                )
                if not created_appointment:
                    all_appointments.append(failed_appointment)

                all_errors.append(
                    f"Error while creating appointment: {error_msg}."
                )
                overall_success = False

                try:
                    if main_window:
                        driver.switch_to.window(main_window)
                except Exception:
                    pass

        return MakeAppointmentResponse(
            success=overall_success,
            message=(
                "Appointments processed with mixed results"
                if not overall_success
                else "All appointments scheduled successfully"
            ),
            appointments=all_appointments,
            errors=all_errors,
        )
    except Exception as e:
        return MakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(UpdateAppointmentResponse)
def update_appointment(
    request: E2openUpdateAppointmentRequest, driver: WebDriver
) -> UpdateAppointmentResponse:
    """Update an appointment in E2open.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        all_appointments = []
        all_errors = []
        overall_success = True

        request_type = request.requestType
        company_name = request.companyName
        operation = request.operation

        for appointment in request.appointments:
            try:
                validation_result = _validate_make_appointment(appointment)

                target_warehouse = appointment.warehouse

                failed_appointment = Appointment(
                    appointmentId="",
                    scheduledTime="",
                    duration=0,
                    status="UNSCHEDULED",
                    warehouse=target_warehouse,
                )

                if not validation_result.success:
                    failed_appointment.notes = validation_result.message
                    all_appointments.append(failed_appointment)
                    all_errors.append(validation_result.message)

                    overall_success = False
                    continue

                pro_id = appointment.proId

                main_window = None
                popup_window = None

                if request_type == "normal":
                    warehouse_details = None
                    nav_to_search(driver)

                    fill_search_form(driver, pro_id)
                    main_window = driver.current_window_handle

                    matching_rows_result = _find_matches(
                        driver, target_warehouse, pro_id
                    )
                    if not matching_rows_result.success:
                        failed_appointment.notes = (
                            "No appointment found for the mentioned warehouse."
                        )
                        all_appointments.append(failed_appointment)

                        all_errors.append(
                            "No appointment found for the mentioned warehouse."
                        )

                        overall_success = False
                        continue

                    first_match = matching_rows_result.matchingRows[0]
                    failed_appointment.warehouse = first_match.warehouseDetails
                    warehouse_details = first_match.warehouseDetails

                    row = first_match.row

                    driver.implicitly_wait(0)

                    appointment_links = row.find_elements(
                        By.XPATH,
                        ".//a[contains(text(), 'Reschedule Live Appt')]",
                    )

                    if not appointment_links:
                        appointment_links = row.find_elements(
                            By.XPATH,
                            ".//a[contains(text(), 'Schedule Live Appt')]",
                        )

                    if appointment_links:
                        appointment_link = appointment_links[0]
                    else:
                        # Direct appointment entry flow
                        appointment_datetime = datetime.strptime(
                            appointment.appointmentTime, "%Y-%m-%dT%H:%M:%S"
                        )

                        date_str = appointment_datetime.strftime("%m/%d/%Y")
                        time_str = appointment_datetime.strftime("%H:%M")

                        # Find date/time inputs
                        start_date_input = row.find_element(
                            By.XPATH,
                            ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointStartDateTime')]",
                        )
                        start_time_input = row.find_element(
                            By.XPATH,
                            ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointStartDateTime')]",
                        )

                        start_date_input.clear()
                        start_date_input.send_keys(date_str)
                        start_time_input.clear()
                        start_time_input.send_keys(time_str)

                        end_date_input = row.find_element(
                            By.XPATH,
                            ".//div[contains(@class, 'lame-duck-input')]/input[contains(@name, 'appointEndDateTime')]",
                        )
                        end_time_input = row.find_element(
                            By.XPATH,
                            ".//input[contains(@name, '-$TIME$') and contains(@name, 'appointEndDateTime')]",
                        )

                        end_date_input.clear()
                        end_date_input.send_keys(date_str)
                        end_time_input.clear()
                        end_time_input.send_keys(time_str)

                        # Submit the form
                        submit_button = driver.find_element(
                            By.XPATH, "//button[contains(text(), 'Save')]"
                        )
                        safe_click(driver, submit_button)

                        alert_status, alert_message = handle_alerts(driver)

                        wait_for_page_load(driver)

                        # Add a short delay to ensure the update appointment request is fully processed by the server
                        time.sleep(1)

                        # Add to results
                        created_appointment = Appointment(
                            appointmentId="-",
                            scheduledTime=appointment.appointmentTime,
                            duration=60,
                            status="SCHEDULED",
                            notes=alert_message,
                            warehouse=first_match.warehouseDetails,
                        )

                        all_appointments.append(created_appointment)

                        continue

                    driver.implicitly_wait(10)

                    if appointment_link:
                        safe_click(driver, appointment_link)
                    else:
                        appointment_link_locator = LOCATORS[
                            "reschedule_live_appt_link2"
                        ]
                        safe_click(driver, appointment_link_locator)

                    if not switch_popup(driver, main_window):
                        failed_appointment.notes = (
                            "Failed to switch to appointment scheduling popup."
                        )
                        all_appointments.append(failed_appointment)

                        all_errors.append(
                            "Failed to switch to appointment scheduling popup."
                        )

                        overall_success = False
                        continue

                    popup_window = driver.current_window_handle
                    wait_for_page_load(driver)

                elif request_type == "3rd_party":
                    matching_rows_result = None

                    nav_to_3rd_party_appt(driver)
                    fill_3rd_party_form(
                        driver,
                        company_name,
                        pro_id,
                        target_warehouse.zipCode,
                        target_warehouse.stopType,
                        operation,
                    )

                    select_rows = driver.find_elements(
                        By.XPATH,
                        """
                        //table[contains(@id, 'results') or .//th]
                        //tr[
                            .//td[contains(@rowspan, '2')]
                            and (
                                .//table//select[.//option[contains(text(), 'Manage Live Appointment')]]
                                or
                                .//input[
                                    @type='checkbox'
                                ]
                            )
                        ]
                        """,
                    )

                    if not select_rows:
                        raise Exception(f"No select rows found")

                    warehouse_matched = False
                    for row in select_rows:
                        location_cells = row.find_elements(By.XPATH, "./td[1]")
                        location_text = (
                            location_cells[0].text.strip()
                            if location_cells
                            else "Unknown location"
                        )
                        warehouse_details = parse_location_3rd_party(
                            location_text
                        )
                        warehouse_details.stopType = target_warehouse.stopType

                        if not _match_warehouse(
                            warehouse_details, target_warehouse
                        ):
                            continue
                        else:
                            warehouse_matched = True
                            break

                    if not warehouse_matched:
                        failed_appointment.notes = "No matching warehouse found for the specified stop type."
                        all_appointments.append(failed_appointment)

                        all_errors.append(failed_appointment.notes)

                        overall_success = False
                        continue

                    try:
                        driver.implicitly_wait(1)
                        row.find_element(
                            By.XPATH,
                            ".//table//select[.//option[contains(text(), 'Manage Live Appointment')]]",
                        )
                        driver.implicitly_wait(10)

                        select_element = row.find_element(
                            By.XPATH, ".//select"
                        )

                        if not select_element:
                            raise Exception(f"No select element found")

                        go_buttons = row.find_elements(
                            By.XPATH,
                            ".//button[contains(text(), 'Go')]",
                        )
                        if not go_buttons:
                            go_buttons = row.find_elements(
                                By.XPATH, ".//button"
                            )

                        if not go_buttons:
                            raise Exception(f"No Go button found in row")

                        go_button = go_buttons[0]

                        select = Select(select_element)
                        select.select_by_visible_text(
                            "Manage Live Appointment"
                        )
                        safe_click(driver, go_button)

                    except Exception:
                        failed_appointment.notes = (
                            "Appointment is not live yet."
                        )
                        all_appointments.append(failed_appointment)

                        all_errors.append("Appointment is not live yet.")

                        overall_success = False
                        continue

                else:
                    return UpdateAppointmentResponse(
                        success=False,
                        message="Invalid request type",
                        errors=["Invalid request type"],
                    )

                wait_for_page_load(driver)

                appointment_time = appointment.appointmentTime
                appointment_datetime = datetime.strptime(
                    appointment_time, "%Y-%m-%dT%H:%M:%S"
                )
                appointment_slot = appointment_datetime.strftime("%H:00")

                if request_type == "normal":
                    set_date(driver, appointment_datetime.strftime("%m/%d/%Y"))
                else:
                    set_date(
                        driver,
                        appointment_datetime.strftime("%m/%d/%Y"),
                        date_button_locator=(
                            By.ID,
                            "test-show-open-appts-button",
                        ),
                    )

                slot_elements = driver.find_elements(
                    By.CSS_SELECTOR, "input[name='startSlotNum']"
                )
                slot_found = False
                for slot_element in slot_elements:
                    label_element = slot_element.find_element(
                        By.XPATH, "./parent::label"
                    )
                    time_text = label_element.text.strip()

                    if time_text == appointment_slot:
                        logger.info(f"Found matching slot: {time_text}")
                        safe_click(driver, slot_element)
                        slot_found = True
                        break

                if not slot_found:
                    if main_window:
                        close_other_windows(driver, main_window)

                    failed_appointment.notes = f"No appointment slots available for {appointment_time}"
                    all_appointments.append(failed_appointment)

                    all_errors.append(
                        "No appointment slots available for the specified time."
                    )

                    overall_success = False
                    continue

                if request_type == "3rd_party":
                    attribute_value_mapping = [
                        {
                            "locator": (By.NAME, "containerNumber"),
                            "value": appointment.containerId,
                        },
                        {
                            "locator": (By.NAME, "trailerNumber"),
                            "value": appointment.trailerId,
                        },
                        {
                            "locator": (By.NAME, "driverName"),
                            "value": appointment.driver,
                        },
                        {
                            "locator": (By.NAME, "vehicleNumber"),
                            "value": appointment.vehicleNumber,
                        },
                        {
                            "locator": (By.NAME, "comment"),
                            "value": appointment.notes,
                        },
                        {
                            "locator": (By.NAME, "scac"),
                            "value": (
                                appointment.scac
                                if appointment.scac
                                else "Scheduled through automated flow."
                            ),
                        },
                    ]
                else:
                    attribute_value_mapping = [
                        {
                            "locator": (By.NAME, "containerNumber"),
                            "value": appointment.containerId,
                        },
                        {
                            "locator": (By.NAME, "trailerNumber"),
                            "value": appointment.trailerId,
                        },
                        {
                            "locator": (By.ID, "trailerLicense"),
                            "value": appointment.trailerLicense,
                        },
                        {
                            "locator": (By.ID, "driverName"),
                            "value": appointment.driver,
                        },
                        {
                            "locator": (By.ID, "vehicleNumber"),
                            "value": appointment.vehicleNumber,
                        },
                        {
                            "locator": (By.ID, "vehicleLicense"),
                            "value": appointment.vehicleLicense,
                        },
                        {
                            "locator": (By.NAME, "comment"),
                            "value": (
                                appointment.notes
                                if appointment.notes
                                else "Scheduled through automated flow."
                            ),
                        },
                    ]

                for attribute in attribute_value_mapping:
                    if attribute["value"]:
                        try:
                            element = driver.find_element(
                                *attribute["locator"]
                            )
                            safe_send_keys(driver, element, attribute["value"])
                        except Exception as e:
                            logger.warning(
                                f"Failed to set {attribute['locator']}: {str(e)}"
                            )

                if request_type == "normal":
                    submit_button = driver.find_element(
                        By.XPATH,
                        "//button[@name='submitbut' and contains(text(), 'Submit Request')]",
                    )
                else:
                    submit_button = driver.find_element(
                        By.XPATH,
                        "//button[contains(text(), 'Submit Request')]",
                    )

                safe_click(driver, submit_button)
                wait_for_page_load(driver)

                # Add a short delay to ensure the update appointment request is fully processed by the server
                time.sleep(1)

                alert_status, alert_message = handle_alerts(
                    driver, popup_window
                )

                # Add to results
                created_appointment = Appointment(
                    appointmentId=appointment_slot,
                    scheduledTime=appointment_time,
                    duration=60,
                    status="SCHEDULED",
                    notes=alert_message,
                    warehouse=warehouse_details,
                )

                all_appointments.append(created_appointment)

                # Close the popup window and return to main window
                if main_window:
                    close_other_windows(driver, main_window)

            except Exception as e:
                logger.error(f"Error processing appointment: {str(e)}")

                error_msg = str(e)
                if "Alert Text" in error_msg:
                    error_msg = error_msg.split("\n")[0]

                failed_appointment.notes = (
                    f"Error while updating appointment: {str(error_msg)}."
                )
                all_appointments.append(failed_appointment)

                all_errors.append(
                    f"Error while updating appointment: {str(error_msg)}."
                )
                overall_success = False

                try:
                    driver.switch_to.window(main_window)
                except Exception:
                    pass

        return UpdateAppointmentResponse(
            success=overall_success,
            message=(
                "Appointments processed with mixed results"
                if not overall_success
                else "All appointments scheduled successfully"
            ),
            appointments=all_appointments,
            errors=all_errors,
        )
    except Exception as e:
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )
