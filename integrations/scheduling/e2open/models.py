"""E2open-specific data models."""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, validator
from selenium.webdriver.remote.webelement import WebElement

from integrations.scheduling.models import (
    Credentials,
    SchedulingActionType,
    SchedulingBaseRequest,
    SchedulingPlatform,
    WarehouseDetails,
)
from models.base import BaseResponse


class E2openAppointmentData(BaseModel):
    """E2open appointment data."""

    appointmentId: Optional[str] = ""
    appointmentTime: str
    containerId: Optional[str] = ""
    driver: Optional[str] = ""
    duration: int
    location: Optional[str] = ""
    notes: Optional[str] = ""
    proId: Optional[str] = ""
    status: Optional[str] = ""
    scac: Optional[str] = ""
    trailerId: Optional[str] = ""
    trailerLicense: Optional[str] = ""
    vehicleLicense: Optional[str] = ""
    vehicleNumber: Optional[str] = ""
    requestType: Optional[str] = "normal"
    warehouse: Optional[WarehouseDetails] = None


class E2openOpenSlotsAppointmentData(BaseModel):
    """E2open open slots appointment data."""

    notes: Optional[str] = ""
    status: Optional[str] = ""
    warehouse: Optional[WarehouseDetails] = None


class E2openLoginRequest(SchedulingBaseRequest):
    """E2open login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    credentials: Credentials


class E2openGetLoadTypesRequest(SchedulingBaseRequest):
    """E2open-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class E2openValidateAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.VALIDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    proId: str
    companyName: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    operation: Optional[str] = None
    warehouse: Optional[WarehouseDetails] = None


class E2openGetOpenSlotsRequest(SchedulingBaseRequest):
    """E2open-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    proId: str
    startDate: Optional[str] = None
    endDate: Optional[str] = None
    companyName: Optional[str] = None
    operation: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    warehouse: Optional[WarehouseDetails] = None


class E2openGetOpenSlotsResponse(BaseResponse):
    """E2open-specific response containing open appointment slots."""

    appointments: Optional[List[E2openOpenSlotsAppointmentData]] = None


class E2openGetWarehouseRequest(SchedulingBaseRequest):
    """E2open-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class E2openCancelAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    appointmentId: Optional[str] = ""
    proId: str
    reason: Optional[str] = None
    warehouse: Optional[WarehouseDetails] = None
    companyName: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    operation: Optional[str] = None


class E2openGetAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN

    proId: str
    companyName: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    operation: Optional[str] = None
    warehouse: Optional[WarehouseDetails] = None


class E2openMakeAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN
    companyName: Optional[str] = None
    operation: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    appointments: List[E2openAppointmentData]


class E2openUpdateAppointmentRequest(SchedulingBaseRequest):
    """E2open-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.E2OPEN
    companyName: Optional[str] = None
    operation: Optional[str] = None
    requestType: str = "normal"  # normal or, 3rd_party
    appointments: List[E2openAppointmentData]


class E2openMatchingRow(BaseModel):
    """E2open-specific container for matching row data."""

    row: WebElement
    warehouseDetails: WarehouseDetails

    class Config:
        arbitrary_types_allowed = True


class E2openMatchingRowsResult(BaseModel):
    """E2open-specific result container for matching rows."""

    success: bool
    message: str
    errors: Optional[List[str]] = None

    matchingRows: Optional[List[E2openMatchingRow]] = None


class E2openOpenSlotsValidationResult(BaseModel):
    """E2open-specific validation result for open slots request."""

    success: bool
    message: Optional[str] = ""

    startDate: Optional[datetime] = None
    endDate: Optional[datetime] = None

    @validator("startDate")
    def validate_start_date(cls, v):
        if v and v.date() < datetime.now().date():
            raise ValueError("Start date cannot be in the past")
        return v

    @validator("endDate")
    def validate_end_date(cls, v, values):
        if v and "startDate" in values and values["startDate"]:
            if v < values["startDate"]:
                raise ValueError("End date must be on or after start date")
        return v


class E2openWarehouseValidator(BaseModel):
    """E2open-specific validator for warehouse fields."""

    # name: str
    stopType: str

    # @validator("name")
    # def validate_name(cls, v):
    #     if not v:
    #         raise ValueError("Warehouse name is required")
    #     return v

    @validator("stopType")
    def validate_stop_type(cls, v):
        if not v:
            raise ValueError("Stop type is required")
        return v


class E2openAppointmentValidationResult(BaseModel):
    """E2open-specific validation result for appointment request."""

    success: bool
    message: Optional[str] = ""

    @validator("success")
    def set_default_message(cls, v, values):
        if v and "message" not in values:
            values["message"] = "Validation successful"
        return v


class E2openAppointmentRequestValidator(BaseModel):
    """E2open-specific validator for appointment request data."""

    appointmentTime: str
    proId: str
    warehouse: WarehouseDetails

    @validator("proId")
    def validate_pro_id(cls, v):
        if not v:
            raise ValueError("PRO ID is required")
        return v

    @validator("appointmentTime")
    def validate_appointment_time(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%dT%H:%M:%S")
            return v
        except ValueError:
            raise ValueError(
                "Invalid appointment time format. Expected: YYYY-MM-DDTHH:MM:SS"
            )

    @validator("warehouse")
    def validate_warehouse(cls, v):
        if not v:
            raise ValueError("Warehouse details are required")
        return v
