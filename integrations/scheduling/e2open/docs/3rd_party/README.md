#### API Examples

All requests are sent as JSON to the endpoint http://localhost:8000/ using POST method. Here are examples for each supported operation:

[Architecture Diagram](../3rd_party/architecture.png)
![architecture.png](../3rd_party/architecture.png)

#### 3rd Party Flow Overview

The 3rd party appointment scheduling flow follows these steps:
1. Select a company (e.g., DART CONTAINER)
2. Select an operation (if applicable for the company)
3. Access the main form window to view, create, update, or cancel appointments

All API requests for 3rd party flow must include:
- `requestType`: Set to "3rd_party" to identify this flow
- `companyName`: The selected company
- `operation`: The selected operation (if applicable, otherwise empty string)

##### Data Retrieval

###### Validate Appointments

Validates appointment details.

This API will provide information of all the appointments related to shared proId. The extended parameter in response will tell whether the appointment is FCFS or not.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "ValidateAppointment",
  "userId": "user123",
  "proId": "1211014",
  "warehouse": {
    "zipCode": "75051"
  },
  "operation": "",
  "requestType": "3rd_party",
  "companyName": "DART CONTAINER",
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId etc. [Invalid request: proId: Field required]

Success Response:
```json
{
    "Note": "Testing Required"
}
```

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

Additional updates:
- pro_id: Unique identifier for appointment added, through which we will fetch the related open slots.
- warehouse: Details of the warehouse for which we should fetch open slots. [Matched with appointment's location]
- None of the details in warehouse are mandatory.
- Default start and end date will be today + 1 week.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "GetOpenSlots",
  "userId": "123456",
  "proId": "1211014",
  "startDate": "2025-05-08",
  "endDate": "2025-05-08",
  "requestType": "3rd_party",
  "operation": "",
  "companyName": "DART CONTAINER",
  "warehouse": {
    "name": "",
    "addressLine1": "",
    "city": "",
    "state": "",
    "zipCode": "75051",
    "country": "",
    "stopType": ""
  },
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId, startDate, endDate etc. [Invalid request: userId: Field required]
- Start date cannot be in the past
- End date must be on or after start date
- Start Date and End Date should be in proper format '%Y-%m-%d'. [time data '04-23-2025' does not match format '%Y-%m-%d']
- Pop-up didn't open on clicking rescheduling appointment link. [Error: Failed to switch to appointment scheduling popup]


Success Response:
```json
{
    "success": true,
    "message": "Successfully retrieved open slots",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "notes": "Open slots available for the respective warehouse.",
            "status": "AVAILABLE",
            "warehouse": {
                "city": "GRAND PRAIRIE",
                "country": "US",
                "name": "SOLO - GRAND PRAIRIE",
                "addressLine1": "1803 W PIONEER PARKWAY",
                "openSlots": [
                    {
                        "duration": 60,
                        "scheduledTime": "2025-05-15T10:00:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-05-15T11:00:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-05-15T13:00:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-05-15T14:00:00"
                    },
                    {
                        "duration": 60,
                        "scheduledTime": "2025-05-15T15:00:00"
                    }
                ],
                "state": "TX",
                "stopType": "pickup",
                "website": "",
                "zipCode": "75051"
            }
        }
    ]
}
```

##### Appointment Management

###### Get Appointment

Get all existing confirmed appointment details related to shared proId.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "GetAppointment",
  "proId": "1211014",
  "warehouse": {
    "zipCode": "75051"
  },
  "operation": "",
  "requestType": "3rd_party",
  "companyName": "DART CONTAINER",
  "userId": "user123",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId, startDate, endDate etc. [Invalid request: userId: Field required]
- Error extracting/parsing appointment date/time
- No appointments found for provided proId.

Success Response:
```json
{
    "Note": "Testing Required"
}
```

###### Create Appointment / Re-Schedule Appointment

Creates a new appointment.

We already have shipment details on this platform, we fetch those using Pro ID, and then make an appointment either for dropoff/pickup on the basis of requestType, on the appointmentTime.

Both, make appointment and re-schedule appointment flow are same as of now.

Additional updates:
- Batch processing added
- If there are 5 valid appointments and 3 wrong, the correct ones will be updated and incorrect ones will be returned with an error written in notes.
- Warehouse name is no longer mandatory, as we match using other warehouse details and proId

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "MakeAppointment",
  "userId": "user123",
  "requestType": "3rd_party",
  "companyName": "DART CONTAINER",
  "operation": "",
  "appointments": [
    {
        "appointmentTime": "2025-05-08T14:00:00",
        "duration": 60,
        "warehouse": {
            "name": "xyz",
            "city": "",
            "state": "",
            "zipCode": "75051",
            "country": "",
            "website": "",
            "stopType": "pickup"
        },
        "proId": "1211014",
        "status": "Scheduled",
        "notes": "NFI pro 3260930"
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: appointmentTime, duration, proId, stopType etc. [Invalid request: appointmentTime: Field required]
- Failed to switch to appointment scheduling popup
- Appointment time should be in proper format '%Y-%m-%dT%H:%M:%S'. [time data '04-23-2025T17:00:00' does not match format '%Y-%m-%dT%H:%M:%S']
- No appointment slots available
- Invalid requestType, companyName, or operation
- Company not supported in 3rd party flow

Success Response:
```json
{
    "Note": "Testing Required"
}
```

###### Cancel Appointment

Cancels an existing appointment.

Only, one appointment will be cancelled at a time.

The matching will be done on the basis of warehouse and proId, if multiple appointment rows matches the details, 1st one will be picked and cancelled.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "CancelAppointment",
  "proId": "1211014",
  "requestType": "3rd_party",
  "companyName": "DART CONTAINER",
  "operation": "",
  "reason": "Appointment cancelled per customer request, through automated flow.",
  "warehouse": {
    "name": "",
    "city": "",
    "state": "",
    "zipCode": "75051",
    "country": "",
    "stopType": ""
  },
  "userId": "user123",
  "appointmentId": "appt123",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: appointmentId, proId etc. [Invalid request: appointmentId: Field required]
- Failed to switch to appointment canceling popup

Success Response:
```json
{
    "Note": "Testing Required"
}
```

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```
