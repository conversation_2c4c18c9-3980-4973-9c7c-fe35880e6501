#### API Examples

All requests are sent as JSON to the endpoint http://localhost:8000/ using POST method. Here are examples for each supported operation:

[Architecture Diagram](../normal/architecture.png)
![architecture.png](../normal/architecture.png)

##### Data Retrieval

###### Validate Appointments

Validates appointment details.

This API will provide information of all the appointments related to shared proId and warehouse details. The extended parameter in response will tell whether the appointment is FC<PERSON> or not.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "ValidateAppointment",
  "userId": "user123",
  "proId": "0044788",
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId etc. [Invalid request: proId: Field required]

Success Response:
```json
{
    "success": true,
    "message": "Appointment validation successful",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "04/29/2025 19:00",
            "duration": 60,
            "location": null,
            "status": "CONFIRMED",
            "notes": null,
            "warehouse": {
                "name": "UTZ QUALITY FOODS - NORTH EAST LOGISTICS CENTER",
                "city": "HANOVER",
                "state": "PA",
                "zipCode": "17331",
                "country": "US",
                "website": "<EMAIL>",
                "stopType": "pickup"
            },
            "extended": {
                "isFcfs": false
            }
        },
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "-",
            "duration": 60,
            "location": null,
            "status": "UNSCHEDULED",
            "notes": null,
            "warehouse": {
                "name": "WALMART DC 6054G-GEN",
                "city": "LAGRANGE",
                "state": "GA",
                "zipCode": "30241",
                "country": "US",
                "website": "",
                "stopType": "dropoff"
            },
            "extended": {
                "isFcfs": true
            }
        }
    ],
    "pagination": null
}
```

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

Additional updates:
- pro_id: Unique identifier for appointment added, through which we will fetch the related open slots.
- warehouse: Details of the warehouse for which we should fetch open slots. [Matched with appointment's location]
- None of the details in warehouse are mandatory.
- Default start and end date will be today + 1 week.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "GetOpenSlots",
  "userId": "dfads",
  "proId": "0044732",
  "startDate": "",
  "endDate": "",
  "warehouse": {
    "name": "",
    "city": "",
    "state": "",
    "zipCode": "",
    "country": "",
    "stopType": ""
  },
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId, startDate, endDate etc. [Invalid request: userId: Field required]
- Start date cannot be in the past
- End date must be on or after start date
- Start Date and End Date should be in proper format '%Y-%m-%d'. [time data '04-23-2025' does not match format '%Y-%m-%d']
- Pop-up didn't open on clicking rescheduling appointment link. [Error: Failed to switch to appointment scheduling popup]


Success Response:
```json
{
  "success": true,
  "message": "Successfully retrieved open slots",
  "errors": null,
  "platformData": null,
  "appointments": [
    {
      "notes": "Open slots available for the respective warehouse.",
      "status": "AVAILABLE",
      "warehouse": {
        "city": "HANOVER",
        "country": "US",
        "name": "UTZ QUALITY FOODS - 240 KINDIG HANOVER, PA",
        "openSlots": [
          {
            "scheduledTime": "2025-04-26T13:00:00",
            "duration": 60
          },
          {
            "scheduledTime": "2025-04-26T15:00:00",
            "duration": 60
          },
          {
            "scheduledTime": "2025-05-02T16:00:00",
            "duration": 60
          }
        ]
      },
      "state": "PA",
      "stopType": "pickup",
      "website": "<EMAIL>",
      "zipCode": "17331"
    },
    {
      "notes": "No open slots available for the respective warehouse.",
      "status": "UNAVAILABLE",
      "warehouse": {
        "city": "DOUGLAS",
        "country": "US",
        "name": "WALMART DC 6010G-GEN",
        "openSlots": [],
        "state": "GA",
        "stopType": "dropoff",
        "website": "",
        "zipCode": "31533"
      }
    },
    {
      "notes": "No open slots available for the respective warehouse.",
      "status": "UNAVAILABLE",
      "warehouse": {
        "city": "ALACHUA",
        "country": "US",
        "name": "WALMART DC 7035G-GEN",
        "openSlots": [],
        "state": "FL",
        "stopType": "dropoff",
        "website": "",
        "zipCode": "32615"
      }
    }
  ]
}
```

##### Appointment Management

###### Get Appointment

Get all existing confirmed appointment details related to shared proId.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "GetAppointment",
  "proId": "0044651",
  "userId": "user123",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: proId, startDate, endDate etc. [Invalid request: userId: Field required]
- Error extracting/parsing appointment date/time
- No appointments found for provided proId.

Success Response:
```json
{
    "success": true,
    "message": "Successfully retrieved 2 appointments for PRO ID 0044651",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "2025-04-25T09:00:00",
            "duration": 60,
            "location": null,
            "status": "CONFIRMED",
            "notes": "",
            "warehouse": {
                "name": "UTZ QUALITY FOODS - NORTH EAST LOGISTICS CENTER",
                "city": "HANOVER",
                "state": "PA",
                "zipCode": "17331",
                "country": "US",
                "website": "<EMAIL>",
                "stopType": "pickup"
            },
            "extended": {
                "proId": "0044651"
            }
        },
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "2025-04-23T17:00:00",
            "duration": 60,
            "location": null,
            "status": "CONFIRMED",
            "notes": "",
            "warehouse": {
                "name": "NEW UTZ QUALITY FOODS - BIRMINGHAM, AL",
                "city": "BIRMINGHAM",
                "state": "AL",
                "zipCode": "35214",
                "country": "US",
                "website": "",
                "stopType": "dropoff"
            },
            "extended": {
                "proId": "0044651"
            }
        }
    ]
}
```

###### Create Appointment / Re-Schedule Appointment

Creates a new appointment.

We already have shipment details on this platform, we fetch those using Pro ID, and then make an appointment either for dropoff/pickup on the basis of requestType, on the appointmentTime.

Both, make appointment and re-schedule appointment flow are same as of now.

Additional updates:
- Batch processing added
- If there are 5 valid appointments and 3 wrong, the correct ones will be updated and incorrect ones will be returned with an error written in notes.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "MakeAppointment",
  "userId": "user123",
  "appointments": [
    {
        "appointmentTime": "2025-04-25T09:00:00",
        "duration": 60,
        "warehouse": {
            "name": "UTZ QUALITY FOODS - HDC HANOVER, PA",
            "city": "",
            "state": "",
            "zipCode": "",
            "country": "",
            "stopType": "pickup"
        },
        "proId": "0044666",
        "status": "Scheduled"
    },
    {
        "appointmentTime": "2025-04-29T06:00:00",
        "duration": 60,
        "warehouse": {
            "name": "UTZ QUALITY FOODS - BIRMINGHAM BULK",
            "city": "",
            "state": "",
            "zipCode": "",
            "country": "",
            "stopType": "pickup"
        },
        "proId": "0044666",
        "status": "Scheduled"
    }
  ],
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: appointmentTime, duration etc. [Invalid request: appointmentTime: Field required]
- Failed to switch to appointment scheduling popup
- Appointment time should be in proper format '%Y-%m-%dT%H:%M:%S'. [time data '04-23-2025T17:00:00' does not match format '%Y-%m-%dT%H:%M:%S']
- No appointment slots available

Success Response:
```json
{
    "success": false,
    "message": "Appointments processed with mixed results",
    "errors": [
        "No appointment found for the mentioned warehouse."
    ],
    "platformData": null,
    "appointments": [
        {
            "appointmentId": "09:00",
            "reference": null,
            "scheduledTime": "2025-04-25T09:00:00",
            "duration": 60,
            "location": null,
            "status": "SCHEDULED",
            "notes": "",
            "warehouse": {
                "name": "UTZ QUALITY FOODS - HDC HANOVER, PA",
                "city": "HANOVER",
                "state": "PA",
                "zipCode": "17331",
                "country": "US",
                "website": "HDC WAREHOUSE",
                "stopType": "pickup"
            },
            "extended": null
        },
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "-",
            "duration": 0,
            "location": null,
            "status": "UNSCHEDULED",
            "notes": "No appointment found for the mentioned warehouse.",
            "warehouse": {
                "name": "UTZ QUALITY FOODS - BIRMINGHAM BULK",
                "city": "",
                "state": "",
                "zipCode": "",
                "country": "",
                "stopType": "pickup"
            },
            "extended": null
        }
    ]
}
```

```json
{
    "success": true,
    "message": "Successfully retrieved open slots",
    "errors": null,
    "platformData": null,
    "appointments": [
        {
            "appointmentId": "-",
            "reference": null,
            "scheduledTime": "-",
            "duration": 0,
            "location": null,
            "status": "UNAVAILABLE",
            "notes": "No open slots available for the respective warehouse.",
            "warehouse": {
                "name": "DOT FOODS - BURLEY D",
                "city": "BURLEY",
                "state": "ID",
                "zipCode": "83318",
                "country": "US",
                "website": "************",
                "stopType": "dropoff"
            },
            "extended": null
        }
    ],
    "pagination": null
}
```

###### Cancel Appointment

Cancels an existing appointment.

Only, one appointment will be cancelled at a time.

The matching will be done on the basis of warehouse and proId, if multiple appointment rows matches the details, 1st one will be picked and cancelled.

Request Payload:
```json
{
  "integration": "scheduling",
  "platform": "E2open",
  "action": "CancelAppointment",
  "proId": "0044666",
  "reason": "Appointment cancelled per customer request, through automated flow.",
  "warehouse": {
    "name": "UTZ QUALITY FOODS - HDC HANOVER, PA",
    "city": "",
    "state": "",
    "zipCode": "",
    "country": "",
    "stopType": "pickup"
  },
  "userId": "user123",
  "appointmentId": "appt123",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

Failure Cases:
- Mandatory parameters missing: appointmentId, proId etc. [Invalid request: appointmentId: Field required]
- Failed to switch to appointment canceling popup

Success Response:
```json
{
    "success": true,
    "message": "Appointment canceled successfully",
    "errors": null,
    "platformData": null,
    "appointmentId": null,
    "proId": "0044841",
    "warehouse": {
        "name": "UTZ QUALITY FOODS - NORTH EAST LOGISTICS CENTER",
        "city": "HANOVER",
        "state": "PA",
        "zipCode": "17331",
        "country": "US",
        "website": "<EMAIL>",
        "stopType": "pickup"
    },
    "alertMessage": ""
}
```

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```
