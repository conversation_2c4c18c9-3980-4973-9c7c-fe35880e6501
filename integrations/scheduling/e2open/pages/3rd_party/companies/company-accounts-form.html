<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="TPShipperAcct" class="transactional-page">
   <head>
      <title>Select Company Account</title>
      <meta name="msapplication-config" content="/browserconfig.xml" />
      <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
      <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
      <script type="text/javascript">
         var faviconICO = document.querySelector('#faviconICO');
         var faviconPNG = document.querySelector('#faviconPNG');
         var darkModeListener = function(event) {
         if (event.matches) {
         faviconICO.setAttribute("href","/favicon_dark.ico");
         faviconPNG.setAttribute("href","/favicon_dark.png");
         } else {
         faviconICO.setAttribute("href","/favicon.ico");
         faviconPNG.setAttribute("href","/favicon.png");
         }
         };
         var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
         if(darkModePreference.addEventListener){
         darkModePreference.addEventListener('change', function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         } else {
         darkModePreference.addListener(function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         }
         darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
      </script>
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/fallback.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <script type="text/javascript">
         function popupMaestro() {




         }
         onDocumentReady(function() {
         var iconContainer = document.createElement('div');
         jQuery(document.body).prepend(iconContainer);
         jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
         popupMaestro();
         });
      </script>
      <script type="text/javascript">
         var useComponents = false;
         var newStylesComponents = false;
         var useETOComponents = false;
         var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
      </script>
      <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript">
         ll.util.initFormats(1, 1,
         'en_US');
         var shortLang = 'en';
         var browserCacheKey = '12abedf2\-c4e0\-4e77\-b867\-e8a038e26ba9';
         ll.lang = ll.lang || {};
         ll.lang.locale = 'en_US';
      </script>
      <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/Modal.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/Popover.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/PanelTransition.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript">
         jQuery.noConflict();
         var LeanActionFormName = '';
         var LeanActionFullPath = '';
         var $LeanActionForm;
         onDocumentReady(function() {



         $LeanActionForm = jQuery('form');


         if (get_browser() === "msie") {
         jQuery('html').addClass("ie");
         }
         if ( 'noValidate' in document.createElement('form') ){
         jQuery('html').addClass("novalidate");
         }
         var $dwrImageDiv = jQuery('#dwrImageDiv');
         if (!$dwrImageDiv.length) {
         $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
         $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
         jQuery('body').append($dwrImageDiv);
         }


         hasExpiredSessionWarningBeenDisplayed = false;
         });
      </script>
      <style>
         .checkbox_wrapper {
         padding: 2px;
         display: inline-block;
         cursor: pointer;
         min-width: 70px;
         width: 100%;
         text-align: center;
         }
      </style>
      <script type="text/javascript">
         function validateForm(form) {
         form.submit();
         }
      </script>
   </head>
   <body >
      <div class="page-message" id="page-message-root">
         <div class="page-message__container">
            <div class="page-message__icon-container"></div>
            <div class="page-message__message-container">
               <ul class="page-message__messages">
                  <li class="page-message__message page-message__message--primary"></li>
               </ul>
               <div class="page-message__button-container" style="display: none;"></div>
            </div>
            <button type="button" class="icon-button page-message__close-button">
               <span role="tooltip" class="icon-span">
                  <svg class="icon  ic_close" focusable="false">
                     <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                  </svg>
               </span>
            </button>
         </div>
      </div>
      <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script>
         (function() {
         var errors = [

         ];
         var success = [

         ];
         onDocumentReady(function() {
         var oldPageMessages = document.querySelectorAll('.old-page-message');
         for(var i = 0; i < oldPageMessages.length; i++) {
         var oldMessage = oldPageMessages[i];
         oldMessage.parentElement.removeChild(oldMessage);
         }
         if(errors.length) {
         PageMessage.error(errors, null, null, false);
         } else if(success.length) {
         PageMessage.success(success, null, null, false);
         }
         });
         })();
      </script>
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/pages/thirdparty/thirdpartyHeader.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <div class="primary-header">
         <div class="primary-header__system-info">
            <div class="primary-header__logo">
               <a href="http://www.blujaysolutions.com/" target="_top">
               <img border="0" alt="e2open v.tm4sprd07-web02-chg:master:2025-04-02_10-50-32" src="/images/logos/e2open_logo.svg" align="bottom"/>
               </a>
            </div>
            <div class="primary-header__system-title"></div>
            <div class="primary-header__system-environment"></div>
         </div>
         <div class="primary-header__system-message">
            &nbsp;
         </div>
         <div class="primary-header__items">
            <div class="primary-header__menu" id="thirdPartyUserMenu">
               <div class="primary-header__menu-anchor">
                  <div title="My Account" class="user-name">
                     <span>CoraMc</span>
                     <span role="tooltip" class="icon-span">
                        <svg class="icon  ic_account_circle" focusable="false">
                           <use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                     </span>
                  </div>
               </div>
               <ul class="primary-header__menu-popver popover use-show-class" id="thirdPartyUserMenuPopover">
                  <li className="primary-header__menu-popver__menu-group">
                     <div class="defaults-label">Your defaults</div>
                     <table class="user-defaults">
                        <tbody>
                           <tr>
                              <td>User:</td>
                              <td>NFI Transportation</td>
                           </tr>
                     </table>
                  </li>
                  <li className="primary-header__menu-popver__menu-group">
                     <a href="javascript:noop()" onclick="popup('/thirdparty/tprequestaccess.do', '_popup', 650, 650);" class="primary-header__menu-popver__menu-link">Request Access</a>
                  </li>
                  <li class="link">
                     <a href="javascript:populate()">PRIVACY SETTINGS</a>
                  </li>
                  <li className="primary-header__menu-popver__menu-group">
                     <a
                        href="/thirdparty/logouttpuser.do"
                        class="primary-header__menu-popver__menu-link"
                        >
                     Close Window
                     </a>
                  </li>
               </ul>
            </div>
         </div>
      </div>
      <div id="cookieDialog" class="display-none-item"></div>
      <hr class="haerder-ruler"/>
      <script>
         function _customerSupport(answerID) {
         if (answerID && answerID != '') {
         popup('/help/customersupport.do?answerID=' + encodeURIComponent(answerID), 'CustomerSupport', 1200, 600);
         } else {
         popup('/help/customersupport.do', 'CustomerSupport', 1200, 600);
         }
         }
         function _webhelp() {
         var error = function() {
         alert(errors_message);
         }
         ll.util.callAjaxAction('/tmsrest/jwt/webhelp', function success(data) {
         ll.util.callAjaxAction(data.url, function success() {
         popup(data.url, 'webhelp', 1200, 600);
         }, error, null, 'POST', data.token, true, 'text/plain', {
         xhrFields: {
         withCredentials: true
         }
         });
         }, error);
         }
         function headerInit() {
         var headerUserItem = document.getElementById('thirdPartyUserMenu');
         var menuUserPopover = new Popover(document.getElementById('thirdPartyUserMenuPopover'), headerUserItem);
         headerUserItem.addEventListener('click', function() {
         menuUserPopover.show();
         });
         }
         if (document.readyState != 'loading'){
         headerInit();
         } else {
         onDocumentReady(headerInit);
         }
         function populate(){
         jQuery('#headerUserPopover').hide();
         ll.util.callAjaxAction('/pages/cookieconsent',
         function(response) {
         if (response != null) {
         var cookies = [];
         var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
         if(response.cookieGroups.length > 0){
         cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
         response.cookieGroups.forEach((cookie) =>{
         cookies.push(cookie.id);
         cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
         var checked = cookie.value ? "checked" : "";
         var disabled = !cookie.enabled ? "disabled" : "";
         cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
         cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
         cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
         cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
         });
         cookieHtml += "\n</table>\n</section>";
         }
         jQuery("#cookieDialog").html(cookieHtml);
         openCookieDialog(response.title,cookies);
         }
         }, null, null, 'GET', null, false);
         }
         function openCookieDialog(title,cookies){
         var dialogID = '#cookieDialog';
         jQuery(dialogID).dialog({
         autoOpen: true,
         width: 1030,
         height : 500,
         modal: true,
         title : title,
         resizable: false,
         draggable: false,
         buttons: {
         "Close": function() {
         jQuery(dialogID).dialog("close");
         },
         "Save": function() {
         var resCookies = cookies.map((cookie) => {
         return {
         id: cookie,
         enabled : jQuery("#"+cookie).is(":checked")
         };
         })
         ll.util.callAjaxAction('/pages/cookieconsent',
         function(response) {
         PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
         jQuery(dialogID).dialog("close");
         },
         function(error) {
         PageMessage.error('An error occurred while performing the update.');
         },
         null,
         'POST',
         {
         preferences : resCookies
         },
         false,
         'application/json'
         );
         }
         }
         });
         }
      </script>
      <p/>
      <form action="/thirdparty/selecttpuseracct.do" method="post" onsubmit="return validateForm(this);">
         <input type="hidden" name="pageaction" value="selectShipperCompany">
         <div class="page-header">
            <div class="page-header__container">
               <div class="page-header__main">
                  <div class="page-header__title-container">
                     <div class="page-header__page-title">Select Company Account</div>
                     <div class="page-header__page-title page-header__page-title--to-top">
                        <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                        Return to the top of Select Company Account                 </button>
                     </div>
                  </div>
               </div>
               <div class="page-message" id="page-message-root">
                  <div class="page-message__container">
                     <div class="page-message__icon-container"></div>
                     <div class="page-message__message-container">
                        <ul class="page-message__messages">
                           <li class="page-message__message page-message__message--primary"></li>
                        </ul>
                        <div class="page-message__button-container" style="display: none;"></div>
                     </div>
                     <button type="button" class="icon-button page-message__close-button">
                        <span role="tooltip" class="icon-span">
                           <svg class="icon  ic_close" focusable="false">
                              <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                           </svg>
                        </span>
                     </button>
                  </div>
               </div>
               <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
               <script>
                  (function() {
                  var errors = [

                  ];
                  var success = [

                  ];
                  onDocumentReady(function() {
                  var oldPageMessages = document.querySelectorAll('.old-page-message');
                  for(var i = 0; i < oldPageMessages.length; i++) {
                  var oldMessage = oldPageMessages[i];
                  oldMessage.parentElement.removeChild(oldMessage);
                  }
                  if(errors.length) {
                  PageMessage.error(errors, null, null, false);
                  } else if(success.length) {
                  PageMessage.success(success, null, null, false);
                  }
                  });
                  })();
               </script>
            </div>
         </div>
         <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
         <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
         <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
         <table class="lean_table resultrow_hover" width="100%">
            <tbody>
               <tr class="loadreportheader1">
                  <td width="23%" align="center">Select</td>
                  <td>Account</td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_12795" name="shipperCompanyID" value="12795" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_12795">ABBOTT NUTRITION</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_15269" name="shipperCompanyID" value="15269" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_15269">AMERICA&#039;S LOGISTICS, LLC</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_32117" name="shipperCompanyID" value="32117" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_32117">AMERICAN SUGAR REFINING, INC</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_35284" name="shipperCompanyID" value="35284" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_35284">ASSOCIATED WHOLESALE GROCERS</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_20481" name="shipperCompanyID" value="20481" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_20481">BATORY FOODS</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_6597" name="shipperCompanyID" value="6597" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_6597">BAY VALLEY FOODS</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_23351" name="shipperCompanyID" value="23351" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_23351">COCA-COLA BEVERAGES FLORIDA</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_21886" name="shipperCompanyID" value="21886" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_21886">COCA-COLA UNITED</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_31802" name="shipperCompanyID" value="31802" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_31802">CROWN CORK AND SEAL</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_37706" name="shipperCompanyID" value="37706" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_37706">DART CONTAINER</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_19681" name="shipperCompanyID" value="19681" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_19681">FERRARA CANDY</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_33269" name="shipperCompanyID" value="33269" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_33269">FLAGSTONE FOODS LLC</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_23353" name="shipperCompanyID" value="23353" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_23353">HEARTLAND COCA-COLA</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_31492" name="shipperCompanyID" value="31492" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_31492">HEARTLAND FOODS</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_36943" name="shipperCompanyID" value="36943" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_36943">HELLOFRESH</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_33791" name="shipperCompanyID" value="33791" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_33791">HIGH LINER FOODS INC</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_19967" name="shipperCompanyID" value="19967" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_19967">INGREDION</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_20986" name="shipperCompanyID" value="20986" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_20986">KEN&#039;S FOODS</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_39070" name="shipperCompanyID" value="39070" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_39070">LASSONDE INDUSTRIES INC</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_28647" name="shipperCompanyID" value="28647" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_28647">MOUNT FRANKLIN FOODS</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_25014" name="shipperCompanyID" value="25014" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_25014">PHILLIPS PET</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_23305" name="shipperCompanyID" value="23305" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_23305">REYES LOGISTICS SOLUTIONS, LLC</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_23354" name="shipperCompanyID" value="23354" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_23354">SWIRE COCA-COLA</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_16133" name="shipperCompanyID" value="16133" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_16133">TOYS R US</label></td>
               </tr>
               <tr class="resultrow1" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_22664" name="shipperCompanyID" value="22664" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_22664">ULTA BEAUTY</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td align="center">
                     <label class="checkbox_wrapper">
                     <input type="radio" id="shipperCompanyID_41300" name="shipperCompanyID" value="41300" onclick="validateForm(this.form)">
                     </label>
                  </td>
                  <td><label for="shipperCompanyID_41300">WINLAND FOODS</label></td>
               </tr>
            </tbody>
         </table>
      </form>
   </body>
</html>
