<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="ApptScheduling" class="search-page">
    <head>
        <title>3rd Party - Appointment Scheduling</title>
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <script type="text/javascript" src="/c/js/utility.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
        <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
        <script type="text/javascript">
            var faviconICO = document.querySelector('#faviconICO');
            var faviconPNG = document.querySelector('#faviconPNG');
            var darkModeListener = function(event) {
            if (event.matches) {
            faviconICO.setAttribute("href","/favicon_dark.ico");
            faviconPNG.setAttribute("href","/favicon_dark.png");
            } else {
            faviconICO.setAttribute("href","/favicon.ico");
            faviconPNG.setAttribute("href","/favicon.png");
            }
            };
            var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
            if(darkModePreference.addEventListener){
            darkModePreference.addEventListener('change', function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            } else {
            darkModePreference.addListener(function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            }
            darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <link rel="stylesheet" type="text/css" href="/c/css/fallback.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <script type="text/javascript">
            function popupMaestro() {




            }
            onDocumentReady(function() {
            var iconContainer = document.createElement('div');
            jQuery(document.body).prepend(iconContainer);
            jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2');
            popupMaestro();
            });
        </script>
        <script type="text/javascript">
            var useComponents = true;
            var newStylesComponents = false;
            var useETOComponents = true;
            var cacheKey = "c26dd71ea33c1d206c21efbb6e8ad749bd3551e2";
        </script>
        <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript">
            ll.util.initFormats(1, 1,
            'en_US');
            var shortLang = 'en';
            var browserCacheKey = 'b28ee050\-2641\-4bb6\-b655\-015b45ca131f';
            ll.lang = ll.lang || {};
            ll.lang.locale = 'en_US';
        </script>
        <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script type="text/javascript">
            jQuery.noConflict();
            var LeanActionFormName = 'ApptSchedulingForm';
            var LeanActionFullPath = '\/thirdparty\/apptscheduling.do';
            var $LeanActionForm;
            onDocumentReady(function() {


            $LeanActionForm = jQuery('form[name=ApptSchedulingForm]');



            if (get_browser() === "msie") {
            jQuery('html').addClass("ie");
            }
            if ( 'noValidate' in document.createElement('form') ){
            jQuery('html').addClass("novalidate");
            }
            var $dwrImageDiv = jQuery('#dwrImageDiv');
            if (!$dwrImageDiv.length) {
            $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
            $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
            jQuery('body').append($dwrImageDiv);
            }


            hasExpiredSessionWarningBeenDisplayed = false;
            });
        </script>
        <style type="text/css">
            .nonStops-actions{
            display: flex;
            flex-wrap: wrap;
            justify-content: end;
            }
            .hide {
            display: hidden;
            }
            .subsection {
            margin: 10px;
            width: calc(100% - 20px);
            }
            .subsection td {
            white-space: nowrap;
            padding: 0 5px 0 0;
            }
            .subsection tr td:last-child {
            width: 99%
            }
            #results {
            padding: 10px 0;
            }
        </style>
        <script type="text/javascript">
            function openMessageEntry(apptlocid) {
            popup("/thirdparty/assistmsgentry.do?apptLocID="+apptlocid, 'assistmsgentry', 800, 500);
            }
            function validateForm() {
            var form = document.forms["ApptSchedulingForm"];
            var pickRefNumSize = 6;
            var dropRefNumSize = 0;
            if(getCheckedValue(form.elements["searchType"])=="1000") {
            var myAppt = false;

            if(form.elements["pickMyAppt"].checked) {
            myAppt = true;
            }

            var pickRefFound = false;
            for(var i = 0; i < pickRefNumSize; i++) {
            if(form.elements["pickRefNums["+ i +"].searchString"] && !isEmpty(trim(form.elements["pickRefNums["+ i +"].searchString"].value))) {
            pickRefFound = true;
            }
            }
            var ymsRef = form.getElementsByClassName("yms-pick");
            var ymsRefValue = ymsRef.length > 0 ? ymsRef[0].value : "";
            if(!pickRefFound && (isEmpty(trim(form.elements["pickConfirmationNums"].value)) && isEmpty(trim(ymsRefValue))) && !myAppt) {
            alert("Either a confirmation number or at least one reference number must be entered.");
            form.elements["pickConfirmationNums"].focus();
            return false;
            }
            if(pickRefFound && !myAppt) {



            if(isEmpty(form.elements["pickGeo"].value,false)) {
            alert("Pick\-up Geography is required.");
            form.elements["pickGeo"].focus();
            return false;
            }

            }
            if(!isEmpty(form.elements["pickConfirmationNums"].value) && !myAppt) {
            if (!validateMultipleFields(form, "pickConfirmationNums", "integer")) {
            return false;
            }



            if(isEmpty(form.elements["pickConfGeo"].value)) {
            alert("Appointment Geography is required.");
            form.elements["pickConfGeo"].focus();
            return false;
            }

            }
            }
            if(getCheckedValue(form.elements["searchType"])=="1001") {
            var myAppt = false;

            var dropRefFound = false;
            for(var i = 0; i < dropRefNumSize; i++) {
            if(form.elements["dropRefNums["+ i +"].searchString"] && !isEmpty(trim(form.elements["dropRefNums["+ i +"].searchString"].value))) {
            dropRefFound = true;
            }
            }
            var dropConfirmationNums = form.elements["dropConfirmationNums"] && form.elements["dropConfirmationNums"].value;
            var ymsDrop = form.getElementsByClassName("yms-drop")[0] ? form.getElementsByClassName("yms-drop")[0].value : '';
            if(!dropRefFound && !myAppt && (isEmpty(trim(dropConfirmationNums)) && isEmpty(trim(ymsDrop))) && !myAppt) {
            alert("Either a confirmation number or at least one reference number must be entered.");
            form.elements["dropConfirmationNums"].focus();
            return false;
            }
            if(dropRefFound && !myAppt) {


            }
            if(!isEmpty(form.elements["dropConfirmationNums"].value) && !myAppt) {
            if (!validateMultipleFields(form, "dropConfirmationNums", "integer")) {
            return false;
            }


            }
            }
            form.elements["pageaction"].value="search";
            return true;
            }
            function setLocation(stopType, name, city, state, zip, zipzone, apptGeo) {
            var form = document.forms["ApptSchedulingForm"];
            if(stopType == 1000) {
            if(apptGeo) {




            form.elements["pickConfGeo"].value = city +", "+ state;





            } else {




            form.elements["pickGeo"].value = city +", "+ state;





            }
            } else if(stopType == 1001){
            if(apptGeo) {








            } else {








            }
            }
            }
            function toggleSearch() {
            var isPickUpSearchType = true;
            var form = document.forms["ApptSchedulingForm"];
            if(getCheckedValue(form.elements["searchType"])=="1000") {
            document.getElementById("pickRow").className="show";
            try {
            document.getElementById("pickMyAppt").className="show";
            } catch(e) {

            }
            document.getElementById("dropRow").className="hide";
            try {
            document.getElementById("dropMyAppt").className="hide";
            } catch(e) {

            }
            document.getElementById("results").style.display = isPickUpSearchType ? '' : 'none';
            } else {
            document.getElementById("dropRow").className="show";
            try {
            document.getElementById("dropMyAppt").className="show";
            } catch(e) {

            }
            try {
            document.getElementById("pickMyAppt").className="hide";
            } catch(e) {

            }
            document.getElementById("pickRow").className="hide";
            document.getElementById("results").style.display = isPickUpSearchType ? 'none' : '';
            }
            }
            function submitForm() {
            var form = document.forms["ApptSchedulingForm"];
            var searchButton = document.getElementById('searchButton');
            if(!searchButton.readOnly && validateForm()) {
            form.submit();
            searchButton.readOnly = true;
            searchButton.className = "search_disabled";
            searchButton.title = "To Cancel the Search, click Stop on the Browser";
            }
            }
            if (document.layers) {
            document.captureEvents(Event.KEYPRESS);
            }
            document.onkeypress = kpress;
            function kpress(e) {
            var key = (window.event) ? window.event.keyCode : e.which ;
            if (key == 13) {

            var eventSrcName = (window.event) ? window.event.srcElement.name : e.target.name ;
            var searchForm = document.forms["ApptSchedulingForm"];
            var elem = searchForm.elements[eventSrcName];
            if (elem) {
            submitForm();
            }
            }
            }
            function searchForLocations(pickOrDrop,apptGeo){
            var goToPage = "/thirdparty/apptlocationsearch.do?stopType="+ pickOrDrop +"&apptGeo="+ apptGeo;
            popup(goToPage, 'Search', 620, 300);
            }
            var nonStops = [

            ];
            function validateSelectedShipments() {
            var form = document.forms["ApptSchedulingForm"];
            var valid = true;

            var shipmentLegIDs = "";
            var apptLocationID = 0;
            for(var i = 0; i < nonStops.length; i++) {
            var nonStop = nonStops[i];
            var shipmentLength = 0;
            if (nonStop.shipments) {
            shipmentLength = nonStop.shipments.length;
            }
            for(var j = 0; j < shipmentLength; j++) {
            var shipment = nonStop.shipments[j];

            if (form.elements["nonStop["+ i +"].shipment["+ j +"]"] && form.elements["nonStop["+ i +"].shipment["+ j +"]"].checked) {
            shipmentLegIDs = shipmentLegIDs + shipment.shipmentLegID +";";
            if (apptLocationID == 0) {
            apptLocationID = shipment.appointmentLocationID;
            } else if (apptLocationID != shipment.appointmentLocationID) {
            alert("Appointment locations do not match.");
            valid = false;
            break;
            }
            }
            }
            }
            form.elements["selectedShipmentLegIDs"].value = shipmentLegIDs;
            return valid;
            }
            function appointSelected(trailerLoadingType) {
            var valid = true;
            var form = document.forms["ApptSchedulingForm"];

            valid = validateSelectedShipments();

            if(valid && form.elements["selectedShipmentLegIDs"].value.length > 0) {
            form.elements["pageaction"].value="appoint";
            form.elements["trailerLoadingType"].value = trailerLoadingType;
            form.submit();
            }
            }
            function consolidateSelected() {
            var valid = true;
            var form = document.forms["ApptSchedulingForm"];

            valid = validateSelectedShipments();

            if(valid && form.elements["selectedShipmentLegIDs"].value.length > 0) {
            form.elements["pageaction"].value="manageGrouping";
            form.submit();
            }
            }
            function handlePStopAppointmentConsolidation() {
            var form = document.forms["ApptSchedulingForm"];
            var pStopCount = form.elements["pStopCount"].value * 1;
            var pStopArrayStr = "";
            var count = 0;
            var currentScac = "";
            var apptLocationID = "";
            var apptEntityID = "";
            for(var i = 0; i < pStopCount; i++) {
            var combineAppointment = form.elements["pStop["+i+"].combineAppointment"].checked;
            var pStopID = form.elements["pStop["+i+"].id"].value;
            var pStopScac = form.elements["pStop["+i+"].scac"].value;
            var pStopApptLocID = form.elements["pStop["+i+"].apptLocationID"].value;
            var pStopApptEntityID = +(form.elements["pStop["+i+"].apptEntityID"].value);
            var pStopApptStatus = +(form.elements["pStop["+i+"].appointmentStatus"].value);
            if(combineAppointment) {
            if(currentScac == "") {
            currentScac = pStopScac;
            } else {
            if(currentScac != "" && pStopScac != currentScac) {
            alert("Carriers do not match. The consolidations cannot be appointed together.");
            return;
            }
            }
            if (apptLocationID == "") {
            apptLocationID = pStopApptLocID;
            } else if (apptLocationID != pStopApptLocID) {
            alert("Appointment Locations do not match.  The consolidations cannot be appointed together.");
            return;
            }
            if (pStopApptEntityID != 0 && (pStopApptStatus == 1 || pStopApptStatus == 2)) {
            if (apptEntityID == "") {
            apptEntityID = pStopApptEntityID;
            } else if (apptEntityID != pStopApptEntityID) {
            alert("The selected consolidations cannot be appointed together because there are already multiple appointments among them which are in Requested or Confirmed status.");
            return;
            }
            }
            if(count > 0) {
            pStopArrayStr += ",";
            }
            pStopArrayStr += ""+pStopID;
            count++;
            }
            }
            if(count < 2) {
            alert("Please select two or more groups before attempting to consolidate an appointment");
            return false;
            }
            form.elements["pageaction"].value="appoint";
            form.elements["savedPStopID"].value = pStopArrayStr;
            form.elements["trailerLoadingType"].value = form.elements["consolidationTrailerLoadingType"].value;
            form.elements["selectedShipmentLegIDs"].value = "";
            form.submit();
            }
            function selectAllValues() {
            var form = document.forms["ApptSchedulingForm"];
            var pStopCount = form.elements["pStopCount"].value * 1;
            var combineAppointment = true;
            for(var i = 0; i < pStopCount; i++) {
            combineAppointment = combineAppointment && form.elements["pStop["+i+"].combineAppointment"].checked;
            if(!combineAppointment) {
            break;
            }
            }
            for(var i = 0; i < pStopCount; i++) {
            form.elements["pStop["+i+"].combineAppointment"].checked = !combineAppointment;
            }
            }
            function handlePStop(select) {
            var form = document.forms["ApptSchedulingForm"];
            var action = form.elements[select].value * 1;

            if (action == 1) {
            var valid = true;
            form.elements["pageaction"].value="manageGrouping";
            form.elements["savedPStopID"].value = form.elements[select +".id"].value;

            var apptLocationID = form.elements[select +".apptLocationID"].value;

            var shipmentLegIDs = "";
            try {
            for(var i = 0; i < nonStops.length; i++) {
            var nonStop = nonStops[i];
            var shipmentLength = 0;
            if (nonStop.shipments) {
            shipmentLength = nonStop.shipments.length;
            }
            for(var j = 0; j < shipmentLength; j++) {
            var shipment = nonStop.shipments[j];

            if(form.elements["nonStop["+ i +"].shipment["+ j +"]"].checked) {
            shipmentLegIDs = shipmentLegIDs + shipment.shipmentLegID + ";";

            if(apptLocationID != shipment.appointmentLocationID) {
            alert("Appointment locations do not match.");
            valid = false;
            break;
            }
            }
            }
            }
            } catch(e) {

            }
            if(valid) {
            form.elements["selectedShipmentLegIDs"].value = shipmentLegIDs;
            form.submit();
            }
            }

            else if (action == 2) {
            form.elements["pageaction"].value="appoint";
            form.elements["savedPStopID"].value = form.elements[select +".id"].value;
            form.elements["selectedShipmentLegIDs"].value = "";
            var trailerLoadingType = jQuery('option:selected', form.elements[select]).data("trailer-loading-type");
            form.elements["trailerLoadingType"].value = trailerLoadingType;
            form.submit();
            }

            else if (action == 3) {
            form.elements["pageaction"].value="editApptDetails";
            form.elements["savedPStopID"].value = form.elements[select +".id"].value;
            form.elements["selectedShipmentLegIDs"].value = "";
            form.submit();
            }

            else if (action == 4 || action == 5 || action == 6) {
            var qString = "consolidationID=" + form.elements[select + ".id"].value +
            "&stopType=" + getCheckedValue(form.elements["searchType"]) +
            "&dockApptEntityID=" + form.elements[select + ".apptEntityID"].value;
            if(action == 4) {
            qString += "&voidConsolidationMode=true";
            }
            if(action == 6) {
            qString += "&removeConsolidationFromAppt=true";
            }
            popup('tpcancelappt.do?' + qString, 'Cancel3rdPartyAppointment', 700, 250);
            }
            }
            function selectAll(stop, count) {
            var form = document.forms["ApptSchedulingForm"];
            for (var i = 0; i < count; i++) {
            if (form.elements["nonStop["+ stop +"].shipment["+ i +"]"] && !form.elements["nonStop["+ stop +"].shipment["+ i +"]"].checked) {
            form.elements["nonStop["+ stop +"].shipment["+ i +"]"].checked = true;
            updateText(form.elements["nonStop["+ stop +"].shipment["+ i +"]"],stop,i);
            }
            }
            }
            function clearForm() {
            var form = document.forms["ApptSchedulingForm"];

            var form = document.forms["ApptSchedulingForm"];
            var refNumSize = 0;
            var prefix = undefined;

            if(getCheckedValue(form.elements["searchType"]) == 1000) {
            refNumSize = 6;
            prefix = "pick";
            } else if(getCheckedValue(form.elements["searchType"]) == 1001) {
            refNumSize = 0;
            prefix = "drop";
            }
            if(prefix) {

            if(form.elements[prefix +"MyAppt"]) {
            form.elements[prefix +"MyAppt"].checked = false;
            toggleMyAppt(form.elements[prefix +"MyAppt"],getCheckedValue(form.elements["searchType"]));
            }
            form.elements[prefix +"ConfirmationNums"].value = "";
            form.elements[prefix +"ConfDate"].value = "";
            form.elements[prefix +"ConfGeo"].value = "";
            form.getElementsByClassName("yms-" + prefix)[0].value = "";

            for(var i = 0; i < refNumSize; i++) {
            if (form.elements[prefix +"RefNums["+ i +"].searchString"]) {
            form.elements[prefix +"RefNums["+ i +"].searchString"].value = "";
            }
            }
            form.elements[prefix +"Date"].value = "";
            form.elements[prefix +"Geo"].value = "";
            }
            }
            function toggleMyAppt(field,stopType) {
            var form = document.forms["ApptSchedulingForm"];
            var disabled = false;
            if(field.checked) {
            disabled = true;
            }
            var refNumSize = 0;
            var prefix = undefined;

            if(stopType == 1000) {
            refNumSize = 6;
            prefix = "pick";
            } else if(stopType == 1001) {
            refNumSize = 0;
            prefix = "drop";
            }
            if(prefix) {

            form.elements[prefix +"ConfirmationNums"].disabled = disabled;
            form.elements[prefix +"ConfDate"].disabled = disabled;
            form.elements[prefix +"ConfGeo"].disabled = disabled;
            var yms = form.getElementsByClassName("yms-" + prefix);
            if(yms.length > 0) {
            yms[0].disabled = disabled;
            }

            for(var i = 0; i < refNumSize; i++) {
            if (form.elements[prefix +"RefNums["+ i +"].searchString"]) {
            form.elements[prefix +"RefNums["+ i +"].searchString"].disabled = disabled;
            }
            }
            form.elements[prefix +"Date"].disabled = disabled;
            form.elements[prefix +"Geo"].disabled = disabled;
            }
            }

            function toggleFields() {
            var theField = undefined;

            var form = document.forms["ApptSchedulingForm"];
            if(getCheckedValue(form.elements["searchType"]) == 1000) {
            theField = form.elements["pickMyAppt"];
            } else if(getCheckedValue(form.elements["searchType"]) == 1001) {
            theField = form.elements["dropMyAppt"];
            }

            if(theField) {
            toggleMyAppt(theField,getCheckedValue(form.elements["searchType"]));
            }
            }
            var piecesTotal = 0;
            var palletsTotal = 0;
            var weightTotal = 0;
            var cubeTotal = 0;
            function updateText(field, stop, shipmentCount) {
            var form = document.forms["ApptSchedulingForm"];
            var shipmentPieces = nonStops[stop].shipments[shipmentCount].pieces;
            var shipmentPallets = nonStops[stop].shipments[shipmentCount].pallets;
            var shipmentWeight = nonStops[stop].shipments[shipmentCount].weight;
            var shipmentCube = nonStops[stop].shipments[shipmentCount].cube;
            if(field.checked) {

            piecesTotal += shipmentPieces;
            palletsTotal += shipmentPallets;
            weightTotal += shipmentWeight;
            cubeTotal += shipmentCube;
            } else if(!field.checked) {

            piecesTotal = piecesTotal - shipmentPieces;
            palletsTotal = palletsTotal - shipmentPallets;
            weightTotal = weightTotal - shipmentWeight;
            cubeTotal = cubeTotal - shipmentCube;
            }
            if(piecesTotal == 0) {
            document.getElementById("pieces").innerHTML = "&nbsp;";
            } else {
            document.getElementById("pieces").innerHTML = ll.util.convertNumberToString(piecesTotal, 0, 0, false, true);
            }
            if(palletsTotal == 0) {
            document.getElementById("pallets").innerHTML = "&nbsp;";
            } else {
            document.getElementById("pallets").innerHTML = ll.util.convertNumberToString(palletsTotal, 0, 2, false, true);
            }
            if(weightTotal == 0) {
            document.getElementById("weight").innerHTML = "&nbsp;";
            } else {
            document.getElementById("weight").innerHTML = ll.util.convertNumberToString(weightTotal, 0, 2, false, true);
            }
            if(cubeTotal == 0) {
            document.getElementById("cube").innerHTML = "&nbsp;";
            } else {
            document.getElementById("cube").innerHTML = ll.util.convertNumberToString(cubeTotal, 0, 2, false, true);
            }
            if(cubeTotal == 0 && weightTotal == 0 && palletsTotal == 0 && piecesTotal == 0) {
            document.getElementById("total").innerHTML = "&nbsp;";
            } else {
            document.getElementById("total").innerHTML = "Selected Total";
            }
            }
            onDocumentReady(function() {
            toggleFields();
            });
        </script>
    </head>
    <body>
        <div class="page-message" id="page-message-root">
            <div class="page-message__container">
                <div class="page-message__icon-container"></div>
                <div class="page-message__message-container">
                    <ul class="page-message__messages">
                        <li class="page-message__message page-message__message--primary"></li>
                    </ul>
                    <div class="page-message__button-container" style="display: none;"></div>
                </div>
                <button type="button" class="icon-button page-message__close-button">
                    <span role="tooltip" class="icon-span">
                        <svg class="icon  ic_close" focusable="false">
                            <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </button>
            </div>
        </div>
        <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
        <script>
            (function() {
            var errors = [

            ];
            var success = [

            ];
            onDocumentReady(function() {
            var oldPageMessages = document.querySelectorAll('.old-page-message');
            for(var i = 0; i < oldPageMessages.length; i++) {
            var oldMessage = oldPageMessages[i];
            oldMessage.parentElement.removeChild(oldMessage);
            }
            if(errors.length) {
            PageMessage.error(errors, null, null, false);
            } else if(success.length) {
            PageMessage.success(success, null, null, false);
            }
            });
            })();
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/pages/thirdparty/thirdpartyHeader.css?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2">
        <div class="primary-header">
            <div class="primary-header__system-info">
                <div class="primary-header__logo">
                    <a href="http://www.blujaysolutions.com/" target="_top">
                    <img border="0" alt="e2open v.tm4sprd07-web04-chg:master:2025-05-02_09-26-07" src="/images/logos/e2open_logo.svg" align="bottom"/>
                    </a>
                </div>
                <div class="primary-header__system-title"></div>
                <div class="primary-header__system-environment"></div>
            </div>
            <div class="primary-header__system-message">
                &nbsp;
            </div>
            <div class="primary-header__items">
                <a
                    href="javascript:_webhelp();"
                    class="primary-header__item"
                    title="Help Documentation"
                    >
                    <span role="tooltip" class="icon-span primary-header__item-image">
                        <svg class="icon  ic_help_header" focusable="false">
                            <use xlink:href="#ic_help_header" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a
                    href="javascript:_customerSupport();"
                    class="primary-header__item"
                    title="Customer Support"
                    >
                    <span role="tooltip" class="icon-span primary-header__item-image">
                        <svg class="icon  ic_support_agent" focusable="false">
                            <use xlink:href="#ic_support_agent" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <div class="primary-header__menu" id="thirdPartyUserMenu">
                    <div class="primary-header__menu-anchor">
                        <div title="My Account" class="user-name">
                            <span>CoraMc</span>
                            <span role="tooltip" class="icon-span">
                                <svg class="icon  ic_account_circle" focusable="false">
                                    <use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <ul class="primary-header__menu-popver popover use-show-class" id="thirdPartyUserMenuPopover">
                        <li className="primary-header__menu-popver__menu-group">
                            <div class="defaults-label">Your defaults</div>
                            <table class="user-defaults">
                                <tbody>
                                    <tr>
                                        <td>User:</td>
                                        <td>NFI Transportation</td>
                                    </tr>
                                    <tr>
                                        <td>Account:</td>
                                        <td>
                                            <a href="/thirdparty/selecttpuseracct.do" class="loadreportsubhead">FLAGSTONE FOODS LLC</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Operation :</td>
                                        <td>
                                            Appointment Scheduling
                                        </td>
                                    </tr>
                            </table>
                        </li>
                        <li className="primary-header__menu-popver__menu-group">
                            <a href="javascript:noop()" onclick="popup('/thirdparty/tprequestaccess.do', '_popup', 650, 650);" class="primary-header__menu-popver__menu-link">Request Access</a>
                        </li>
                        <li className="primary-header__menu-popver__menu-group">
                            <a href="javascript:noop()" onclick="popup('/thirdparty/tpadvisorprofile.do', '_popup', 750, 650);" class="primary-header__menu-popver__menu-link">Advisor Management</a>
                        </li>
                        <li class="link">
                            <a href="javascript:populate()">PRIVACY SETTINGS</a>
                        </li>
                        <li className="primary-header__menu-popver__menu-group">
                            <a
                                href="/thirdparty/logouttpuser.do"
                                class="primary-header__menu-popver__menu-link"
                                >
                            Close Window
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div id="cookieDialog" class="display-none-item"></div>
        <hr class="haerder-ruler"/>
        <script>
            function _customerSupport(answerID) {
            if (answerID && answerID != '') {
            popup('/help/customersupport.do?answerID=' + encodeURIComponent(answerID), 'CustomerSupport', 1200, 600);
            } else {
            popup('/help/customersupport.do', 'CustomerSupport', 1200, 600);
            }
            }
            function _webhelp() {
            var error = function() {
            alert(errors_message);
            }
            ll.util.callAjaxAction('/tmsrest/jwt/webhelp', function success(data) {
            ll.util.callAjaxAction(data.url, function success() {
            popup(data.url, 'webhelp', 1200, 600);
            }, error, null, 'POST', data.token, true, 'text/plain', {
            xhrFields: {
            withCredentials: true
            }
            });
            }, error);
            }
            function headerInit() {
            var headerUserItem = document.getElementById('thirdPartyUserMenu');
            var menuUserPopover = new Popover(document.getElementById('thirdPartyUserMenuPopover'), headerUserItem);
            headerUserItem.addEventListener('click', function() {
            menuUserPopover.show();
            });
            }
            if (document.readyState != 'loading'){
            headerInit();
            } else {
            onDocumentReady(headerInit);
            }
            function populate(){
            jQuery('#headerUserPopover').hide();
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            if (response != null) {
            var cookies = [];
            var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
            if(response.cookieGroups.length > 0){
            cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
            response.cookieGroups.forEach((cookie) =>{
            cookies.push(cookie.id);
            cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
            var checked = cookie.value ? "checked" : "";
            var disabled = !cookie.enabled ? "disabled" : "";
            cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
            cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
            cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
            cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
            });
            cookieHtml += "\n</table>\n</section>";
            }
            jQuery("#cookieDialog").html(cookieHtml);
            openCookieDialog(response.title,cookies);
            }
            }, null, null, 'GET', null, false);
            }
            function openCookieDialog(title,cookies){
            var dialogID = '#cookieDialog';
            jQuery(dialogID).dialog({
            autoOpen: true,
            width: 1030,
            height : 500,
            modal: true,
            title : title,
            resizable: false,
            draggable: false,
            buttons: {
            "Close": function() {
            jQuery(dialogID).dialog("close");
            },
            "Save": function() {
            var resCookies = cookies.map((cookie) => {
            return {
            id: cookie,
            enabled : jQuery("#"+cookie).is(":checked")
            };
            })
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
            jQuery(dialogID).dialog("close");
            },
            function(error) {
            PageMessage.error('An error occurred while performing the update.');
            },
            null,
            'POST',
            {
            preferences : resCookies
            },
            false,
            'application/json'
            );
            }
            }
            });
            }
        </script>
        <form name="ApptSchedulingForm" method="post" action="/thirdparty/apptscheduling.do">
            <input type="hidden" name="pageaction" value="unspecified">
            <input type="hidden" name="pickRefNumSize" value="6">
            <input type="hidden" name="dropRefNumSize" value="0">
            <input type="hidden" name="selectedShipmentLegIDs" value="">
            <input type="hidden" name="savedPStopID" value="">
            <input type="hidden" name="trailerLoadingType" value="">
            <div class="page-header">
                <div class="page-header__container">
                    <div class="page-header__main">
                        <div class="page-header__title-container">
                            <div class="page-header__page-title">Appointment Scheduling Shipment Search</div>
                            <div class="page-header__page-title page-header__page-title--to-top">
                                <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                                Return to the top of Appointment Scheduling Shipment Search                 </button>
                            </div>
                        </div>
                        <div class="page-header__options">
                            <a class="icon-link" href="javascript:openMessageEntry(0);">
                                <span role="tooltip" title="Click to send an assistance message" class="icon-span">
                                    <svg class="icon  ic_comment" focusable="false">
                                        <use xlink:href="#ic_comment" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                    </svg>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="page-message" id="page-message-root">
                        <div class="page-message__container">
                            <div class="page-message__icon-container"></div>
                            <div class="page-message__message-container">
                                <ul class="page-message__messages">
                                    <li class="page-message__message page-message__message--primary"></li>
                                </ul>
                                <div class="page-message__button-container" style="display: none;"></div>
                            </div>
                            <button type="button" class="icon-button page-message__close-button">
                                <span role="tooltip" class="icon-span">
                                    <svg class="icon  ic_close" focusable="false">
                                        <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                    <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
                    <script>
                        (function() {
                        var errors = [

                        ];
                        var success = [

                        ];
                        onDocumentReady(function() {
                        var oldPageMessages = document.querySelectorAll('.old-page-message');
                        for(var i = 0; i < oldPageMessages.length; i++) {
                        var oldMessage = oldPageMessages[i];
                        oldMessage.parentElement.removeChild(oldMessage);
                        }
                        if(errors.length) {
                        PageMessage.error(errors, null, null, false);
                        } else if(success.length) {
                        PageMessage.success(success, null, null, false);
                        }
                        });
                        })();
                    </script>
                </div>
            </div>
            <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
            <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=c26dd71ea33c1d206c21efbb6e8ad749bd3551e2"></script>
            <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
            <table cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                    <td colspan="2">
                        <div class="subheader">
                            Appointment Stop Type
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr class="loadreportbody">
                                <td>
                                    &nbsp;&nbsp;
                                    <input type="radio" name="searchType" value="1000" checked="checked">
                                    Pick-up
                                </td>
                                <td>
                                    <button id="clearButton" class="text" type="button" onclick="clearForm();">
                                    Clear
                                    </button>
                                    <button id="searchButton" class="leanbutton search-button search" type="button" onclick="submitForm()">
                                    Search
                                    </button>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <div class="show" id="pickRow">
                <div class="subheader">
                    Search by a Reference Number(s)
                </div>
                <table class="subsection lean_table">
                    <tr class="loadreportbody">
                        <td colspan="2" width="20%">
                            <table class="lean_table" width="100%">
                                <tr class="loadreportbody">
                                    <td class="info-label">
                                        Customer Purchase Order #
                                    </td>
                                    <td>
                                        <input
                                            type="hidden"
                                            name="pickRefNums[0].type"
                                            value="1574"
                                            />
                                        <input
                                            type="text"
                                            name="pickRefNums[0].searchString"
                                            value=""
                                            class="formfieldinputs"
                                            size="50"
                                            />
                                    </td>
                                </tr>
                                <tr class="loadreportbody">
                                    <td class="info-label">
                                        Broker PO #
                                    </td>
                                    <td>
                                        <input
                                            type="hidden"
                                            name="pickRefNums[1].type"
                                            value="2024"
                                            />
                                        <input
                                            type="text"
                                            name="pickRefNums[1].searchString"
                                            value=""
                                            class="formfieldinputs"
                                            size="50"
                                            />
                                    </td>
                                </tr>
                                <tr class="loadreportbody">
                                    <td class="info-label">
                                        Customer PO#
                                    </td>
                                    <td>
                                        <input
                                            type="hidden"
                                            name="pickRefNums[2].type"
                                            value="2026"
                                            />
                                        <input
                                            type="text"
                                            name="pickRefNums[2].searchString"
                                            value=""
                                            class="formfieldinputs"
                                            size="50"
                                            />
                                    </td>
                                </tr>
                                <tr class="loadreportbody">
                                    <td class="info-label">
                                        Delivery #
                                    </td>
                                    <td>
                                        <input
                                            type="hidden"
                                            name="pickRefNums[4].type"
                                            value="2034"
                                            />
                                        <input
                                            type="text"
                                            name="pickRefNums[4].searchString"
                                            value=""
                                            class="formfieldinputs"
                                            size="50"
                                            />
                                    </td>
                                </tr>
                                <tr class="loadreportbody">
                                    <td class="info-label">
                                        Sales Order #
                                    </td>
                                    <td>
                                        <input
                                            type="hidden"
                                            name="pickRefNums[5].type"
                                            value="2035"
                                            />
                                        <input
                                            type="text"
                                            name="pickRefNums[5].searchString"
                                            value=""
                                            class="formfieldinputs"
                                            size="50"
                                            />
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td></td>
                        <td colspan="2">
                            <input type="hidden" name="pickDate" value="" />
                        </td>
                        <td>
                            <table class="geo-inputs">
                                <tr>
                                    <td class="info-label">
                                        Geography
                                    </td>
                                    <td>
                                        <input type="text" name="pickGeo" value="" class="formfieldinputs">&nbsp;*
                                    </td>
                                    <td>
                                        <a href="javascript:searchForLocations('1000',false)" class="formfieldinputs">
                                        CITY, ST/PRV
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <div class="subheader">
                    Search by Appointment(s)
                </div>
                <table class="subsection lean_table">
                    <tr class="loadreportbody">
                        <td class="info-label">
                            Confirmation #
                        </td>
                        <td>
                            <input type="text" name="pickConfirmationNums" size="50" value="" class="formfieldinputs">
                        </td>
                        <td ></td>
                        <td colspan="2">
                            <input type="hidden" name="pickConfDate" value="" />&nbsp;
                        </td>
                        <td>
                            <table class="geo-inputs">
                                <tr>
                                    <td class="info-label">
                                        Geography
                                    </td>
                                    <td>
                                        <input type="text" name="pickConfGeo" value="" class="formfieldinputs">&nbsp;*
                                    </td>
                                    <td>
                                        <a href="javascript:searchForLocations('1000',true)" class="formfieldinputs">
                                        CITY, ST/PRV
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr class="loadreportbody">
                        <td class="info-label">
                            My Appointments
                        </td>
                        <td>
                            &nbsp;&nbsp;&nbsp;
                            <input
                                type="checkbox"
                                name="pickMyAppt"
                                onclick="toggleMyAppt(this,1000);"
                                />
                            <div id="tmsComponent-IconWithTooltipTag--1-6b3a16dc-89a5-487d-9815-320b71e79fb4" class="js-tms-component-root  icon-with-tooltip--container"></div>
                            <script type="text/javascript">
                                (function() {
                                var props = {"name":"information","id":"test-blueberry-my-appt-note-info-pickup","dangerHTML":false};
                                var children = [{"children":"Appointments you have previously appointed in the last 14 days.","componentName":"Fragment"}];
                                TMSComponents.init(document.getElementById('tmsComponent-IconWithTooltipTag--1-6b3a16dc-89a5-487d-9815-320b71e79fb4'), TMSComponents['IconWithTooltip'], props, children);
                                })();
                            </script>
                        </td>
                        <td colspan="4"></td>
                    </tr>
                </table>
            </div>
            <br />
            <div id="results">
            </div>
        </form>
    </body>
</html>