<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="TPShipperAcctModule" class="transactional-page">
   <head>
      <title>3rd Party - Select An Operation</title>
      <meta name="msapplication-config" content="/browserconfig.xml" />
      <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
      <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
      <script type="text/javascript">
         var faviconICO = document.querySelector('#faviconICO');
         var faviconPNG = document.querySelector('#faviconPNG');
         var darkModeListener = function(event) {
         if (event.matches) {
         faviconICO.setAttribute("href","/favicon_dark.ico");
         faviconPNG.setAttribute("href","/favicon_dark.png");
         } else {
         faviconICO.setAttribute("href","/favicon.ico");
         faviconPNG.setAttribute("href","/favicon.png");
         }
         };
         var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
         if(darkModePreference.addEventListener){
         darkModePreference.addEventListener('change', function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         } else {
         darkModePreference.addListener(function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         }
         darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
      </script>
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/fallback.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
      <script type="text/javascript">
         function popupMaestro() {




         }
         onDocumentReady(function() {
         var iconContainer = document.createElement('div');
         jQuery(document.body).prepend(iconContainer);
         jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
         popupMaestro();
         });
      </script>
      <script type="text/javascript">
         var useComponents = false;
         var newStylesComponents = false;
         var useETOComponents = false;
         var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
      </script>
      <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript">
         ll.util.initFormats(1, 1,
         'en_US');
         var shortLang = 'en';
         var browserCacheKey = '831bc4a1\-5525\-4ac5\-ab0a\-82e8dd2ff0d0';
         ll.lang = ll.lang || {};
         ll.lang.locale = 'en_US';
      </script>
      <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/Modal.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/Popover.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript" src="/c/js/components/PanelTransition.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
      <script type="text/javascript">
         jQuery.noConflict();
         var LeanActionFormName = '';
         var LeanActionFullPath = '';
         var $LeanActionForm;
         onDocumentReady(function() {



         $LeanActionForm = jQuery('form');


         if (get_browser() === "msie") {
         jQuery('html').addClass("ie");
         }
         if ( 'noValidate' in document.createElement('form') ){
         jQuery('html').addClass("novalidate");
         }
         var $dwrImageDiv = jQuery('#dwrImageDiv');
         if (!$dwrImageDiv.length) {
         $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
         $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
         jQuery('body').append($dwrImageDiv);
         }


         hasExpiredSessionWarningBeenDisplayed = false;
         });
      </script>
      <style>
         .list td.selection-column {
         width: 1%;
         text-align: center;
         }
      </style>
      <script type="text/javascript">
         function validateForm(form) {
         form.submit();
         }
      </script>
   </head>
   <body >
      <form action="/thirdparty/selecttpusermodule.do" method="post" onsubmit="return validateForm(this);">
         <input type="hidden" name="pageaction" value="selectModule">
         <div class="page-header">
            <div class="page-header__container">
               <div class="page-header__main">
                  <div class="page-header__title-container">
                     <div class="page-header__page-title">Select an Operation</div>
                     <div class="page-header__page-title page-header__page-title--to-top">
                        <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                        Return to the top of Select an Operation                 </button>
                     </div>
                  </div>
               </div>
               <div class="page-message" id="page-message-root">
                  <div class="page-message__container">
                     <div class="page-message__icon-container"></div>
                     <div class="page-message__message-container">
                        <ul class="page-message__messages">
                           <li class="page-message__message page-message__message--primary"></li>
                        </ul>
                        <div class="page-message__button-container" style="display: none;"></div>
                     </div>
                     <button type="button" class="icon-button page-message__close-button">
                        <span role="tooltip" class="icon-span">
                           <svg class="icon  ic_close" focusable="false">
                              <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                           </svg>
                        </span>
                     </button>
                  </div>
               </div>
               <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
               <script>
                  (function() {
                  var errors = [

                  ];
                  var success = [

                  ];
                  onDocumentReady(function() {
                  var oldPageMessages = document.querySelectorAll('.old-page-message');
                  for(var i = 0; i < oldPageMessages.length; i++) {
                  var oldMessage = oldPageMessages[i];
                  oldMessage.parentElement.removeChild(oldMessage);
                  }
                  if(errors.length) {
                  PageMessage.error(errors, null, null, false);
                  } else if(success.length) {
                  PageMessage.success(success, null, null, false);
                  }
                  });
                  })();
               </script>
            </div>
         </div>
         <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
         <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
         <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
         <table class="list">
            <thead>
               <tr>
                  <th class="selection-column"> Select </th>
                  <th> Type </th>
                  <th> Operation </th>
               </tr>
            </thead>
            <tbody>
               <tr class="resultrow1" >
                  <td class="selection-column">
                     <input type="radio" id="tpDefModuleleID_0" name="tpDefModuleleID" value="145376;3323;3" onclick="validateForm(this.form)"/>
                  </td>
                  <td>
                     <label for="tpDefModuleleID_0">
                     Appointment Scheduling
                     </label>
                  </td>
                  <td><label for="tpDefModuleleID_0">WINLAND FOODS 3RD PARTY APPT SCHED</label></td>
               </tr>
               <tr class="resultrow2" >
                  <td class="selection-column">
                     <input type="radio" id="tpDefModuleleID_1" name="tpDefModuleleID" value="145377;3324;3" onclick="validateForm(this.form)"/>
                  </td>
                  <td>
                     <label for="tpDefModuleleID_1">
                     Appointment Scheduling
                     </label>
                  </td>
                  <td><label for="tpDefModuleleID_1">WINLAND FOODS 3RD PARTY APPT SCHED - INT</label></td>
               </tr>
            </tbody>
         </table>
      </form>
   </body>
</html>