<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="TPScheduleAppt" class="transactional-page">
   <head>
      <title>Schedule Appointment</title>
      <meta name="msapplication-config" content="/browserconfig.xml" />
      <script type="text/javascript" src="/c/js/utility.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
      <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
      <script type="text/javascript">
         var faviconICO = document.querySelector('#faviconICO');
         var faviconPNG = document.querySelector('#faviconPNG');
         var darkModeListener = function(event) {
         if (event.matches) {
         faviconICO.setAttribute("href","/favicon_dark.ico");
         faviconPNG.setAttribute("href","/favicon_dark.png");
         } else {
         faviconICO.setAttribute("href","/favicon.ico");
         faviconPNG.setAttribute("href","/favicon.png");
         }
         };
         var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
         if(darkModePreference.addEventListener){
         darkModePreference.addEventListener('change', function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         } else {
         darkModePreference.addListener(function(e) {
         if (e.matches) {
         activateDarkMode();
         }
         });
         }
         darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
      </script>
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <link rel="stylesheet" type="text/css" href="/c/css/fallback.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <script type="text/javascript">
         function popupMaestro() {

         }
         onDocumentReady(function() {
         var iconContainer = document.createElement('div');
         jQuery(document.body).prepend(iconContainer);
         jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3');
         popupMaestro();
         });
      </script>
      <script type="text/javascript">
         var useComponents = true;
         var newStylesComponents = false;
         var useETOComponents = true;
         var cacheKey = "e7888b8ab25b8fc06bc94652a2a3cdc7410684f3";
      </script>
      <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript">
         ll.util.initFormats(1, 1,
         'en_US');
         var shortLang = 'en';
         var browserCacheKey = '04773c46\-4183\-4ea4\-9942\-5b0d7305e004';
         ll.lang = ll.lang || {};
         ll.lang.locale = 'en_US';
      </script>
      <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script type="text/javascript">
         jQuery.noConflict();
         var LeanActionFormName = 'TPScheduleApptForm';
         var LeanActionFullPath = '\/thirdparty\/tpscheduleappt.do';
         var $LeanActionForm;
         onDocumentReady(function() {


         $LeanActionForm = jQuery('form[name=TPScheduleApptForm]');



         if (get_browser() === "msie") {
         jQuery('html').addClass("ie");
         }
         if ( 'noValidate' in document.createElement('form') ){
         jQuery('html').addClass("novalidate");
         }
         var $dwrImageDiv = jQuery('#dwrImageDiv');
         if (!$dwrImageDiv.length) {
         $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
         $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
         jQuery('body').append($dwrImageDiv);
         }


         hasExpiredSessionWarningBeenDisplayed = false;
         });
      </script>
      <script type="text/javascript">
         var dockStartTimes = {"6864":[{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"08:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"09:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"10:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"11:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"12:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"13:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"14:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":4102376400000,"startTime":"15:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646888400000,"expirationDate":1646888400000,"startTime":"16:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false}],"8649":[{"effectiveDate":1731906000000,"expirationDate":4102376400000,"startTime":"23:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false}],"8650":[{"effectiveDate":1731906000000,"expirationDate":4102376400000,"startTime":"12:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false}],"6909":[{"effectiveDate":1646715600000,"expirationDate":4102376400000,"startTime":"23:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1646715600000,"expirationDate":4102376400000,"startTime":"23:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false},{"effectiveDate":1667970000000,"expirationDate":4102376400000,"startTime":"14:00","sunday":false,"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":false}]};
         function updateStartTimes() {
         var selectedDock = jQuery("select[name=shipperOverRideDockID]").val();
         jQuery("select[name=shipperOverRideAffectedStartTime]").empty();
         if(dockStartTimes[selectedDock] && jQuery("input[name=shipperOverRideAffectDockUsage]:checked").val() == "true") {
         for(var i = 0; i < dockStartTimes[selectedDock].length; i++) {
         var time = dockStartTimes[selectedDock][i];
         var d = new Date(jQuery("input[name=shipperOverRideApptDate]").val());
         if(d.getTime() >= time.effectiveDate && d.getTime() <= time.expirationDate &&
         (d.getDay() == 0 && time.sunday || d.getDay() == 1 && time.monday || d.getDay() == 2 && time.tuesday ||
         d.getDay() == 3 && time.wednesday || d.getDay() == 4 && time.thursday || d.getDay() == 5 && time.friday ||
         d.getDay() == 6 && time.saturday)) {
         jQuery("select[name=shipperOverRideAffectedStartTime]").addOption(time.startTime, time.startTime);
         }
         }
         }
         if(jQuery("select[name=shipperOverRideAffectedStartTime]").children().length > 0) {
         jQuery("select[name=shipperOverRideAffectedStartTime]").show();
         } else {
         jQuery("select[name=shipperOverRideAffectedStartTime]").hide();
         }
         }

         function cancelRequest() {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["pageaction"].value = "search";
         form.submit();
         }
         function previousSearch() {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["pageaction"].value = "search";
         form.submit();
         }
         function newSearch() {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["pageaction"].value = "newSearch";
         form.submit();
         }
         function validateAndSubmit() {
         var form = document.forms["TPScheduleApptForm"];
         if (validateForm(form)) {
         form.submit();
         }
         }
         function prepCarrierReference() {
         var form = document.forms["TPScheduleApptForm"];
         var ref = form.elements["scac"];
         if(!isEmpty(ref.value)) {
         var quoteless = stripQuotes(ref.value);
         if(quoteless.length != ref.value.length) {
         ref.value = "\"" + quoteless.trim() + "\"";
         }
         }
         }
         function validateCarrierReference(ref) {
         var valid = false;
         if(validateQuotedRefNum(ref)) {
         var value = stripQuotes(ref);
         valid = true;
         if(value.length < 4) {
         alert("Carrier Reference length cannot be less than 4");
         valid = false;
         }
         if(value.length > 4) {
         alert("Carrier Reference length cannot be greater than 4");
         valid = false;
         }
         }
         return valid;
         }
         function validateForm(form) {




         if(!validateApptDetails(form)) {
         return false;
         }
         var selected = false;
         var startSlotElement = form.elements['startSlotNum'];
         if (startSlotElement != null) {
         if (startSlotElement.length) {
         var count = startSlotElement.length;
         for (var i = 0; i < count; i++) {
         if (startSlotElement[i].checked) {
         selected = true;
         break;
         }
         }
         } else {
         if (startSlotElement.checked) {
         selected = true;
         }
         }
         }
         if (form.elements["shipperOverRideApptDate"] && !isEmpty(form.elements["shipperOverRideApptDate"].value)) {
         if (form.elements['isForceAppt'].value == 'true') {
         return forceAppt();
         }
         if (selected && confirm("Since you have entered a date under Option 2, the slot time selected under Option 1 will not be considered.\nDo you want to proceed?")) {
         form.elements['isForceAppt'].value = "true";
         }
         if (!forceAppt()) {
         return false;
         }
         } else if (!selected) {
         alert("Please select a time slot to proceed.");
         return false;
         }

         var shipmentSize = form.elements["shipmentSize"].value;
         for(var i = 0; i < shipmentSize; i++) {
         if(!isInteger(form.elements["shipments["+ i +"].loadingOrder"].value)) {
         alert("Sequence must be a number.");
         form.elements["shipments["+ i +"].loadingOrder"].focus();
         return false;
         }
         }
         if (form.elements['submitbut'] != null) {
         form.elements['submitbut'].disabled = true;
         }
         if (form.elements['nextdatbut'] != null) {
         form.elements['nextdatbut'].disabled = true;
         }
         if (form.elements['forcebut'] != null) {
         form.elements['forcebut'].disabled = true;
         }
         if (form.elements['cancelbut'] != null) {
         form.elements['cancelbut'].disabled = true;
         }
         return true;


         }
         function forceAppt() {
         var form = document.forms["TPScheduleApptForm"];
         var shipperOverRideApptDate = form.elements["shipperOverRideApptDate"];
         var shipperOverRideApptTime = form.elements["-$TIME$shipperOverRideApptDate"];
         if (shipperOverRideApptDate != null) {
         if (!ll.util.checkDate(shipperOverRideApptDate, false)) {
         return false;
         }
         }
         if (!checkMilitaryTime(shipperOverRideApptTime, false)) {
         return false;
         }
         if (!ll.util.isPositiveInteger(form.elements["shipperOverRideDockTime"].value, false)) {
         alert("Load\/Unloading time must be a valid value greater than zero");
         return false;
         }
         if (ll.util.convertStringToNumber(form.elements["shipperOverRideDockTime"].value) > 1440) {
         alert("Load\/Unloading time exceeds 24 hrs. Please Check.");
         return false;
         }
         if ((form.shipperOverRideDockID.value * 1) <= 0) {
         alert("You must select a dock where appointment be made.");
         form.shipperOverRideDockID.focus();
         return false;
         }
         var comment = form.elements["comment"];
         var commentValue = comment && ((comment.component && comment.component.value) || comment.value);
         var reasonCode = !!form.elements['reasonCodeSelection'] ? form.elements['reasonCodeSelection'].value : null;
         if (isRequiredComments(reasonCode)) {
         var divobj = document.getElementById("reasonCheckLabel");
         if (divobj) {
         if (divobj.style.visibility=="visible" && isEmpty(commentValue)) {
         alert("You must enter a comment");
         comment.focus();
         return false;
         }
         }
         else if (isEmpty(commentValue)) {
         alert("You must enter a comment");
         comment.focus();
         return false;
         }
         }
         if (comment) {
         if (commentValue.length > 500) {
         alert("Comment must not exceed 500 characters.");
         comment.focus();
         return false;
         }
         }
         form.elements["pageaction"].value = 'forceAppt';
         return true;
         }
         function validateApptDetails(form) {
         var comment = form.elements["comment"];
         var commentValue = comment && ((comment.component && comment.component.value) || comment.value);

         var divobj = document.getElementById("reasonCheckLabel");
         if(divobj) {
         if (divobj.style.visibility=="visible" && isEmpty(commentValue)) {
         alert("You must enter a comment");
         comment.focus();
         return false;
         }
         }
         else if(isEmpty(commentValue)){
         alert("You must enter a comment");
         comment.focus();
         return false;
         }

         if (comment) {
         if (commentValue.length > 500) {
         alert("Comment must not exceed 500 characters.");
         comment.focus();
         return false;
         }
         }
         if (form.elements["trailerNumber"]) {
         var trailerNumber = form.elements["trailerNumber"];
         var emptyOk = true;

         if (!validateQuotedRefNumField(trailerNumber, emptyOk)) {
         trailerNumber.focus();
         return false;
         }
         }
         if (form.elements["driverName"]) {
         var driverName = form.elements["driverName"];
         var emptyOk = true;

         if (!isString(driverName.value, emptyOk)) {
         alert("Driver Required");
         driverName.focus();
         return false;
         }
         }
         if (form.elements["vehicleNumber"]) {
         var vehicleNumber = form.elements["vehicleNumber"];
         var emptyOk = true;

         if (!validateQuotedRefNumField(vehicleNumber, emptyOk)) {
         vehicleNumber.focus();
         return false;
         }
         }



         var consolRef = form.elements["consolRef"];
         var emptyOk = true;

         if (!isString(consolRef.value, emptyOk)) {
         alert("Please enter a valid Consolidation Reference");
         consolRef.focus();
         return false;
         }

         form.elements["pageaction"].value = "updateAppt";
         return true;
         }
         function getNextDay() {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["pageaction"].value = "nextDate";
         if (form.submitbut != null) {
         form.submitbut.disabled = true;
         }
         form.submit();
         }
         function getPrevDay() {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["pageaction"].value = "prevDate";
         if (form.submitbut != null) {
         form.submitbut.disabled = true;
         }
         form.submit();
         }
         function getNextDateAppts() {
         var form = document.forms["TPScheduleApptForm"];
         var nextApptDate = form.elements["nextApptDate"];

         if (nextApptDate != null) {
         if (!ll.util.checkDate(nextApptDate, true)) {
         return false;
         }
         }
         var jsDate1 = ll.util.getJSDateMilitaryTime(nextApptDate.value);
         var today = new Date();
         today = new Date(today.getFullYear(), today.getMonth(), today.getDate());
         if (jsDate1.getTime() < today.getTime()) {
         alert("Appointment Date is in the past");
         return false;
         }
         form.elements["apptDate"].value = nextApptDate.value;
         if (form.elements["submitbut"] != null) {
         form.elements["submitbut"].disabled = true;
         }
         if (form.elements["nextdatbut"] != null) {
         form.elements["nextdatbut"].disabled = true;
         }
         if (form.elements["forcebut"] != null) {
         form.elements["forcebut"].disabled = true;
         }
         form.elements["pageaction"].value = 'nextApptDate';
         form.submit();
         }
         function openMessageEntry(apptlocid) {
         popup("/thirdparty/assistmsgentry.do?apptLocID="+apptlocid, 'assistmsgentry', 800, 500);
         }
         if (document.layers) {
         document.captureEvents(Event.KEYPRESS);
         }
         document.onkeypress = dealWithEnterKeypress;

         function dealWithEnterKeypress(e) {
         var evt = (e) ? e : ((window.event) ? window.event : null);
         var key = (evt.keyCode) ? evt.keyCode : evt.charCode;
         if (key == 13) {
         var eventSrcName = (evt.srcElement) ? evt.srcElement.name : evt.target.name;
         if (eventSrcName == "scac" || eventSrcName == "consolRef") {
         validateAndSubmit();
         } else if (eventSrcName == "nextApptDate") {
         getNextDateAppts();
         }
         }
         }
         function manageCommentLabel() {
         var form = document.forms["TPScheduleApptForm"];
         var divobj = document.getElementById("reasonCheckLabel");
         if(divobj) {

         if(form.elements["selectedReasonCode"].value==0) {
         divobj.style.visibility = "hidden";
         }
         }
         }
         onDocumentReady(function() {
         updateStartTimes();
         manageCommentLabel();
         jQuery('input[name=shipperOverRideForceOverbook]').prop('disabled', false);
         var form = document.forms["TPScheduleApptForm"];
         if(form.elements["driverName"]) {
         jQuery('#driverName').ll_autocomplete( { source: getDriverInfo, selectItem:driverSelected, displayItem:driverNameSelector, minLength:2, choices:15 });
         }
         if(form.elements["vehicleNumber"]) {
         jQuery('#vehicleNumber').ll_autocomplete( { source: getVehicleInfo, selectItem:vehicleSelected, displayItem:vehicleNumberSelector, minLength:2, choices:15 });
         }
         });
         function getDriverInfo(request, response) {
         var url = '/tmsrest/autocomplete/carrier_drivers?q=' + encodeURIComponent(request.term) + '&carrierRef=' + encodeURIComponent(document.forms["TPScheduleApptForm"].elements["scac"].value);
         ll.util.callAjaxAction(url, response);
         }
         function driverSelected(inputElement, selectedElement, driverObject) {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["driverName"].value = driverObject.driverName;
         if(driverObject.defaultVehicleNumber != null) {
         form.elements["vehicleNumber"].value = driverObject.defaultVehicleNumber;
         }
         }
         function driverNameSelector(driverObject) {
         return driverObject.driverName;
         }
         function getVehicleInfo(request, response) {
         var carrierRef = document.forms["TPScheduleApptForm"].elements["scac"].value;
         var url = '/tmsrest/autocomplete/carrier_vehicles?q=' + encodeURIComponent(request.term) + '&carrierRef=' + encodeURIComponent(document.forms["TPScheduleApptForm"].elements["scac"].value);
         ll.util.callAjaxAction(url, response);
         }
         function vehicleSelected(inputElement, selectedElement, vehicleNumber) {
         var form = document.forms["TPScheduleApptForm"];
         form.elements["vehicleNumber"].value = vehicleNumber;
         }
         function vehicleNumberSelector(vehicleNumber) {
         return vehicleNumber;
         }
         function isRequiredComments(reasonCodeValue) {
         if (!!reasonCodeValue) {

         }
         return false;
         }
      </script>
      <style>
         .align-center {
         text-align: center
         }
         .previous-day-cell {
         width: 50%;
         text-align: right;
         vertical-align: middle;
         padding-right: 20px;
         }
         .standing-appts-day-cell {
         width: 1%; /* This is here to make the cell as small as the words to help center the text */
         text-align: center;
         vertical-align: middle;
         white-space: nowrap;
         }
         .next-day-cell {
         width: 50%;
         text-align: left;
         vertical-align: middle;
         padding-left: 20px;
         }
         .list {
         margin: 10px;
         }
         .subsection {
         margin: 10px;
         width: calc(100% - 20px);
         }
         .option-header {
         width: calc(100% - 20px);
         font-weight: 700;
         margin: 20px 0 0 10px;
         }
         .schedule-appt-action-bar{
         margin: 10px;
         display: flex;
         justify-content: center;
         }
         .attributes .formfieldinputs {
         min-width: 200px;
         }
         #test-schedule-appt-comments {
         min-width: 400px;
         }
      </style>
   </head>
   <body>
      <div class="page-message" id="page-message-root">
         <div class="page-message__container">
            <div class="page-message__icon-container"></div>
            <div class="page-message__message-container">
               <ul class="page-message__messages">
                  <li class="page-message__message page-message__message--primary"></li>
               </ul>
               <div class="page-message__button-container" style="display: none;"></div>
            </div>
            <button type="button" class="icon-button page-message__close-button">
               <span role="tooltip" class="icon-span">
                  <svg class="icon  ic_close" focusable="false">
                     <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                  </svg>
               </span>
            </button>
         </div>
      </div>
      <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
      <script>
         (function() {
         var errors = [

         ];
         var success = [

         "Appointment Confirmed",

         ];
         onDocumentReady(function() {
         var oldPageMessages = document.querySelectorAll('.old-page-message');
         for(var i = 0; i < oldPageMessages.length; i++) {
         var oldMessage = oldPageMessages[i];
         oldMessage.parentElement.removeChild(oldMessage);
         }
         if(errors.length) {
         PageMessage.error(errors, null, null, false);
         } else if(success.length) {
         PageMessage.success(success, null, null, false);
         }
         });
         })();
      </script>
      <link rel="stylesheet" type="text/css" href="/c/css/newstyles/pages/thirdparty/thirdpartyHeader.css?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3">
      <div class="primary-header">
         <div class="primary-header__system-info">
            <div class="primary-header__logo">
               <a href="http://www.blujaysolutions.com/" target="_top">
               <img border="0" alt="e2open v.tm4sprd07-web02-chg:master:2025-07-09_12-40-19" src="/images/logos/e2open_logo.svg" align="bottom"/>
               </a>
            </div>
            <div class="primary-header__system-title"></div>
            <div class="primary-header__system-environment"></div>
         </div>
         <div class="primary-header__system-message">
            &nbsp;
         </div>
         <div class="primary-header__items">
            <a
               href="javascript:_webhelp();"
               class="primary-header__item"
               title="Help Documentation"
               >
               <span role="tooltip" class="icon-span primary-header__item-image">
                  <svg class="icon  ic_help_header" focusable="false">
                     <use xlink:href="#ic_help_header" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                  </svg>
               </span>
            </a>
            <a
               href="javascript:_customerSupport();"
               class="primary-header__item"
               title="Customer Support"
               >
               <span role="tooltip" class="icon-span primary-header__item-image">
                  <svg class="icon  ic_support_agent" focusable="false">
                     <use xlink:href="#ic_support_agent" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                  </svg>
               </span>
            </a>
            <div class="primary-header__menu" id="thirdPartyUserMenu">
               <div class="primary-header__menu-anchor">
                  <div title="My Account" class="user-name">
                     <span>CoraMc</span>
                     <span role="tooltip" class="icon-span">
                        <svg class="icon  ic_account_circle" focusable="false">
                           <use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                     </span>
                  </div>
               </div>
               <ul class="primary-header__menu-popver popover use-show-class" id="thirdPartyUserMenuPopover">
                  <li className="primary-header__menu-popver__menu-group">
                     <div class="defaults-label">Your defaults</div>
                     <table class="user-defaults">
                        <tbody>
                           <tr>
                              <td>User:</td>
                              <td>NFI Transportation</td>
                           </tr>
                           <tr>
                              <td>Account:</td>
                              <td>
                                 <a href="/thirdparty/selecttpuseracct.do" class="loadreportsubhead">DART CONTAINER</a>
                              </td>
                           </tr>
                           <tr>
                              <td>Operation :</td>
                              <td>
                                 Appointment Scheduling
                              </td>
                           </tr>
                     </table>
                  </li>
                  <li className="primary-header__menu-popver__menu-group">
                     <a href="javascript:noop()" onclick="popup('/thirdparty/tprequestaccess.do', '_popup', 650, 650);" class="primary-header__menu-popver__menu-link">Request Access</a>
                  </li>
                  <li className="primary-header__menu-popver__menu-group">
                     <a href="javascript:noop()" onclick="popup('/thirdparty/tpadvisorprofile.do', '_popup', 750, 650);" class="primary-header__menu-popver__menu-link">Advisor Management</a>
                  </li>
                  <li class="link">
                     <a href="javascript:populate()">PRIVACY SETTINGS</a>
                  </li>
                  <li className="primary-header__menu-popver__menu-group">
                     <a
                        href="/thirdparty/logouttpuser.do"
                        class="primary-header__menu-popver__menu-link"
                        >
                     Close Window
                     </a>
                  </li>
               </ul>
            </div>
         </div>
      </div>
      <div id="cookieDialog" class="display-none-item"></div>
      <hr class="haerder-ruler"/>
      <script>
         function _customerSupport(answerID) {
         if (answerID && answerID != '') {
         popup('/help/customersupport.do?answerID=' + encodeURIComponent(answerID), 'CustomerSupport', 1200, 600);
         } else {
         popup('/help/customersupport.do', 'CustomerSupport', 1200, 600);
         }
         }
         function _webhelp() {
         var error = function() {
         alert(errors_message);
         }
         ll.util.callAjaxAction('/tmsrest/jwt/webhelp', function success(data) {
         ll.util.callAjaxAction(data.url, function success() {
         popup(data.url, 'webhelp', 1200, 600);
         }, error, null, 'POST', data.token, true, 'text/plain', {
         xhrFields: {
         withCredentials: true
         }
         });
         }, error);
         }
         function headerInit() {
         var headerUserItem = document.getElementById('thirdPartyUserMenu');
         var menuUserPopover = new Popover(document.getElementById('thirdPartyUserMenuPopover'), headerUserItem);
         headerUserItem.addEventListener('click', function() {
         menuUserPopover.show();
         });
         }
         if (document.readyState != 'loading'){
         headerInit();
         } else {
         onDocumentReady(headerInit);
         }
         function populate(){
         jQuery('#headerUserPopover').hide();
         ll.util.callAjaxAction('/pages/cookieconsent',
         function(response) {
         if (response != null) {
         var cookies = [];
         var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
         if(response.cookieGroups.length > 0){
         cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
         response.cookieGroups.forEach((cookie) =>{
         cookies.push(cookie.id);
         cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
         var checked = cookie.value ? "checked" : "";
         var disabled = !cookie.enabled ? "disabled" : "";
         cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
         cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
         cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
         cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
         });
         cookieHtml += "\n</table>\n</section>";
         }
         jQuery("#cookieDialog").html(cookieHtml);
         openCookieDialog(response.title,cookies);
         }
         }, null, null, 'GET', null, false);
         }
         function openCookieDialog(title,cookies){
         var dialogID = '#cookieDialog';
         jQuery(dialogID).dialog({
         autoOpen: true,
         width: 1030,
         height : 500,
         modal: true,
         title : title,
         resizable: false,
         draggable: false,
         buttons: {
         "Close": function() {
         jQuery(dialogID).dialog("close");
         },
         "Save": function() {
         var resCookies = cookies.map((cookie) => {
         return {
         id: cookie,
         enabled : jQuery("#"+cookie).is(":checked")
         };
         })
         ll.util.callAjaxAction('/pages/cookieconsent',
         function(response) {
         PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
         jQuery(dialogID).dialog("close");
         },
         function(error) {
         PageMessage.error('An error occurred while performing the update.');
         },
         null,
         'POST',
         {
         preferences : resCookies
         },
         false,
         'application/json'
         );
         }
         }
         });
         }
      </script>
      <div class="pagestatussuccess old-page-message">
         <ul>
            <li>Appointment Confirmed</li>
         </ul>
      </div>
      <form name="TPScheduleApptForm" method="POST" action="/thirdparty/tpscheduleappt.do" onsubmit="return validateForm(this)">
         <input type="hidden" name="reasonCodeDataObj" value="U3sZ5PaurJxcxFh3JOQRpu8FZTyylK-Yfw9dcp-obVNbYuq-CbPI1KgiKZU6lFMSKoV5jPsbgGjMvSZ6v92KCp1w9RYdZnTj1VbjOgtnA8Tf1EOfiSOyH-vIcRvROlh9jpf6vB0lolRUe-CpiXla7EYEWGcZ4lEaRvtah5pJj9i9mCCoYXlaaV9jQlQwxmIiAFyvNzbxFz1YlHA0jlK_qpLv-jmqNrR6Kyt40W90OsUag1vGQYV38KaSiFdRuybcJ5IT2Co_cJ8mMeBV02LBcWFQ3a8PK_ur7gZjwQJIvrKL0FYxe40bWWim0TMy6IbK4f-F5kJ6KDrt8qIAZCyWx4ytAAB9WlBLAwQUAAgICADUTPZaAAAAAAAAAAAAAAAABwAAAGxlYW5vYmpNTstOw0AMdCuhHpB4VEjwFckHcEKtOG0FanvqzcqaYLTZ3dreihNfxK_xDySkUpiTx54Zz_cPXKjAU5O6KhDGkFpW40YrfeecObZqaKWnPlXaCFGstoSa4ip5esWW1mi4W7_AiNkcFgd48PSGJdg-PbOoTYYDXMkf2dKxsJB3cHvWTiKDpfvAE9YBY1vvTPoWjw4uOapJaYxTPMIXzBzc_1vtBaMGHEY1uB4TinGoN5h7-42cX65S11E0HTLmDu605JzEyE8NxttnBsgDfgFQSwcI51TgOM8AAAAlAQAAUEsBAhQAFAAICAgA1Ez2WudU4DjPAAAAJQEAAAcAAAAAAAAAAAAAAAAAAAAAAGxlYW5vYmpQSwUGAAAAAAEAAQA1AAAABAEAAAAA">
         <input type="hidden" name="reasonCodeForDisplay" value="">
         <input type="hidden" name="useTransShipReasonCodes" value="true">
         <input type="hidden" name="pageaction" value="updateAppt">
         <input type="hidden" name="shipperID" value="37706">
         <input type="hidden" name="dwellTime" value="120">
         <input type="hidden" name="apptDate" value="07/25/2025" />
         <input type="hidden" name="usageType" value="0">
         <input type="hidden" name="searchType" value="1000">
         <input type="hidden" name="dueDate" value="">
         <input type="hidden" name="refValue" value="">
         <input type="hidden" name="referenceNbrs" value="">
         <input type="hidden" name="currApptInfo" value="">
         <input type="hidden" name="tempClassID" value="0">
         <input type="hidden" name="apptLocGenericMsg" value="">
         <input type="hidden" name="apptLocNoApptsAvailMsg" value="">
         <input type="hidden" name="pseudoStopIDs[0]" value="13673152" />
         <input type="hidden" name="numStops" value="1" />
         <input type="hidden" name="selectedShipmentLegIDs" value="451391981;">
         <input type="hidden" name="pickRefNumSize" value="6">
         <input type="hidden" name="dropRefNumSize" value="6">
         <input type="hidden" name="pickRefNums[0].type" value="1571">
         <input type="hidden" name="pickRefNums[0].searchString" value="">
         <input type="hidden" name="pickRefNums[1].type" value="1572">
         <input type="hidden" name="pickRefNums[1].searchString" value="">
         <input type="hidden" name="pickRefNums[2].type" value="1574">
         <input type="hidden" name="pickRefNums[2].searchString" value="1216500">
         <input type="hidden" name="pickRefNums[3].type" value="2014">
         <input type="hidden" name="pickRefNums[3].searchString" value="">
         <input type="hidden" name="pickRefNums[4].type" value="2026">
         <input type="hidden" name="pickRefNums[4].searchString" value="">
         <input type="hidden" name="pickRefNums[5].type" value="0">
         <input type="hidden" name="pickRefNums[5].searchString" value="">
         <input type="hidden" name="dropRefNums[0].type" value="1571">
         <input type="hidden" name="dropRefNums[0].searchString" value="">
         <input type="hidden" name="dropRefNums[1].type" value="1572">
         <input type="hidden" name="dropRefNums[1].searchString" value="">
         <input type="hidden" name="dropRefNums[2].type" value="1574">
         <input type="hidden" name="dropRefNums[2].searchString" value="">
         <input type="hidden" name="dropRefNums[3].type" value="2014">
         <input type="hidden" name="dropRefNums[3].searchString" value="">
         <input type="hidden" name="dropRefNums[4].type" value="2026">
         <input type="hidden" name="dropRefNums[4].searchString" value="">
         <input type="hidden" name="dropRefNums[5].type" value="0">
         <input type="hidden" name="dropRefNums[5].searchString" value="">
         <input type="hidden" name="pickConfirmationNums" value="">
         <input type="hidden" name="dropConfirmationNums" value="">
         <input type="hidden" name="pickDate" value="" />
         <input type="hidden" name="pickGeo" value="75051">
         <input type="hidden" name="pickMyAppt" value="false">
         <input type="hidden" name="dropDate" value="" />
         <input type="hidden" name="dropGeo" value="">
         <input type="hidden" name="dropMyAppt" value="false">
         <input type="hidden" name="pickConfDate" value="" />
         <input type="hidden" name="pickConfGeo" value="">
         <input type="hidden" name="dropConfDate" value="" />
         <input type="hidden" name="dropConfGeo" value="">
         <input type="hidden" name="standingApptProfileID" value="0">
         <input type="hidden" name="isForceAppt" value="false">
         <input type="hidden" name="$ENABLETIMEPARSING$" value="true" />
         <div class="page-header">
            <div class="page-header__container">
               <div class="page-header__main">
                  <div class="page-header__title-container">
                     <div class="page-header__page-title">Schedule Appointment</div>
                     <div class="page-header__page-title page-header__page-title--to-top">
                        <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                        Return to the top of Schedule Appointment                 </button>
                     </div>
                  </div>
               </div>
               <div class="page-message" id="page-message-root">
                  <div class="page-message__container">
                     <div class="page-message__icon-container"></div>
                     <div class="page-message__message-container">
                        <ul class="page-message__messages">
                           <li class="page-message__message page-message__message--primary"></li>
                        </ul>
                        <div class="page-message__button-container" style="display: none;"></div>
                     </div>
                     <button type="button" class="icon-button page-message__close-button">
                        <span role="tooltip" class="icon-span">
                           <svg class="icon  ic_close" focusable="false">
                              <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                           </svg>
                        </span>
                     </button>
                  </div>
               </div>
               <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
               <script>
                  (function() {
                  var errors = [

                  ];
                  var success = [

                  "Appointment Confirmed",

                  ];
                  onDocumentReady(function() {
                  var oldPageMessages = document.querySelectorAll('.old-page-message');
                  for(var i = 0; i < oldPageMessages.length; i++) {
                  var oldMessage = oldPageMessages[i];
                  oldMessage.parentElement.removeChild(oldMessage);
                  }
                  if(errors.length) {
                  PageMessage.error(errors, null, null, false);
                  } else if(success.length) {
                  PageMessage.success(success, null, null, false);
                  }
                  });
                  })();
               </script>
            </div>
         </div>
         <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
         <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=e7888b8ab25b8fc06bc94652a2a3cdc7410684f3"></script>
         <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
         <div class="subheader">
            Appointment Information
         </div>
         <table class="lean_table subsection">
            <tr>
               <td valign="top">
                  <table cellpadding="4" cellspacing="0" border="0">
                     <tr>
                        <td class="info-label">
                           Current Appt
                        </td>
                        <td id="test-tpScheduleAppt-currappt">
                           07/25/2025 14:00
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Appt. Status</td>
                        <td id="test-tpScheduleAppt-status">
                           CONFIRMED
                           &nbsp;&nbsp;
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Type</td>
                        <td id="test-tpScheduleAppt-type">
                           LIVE TRAILER
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Comments</td>
                        <td id="test-tpScheduleAppt-comments">--</td>
                     </tr>
                  </table>
               </td>
               <td valign="top">
                  <table cellpadding="4" cellspacing="0" border="0">
                     <tr>
                        <td
                           class="info-label"
                           id="test-tpScheduleAppt-location-type"
                           valign="top"
                           >
                           Pick-up Location
                        </td>
                        <td>&nbsp;</td>
                        <td
                           class="resultrow2"
                           id="test-tpScheduleAppt-address"
                           valign="top"
                           >
                           SOLO - GRAND PRAIRIE<br />
                           1803 W PIONEER PARKWAY<br />
                           GRAND PRAIRIE,
                           TX
                           75051
                           US
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Instructions</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-appointmentLocationComment">If appointing a drop trailer for 23:00, expectation is that drop trailer is picked up anytime during business hours, to meet on time delivery</td>
                     </tr>
                     <tr>
                        <td class="info-label">Consolidation Reference</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-consolRefs">
                        </td>
                     </tr>
                  </table>
               </td>
               <td valign="top" width="30%">
                  <table cellpadding="4" cellspacing="0" border="0" >
                     <tr>
                        <td class="info-label">Carrier</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-carrier-scac">
                           NFBR
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Trailer #</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-trailer-number">
                           --
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Driver</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-driver-number">
                           --
                        </td>
                     </tr>
                     <tr>
                        <td class="info-label">Vehicle #</td>
                        <td></td>
                        <td id="test-tpScheduleAppt-vehicle-number">
                           --
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
         </table>
         <table class="lean_table" width="100%">
            <tr class="loadreportsubhead">
               <td valign="top" align="center" colspan="11">
                  <button
                     class="leanbutton subtle"
                     id="test-previous-search-button"
                     onclick="previousSearch();"
                     type="button"
                     >
                  Previous Shipment Search
                  </button>
                  <button
                     class="leanbutton subtle"
                     id="test-new-search-button"
                     onclick="newSearch();"
                     type="button"
                     >
                  New Shipment Search
                  </button>
               </td>
            </tr>
         </table>
         <div class="subheader">
            Shipment Information
         </div>
         <div class="subsection">
         </div>
         <table class="list no-stripes">
            <thead>
               <tr >
                  <th width="20%">Ref #</th>
                  <th width="15%" align="center">
                     Status<br>
                     Plan Date
                  </th>
                  <th width="10%" valign="top">
                     Delivery<br />Location
                  </th>
                  <th width="8%" align="left">Priority</th>
                  <th width="8%" class="cell-align--right">Pieces</th>
                  <th width="8%" class="cell-align--right">Pallets</th>
                  <th width="8%" class="cell-align--right">Weight</th>
                  <th width="8%" class="cell-align--right">Volume</th>
                  <th width="10%" align="center">Sequence</th>
               </tr>
            </thead>
            <tbody>
               <tr class="resultrow2">
                  <td width="20%">
                     Unique: 0083646299<br />
                     PO: 1216500<br />
                     Order Number: 0001993055<br />
                     Delivery#: 0083646299<br />
                  </td>
                  <td width="15%" align="center">Open<br>
                     07/23/2025
                  </td>
                  <td width="10%" valign="top" align="left">
                     WILL ROGERS LINEAGE C/O APP WHOLESA<br />
                     6445 WILL ROGERS BOULEVARD<br />
                     FT WORTH,&nbsp;
                     TX&nbsp;
                     76134&nbsp;
                     US
                  </td>
                  <td width="8%" valign="top" align="left">
                     --
                  </td>
                  <td width="8%" class="cell-align--right">
                     1,440
                  </td>
                  <td width="8%" class="cell-align--right">
                     0
                  </td>
                  <td width="8%" class="cell-align--right">
                     11,476.8&nbsp;lb
                  </td>
                  <td width="8%" class="cell-align--right">
                     2,424.96&nbsp;cu ft
                  </td>
                  <td width="10%" align="center">
                     0
                  </td>
               </tr>
               <tr class="searchcriteria">
                  <td colspan="2">&nbsp;<input type="hidden" name="shipmentSize" value="1" /></td>
                  <td>&nbsp;</td>
                  <td class="cell-align--right">Total</td>
                  <td class="cell-align--right">1,440</td>
                  <td class="cell-align--right">0</td>
                  <td class="cell-align--right">11,476.8&nbsp;lb</td>
                  <td class="cell-align--right">2,424.96&nbsp;cu ft</td>
                  <td>&nbsp;</td>
               </tr>
            </tbody>
         </table>
      </form>
   </body>
</html>