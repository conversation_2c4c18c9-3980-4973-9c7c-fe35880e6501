





























<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="CarrierAppointmentSearch" class="search-page">
<head>
<title>Appoint Loads</title>












<meta name="msapplication-config" content="/browserconfig.xml" />
<script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>

<link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
<link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
<script type="text/javascript">
var faviconICO = document.querySelector('#faviconICO');
var faviconPNG = document.querySelector('#faviconPNG');
var darkModeListener = function(event) {
if (event.matches) {
faviconICO.setAttribute("href","/favicon_dark.ico");
faviconPNG.setAttribute("href","/favicon_dark.png");
} else {
faviconICO.setAttribute("href","/favicon.ico");
faviconPNG.setAttribute("href","/favicon.png");
}
};
var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
if(darkModePreference.addEventListener){
darkModePreference.addEventListener('change', function(e) {
if (e.matches) {
activateDarkMode();
}
});
} else {
darkModePreference.addListener(function(e) {
if (e.matches) {
activateDarkMode();
}
});
}
darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
</script>

<link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
<link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
<link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">


<link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">

<script type="text/javascript">
function popupMaestro() {




}
onDocumentReady(function() {
var iconContainer = document.createElement('div');
jQuery(document.body).prepend(iconContainer);
jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
popupMaestro();
});
</script>



<script type="text/javascript">

var useComponents = true;
var newStylesComponents = false;
var useETOComponents = true;
var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"; 
</script>
<script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>

<script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>


<script type="text/javascript">
ll.util.initFormats(1, 1,
'en_US');
var shortLang = 'en';
var browserCacheKey = '18867ea6\-72f2\-410e\-850a\-42ef0ec9f72b';
ll.lang = ll.lang || {};
ll.lang.locale = 'en_US';
</script>


<script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>



<script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>








<script type="text/javascript">
jQuery.noConflict();
var LeanActionFormName = 'CarrierAppointmentSearchForm';
var LeanActionFullPath = '\/apptschedule\/carrierappointmentsearch.do';
var $LeanActionForm;
onDocumentReady(function() {


$LeanActionForm = jQuery('form[name=CarrierAppointmentSearchForm]');



if (get_browser() === "msie") {
jQuery('html').addClass("ie");
}
if ( 'noValidate' in document.createElement('form') ){
jQuery('html').addClass("novalidate");
}
var $dwrImageDiv = jQuery('#dwrImageDiv');
if (!$dwrImageDiv.length) {
$dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
$dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
jQuery('body').append($dwrImageDiv);
}


hasExpiredSessionWarningBeenDisplayed = false;
});
</script>


































<script type="text/javascript" src="/execution/vessel.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript">
var reasonCodeProfileData = {"stopData":{},"profiles":{}};
var apptValidationData= {"414022852":{"isEndDateTimeOpen":true,"endDate":"01/01/2100","isZeroRange":false,"startTime":"00:00","endTime":"00:00","isFullRange":true,"isStartDateTimeOpen":true,"startDate":"01/01/2000"},"414022851":{"isEndDateTimeOpen":true,"endDate":"01/01/2100","isZeroRange":false,"startTime":"00:00","endTime":"00:00","isFullRange":true,"isStartDateTimeOpen":true,"startDate":"01/01/2000"}};
var popupWin;
var loadDates = {};
function addDate(name, detailNum, definedType, checkTime, required) {
var tempRow = new Array(name,definedType,checkTime,required);
loadDates[detailNum][definedType] = tempRow;
}
var loadValidation = [[{"commentRequired":false,"appointmentChanged":false,"requiredAppointmentFCFS":false,"referenceNumberRequired":false},{"commentRequired":false,"appointmentChanged":false,"requiredAppointmentFCFS":true,"referenceNumberRequired":false},{"commentRequired":false,"appointmentChanged":false,"requiredAppointmentFCFS":true,"referenceNumberRequired":false}]];
function setQuickDate(select) {
var opt = getSelectedValue(select);
var form = document.forms["CarrierAppointmentSearchForm"];
var startDate = form.elements["pickDateStart"].value;
var endDate = form.elements["pickDateEnd"].value;
switch(opt){
case "1": startDate="04/23/2025";endDate="04/23/2025";break;
case "2": startDate="04/22/2025";endDate="04/22/2025";break;
case "3": startDate="04/24/2025";endDate="04/24/2025";break;
case "4": startDate="04/20/2025";endDate="04/26/2025";break;
case "5": startDate="04/13/2025";endDate="04/19/2025";break;
case "6": startDate="04/27/2025";endDate="05/03/2025";break;
case "7": startDate="04/01/2025";endDate="04/30/2025";break;
case "8": startDate="03/01/2025";endDate="03/31/2025";break;
case "9": startDate="05/01/2025";endDate="05/31/2025";break;
case "16": startDate="04/01/2025";endDate="06/30/2025";break;
case "17": startDate="01/01/2025";endDate="03/31/2025";break;
case "18": startDate="07/01/2025";endDate="09/30/2025";break;
case "10": startDate="01/01/2025";endDate="04/23/2025";break;
case "12": startDate="06/01/2025";endDate="06/30/2025";break;
case "19": startDate="01/23/2025";endDate="04/23/2025";break;
case "20": startDate="10/23/2024";endDate="04/23/2025";break;
case "13": startDate="04/23/2024";endDate="04/23/2025";break;
case "14": startDate="10/23/2023";endDate="04/23/2025";break;
case "15": startDate="04/13/2025";endDate="05/03/2025";break;
default: startDate=""; endDate=""; break;
}
form.elements["pickDateStart"].value = startDate;
form.elements["pickDateEnd"].value = endDate;
}
function enableReasonInputs(idx, detailIdx) {
var form = document.forms["CarrierAppointmentSearchForm"];
var reasonCode = form.elements["results["+idx+"].details["+detailIdx+"].reasonCode"];
var reasonCodeComment = form.elements["results["+idx+"].details["+detailIdx+"].reasonCodeComment"];
if (reasonCode) {
reasonCode.disabled = false;
showCommentRequired(idx, detailIdx);
}
if (reasonCodeComment) {
reasonCodeComment.disabled = false;
}
}
function showCommentRequired(idx, detailIdx) {
var form = document.forms["CarrierAppointmentSearchForm"];
var reasonCode = form.elements["results["+idx+"].details["+detailIdx+"].reasonCode"];
var option = reasonCode.options[reasonCode.selectedIndex];
var commentRequiredDiv = document.getElementById("reasonCheckLabel["+idx+"]["+detailIdx+"]");
if(option.id == "true") {
commentRequiredDiv.style.display = "";
loadValidation[idx][detailIdx].commentRequired = true;
} else {
commentRequiredDiv.style.display = "none";
loadValidation[idx][detailIdx].commentRequired = false;
}
}
function updateDropTrailer(checkbox, hiddenFieldName) {
var form = document.forms["CarrierAppointmentSearchForm"];
var dropTrailerType = form.elements[hiddenFieldName];
if(checkbox.checked) {
dropTrailerType.value = 2;
} else {
dropTrailerType.value = 1;
}
}
function getDriverInfo(request, response) {
ll.util.callAjaxAction('/tmsrest/autocomplete/carrier_drivers?q=' + encodeURIComponent(request.term), response);
}
function driverSelected(inputElement, selectedElement, driverObject) {
jQuery(inputElement).val(driverObject.driverName);
if(driverObject.defaultVehicleNumber != null) {
jQuery(inputElement).closest('tr').find('input[name$="vehicleNumber"]').val(driverObject.defaultVehicleNumber);
}
}
function driverNameSelector(driverObject) {
return driverObject.driverName;
}
function getVehicleInfo(request, response) {
ll.util.callAjaxAction('/tmsrest/autocomplete/carrier_vehicles?q=' + encodeURIComponent(request.term), response);
}
function vehicleSelected(inputElement, selectedElement, vehicleNumber) {
jQuery(inputElement).val(vehicleNumber.value);
}
function vehicleNumberSelector(vehicleNumber) {
return vehicleNumber;
}
onDocumentReady(function() {
jQuery('div.criteria * input').on('keypress', function(e) {
if (e.which == 13) {
ll.util.triggerEvent(this, 'blur');
ll.util.triggerEvent(this, 'change');
ll.util.triggerEvent(ll.util.triggerEvent(jQuery('#searchButton'), 'focus'), 'click');
}
});
jQuery('form[name="CarrierAppointmentSearchForm"] input')
.filter('[name$="appointScheduledOnDate"], [name$="appointStartDateTime"], [name$="appointEndDateTime"]')
.each(function() {
var dDate = jQuery(this).closest('tr').find('input[name$="quickKeyDDate"]').val();
jQuery(this).on('change', function(e) {
var dateFld, timeFld, fieldType;
if (jQuery(this).prop('name').indexOf('$TIME$') > -1) {
dateFld = jQuery('input[name="' + this.name.substring(7) + '"]').get(0);
timeFld = this;
fieldType = "time";
} else {
dateFld = this;
timeFld = jQuery('input[name="-$TIME$' + this.name + '"]').get(0);
fieldType = "date";
}
ll.util.quickKey(dateFld, timeFld, fieldType, dDate);
if (fieldType == "date") {
ll.util.formatAndValidateDateInput(this);
} else {
checkMilitaryTime(this, true);
}
})
});
jQuery('input[name$="driverName"]').ll_autocomplete( { source: getDriverInfo, selectItem:driverSelected, displayItem:driverNameSelector, minLength:2, choices:15 });
jQuery('input[name$="vehicleNumber"]').ll_autocomplete( { source: getVehicleInfo, selectItem:vehicleSelected, displayItem:vehicleNumberSelector, minLength:2, choices:15 });
jQuery('.drayLoad').on('change keyup', ll.vessel.onContainerRefChange);
var sortLinkLoadNum = document.getElementById('sortLinkLoadNum');
var sortLinkDueDate = document.getElementById('sortLinkDueDate');
var sortLinkApptDate = document.getElementById('sortLinkApptDate');
if(sortLinkLoadNum){
sortLinkLoadNum.addEventListener('click', function() { sortResults('SORT_FLD_LOADNUM')});
}
if(sortLinkDueDate){
sortLinkDueDate.addEventListener('click', function() { sortResults('SORT_FLD_DUEDATE')});
}
if(sortLinkApptDate){
sortLinkApptDate.addEventListener('click', function() { sortResults('SORT_FLD_APPTDATE')});
}
});
function search() {
var form = document.forms["CarrierAppointmentSearchForm"];
var searchByRef = false;
var searchByType = false;
if (!validateMultipleFields(form,"leanIDString","integer")) {
return false;
}
if (!validateMultipleFields(form,"proNumString","quotableRefNum")) {
return false;
}
if (!validateMultipleFields(form,"refNumString","quotableRefNum")) {
return false;
}
if (!ll.util.checkDate(form.elements["pickDateStart"], true)) {
return false;
}
if (!ll.util.checkDate(form.elements["pickDateEnd"], true)) {
return false;
}
if (form.elements["leanIDString"].value.length > 0
|| form.elements["proNumString"].value.length > 0
|| form.elements["refNumString"].value.length > 0)
{
searchByRef = true;
}
if (form.elements["pickDateStart"].value.length > 0 || form.elements["pickDateEnd"].value.length > 0 ||
form.elements["searchQO.unappt"].checked || form.elements["searchQO.partAppt"].checked || form.elements["searchQO.fullAppt"].checked)
{
searchByType = true;
}
if ((form.elements["searchQO.groups"].selectedIndex == -1 || form.elements["searchQO.groups"].selectedIndex == null) && !form.elements["searchQO.myLoads"].checked) {
alert("You must select at least one group or choose to search for \x27My Loads\x27.");
return false;
}
if (!searchByRef && !searchByType) {
alert("You must enter some search criteria before a search can be performed."); 
return false;
}
form.elements["pageaction"].value = "search";
var searchBtn = document.getElementById('searchButton');
searchBtn.readOnly = true;
searchBtn.className = "search_disabled";
searchBtn.title = "To Cancel the Search, click Stop on the Browser";
form.submit();
}
function sortResults(sortByOption) {
var form = document.forms["CarrierAppointmentSearchForm"];
var sortByFld = form.elements["searchQO.sortBy"];
var sortOrderFld = form.elements["searchQO.sortAscending"];
if (sortByFld.value == sortByOption) {
sortOrderFld.value = (sortOrderFld.value == "true" ? "false" : "true");
} else {
sortByFld.value = sortByOption;
sortOrderFld.value = "true";
}
form.elements["pageaction"].value = "sort";
form.submit();
}
function nextPage() {
var form = document.forms["CarrierAppointmentSearchForm"];
form.elements["pageaction"].value = "nextPage";
form.submit();
}
function previousPage() {
var form = document.forms["CarrierAppointmentSearchForm"];
form.elements["pageaction"].value = "previousPage";
form.submit();
}
function submitFormForSave() {
if (popupWin != null && !popupWin.closed) {
popupWin.close();
}
var form = document.forms["CarrierAppointmentSearchForm"];
form.elements['submitBtn'].disabled = true;
form.elements["pageaction"].value = "save";
form.submit();
}
function save() {
var form = document.forms["CarrierAppointmentSearchForm"];
var today = new Date();

var loadApptDates = new Object();
var checkApptDates = false;
for (var loadIdx=0; loadIdx < 1; loadIdx++) {
var loadInfo = loadValidation[loadIdx];
var proNum = form.elements["results["+loadIdx+"].proNum"];
if (!validateQuotedRefNumField(proNum)) {
return false;
}
var pastRestrictApptDays = form.elements["results["+loadIdx+"].pastRestrictApptDays;"];
var futureRestrictApptDays = form.elements["results["+loadIdx+"].futureRestrictApptDays;"];
today.setHours(23);
today.setMinutes(59);
today.setSeconds(59);
var futureDaysAhead = new Date(today.getTime()+(futureRestrictApptDays*24*60*60*1000));
today.setHours(0);
today.setMinutes(0);
today.setSeconds(0);
var pastDaysAgo = new Date(today.getTime()-(pastRestrictApptDays*24*60*60*1000));
var loadID = form.elements["results["+loadIdx+"].loadID"].value;
var apptDates = new Array();
for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
var validationObj = loadValidation[loadIdx][detailIdx];
validationObj.commentRequired;
validationObj.appointmentChanged;
var appointmentStartDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
var appointmentStartTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
var appointmentEndDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
var appointmentEndTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
var appointmentScheduledDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointScheduledOnDate"];
var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
var appointmentChanged = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointmentChanged"].value;

if (appointmentScheduledDate != null && appointmentStartDate != null) {
var appointmentFCFS = form.elements["results["+loadIdx+"].details["+detailIdx+"].FCFS"];
var appointmentReference = form.elements["results["+loadIdx+"].details["+detailIdx+"].apptReference"];
var appointmentDropTrailer = form.elements["results["+loadIdx+"].details["+detailIdx+"].dropTrailer"];
var appointmentScheduledTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointScheduledOnDate"];
var dateTimeFields = [];
dateTimeFields.push({date: appointmentScheduledDate, time: appointmentScheduledTime});
dateTimeFields.push({date: appointmentStartDate, time: appointmentStartTime});
dateTimeFields.push({date: appointmentEndDate, time: appointmentEndTime});
for (var i=0; i<dateTimeFields.length; i++) {
var date = dateTimeFields[i].date;
var time = dateTimeFields[i].time;

if (!ll.util.checkDate(date, true)) {
return false;
}
if (!checkMilitaryTime(time, true)) {
return false;
}

if (!isEmpty(time.value) && isEmpty(date.value)) {
alert('If entering an appointment time you must enter an appointment date');
date.focus();
return false;
}

if (!isEmpty(date.value)) {
var jsDate = ll.util.jsDateTime(date.value, 23, 59);
if (jsDate < pastDaysAgo) {
alert(ll.util.formatMessage('One of the dates for Load# {0} cannot be more than {1} days ago. \n Please enter a valid date.', loadID, pastApptDays));
date.focus();
return false;
}
if (jsDate > futureDaysAhead) {
alert(ll.util.formatMessage('One of the dates for Load# {0} cannot be more than {1} days in the future. \n Please enter a valid date.', loadID, futureApptDays));
date.focus();
return false;
}
}
}

if (appointmentFCFS.checked && validationObj.requiredAppointmentFCFS) {
if (isEmpty(appointmentStartDate.value)) {
alert(ll.util.formatMessage('An appointment date is required with FCFS for load ID# {0}.', loadID));
appointmentStartDate.focus();
return false;
}
}
if (!appointmentFCFS.checked && !isEmpty(appointmentStartDate.value) && isEmpty(appointmentStartTime.value)) {
alert('If FCFS has not been selected, you must enter a time when entering an appointment date.');
appointmentStartTime.focus();
return false;
}
if (isEmpty(appointmentStartDate.value) && !isEmpty(appointmentEndDate.value)) {
alert('You may not have an end time without entering a start time.');
appointmentStartDate.focus();
return false;
}


if (!isEmpty(appointmentStartDate.value) && !isEmpty(appointmentEndDate.value)) {
if (ll.util.getJSDateMilitaryTime(appointmentStartDate.value, appointmentStartTime.value) > ll.util.getJSDateMilitaryTime(appointmentEndDate.value, appointmentEndTime.value)) {
alert('Appointment start time is set after the end time.  This is not allowed.');
appointmentStartTime.focus();
return false;
}
}
var datePresent = (appointmentFCFS.checked || !isEmpty(appointmentStartDate.value));
if (!datePresent && !isEmpty(appointmentReference.value)) {
alert('Reference numbers cannot be saved unless an appointment date is also present or FCFS is checked.');
appointmentReference.focus();
return false;
}
var referenceNumberRequired = (datePresent && validationObj.referenceNumberRequired);
if (!validateApptRef(appointmentReference, referenceNumberRequired)) {
return false;
}
if (appointmentDropTrailer && appointmentDropTrailer.type == "checkbox" && appointmentDropTrailer.checked) {
if (isEmpty(appointmentStartDate.value) && !appointmentFCFS.checked) {
alert('Trailer Loading Type setting cannot be saved unless an appointment date is also present or FCFS is checked.');
appointmentStartDate.focus();
return false;
}
}
var validTimeRange = apptValidationData[loaddetailid];
if(validTimeRange != null && appointmentChanged == 'true'){
var validStartDate = ll.util.getJSDateMilitaryTime(validTimeRange.startDate, validTimeRange.startTime );
var validEndDate = ll.util.getJSDateMilitaryTime(validTimeRange.endDate, validTimeRange.endTime );
var apptStartDate = ll.util.getJSDateMilitaryTime( appointmentStartDate.value, appointmentStartTime.value );
if(!validTimeRange.isFullRange){
if(validTimeRange.isZeroRange){
var alertMsg = "No Valid Appointment dates based on rules.";
alert(alertMsg);
appointmentStartDate.focus();
return false;
} else {
if(apptStartDate.getTime() < validStartDate.getTime() || apptStartDate.getTime() > validEndDate.getTime() ){
var alertMsg = "Appointment date is not valid based on rules.";
alertMsg = alertMsg + "\n\n" + "Valid appointment range:" + "\n";
if(!validTimeRange.isStartDateTimeOpen){
alertMsg = alertMsg + "\t\t " + ll.util.convertDateToString(validStartDate, true);
} else{
alertMsg = alertMsg + " Indefinite";
}
alertMsg = alertMsg + " - ";
if(!validTimeRange.isEndDateTimeOpen){
alertMsg = alertMsg + " " + ll.util.convertDateToString(validEndDate, true);
} else{
alertMsg = alertMsg + " Indefinite";
}
alert(alertMsg);
appointmentStartDate.focus();
return false;
}
}
}
}
var rcStopData = reasonCodeProfileData.stopData[loaddetailid];
var reasonCodeFld = form.elements["results["+loadIdx+"].details["+detailIdx+"].reasonCode"];
if (datePresent && rcStopData && reasonCodeFld) {
var rcProfile = reasonCodeProfileData.profiles[rcStopData.profileID];
var selectedReason = getSelectedValue(reasonCodeFld);
if (!reasonCodeFld.disabled && rcProfile.isReasonRequired && selectedReason == 0) {
alert('A reason code is required.  Please select a reason code.');
reasonCodeFld.focus();
return false;
}
if (!reasonCodeFld.disabled && rcProfile.reasonCodes[selectedReason] && rcProfile.reasonCodes[selectedReason].isRequireComment) {
var commentFld = form.elements["results["+loadIdx+"].details["+detailIdx+"].reasonCodeComment"];
if (commentFld.value == '') {
var alertMsg = ll.util.formatMessage('Please enter Comments for Reason Code: {0}', rcProfile.reasonCodes[selectedReason].label);
alert(alertMsg);
commentFld.focus();
return false;
}
}
}
}

var foundDates = false;
var apptObject = new Object();
apptObject.timezone = form.elements["timezone"+loaddetailid].value;
if (appointmentStartDate != null && appointmentStartDate.value != "") {
apptObject.startDate = appointmentStartDate.value;
if(appointmentStartTime != null && appointmentStartTime.value != "") {
apptObject.startTime = appointmentStartTime.value;
}
foundDates = true;
if (appointmentStartDate.getAttribute("type") == "text") {

checkApptDates = true;
}
}
if (appointmentEndDate != null && appointmentEndDate.value != "") {
apptObject.endDate = appointmentEndDate.value;
if(appointmentEndTime != null && appointmentEndTime.value != "") {
apptObject.endTime = appointmentEndTime.value;
}
foundDates = true;
if (appointmentEndDate.getAttribute("type") == "text") {
checkApptDates = true;
}
}
if (foundDates) {

apptDates.push(apptObject);
}
if (loadDates[loaddetailid]) {
var loadDateArray = loadDates[loaddetailid];
for (var d in loadDateArray) {
var current = loadDateArray[d];
var dateField = form.elements["stop" + loaddetailid +"type"+ current[1]];
var timeField = form.elements["stop" + loaddetailid +"time"+ current[1]];
var required = false; 
if (current[3] == 1 && !isEmpty(appointmentStartDate.value) && "--" !== appointmentStartDate.value) {
required = true; 
}
if (!ll.util.checkDate(dateField, !required)) {
dateField.focus();
dateField.select();
return false;
}
if (current[2] == 0 && timeField) { 
if (!checkMilitaryTime(timeField, !required)) {
timeField.focus();
timeField.select();
return false;
}
}
}
}
}

loadApptDates[loadID+""] = apptDates;
var proNum = form.elements["results["+loadIdx+"].proNum"];
var trailerNumber = form.elements["results["+loadIdx+"].trailerNumber"];
var containerNumber = form.elements["results["+loadIdx+"].containerNumber"];
var driverName = form.elements["results["+loadIdx+"].driverName"];
var vehicleNumber = form.elements["results["+loadIdx+"].vehicleNumber"];
var drayageContainerID = form.elements["results["+loadIdx+"].drayageContainerID"];
if (!validateQuotedRefNumField(proNum)) {
return false;
}
if (trailerNumber && !validateQuotedRefNumField(trailerNumber, true, 30, true)) {
trailerNumber.focus();
return false;
}
if (containerNumber && !validateQuotedRefNumField(containerNumber, true, 30, true)) {
containerNumber.focus();
return false;
}
if (containerNumber && drayageContainerID > 0 && !ll.vessel.validateContainerReference(jQuery(containerNumber))) {
if (!confirm(ll.util.formatMessage('Container # \x27{0}\x27 is not in standard ISO 6346 format (e.g. \x27CSQU3054383\x27). Continue?', containerNumber.value))) {
containerNumber.focus();
return false;
}
}
if (driverName && (!isString(driverName.value,true) || driverName.value.length > 60)) {
alert('The Driver Name is invalid.');
driverName.focus();
return false;
}
if (vehicleNumber && !validateQuotedRefNumField(vehicleNumber, true, 30, true)) {
vehicleNumber.focus();
return false;
}
}

if (checkApptDates) {

ll.util.callAjaxAction("/tmsrest/datelookup/appt_sequence_warnings", onDateSequenceValidationLookup, null, null, 'POST',
JSON.stringify(loadApptDates), false, 'text/plain; charset=UTF-8');
} else {
submitFormForSave();
}
}
function onDateSequenceValidationLookup(warning) {
if (isEmpty(warning) || confirm(warning+"\n\nContinue appointing the loads?")) {
submitFormForSave();
}
}
function checkApptTimes(chkBox, loadIdx, detailIdx) {
if (!chkBox.checked) {
var form = chkBox.form;
var startDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
var startTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
var endDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
var endTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
if ((startDate.value == endDate.value || isEmpty(endDate.value)) &&
startTime.value == '00:00' && endTime.value == '23:59')
{
startTime.value = "";
endTime.value = "";
}
}
}
function validateApptRef(refNum, refNumReq) {
if (refNum != null && refNum.value != null) {
if (refNumReq && isWhitespace(refNum.value)) {
alert("Please enter an Appointment Confirmation #");
refNum.focus();
return false;
}
if (!isString(refNum.value, !refNumReq)) {
alert("Reference numbers may only be letters, numbers and spaces.");
refNum.focus();
return false;
}
}
return true;
}
function clearForm() {
var form = document.forms["CarrierAppointmentSearchForm"];
form.elements["leanIDString"].value = "";
form.elements["proNumString"].value = "";
form.elements["refNumString"].value = "";
form.elements["searchQO.shipper"].value = "";
form.elements["pickDateStart"].value = "";
form.elements["pickDateEnd"].value = "";
form.elements["quickDate"].selectedIndex = 0;
form.elements["searchQO.myLoads"].checked = true;
var selectBox = form.elements["searchQO.groups"];
for (var i=0; i < selectBox.options.length; i++) {
selectBox.options[i].selected = false;
}
}
function getEligibleDateRange(stopID, loadRowID, stopRowID, forConsolidation) {
var form = document.forms['CarrierAppointmentSearchForm'];
var rangeStartDate;
var rangeStartTime;
var rangeStartTimezone;
var rangeEndDate;
var rangeEndTime;
var rangeEndTimezone;
var numStops = loadValidation[loadRowID].length;
var stopBefore = stopRowID - 1;
var stopAfter = stopRowID + 1;
if (stopBefore >= 0) {
var detailNum = form.elements["results["+loadRowID+"].details["+stopBefore+"].detailNum"].value;
var appointmentStartDate = form.elements["results["+loadRowID+"].details["+stopBefore+"].appointStartDateTime"].value;
var appointmentStartTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopBefore+"].appointStartDateTime"].value;
var appointmentEndDate = form.elements["results["+loadRowID+"].details["+stopBefore+"].appointEndDateTime"].value;
var appointmentEndTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopBefore+"].appointEndDateTime"].value;
var appointmentTimezone = form.elements["timezone"+detailNum].value;
if (!isEmpty(appointmentEndDate)) { 
rangeStartDate = appointmentEndDate;
if (!isEmpty(appointmentEndTime)) {
rangeStartTime = appointmentEndTime;
}
rangeStartTimezone = appointmentTimezone;
} else if(!isEmpty(appointmentStartDate)) {
rangeStartDate = appointmentStartDate;
if (!isEmpty(appointmentStartTime)) {
rangeStartTime = appointmentStartTime;
}
rangeStartTimezone = appointmentTimezone;
}
}
if (stopAfter < numStops) {
var detailNum = form.elements["results["+loadRowID+"].details["+stopAfter+"].detailNum"].value;
var appointmentStartDate = form.elements["results["+loadRowID+"].details["+stopAfter+"].appointStartDateTime"].value;
var appointmentStartTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopAfter+"].appointStartDateTime"].value;
var appointmentEndDate = form.elements["results["+loadRowID+"].details["+stopAfter+"].appointEndDateTime"].value;
var appointmentEndTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopAfter+"].appointEndDateTime"].value;
var appointmentTimezone = form.elements["timezone"+detailNum].value;
if(!isEmpty(appointmentStartDate)) { 
rangeEndDate = appointmentStartDate;
if (!isEmpty(appointmentStartTime)) {
rangeEndTime = appointmentStartTime;
}
rangeEndTimezone = appointmentTimezone;
} else if (!isEmpty(appointmentEndDate)) {
rangeEndDate = appointmentEndDate;
if (!isEmpty(appointmentEndTime)) {
rangeEndTime = appointmentEndTime;
}
rangeEndTimezone = appointmentTimezone;
}
}
if (forConsolidation) {
var range = new Object();
range.startDate = rangeStartDate;
range.startTime = rangeStartTime;
range.startTimezone = rangeStartTimezone;
range.endDate = rangeEndDate;
range.endTime = rangeEndTime;
range.endTimezone = rangeEndTimezone;
return range;
} else {
var params= "";
if (!isEmpty(rangeStartDate)) {
params += "&rangeStartTimezone=" + rangeStartTimezone + "&rangeStartDate=" + rangeStartDate;
if (!isEmpty(rangeStartTime)) {
params += "&rangeStartTime=" + rangeStartTime;
}
}
if (!isEmpty(rangeEndDate)) {
params += "&rangeEndTimezone=" + rangeEndTimezone + "&rangeEndDate=" + rangeEndDate;
if (!isEmpty(rangeEndTime)) {
params += "&rangeEndTime=" + rangeEndTime;
}
}
return params;
}
}
function scheduleExternalProviderAppt(stopID) {
var theURL = "/externalprovider/scheduleapptcontroller.do?stopID="+stopID;
popupWin = popup(theURL, 'schappt', 1100, 650);
}
function scheduleAppt(stopID, loadRowID, stopRowID) {
var params = getEligibleDateRange(stopID, loadRowID, stopRowID, false);
var theURL = "/apptschedule/scheduleappt.do?stopIDs="+stopID+params;
popupWin = popup(theURL, 'schappt', 1100, 650);
}
function scheduleDropTrailerAppt(stopID, loadRowID, stopRowID) {
var params = getEligibleDateRange(stopID, loadRowID, stopRowID, false);
var theURL = "/apptschedule/scheduledroptrailerappt.do?stopIDs="+stopID+params;
popupWin = popup(theURL, 'schappt', 1100, 650);
}
function cancelAppt(stopID) {
var theURL = "/apptschedule/cancelappt.do?stopID="+stopID;
popupWin = popup(theURL, 'cancelappt', 700, 300);
}
function removeFromConsolidation(stopID) {
var theURL = "/apptschedule/cancelappt.do?removeLoadFromConsolidatedAppt=true&stopID="+stopID;
popupWin = popup(theURL, 'cancelappt', 700, 300);
}
function viewApptComments(stopID) {
var theURL = "/apptschedule/carrierdockapptcomment.do?stopID=" + stopID;
popupWin = popup(theURL, 'apptcomments', 800, 450);
}
function markAppointmentChanged(idx, detailIdx) {
var form = document.forms["CarrierAppointmentSearchForm"];
var appointmentChanged = form.elements["results["+idx+"].details["+detailIdx+"].appointmentChanged"];
if(appointmentChanged) {
appointmentChanged.value = true;
}
}
function handleConsolidationAction() {
var form = document.forms["CarrierAppointmentSearchForm"];
var action = getSelectedValue(form.elements["consolidationOptions"])*1;
switch(action) {
case 1: 
createConsolidatedLiveAppointment();
break;
case 2:
createConsolidatedDropAppointment();
break;
}
}
var stopIDsStr = "";
function createConsolidatedLiveAppointment() {
var form = document.forms["CarrierAppointmentSearchForm"];
stopIDsStr = "";
var rangeObject = new Object();
var rangeArray = new Array();
for (var loadIdx=0; loadIdx < 1; loadIdx++) {
var loadInfo = loadValidation[loadIdx];
for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
var checkbox = form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"];
if(checkbox != null && checkbox.checked) {
var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
if(stopIDsStr.length > 0) {
stopIDsStr += "&";
}
stopIDsStr += "stopIDs="+loaddetailid;
rangeArray.push(getEligibleDateRange(loaddetailid, loadIdx, detailIdx, true));
}
}
}
if (!isEmpty(stopIDsStr)) {

rangeObject.range = rangeArray;
ll.util.callAjaxAction("/tmsrest/datelookup/eligible_date_ranges", openScheduleLiveAppt, null, null, 'POST',
JSON.stringify(rangeObject), false, 'text/plain; charset=UTF-8');
} else {
alert("Please select one or more stops on which to perform the action.");
}
}
function openScheduleLiveAppt(rangeObject) {
var warning = rangeObject.warning;
if (!warning || confirm(warning)) {
var params = rangeObject.params;
if (!params) {
params = ""; 
}
var theURL = "/apptschedule/scheduleappt.do?"+stopIDsStr+params;
popupWin = popup(theURL, 'schappt', 800, 450);
}
}
function createConsolidatedDropAppointment() {
var form = document.forms["CarrierAppointmentSearchForm"];
stopIDsStr = "";
var rangeObject = new Object();
var rangeArray = new Array();
for (var loadIdx=0; loadIdx < 1; loadIdx++) {
var loadInfo = loadValidation[loadIdx];
for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
var checkbox = form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"];
if(checkbox != null && checkbox.checked) {
var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
if(stopIDsStr.length > 0) {
stopIDsStr += "&";
}
stopIDsStr += "stopIDs="+loaddetailid;
rangeArray.push(getEligibleDateRange(loaddetailid, loadIdx, detailIdx, true));
}
}
}
if (!isEmpty(stopIDsStr)) {

rangeObject.range = rangeArray;
ll.util.callAjaxAction("/tmsrest/datelookup/eligible_date_ranges", openScheduleDropAppt, null, null, 'POST',
JSON.stringify(rangeObject), false, 'text/plain; charset=UTF-8');
} else {
alert("Please select one or more stops on which to perform the action.");
}
}
function openScheduleDropAppt(rangeObject) {
var warning = rangeObject.warning;
if (!warning || confirm(warning)) {
var params = rangeObject.params;
if (!params) {
params = ""; 
}
var theURL = "/apptschedule/scheduledroptrailerappt.do?"+stopIDsStr+params;
popupWin = popup(theURL, 'schappt', 800, 450);
}
}
function updateASMDisplay(stopID, apptDate, apptTime, apptStatusID, droptrailer) {
var apptdivobj = document.getElementById("apptdiv"+stopID);
var apptcommentdivobj = document.getElementById("apptcommentdiv"+stopID);
if (apptdivobj != null) {
var msgTxt = "";
if (droptrailer) {
msgTxt += "Drop Trailer<br />";
}
var apptStatusTxt = "";
if (apptStatusID == '2') {
apptStatusTxt = "CONFIRMED";
} else if (apptStatusID == '1') {
apptStatusTxt = "REQUESTED";
}
msgTxt += apptDate + ' ' + apptTime + ' <br /><span class="success-highling">' + apptStatusTxt + '<span>';
jQuery('input[name^="results"]', apptdivobj).val(apptDate);
jQuery('input[name^="-$TIME$"]', apptdivobj).val(apptTime);
jQuery('span', apptdivobj).html(msgTxt);
}
if (apptcommentdivobj != null) {
var msgTxt = "";
if ((apptStatusID=="2")||(apptStatusID=="1")) {
msgTxt = "<a href=\"javascript:viewApptComments(" +stopID+ ")\">Check Appt Comments</a>";
}
apptcommentdivobj.innerHTML = msgTxt;
}
}
function apptCancelled(stopID) {
jQuery("#apptdiv" + stopID).remove();
jQuery("#apptcanceldiv" + stopID).remove();
jQuery("#apptremovediv" + stopID).remove();
}
function stopRemovedFromAppt(stopID) {
jQuery("#apptdiv" + stopID).remove();
jQuery("#apptcanceldiv" + stopID).remove();
jQuery("#apptremovediv" + stopID).remove();
}
function selectAllValues(stopType) {
var form = document.forms["CarrierAppointmentSearchForm"];
var consolidateAppointment = true;
for (var loadIdx=0; loadIdx < 1; loadIdx++) {
var loadInfo = loadValidation[loadIdx];
for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
if(stopType == form.elements["results["+loadIdx+"].details["+detailIdx+"].stopType"].value * 1) {
if(form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"]) {
consolidateAppointment = consolidateAppointment && form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"].checked;
}
}
if(!consolidateAppointment) {
break;
}
}
if(!consolidateAppointment) {
break;
}
}
for (var loadIdx=0; loadIdx < 1; loadIdx++) {
var loadInfo = loadValidation[loadIdx];
for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
if(stopType == form.elements["results["+loadIdx+"].details["+detailIdx+"].stopType"].value * 1) {
if(form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"]) {
form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"].checked = !consolidateAppointment;
}
}
}
}
}
</script>
<style type="text/css">
.field-input-label {
white-space: nowrap;
width: 1%;
padding-right: 10px;
}
table.list td.action-section {
text-align: right;
}
.save-button {
margin-right: 10px;
}
.criteria {
margin: 0 10px 20px 10px;
padding-bottom: 20px;
border-bottom: 1px solid #ccc;
font-weight: 700;
}
.criteria tr:first-of-type {
vertical-align: top;
}
.criteria label {
font-weight: 400;
}
.success-highling {
color: #4cc14c;
font-weight: 700;
}
</style>
</head>
<body>











<script type="text/javascript" src="/c/js/menu_js_functions.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript" src="/c/js/menu/menu.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript" src="/c/js/menu/menu-search.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>

<script type="text/javascript" src="/c/js/map/mapControls.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>


<script type="text/javascript">
function popupCarrierConsole() {
popup("/console/carrierconsole.do", "Console382705", 1100, 620);
}
function ll_popupSystemNotification(percent) {
var w = 900, h = 600; 
if (window.screen) {
w = window.screen.availWidth * percent / 100;
h = window.screen.availHeight * percent / 100;
}
popup("/agent/systemnotification.do?", "notes", w, h);
}

onDocumentReady(function() {



popupMaestro();
});
</script>








<style type="text/css">
.admin-info {
padding-right: 10px;
vertical-align: top;
}
.system-logo {
text-align: center;
display: inline-block;
padding-left: 15px;
padding-right: 15px;
font-size: 15px;
color: #4A4E50;
}
img.logo {
height: 48px;
}
img.logo.logo__default {
padding: 12px 0;
}
.display-none-item {
display: none;
}
#cookie-policy-privacy-subtitle, #cookie-selection {
margin-top: 25px;
}
#cookie-policy-privacy-subtitle, #strictlyCookies, #functionalCookies, #performanceAndAnalyticsCookies {
font-size: 14px;
line-height: 180%;
}
h3{
font-weight: 600;
font-size: 18px;
}
.eto-switch {
display: -ms-flexbox;
display: flex;
position: relative;
}
.eto-switch__field {
position: absolute;
opacity: 0;
z-index: 0;
}
.eto-switch__box {
background-color: #FFF;
background-image: linear-gradient(to top, rgba(70, 129, 147, 0), rgba(70, 129, 147, .24)), linear-gradient(rgba(70, 129, 147, .24), rgba(70, 129, 147, .24));
border-radius: 2rem;
display: block;
height: 2.5rem;
position: relative;
transition-duration: .2s;
transition-property: background-color,background-image;
width: 5.5rem;
}
.eto-switch__box::before {
background-color: #FFF;
border-radius: 100%;
box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
content: '';
display: block;
height: 2.2rem;
position: absolute;
top: calc(.20rem - 1px);
transition-duration: .1s;
transition-property: left;
width: 2.2rem;
z-index: 1;
}
.eto-switch__label--on {
display: none;
}
.eto-switch__label,.eto-switch__label--off,.eto-switch__label--on {
-ms-flex: 1 1 auto;
flex: 1 1 auto;
line-height: 2.3rem;
}
.eto-switch__field:checked~.eto-switch__label--off {
display: none;
}
.eto-switch--integrated .eto-switch__label--off,.eto-switch--integrated .eto-switch__label--on {
left: 0;
margin: 2px;
position: absolute;
}
.eto-switch--integrated .eto-switch__label--off {
padding-left: 3rem;
}
.eto-switch__field:checked~.eto-switch__label--on {
display: block;
}
.eto-switch--integrated .eto-switch__label--on {
color: #FFF;
padding-left: 0.7rem;
}
.eto-switch__field:checked~.eto-switch__box {
background-color: #277AB5;
background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .24));
}
.eto-switch__field:checked~.eto-switch__box::before {
left: calc(100% - 2.525rem);
box-shadow: 0 0 0 2px transparent,0 3px 3px rgba(0, 0, 0, .3);
}
.eto-switch__field[disabled]~.eto-switch__box {
background-color: #F6F6F6;
background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .16));
cursor: default;
}
.eto-switch__field[disabled]~.eto-switch__box::before {
background-color: #E5E8EB;
box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
}
</style>
<div class="primary-header">
<div>
<a href="http://www.blujaysolutions.com/" target="_top">
<img
class="logo logo__default"
alt="Transportation Management v. tm4sprd07-web04-chg:master:2025-04-02_10-50-32"
src="/images/logos/e2open_logo.svg"
/>
</a>
<div class="system-title">
Transportation Management
</div>










</div>


<div class="items">

<a href="javascript:_webhelp();" title="Help" class="item">
<span role="tooltip" class="icon-span item-image"><svg class="icon  ic_help_header" focusable="false"><use xlink:href="#ic_help_header" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span>
</a>

<a href="/tmshealthcheck/tmshealthcheck.do" title="TMS Health Status" class="item">
<span role="tooltip" class="icon-span item-image"><svg class="icon  ic_monitor_heart" focusable="false"><use xlink:href="#ic_monitor_heart" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span>
</a>
<a href="javascript:_customerSupport();" title="Customer Support" class="item">
<span role="tooltip" class="icon-span item-image"><svg class="icon  ic_support_agent" focusable="false"><use xlink:href="#ic_support_agent" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span>
</a>


<a href="/agent/webmessages.do?query.current=true" title="Messages" class="item">
<span role="tooltip" class="icon-span item-image"><svg class="icon  ic_bell" focusable="false"><use xlink:href="#ic_bell" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span>
</a>
<div class="item user" id="headerUserItem">
<div class="item-image">
<div title="My Account" class="user-name">Brooke.Carroll<span role="tooltip" class="icon-span"><svg class="icon  ic_account_circle" focusable="false"><use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span></div>
</div>
<ul class="popover use-show-class" id="headerUserPopover">
<li>
<div class="defaults-label">Your defaults</div>
<table class="user-defaults">
<tbody>
<tr>
<td>Company:</td>
<td>FETCH FREIGHT LLC</td>
</tr>
<tr>
<td>Load Group:</td>
<td>THE J. M. SMUCKER COMPANY</td>
</tr>


</tbody>
</table>
</li>
<li class="link">
<a href="/security/managepassword.do">Account Information</a>
</li>


<li class="link">
<a href="javascript:populate()">PRIVACY SETTINGS</a>
</li>

<li class="link"><a href="/security/LogOut.jsp?dt=*************">



Log Out


</a></li>
</ul>
</div>
</div>
</div>
<div id="cookieDialog" class="display-none-item">
</div>
<script>
function headerInit() {
var headerUserItem = document.getElementById('headerUserItem');
var menuUserPopover = new Popover(document.getElementById('headerUserPopover'), headerUserItem);
headerUserItem.addEventListener('click', function() {
menuUserPopover.show();
});
}
onDocumentReady(headerInit);
function populate(){
jQuery('#headerUserPopover').hide();
ll.util.callAjaxAction('/pages/cookieconsent',
function(response) {
if (response != null) {
var cookies = [];
var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
if(response.cookieGroups.length > 0){
cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
response.cookieGroups.forEach((cookie) =>{
cookies.push(cookie.id);
cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
var checked = cookie.value ? "checked" : "";
var disabled = !cookie.enabled ? "disabled" : "";
cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
});
cookieHtml += "\n</table>\n</section>";
}
jQuery("#cookieDialog").html(cookieHtml);
openCookieDialog(response.title,cookies);
}
}, null, null, 'GET', null, false);
}
function openCookieDialog(title,cookies){
var dialogID = '#cookieDialog';
jQuery(dialogID).dialog({
autoOpen: true,
width: 1030,
height : 500,
modal: true,
title : title,
resizable: false,
draggable: false,
buttons: {
"Close": function() {
jQuery(dialogID).dialog("close");
},
"Save": function() {
var resCookies = cookies.map((cookie) => {
return {
id: cookie,
enabled : jQuery("#"+cookie).is(":checked")
};
})
ll.util.callAjaxAction('/pages/cookieconsent',
function(response) {
PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
jQuery(dialogID).dialog("close");
},
function(error) {
PageMessage.error('An error occurred while performing the update.');
},
null,
'POST',
{
preferences : resCookies
},
false,
'application/json'
);
}
}
});
}
</script>

<div class="menu">
<nav id="menu-root"></nav>
04/23/2025 05:18 CDT
</div>
<script type="text/javascript">

jQuery.ajax('/tmsrest/menu/json?cacheKey=18867ea6-72f2-410e-850a-42ef0ec9f72b').done(function(json) {
new Menu(document.getElementById('menu-root'), json);
});

document.body.classList.add('with-footer');

</script>








<form name="CarrierAppointmentSearchForm" method="post" action="/apptschedule/carrierappointmentsearch.do">


<input type="hidden" name="encodedPageData" value="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">
<input type="hidden" name="pageaction" value="save">
<input type="hidden" name="canCreateMiscAppts" value="false">
<input type="hidden" name="searchQO.sortBy" value="SORT_FLD_DUEDATE">
<input type="hidden" name="searchQO.sortAscending" value="true">
<input type="hidden" name="$ENABLETIMEPARSING$" value="true" />
<input type="hidden" name="$ENABLETZPARSING$" value="true" />
<div class="page-header">
    <div class="page-header__container">
        <div class="page-header__main">
            <div class="page-header__title-container">
                 <div class="page-header__page-title">Appoint Loads</div>
             <div class="page-header__page-title page-header__page-title--to-top">
                 <button type="button" tabIndex="-1" class="text page-header__to-top-button">
Return to the top of Appoint Loads                 </button>
             </div>
            </div>
        </div>




<div class="page-message" id="page-message-root">
<div class="page-message__container">
<div class="page-message__icon-container"></div>
<div class="page-message__message-container">
<ul class="page-message__messages">
<li class="page-message__message page-message__message--primary"></li>
</ul>
<div class="page-message__button-container" style="display: none;"></div>
</div>
<button type="button" class="icon-button page-message__close-button">
<span role="tooltip" class="icon-span"><svg class="icon  ic_close" focusable="false"><use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use></svg></span>
</button>
</div>
</div>
<script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script>
(function() {
var errors = [

];
var success = [

];
onDocumentReady(function() {
var oldPageMessages = document.querySelectorAll('.old-page-message');
for(var i = 0; i < oldPageMessages.length; i++) {
var oldMessage = oldPageMessages[i];
oldMessage.parentElement.removeChild(oldMessage);
}
if(errors.length) {
PageMessage.error(errors, null, null, false);
} else if(success.length) {
PageMessage.success(success, null, null, false);
}
});
})();
</script>
    </div>
</div>
<script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
<script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
<div class="criteria">
<table width="100%" class="lean_table">
<tr>
<td class="field-input-label">TMS ID(s)</td>
<td width="15%" ><input type="text" name="leanIDString" value="" class="formfieldinputs"></td>
<td width="15%" rowspan="4" nowrap>
<div>
Status
</div>
<div>
<label>
<input type="checkbox" name="searchQO.unappt" value="on" checked="checked">&nbsp;Unappointed
</label>
</div>
<div>
<label>
<input type="checkbox" name="searchQO.partAppt" value="on" checked="checked">&nbsp;Partially Appointed
</label>
</div>
<div>
<label>
<input type="checkbox" name="searchQO.fullAppt" value="on" checked="checked">&nbsp;Fully Appointed
</label>
</div>
</td>
<td width="24%" rowspan="4">
Pick Date <br />
<table class="lean_table searchcriteria">
<tr>
<td colspan="2">
<select name="quickDate" size="1" onchange="setQuickDate(this)" class="formfieldinputs"><option value="0">-- Choose --</option>
<option value="1">Today</option>
<option value="2">Yesterday</option>
<option value="3">Tomorrow</option>
<option value="4">This Week</option>
<option value="5">Last Week</option>
<option value="6">Next Week</option>
<option value="7">This Month</option>
<option value="8">Last Month</option>
<option value="9">Next Month</option></select>
</td>
</tr>
<tr>
<td class="field-input-label">Start</td>
<td><input type="text" name="pickDateStart" maxlength="10" size="7" value="" onchange="ll.util.formatAndValidateDateInput(this)" class="ll_datepicker formfieldinputs"></td>
</tr>
<tr>
<td class="field-input-label">End</td>
<td><input type="text" name="pickDateEnd" maxlength="10" size="7" value="" onchange="ll.util.formatAndValidateDateInput(this)" class="ll_datepicker formfieldinputs"></td>
</tr>
</table>
</td>
<td width="25%" rowspan="4">
Load Group<br />
<select name="searchQO.groups" multiple="multiple" size="4" style="margin-right: 5px;" class="formfieldinputs"><option value="0" class="formfieldinputs">-- All --</option>
<option value="169586" selected="selected">JM SMUCKER</option>
<option value="173575" selected="selected">MISC</option>
<option value="184269" selected="selected">THE J. M. SMUCKER COMPANY</option>
<option value="173328" selected="selected">UTZ SNACKS</option></select>
</td>
<td width="15%" rowspan="4">
<label>
<input type="checkbox" name="searchQO.myLoads" value="on">
My Loads
</label>
<div>
<button id="clearButton" class="leanbutton text" type="button" onclick="clearForm()">Clear</button>
<button id="searchButton" class="leanbutton search-button search" type="button" onclick="search()">Search</button>
</div>
</td>
</tr>
<tr>
<td>Pro #(s)</td>
<td><input type="text" name="proNumString" value="0044763" class="formfieldinputs"></td>
</tr>
<tr>
<td>Ref #(s)</td>
<td><input type="text" name="refNumString" value="" class="formfieldinputs"></td>
</tr>
<tr>
<td>Shipper</td>
<td><input type="text" name="searchQO.shipper" value="" class="formfieldinputs"></td>
</tr>
</table>
</div>


<table class="list no-stripes">
<thead>
<tr>
<th id="sortLinkLoadNum" class="sortable

">
TMS ID
</th>
<th>Notes</th>
<th>Stop<br />Type</th>
<th>Location</th>
<th id="sortLinkDueDate" class="sortable

sorted


">
Plan<br />Date
</th>
<th>Date Appt Recorded<br/>Date&nbsp;&nbsp;-&nbsp;&nbsp;Time</th>
<th>FCFS</th>
<th id="sortLinkApptDate" class="sortable

">
Appointment Start Time
</th>
<th nowrap>Appointment End Time</th>
<th>Appointment<br/>Confirmation #</th>
<th>Drop Trailer</th>

<th >Consolidate <br /> Appointment</th>

<th></th>
</tr>
</thead>
<tbody>



<tr class="resultrow1" style="vertical-align:text-top;">
<td>

<a href="javascript: noop()" id="test-load0" class="resultheaderlink" onClick="popup('/LoadReport2.jsp?inpopup=y&loadID=189382262&dt=1745403520935', 'LoadReport', 750, 500)">189382262</a>



<input type="hidden" name="results[0].pastRestrictApptDays" value="180">
<input type="hidden" name="results[0].futureRestrictApptDays" value="180">
<input type="hidden" name="results[0].loadID" value="189382262">
</td>
<td>


<a href="javascript:noop()" id="test-create0" onClick="popup('/agent/createloadnote.do?loadID=189382262', 'LoadNote', 750, 550);" class="resultheaderlink">
Create
</a>


</td>














<td>


<strong>Pick</strong>



<input type="hidden" name="results[0].details[0].stopType" value="1000">
<input type="hidden" name="results[0].details[0].detailNum" value="414022850">
<input type="hidden" name="timezone414022850" value="America/New_York" />
<input type="hidden" name="results[0].details[0].appointmentChanged" value="false" />
</td>
<td>
UTZ QUALITY FOODS - NORTH EAST LOGISTICS CENTER<br />HANOVER, PA&nbsp;17331&nbsp;US

<br /><EMAIL>


</td>
<td>



04/27/2025 00:00


</td>




<td>04/17/2025 12:07</td>
<td>&nbsp;</td>
<td>04/27/2025 09:00</td>
<td>04/27/2025 09:00</td>
<td>--</td>
<td>


Live



</td>
<td>&nbsp;</td>


<td></td>
</tr>


























<tr class="resultrow1" style="vertical-align:text-top;">
<td colspan="2">&nbsp;</td>

<td>



<strong>Drop</strong>


<input type="hidden" name="results[0].details[1].stopType" value="1001">
<input type="hidden" name="results[0].details[1].detailNum" value="414022851">
<input type="hidden" name="timezone414022851" value="America/New_York" />
<input type="hidden" name="results[0].details[1].appointmentChanged" value="false" />
</td>
<td>
IF-VISTAR/VSA - 2160<br />LAWRENCEVILLE, GA&nbsp;30043&nbsp;US

<br />RETALIXTRAFFIC.COM


</td>
<td>



04/29/2025 00:00


</td>




<td></td>
<td>&nbsp;</td>
<td>04/28/2025 10:00</td>
<td>04/28/2025 10:00</td>
<td>5285675</td>
<td>


Live



</td>
<td>&nbsp;</td>


<td></td>
</tr>


























<tr class="resultrow1" style="vertical-align:text-top;">
<td colspan="2">&nbsp;</td>

<td>



<strong>Drop</strong>


<input type="hidden" name="results[0].details[2].stopType" value="1001">
<input type="hidden" name="results[0].details[2].detailNum" value="414022852">
<input type="hidden" name="timezone414022852" value="America/Chicago" />
<input type="hidden" name="results[0].details[2].appointmentChanged" value="false" />
</td>
<td>
STACY WILLIAMS CO.<br />BIRMINGHAM, AL&nbsp;35204&nbsp;US


<br />205-780-9933


</td>
<td>



04/29/2025 00:00


</td>




<td></td>
<td>&nbsp;</td>
<td>04/29/2025 05:00</td>
<td>04/29/2025 05:00</td>
<td>--</td>
<td>


Live



</td>
<td>&nbsp;</td>


<td></td>
</tr>











<tr class="resultrow1">
<td colspan="2" style="vertical-align:bottom;">
<div><strong>Contact</strong>&nbsp;Utz Brands&nbsp;Fetch Freight</div>
</td>
<td colspan="2">
<strong>Pro #</strong><br>


0044763



</td>
<td colspan="2">
<strong>Vehicle #</strong><br>


--



</td>
<td colspan="2">
<strong>
Trailer #
</strong>
<br />


--



</td>
<td colspan="5">
<strong>Driver</strong><br>


--



</td>
</tr>
<tr class="resultrow1">
<td colspan="2"/>
<td colspan="2">
<strong>
Container #
</strong>
<br />


--



</td>


<td colspan="2">
<strong>Vehicle License Plate #</strong><br>


--



</td>
<td colspan="2">
<strong>Trailer License Plate #</strong><br>


--



</td>
<td colspan="5">&nbsp;</td>



</tr>

<tr class="resultrow1">
<td colspan="13">







<NOBR>

<strong>PRO #</strong>&nbsp;










0044763
<br />


</NOBR>


<NOBR>

<strong>Appt #</strong>&nbsp;




5285675


</NOBR>
</td>
</tr>


<tr class="resultrow1">
<td colspan="13"><strong>Shipments</strong>&nbsp;S035790641,S035674046</td>
</tr>




</tbody>
<tfoot>
<tr>
<td colspan="5">

1 result found.
&nbsp;
 
</td>

</tr>
</tfoot>
</table>





</form>


</body>
</html>
