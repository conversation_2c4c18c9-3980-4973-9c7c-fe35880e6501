<!doctype html>
<html
  id="Login"
  class="transactional-page TridactylThemeMidnight no-applicationcache geolocation history postmessage websockets localstorage no-websqldatabase webworkers hashchange audio canvas canvastext video webgl cssgradients supports svgclippaths smil formvalidation textshadow indexeddb indexeddb-deletedatabase cssanimations borderimage csscolumns csscolumns-width csscolumns-span csscolumns-fill csscolumns-gap csscolumns-rule csscolumns-rulecolor csscolumns-rulestyle csscolumns-rulewidth no-csscolumns-breakbefore no-csscolumns-breakafter no-csscolumns-breakinside flexbox flexboxlegacy no-cssreflections csstransforms3d csstransitions novalidate"
  style=""
>
  <head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <title>Transportation Management - User Login</title>

    <meta name="msapplication-config" content="/browserconfig.xml" />
    <script type="text/javascript" src="login_files/utility.js"></script>

    <link
      rel="icon"
      type="image/x-icon"
      href="https://na-app.tms.e2open.com/favicon.ico"
      id="faviconICO"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://na-app.tms.e2open.com/favicon.png"
      id="faviconPNG"
    />
    <script type="text/javascript">
      var faviconICO = document.querySelector("#faviconICO");
      var faviconPNG = document.querySelector("#faviconPNG");
      var darkModeListener = function (event) {
        if (event.matches) {
          faviconICO.setAttribute("href", "/favicon_dark.ico");
          faviconPNG.setAttribute("href", "/favicon_dark.png");
        } else {
          faviconICO.setAttribute("href", "/favicon.ico");
          faviconPNG.setAttribute("href", "/favicon.png");
        }
      };
      var darkModePreference = window.matchMedia(
        "(prefers-color-scheme: dark)",
      );
      if (darkModePreference.addEventListener) {
        darkModePreference.addEventListener("change", function (e) {
          if (e.matches) {
            activateDarkMode();
          }
        });
      } else {
        darkModePreference.addListener(function (e) {
          if (e.matches) {
            activateDarkMode();
          }
        });
      }
      darkModeListener({
        matches:
          window.matchMedia &&
          window.matchMedia("(prefers-color-scheme: dark)").matches,
      });
    </script>

    <link rel="stylesheet" type="text/css" href="login_files/unity.css" />
    <link rel="stylesheet" type="text/css" href="login_files/new_styles.css" />
    <link rel="stylesheet" type="text/css" href="login_files/components.css" />

    <link rel="stylesheet" type="text/css" href="login_files/fallback.css" />

    <link
      rel="stylesheet"
      type="text/css"
      href="login_files/intermediate_icons.css"
    />

    <script type="text/javascript">
      function popupMaestro() {}
      onDocumentReady(function () {
        var iconContainer = document.createElement("div");
        jQuery(document.body).prepend(iconContainer);
        jQuery(iconContainer).load(
          "/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a",
        );
        popupMaestro();
      });
    </script>

    <script type="text/javascript">
      var useComponents = true;
      var newStylesComponents = false;
      var useETOComponents = false;
      var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
    </script>
    <script type="text/javascript" src="login_files/i18next.js"></script>

    <script type="text/javascript" src="login_files/lib.js"></script>
    <script type="text/javascript" src="login_files/all_en_US.js"></script>

    <script type="text/javascript" src="login_files/react.js"></script>
    <script type="text/javascript" src="login_files/tms.components.js"></script>

    <script type="text/javascript" src="login_files/components.js"></script>

    <script type="text/javascript">
      jQuery.noConflict();
      var LeanActionFormName = "loginForm";
      var LeanActionFullPath = "\/security\/login.do";
      var $LeanActionForm;
      onDocumentReady(function () {
        $LeanActionForm = jQuery("form[name=loginForm]");

        if (get_browser() === "msie") {
          jQuery("html").addClass("ie");
        }
        if ("noValidate" in document.createElement("form")) {
          jQuery("html").addClass("novalidate");
        }
        var $dwrImageDiv = jQuery("#dwrImageDiv");
        if (!$dwrImageDiv.length) {
          $dwrImageDiv = jQuery(
            '<div id="dwrImageDiv" style="display:none;"/>',
          );
          $dwrImageDiv.append(
            jQuery(
              '<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' +
                validate_activityIndicator +
                '">',
            ),
          );
          jQuery("body").append($dwrImageDiv);
        }

        hasExpiredSessionWarningBeenDisplayed = false;
      });
    </script>

    <script type="text/javascript">
      function useCompanyLogin() {
        jQuery('input[name="loginPageAction"]').val("companyLogin");
      }
      function useUserLogin() {
        jQuery('input[name="loginPageAction"]').val("login");
      }
      function changeLanguage(field) {
        if (field.component.value != "") {
          var form = jQuery("#languageForm").get(0);
          form.elements["lastAction"].value =
            form.elements["loginPageAction"].value;
          form.elements["loginPageAction"].value = "changeLanguage";
          form.submit();
        }
      }
      function createCookie(name, value) {
        document.cookie = name + "=" + value + "; path=/";
      }
      function readCookie(name) {
        var nameEQ = name + "=";
        var cookieArray = document.cookie.split(";");
        for (var i = 0; i < cookieArray.length; i++) {
          var cookie = cookieArray[i];
          while (cookie.charAt(0) == " ") {
            cookie = cookie.substring(1, cookie.length);
          }
          if (cookie.indexOf(nameEQ) == 0) {
            return cookie.substring(nameEQ.length, cookie.length);
          }
        }
        return null;
      }
      function eraseCookie(name) {
        createCookie(name, "", -1);
      }
      function areCookiesEnabled() {
        var found = false;
        createCookie("testing", "Hello");
        if (readCookie("testing") != null) {
          found = true;
          eraseCookie("testing");
        }
        return found;
      }
      function checkCapsLock(e) {
        var s = String.fromCharCode(e.which);
        if (s.toUpperCase() === s && s.toLowerCase() !== s && !e.shiftKey) {
          jQuery("#capslockdiv").fadeIn("fast");
        } else {
          jQuery("#capslockdiv").hide();
        }
      }
      onDocumentReady(function () {
        new Tabs(document.querySelector(".tabs"));
        ll.util.triggerEvent(jQuery('input[name="userID"]'), "focus");

        if (get_browser() === "chrome") {
          document.getElementById("userID").component.autofilled = true;
          document.getElementById("password").component.autofilled = true;
          document.getElementById("companyDomain").component.autofilled = true;
        }
        if (!areCookiesEnabled()) {
          var message =
            "<b>Usability Notice \- You appear to have cookies disabled on your browser.<\/b><br \/><br \/>Having cookies enabled is required to use Transportation Management<br \/>Please enable cookies in your browser preferences to continue.";
          PageMessage.warn(ll.util.formatMessage(message), null, null, true);
        }

        if (
          (get_browser() === "msie" &&
            parseInt(get_browser_version(), 10) < 11) ||
          (get_browser() === "firefox" &&
            parseInt(get_browser_version(), 10) < 45)
        ) {
          var message =
            "<b>Browser Compatibility Notice \- You appear to be using an outdated browser.<\/b><br\/><br\/>Using an outdated browser may lead you to encounter compatibility issues.<br \/>We recommend you upgrade to {1}IE 11{0} or above, or {2}Mozilla Firefox 45{0} or above.<br \/>If you need assistance in the upgrade process, we recommend that you contact your IT department.";
          PageMessage.warn(
            ll.util.formatMessage(
              message,
              "</a>",
              "<a href='http://www.microsoft.com/windows/ie/default.htm'>",
              "<a href='https://www.mozilla.com/firefox/'>",
              "<a href='https://www.google.com/chrome'>",
            ),
            null,
            null,
            true,
          );
        }

        var warnings = [];
        var errors = [];
        if (!isEmpty(errors)) {
          PageMessage.error(errors, null, null, true);
        } else if (!isEmpty(warnings)) {
          PageMessage.warn(warnings, null, null, true);
        }
        jQuery("#userLoginForm").ll_validate({
          prepSubmit: function () {
            jQuery("#userSubmit").attr("disabled", "disabled");
            return true;
          },
        });
        jQuery("#companyLoginForm").ll_validate({
          prepSubmit: function () {
            jQuery("#companySubmit").attr("disabled", "disabled");
            return true;
          },
        });
      });
    </script>

    <style type="text/css">
      #loginPanel {
        background: white;
        border-radius: 10px;
        padding: 0.76923rem;
        min-height: 400px;
        max-width: 400px;
        min-width: 300px;
        margin-right: 150px;
        margin-left: 150px;
        box-shadow: 0px 2px 8px rgba(54, 54, 54, 0.345);
      }
      #panelGrid {
        display: inline;
      }
      /* This is to adjust margin when the browser is displayed on just half the screen. */
      @media (max-width: 1070px) {
        #loginPanel {
          margin-left: 15px;
          margin-right: 15px;
        }
      }
      #logos {
        text-align: center;
        max-height: 400px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-flow: column;
        flex-grow: 1;
      }
      .login-stripe {
        background: rgba(255, 255, 255, 0.7);
        width: 100%;
        position: absolute;
        top: 50%;
        left: 0px;
        transform: translateY(-50%);
        padding: 30px;
        display: inline-flex;
        flex-wrap: nowrap;
      }
      .blujay-logo {
        height: 30%;
        width: 60%;
      }
      .blujay-logo a {
        height: 100%;
        width: 100%;
        display: inline-block;
      }
      .system-logo {
        text-align: center;
        display: inline-block;
        padding-left: 15px;
        padding-right: 15px;
        font-size: 25px;
        margin-top: 60px;
        color: #4a4e50;
      }
      .tms-logo {
        height: 20%;
        margin-top: 45px;
      }
      img {
        height: 100%;
        max-width: 100%;
      }
      .topper {
        background: #545f66;
        padding-top: 5px;
      }
      .url-instruction {
        text-align: center;
      }
      .url-spacer {
        height: 2em;
      }
      body {
        background: url(/images/BG_Globe.jpg) no-repeat center fixed;
        background-size: cover;
        overflow: none;
      }
      .tab-content {
        padding: 0.76923rem;
      }
      .example-domain {
        color: #4a4e50;
      }
      .submitbut {
        text-align: right;
      }
      #defaultLanguage {
        background: transparent;
        border-color: white;
        color: white;
      }
      .language-label {
        color: white;
        text-align: right;
      }
      #tmsComponent-becomeUser {
        padding-top: 10px;
      }
      #page-message-root.page-message--error,
      #page-message-root.page-message--warn {
        position: absolute;
        top: 45px;
        overflow: none;
      }
      #page-message-root.page-message--warn a {
        color: black;
        font-weight: bold;
      }
      #capslockdiv {
        height: 30px;
        display: none;
      }
      svg.icon.ic_single_select {
        fill: white;
      }
      hr {
        color: #bbbfc2;
      }
    </style>
    <style type="text/css">
      @media print {
        .TridactylStatusIndicator {
          display: none !important;
        }
      }
    </style></head
  ><iframe
    class="cleanslate hidden"
    src="login_files/commandline.html"
    id="cmdline_iframe"
    loading="lazy"
    style="height: 0px !important"
  ></iframe>
  <body>
    <div>
      <svg
        id="icon_library"
        xmlns="http://www.w3.org/2000/svg"
        style="display: none"
      >
        <symbol id="ic_account_circle" viewBox="0 0 24 24">
          <path
            d="M12 2a10 10 0 100 20 10 10 0 000-20zM7.07 18.28c.43-.9 3.05-1.78 4.93-1.78s4.51.88 4.93 1.78a7.9 7.9 0 01-9.86 0zm11.29-1.45c-1.43-1.74-4.9-2.33-6.36-2.33s-4.93.59-6.36 2.33A8.01 8.01 0 1120 12c0 1.82-.62 3.49-1.64 4.83zM12 6c-1.94 0-3.5 1.56-3.5 3.5S10.06 13 12 13s3.5-1.56 3.5-3.5S13.94 6 12 6zm0 5a1.5 1.5 0 110-3 1.5 1.5 0 010 3z"
          ></path>
        </symbol>

        <symbol id="ic_add" viewBox="0 0 24 24">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path>
        </symbol>

        <symbol id="ic_add_circle" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"
          ></path>
        </symbol>

        <symbol id="ic_add_circle_outline" viewBox="0 0 24 24">
          <path
            d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
          ></path>
        </symbol>

        <symbol
          id="ic_add_floating_action_button"
          fill="#282828"
          viewBox="0 0 24 24"
        >
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path>
        </symbol>

        <symbol id="ic_add" viewBox="0 0 24 24">
          <path
            d="M20 1v3h3v2h-3v3h-2V6h-3V4h3V1h2zm-8 12c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2-9.75V7h3v3h2.92c.05.39.08.79.08 1.2 0 3.32-2.67 7.25-8 11.8-5.33-4.55-8-8.48-8-11.8a7.95 7.95 0 0 1 10-7.95z"
          ></path>
        </symbol>

        <symbol id="ic_add_location" viewBox="0 0 24 24">
          <path
            d="M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7zm4 8h-3v3h-2v-3H8V8h3V5h2v3h3v2z"
          ></path>
        </symbol>

        <symbol id="ic_add_location_alt" viewBox="0 0 24 24">
          <path
            d="M20 1v3h3v2h-3v3h-2V6h-3V4h3V1h2zm-8 12c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2-9.75V7h3v3h2.92c.05.39.08.79.08 1.2 0 3.32-2.67 7.25-8 11.8-5.33-4.55-8-8.48-8-11.8C4 6.22 7.8 3 12 3c.68 0 1.35.08 2 .25z"
          ></path>
        </symbol>

        <symbol id="ic_add_reversed" viewBox="0 0 24 24">
          <path
            d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_air" viewBox="0 0 24 24">
          <path d="M10.18 9"></path>
          <path
            d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"
          ></path>
        </symbol>

        <symbol id="ic_alarm" viewBox="0 0 24 24">
          <path
            d="M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12.5 8H11v6l4.75 2.85.75-1.23-4-2.37V8zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"
          ></path>
        </symbol>

        <symbol id="ic_appointment_confirm" viewBox="0 0 24 24">
          <path
            d="M16.53 11.06L15.47 10l-4.88 4.88-2.12-2.12-1.06 1.06L10.59 17l5.94-5.94zM19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11z"
          ></path>
        </symbol>

        <symbol id="ic_appointment_deny" viewBox="0 0 24 24">
          <path
            d="M9.31 17l2.44-2.44L14.19 17l1.06-1.06-2.44-2.44 2.44-2.44L14.19 10l-2.44 2.44L9.31 10l-1.06 1.06 2.44 2.44-2.44 2.44L9.31 17zM19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11z"
          ></path>
        </symbol>

        <symbol id="ic_appointment_notes" viewBox="0 0 24 24">
          <path
            d="M17 10H7v2h10v-2zm2-7h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zm-5-5H7v2h7v-2z"
          ></path>
        </symbol>

        <symbol id="ic_attach_file" viewBox="0 0 24 24">
          <path
            d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"
          ></path>
        </symbol>

        <symbol id="ic_bell" viewBox="0 0 24 24">
          <path
            d="M12 22a2 2 0 0 0 2-2h-4a2 2 0 0 0 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4a1.5 1.5 0 1 0-3 0v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"
          ></path>
        </symbol>

        <symbol id="ic_bell_alert" viewBox="0 0 24 24">
          <path
            d="M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29V4A2,2 0 0,1 12,2A2,2 0 0,1 14,4V4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21H14M11,8V13H13V8H11M11,15V17H13V15H11Z"
          ></path>
        </symbol>

        <symbol id="ic_build" viewBox="0 0 24 24">
          <path
            d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"
          ></path>
        </symbol>

        <symbol id="ic_building" viewBox="0 0 24 24">
          <path
            d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"
          ></path>
        </symbol>

        <symbol id="ic_calculator" viewBox="0 0 24 24">
          <path
            d="M7,2H17A2,2 0 0,1 19,4V20A2,2 0 0,1 17,22H7A2,2 0 0,1 5,20V4A2,2 0 0,1 7,2M7,4V8H17V4H7M7,10V12H9V10H7M11,10V12H13V10H11M15,10V12H17V10H15M7,14V16H9V14H7M11,14V16H13V14H11M15,14V16H17V14H15M7,18V20H9V18H7M11,18V20H13V18H11M15,18V20H17V18H15Z"
          ></path>
        </symbol>

        <symbol id="ic_calendar_clock" viewBox="0 0 24 24">
          <path
            d="M15,13H16.5V15.82L18.94,17.23L18.19,18.53L15,16.69V13M19,8H5V19H9.67C9.24,18.09 9,17.07 9,16A7,7 0 0,1 16,9C17.07,9 18.09,9.24 19,9.67V8M5,21C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3H6V1H8V3H16V1H18V3H19A2,2 0 0,1 21,5V11.1C22.24,12.36 23,14.09 23,16A7,7 0 0,1 16,23C14.09,23 12.36,22.24 11.1,21H5M16,11.15A4.85,4.85 0 0,0 11.15,16C11.15,18.68 13.32,20.85 16,20.85A4.85,4.85 0 0,0 20.85,16C20.85,13.32 18.68,11.15 16,11.15Z"
          ></path>
        </symbol>

        <symbol id="ic_camera" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="3.2"></circle>
          <path
            d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z"
          ></path>
        </symbol>

        <symbol id="ic_chat" viewBox="0 0 24 24">
          <path
            d="M21 6h-2v9H6v2c0 .55.45 1 1 1h11l4 4V7c0-.55-.45-1-1-1zm-4 6V3c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v14l4-4h10c.55 0 1-.45 1-1z"
          ></path>
        </symbol>

        <symbol id="ic_chart_pie" viewBox="0 0 24 24">
          <path
            d="M11 2v20c-5.1-.5-9-4.8-9-10s3.9-9.5 9-10m2 0v9h9c-.5-4.8-4.2-8.5-9-9m0 11v9c4.7-.5 8.5-4.2 9-9h-9z"
          ></path>
        </symbol>

        <symbol id="ic_close" viewBox="0 0 24 24">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
          ></path>
        </symbol>

        <symbol id="ic_columns" viewBox="0 0 24 24">
          <path
            d="M8.994 19.459h5.294V5.694H8.994V19.46zm-6.353 0h5.294V5.694H2.64V19.46zM15.347 5.694V19.46h5.294V5.694h-5.294z"
          ></path>
        </symbol>

        <symbol id="ic_comment" viewBox="0 0 24 24">
          <path
            d="M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4-.01-18zM18 14H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"
          ></path>
        </symbol>

        <symbol id="ic_consoles" viewBox="0 0 24 24">
          <path
            d="M13 13v8h8v-8h-8zM3 21h8v-8H3v8zM3 3v8h8V3H3zm13.66-1.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65z"
          ></path>
        </symbol>

        <symbol id="ic_contacts" viewBox="0 0 24 24">
          <path
            d="M20 0H4v2h16V0zM4 24h16v-2H4v2zM20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-8 2.75c1.24 0 2.25 1.01 2.25 2.25s-1.01 2.25-2.25 2.25S9.75 10.24 9.75 9 10.76 6.75 12 6.75zM17 17H7v-1.5c0-1.67 3.33-2.5 5-2.5s5 .83 5 2.5V17z"
          ></path>
        </symbol>

        <symbol id="ic_consoles_blujay" viewBox="0 0 20 20">
          <path
            d="M10 12.5c-.2 0-.4 0-.6.1l-2.1-4-.9.5 2.1 4c-.4.4-.6.9-.6 1.5 0 1.1.9 2.1 2.1 2.1s2.1-.9 2.1-2.1c0-1.2-1-2.1-2.1-2.1zm0 3.1c-.6 0-1-.5-1-1 0-.6.5-1 1-1 .6 0 1 .5 1 1s-.4 1-1 1z"
          ></path>
          <path
            d="M10 2.6C4.8 2.6.6 6.8.6 12c0 1.6.4 3.3 1.3 4.7.1.2.3.3.5.3.1 0 .2 0 .3-.1l2.2-1.3-.5-.9-1.8 1c-.5-1-.8-2.1-.8-3.2h1.5v-1H1.7c.1-1.1.4-2.2.9-3.2l1.3.7.5-.9-1.3-.7c.6-1 1.4-1.8 2.3-2.4l.8 1.3.9-.5-.8-1.3c1-.5 2-.8 3.2-.9v1.5h1V3.7c1.1.1 2.2.4 3.2.9l-.8 *******.8-1.4c.9.6 1.7 1.4 2.3 2.3l-*******.9 1.3-.8c.5 1 .8 2 .9 3.2h-1.5v1h1.5c-.1 1.1-.3 2.2-.8 3.2l-1.8-1-.5.9 2.2 1.3c.*******.7-.2.8-1.4 1.3-3 1.3-4.7 0-5.1-4.2-9.3-9.4-9.3z"
          ></path>
        </symbol>

        <symbol id="ic_copy" viewBox="0 0 24 24">
          <path
            d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
          ></path>
        </symbol>

        <symbol id="ic_copy_to_clipboard" viewBox="0 0 24 24">
          <path
            d="M4 7v14h14v2H4a2 2 0 01-2-2V7h2m16-4a2 2 0 012 2v12a2 2 0 01-2 2H8a2 2 0 01-2-2V5c0-1.1.9-2 2-2h3.2A3 3 0 0114 1a3 3 0 012.8 2H20m-6 0a1 1 0 00-1 1c0 .5.4 1 1 1s1-.5 1-1-.4-1-1-1m-4 4V5H8v12h12V5h-2v2h-8z"
          ></path>
        </symbol>

        <symbol id="ic_create_optimization_run" viewBox="0 0 24 24">
          <path
            d="M11 10c.7 1.7 3.1 3 4.3 3v7.6c0 .7-.6 1.3-1.4 1.3H4.7c-.8 0-1.4-.6-1.4-1.3v-9.3c0-.7.6-1.3 1.4-1.3H11zm-5.6 4.3v6h1.9v-6H5.4zm3.1-2.6v8.5h1.7v-8.5H8.5zm3 4.9v3.6H13v-3.6h-1.7zM16 2.2a4.9 4.9 0 1 1 0 9.8 4.9 4.9 0 0 1 0-9.8zm2.6 5.5V6.6h-2.1V4.4h-1v2.2h-2.2v1h2.1v2.2h1V7.7h2.2z"
          ></path>
        </symbol>

        <symbol id="ic_credit" viewBox="0 0 24 24">
          <path
            d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"
          ></path>
        </symbol>

        <symbol id="ic_crosshairs" viewBox="0 0 24 24">
          <path
            d="M11 2v2.07A8 8 0 004.07 11H2v2h2.07A8 8 0 0011 19.93V22h2v-2.07A8 8 0 0019.93 13H22v-2h-2.07A8 8 0 0013 4.07V2m-2 4.08V8h2V6.09c2.5.41 4.5 2.41 4.92 4.91H16v2h1.91A6.01 6.01 0 0113 17.92V16h-2v1.91A6.01 6.01 0 016.08 13H8v-2H6.09A6.01 6.01 0 0111 6.08M12 11a1 1 0 100 2 1 1 0 000-2z"
            fill="#0079AC"
            fill-rule="nonzero"
          ></path>
        </symbol>

        <symbol id="ic_currency" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"
          ></path>
        </symbol>

        <symbol id="ic_customer_care" viewBox="0 0 24 24">
          <path
            d="M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z"
          ></path>
        </symbol>

        <symbol id="ic_data_density" viewBox="0 0 24 24">
          <path d="M2.5 4v3h5v12h3V7h5V4h-13zm19 5h-9v3h3v7h3v-7h3V9z"></path>
        </symbol>

        <symbol id="ic_data_density_colossal" viewBox="0 0 24 24">
          <path
            id="a"
            d="M5 3h14a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2zm0 10h14a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4c0-1 .9-2 2-2z"
          ></path>
        </symbol>

        <symbol id="ic_data_density_compact" viewBox="0 0 24 24">
          <path
            d="M4 3h16c.6 0 1 .4 1 1v1c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V4c0-.6.4-1 1-1zm0 5h16c.6 0 1 .4 1 1v1c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V9c0-.6.4-1 1-1zm0 5h16c.6 0 1 .4 1 1v1c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1v-1c0-.6.4-1 1-1zm0 5h16c.6 0 1 .4 1 1v1c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1v-1c0-.6.4-1 1-1z"
          ></path>
        </symbol>

        <symbol id="ic_data_density_cozy" viewBox="0 0 24 24">
          <path
            d="M4.5 3h15c.8 0 1.5.7 1.5 1.5v2c0 .8-.7 1.5-1.5 1.5h-15C3.7 8 3 7.3 3 6.5v-2C3 3.7 3.7 3 4.5 3zm0 6.5h15c.8 0 1.5.7 1.5 1.5v2c0 .8-.7 1.5-1.5 1.5h-15c-.8 0-1.5-.7-1.5-1.5v-2c0-.8.7-1.5 1.5-1.5zm0 6.5h15c.8 0 1.5.7 1.5 1.5v2c0 .8-.7 1.5-1.5 1.5h-15c-.8 0-1.5-.7-1.5-1.5v-2c0-.8.7-1.5 1.5-1.5z"
          ></path>
        </symbol>

        <symbol id="ic_date" viewBox="0 0 24 24">
          <path
            d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
          ></path>
        </symbol>

        <symbol id="ic_date_range" viewBox="0 0 24 24">
          <path
            d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"
          ></path>
        </symbol>

        <symbol id="ic_decrease" viewBox="0 0 24 24">
          <path
            fill="#40835F"
            d="M10.5,11.5 L10.5,7.5 L13.5,7.5 L13.5,11.5 L17,11.5 L12,16.5 L7,11.5 L10.5,11.5 Z"
          ></path>
        </symbol>

        <symbol id="ic_delete_all" viewBox="0 0 24 24">
          <path
            d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
          ></path>
        </symbol>

        <symbol id="ic_delete_one" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11H7v-2h10v2z"
          ></path>
        </symbol>

        <symbol id="ic_delete_one_reversed" viewBox="0 0 24 24">
          <path
            d="M7 11v2h10v-2H7zm5-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_description" viewBox="0 0 24 24">
          <path
            d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
          ></path>
        </symbol>

        <symbol id="ic_directions" viewBox="0 0 24 24">
          <path
            d="m21.41 10.59-7.99-8c-.78-.78-2.05-.78-2.83 0l-8.01 8c-.78.78-.78 2.05 0 2.83l8.01 8c.78.78 2.05.78 2.83 0l7.99-8c.79-.79.79-2.05 0-2.83zM13.5 14.5V12H10v3H8v-4c0-.55.45-1 1-1h4.5V7.5L17 11l-3.5 3.5z"
          ></path>
        </symbol>

        <symbol id="ic_download" viewBox="0 0 24 24">
          <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"></path>
        </symbol>

        <symbol id="ic_drag_handle" viewBox="0 0 24 24">
          <path
            d="M11 18a2 2 0 0 1-2 2 2 2 0 0 1-2-2c0-1.1.9-2 2-2a2 2 0 0 1 2 2zm-2-8a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm0-6a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm6 4a2 2 0 0 0 2-2 2 2 0 0 0-2-2 2 2 0 0 0-2 2c0 1.1.9 2 2 2zm0 2a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm0 6a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2z"
          ></path>
        </symbol>

        <symbol id="ic_edit" viewBox="0 0 24 24">
          <path
            d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
          ></path>
        </symbol>

        <symbol id="ic_edit_date" viewBox="0 0 24 24">
          <path
            d="M17.486619,5.3280711 L16.6526279,5.3280711 L16.6526279,3.66008887 L14.9846457,3.66008887 L14.9846457,5.3280711 L8.31271679,5.3280711 L8.31271679,3.66008887 L6.64473457,3.66008887 L6.64473457,5.3280711 L5.81074346,5.3280711 C4.88954231,5.3280711 4.14276123,6.07485218 4.14276123,6.99605332 L4.14276123,18.6719289 C4.14276123,19.59313 4.88954231,20.3399111 5.81074346,20.3399111 L9.98069902,20.3399111 L9.98069902,18.6719289 L5.81074346,18.6719289 L5.81074346,9.49802666 L17.486619,9.49802666 L17.486619,10.3320178 L19.1546013,10.3320178 L19.1546013,6.99605332 C19.1546013,6.07485218 18.4078202,5.3280711 17.486619,5.3280711 Z M19.738395,13.9598791 L18.9044039,14.7938702 L17.1947221,13.125888 L18.0287133,12.2918969 C18.1955115,12.1167588 18.4790685,12.1084188 18.6708864,12.2918969 L19.738395,13.3594055 C19.8968533,13.5262037 19.8968533,13.7930809 19.738395,13.9598791 Z M11.6486812,18.6218894 L16.7110073,13.5679033 L18.4206891,15.2358855 L13.3667029,20.3399111 L11.6486812,20.3399111 L11.6486812,18.6218894 Z"
          ></path>
        </symbol>

        <symbol id="ic_error" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
          ></path>
        </symbol>

        <symbol id="ic_error_reversed" viewBox="0 0 24 24">
          <path
            d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_expand" viewBox="0 0 24 24">
          <path
            d="M15 3l2.3 2.3-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3zM3 9l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3 9 3H3zm6 12l-2.3-2.3 2.89-2.87-1.42-1.42L5.3 17.3 3 15v6zm12-6l-2.3 2.3-2.87-2.89-1.42 1.42 2.89 2.87L15 21h6z"
          ></path>
        </symbol>

        <symbol id="ic_export_filing" viewBox="0 0 24 24">
          <path
            d="M19 3h-4.2c-.4-1.2-1.5-2-2.8-2s-2.4.8-2.8 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.6 0 1 .5 1 1s-.4 1-1 1-1-.4-1-1 .4-1 1-1zm0 6l5 5h-3v4h-4v-4H7l5-5z"
          ></path>
        </symbol>

        <symbol id="ic_favorite" viewBox="0 0 24 24">
          <path
            d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
          ></path>
        </symbol>

        <symbol id="ic_fiber_manual_record" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="8"></circle>
        </symbol>

        <symbol id="ic_filter" viewBox="0 0 24 24">
          <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
        </symbol>

        <symbol id="ic_filter_alt" viewBox="0 0 24 24">
          <path
            d="M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61z"
          ></path>
        </symbol>

        <symbol id="ic_flag" viewBox="0 0 24 24">
          <path d="M14.4 6L14 4H5v17h2v-7h5.6l.4 2h7V6z"></path>
        </symbol>

        <symbol id="ic_folder" viewBox="0 0 24 24">
          <path
            d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"
          ></path>
        </symbol>

        <symbol id="ic_group" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM8 17.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5zM9.5 8c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5S9.5 9.38 9.5 8zm6.5 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
          ></path>
        </symbol>

        <symbol
          id="ic_handle"
          viewBox="0 0 20 20"
          style="opacity: 0.3"
          preserveAspectRatio="none"
        >
          <path fill="#CCC" d="M6 3h1v14H6z"></path>
          <path fill="#666" d="M7 3h1v14H7z"></path>
          <path fill="#CCC" d="M9 3h1v14H9z"></path>
          <path fill="#666" d="M10 3h1v14h-1z"></path>
          <path fill="#CCC" d="M12 3h1v14h-1z"></path>
          <path fill="#666" d="M13 3h1v14h-1z"></path>
        </symbol>

        <symbol id="ic_heart_blujay" viewBox="0 0 20 20">
          <path
            d="M10 17.8c-.1 0-.2 0-.2-.1l-.6-.3C6.3 15.7.9 12.5.9 7.3.9 4.4 3.3 2 6.2 2c1.4.1 2.8.7 3.8 1.7 1-1 2.4-1.7 3.8-1.7 2.9 0 5.3 2.4 5.3 5.3 0 5.2-5.4 8.4-8.3 10.1l-.6.3c0 .1-.1.1-.2.1zM6.2 3C3.8 3 1.9 5 1.9 7.3 1.9 12 7 15 9.7 16.6l.3.2.3-.2C13 15 18.1 12 18.1 7.3 18.1 5 16.2 3 13.8 3c-1.3 0-2.6.6-3.4 1.7-.2.3-.6.3-.8 0C8.8 3.7 7.5 3 6.2 3z"
          ></path>
        </symbol>

        <symbol id="ic_help" viewBox="0 0 24 24">
          <path
            d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"
          ></path>
        </symbol>

        <symbol id="ic_help_header" viewBox="0 0 24 24">
          <path fill="none" d="M0 0h24v24H0z"></path>
          <path
            d="M12 2a10 10 0 100 20 10 10 0 000-20zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92A3.4 3.4 0 0013 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8a4 4 0 118 0c0 .88-.36 1.68-.93 2.25z"
          ></path>
        </symbol>

        <symbol id="ic_hidden" viewBox="0 0 24 24">
          <path
            d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"
          ></path>
        </symbol>

        <symbol id="ic_history" viewBox="0 0 24 24">
          <path
            d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"
          ></path>
        </symbol>

        <symbol id="ic_house" viewBox="0 0 24 24">
          <path
            d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z"
          ></path>
        </symbol>

        <symbol id="ic_import_filing" viewBox="0 0 24 24">
          <path
            d="M19 3h-4.2c-.4-1.2-1.5-2-2.8-2s-2.4.8-2.8 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.6 0 1 .5 1 1s-.4 1-1 1-1-.4-1-1 .4-1 1-1zm0 15l-5-5h3V9h4v4h3l-5 5z"
          ></path>
        </symbol>

        <symbol id="ic_increase" viewBox="0 0 24 24">
          <polygon
            fill="#D0011B"
            points="10.5 12.5 7 12.5 12 7.5 17 12.5 13.5 12.5 13.5 16.5 10.5 16.5"
          ></polygon>
        </symbol>

        <symbol id="ic_information" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
          ></path>
        </symbol>

        <symbol id="ic_information_reversed" viewBox="0 0 24 24">
          <path
            d="M11 17h2v-6h-2v6zm1-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zM11 9h2V7h-2v2z"
          ></path>
        </symbol>

        <symbol id="ic_key" viewBox="0 -960 960 960">
          <path
            d="M280-400q-33 0-56.5-23.5T200-480q0-33 23.5-56.5T280-560q33 0 56.5 23.5T360-480q0 33-23.5 56.5T280-400Zm0 160q-100 0-170-70T40-480q0-100 70-170t170-70q67 0 121.5 33t86.5 87h352l120 120-180 180-80-60-80 60-85-60h-47q-32 54-86.5 87T280-240Zm0-80q56 0 98.5-34t56.5-86h125l58 41 82-61 71 55 75-75-40-40H435q-14-52-56.5-86T280-640q-66 0-113 47t-47 113q0 66 47 113t113 47Z"
          ></path>
        </symbol>

        <symbol id="ic_keyboard" viewBox="0 0 24 24">
          <path
            d="M20 5H4c-1.1 0-1.99.9-1.99 2L2 17c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-9 3h2v2h-2V8zm0 3h2v2h-2v-2zM8 8h2v2H8V8zm0 3h2v2H8v-2zm-1 2H5v-2h2v2zm0-3H5V8h2v2zm9 7H8v-2h8v2zm0-4h-2v-2h2v2zm0-3h-2V8h2v2zm3 3h-2v-2h2v2zm0-3h-2V8h2v2z"
          ></path>
        </symbol>

        <symbol id="ic_launch_new_window" viewBox="0 0 24 24">
          <path
            d="M19 19H5V5h7V3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"
          ></path>
        </symbol>

        <symbol id="ic_list" viewBox="0 0 24 24">
          <path
            d="M2 13h2v.5H3v1h1v.5H2v1h3v-4H2v1zm0-5h1.8L2 10.1v.9h3v-1H3.2L5 7.9V7H2v1zm1-2h1V2H2v1h1v3zm4-3v2h9V3H7zm0 12h9v-2H7v2zm0-5h9V8H7v2z"
          ></path>
        </symbol>

        <symbol id="ic_loader" viewBox="0 0 24 24">
          <path
            d="M12 21.6a9.6 9.6 0 1 0-9.6-9.3h2.4V12a7.2 7.2 0 1 1 7.2 7.2v2.4z"
          ></path>
        </symbol>

        <symbol id="ic_local_delivery" viewBox="0 0 24 24">
          <path
            d="M12.917 4.583c.916 0 1.666-.75 1.666-1.666 0-.917-.75-1.667-1.666-1.667-.917 0-1.667.75-1.667 1.667 0 .916.75 1.666 1.667 1.666zM4.167 10A4.126 4.126 0 0 0 0 14.167a4.126 4.126 0 0 0 4.167 4.166 4.126 4.126 0 0 0 4.166-4.166A4.126 4.126 0 0 0 4.167 10zm0 7.083c-1.584 0-2.917-1.333-2.917-2.916 0-1.584 1.333-2.917 2.917-2.917 1.583 0 2.916 1.333 2.916 2.917 0 1.583-1.333 2.916-2.916 2.916zM9 8.75l2-2 .667.667c1.083 1.083 2.5 1.75 4.25 1.75V7.5c-1.25 0-2.25-.5-3-1.25l-1.584-1.583c-.416-.334-.833-.5-1.333-.5s-.917.166-1.167.5L6.5 7c-.333.333-.5.75-.5 1.167 0 .5.167.916.5 1.166l2.667 2.334v4.166h1.666v-5.166L9 8.75zM15.833 10a4.126 4.126 0 0 0-4.166 4.167 4.126 4.126 0 0 0 4.166 4.166A4.126 4.126 0 0 0 20 14.167 4.126 4.126 0 0 0 15.833 10zm0 7.083c-1.583 0-2.916-1.333-2.916-2.916 0-1.584 1.333-2.917 2.916-2.917 1.584 0 2.917 1.333 2.917 2.917 0 1.583-1.333 2.916-2.917 2.916z"
          ></path>
          <rect width="5" height="5" y="4" rx="1"></rect>
        </symbol>

        <symbol id="ic_local_shipping" viewBox="0 0 24 24">
          <path
            d="M20 8h-3V4H3a2 2 0 0 0-2 2v11h2a3 3 0 1 0 6 0h6a3 3 0 1 0 6 0h2v-5l-3-4zM6 18.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm13.5-9 1.96 2.5H17V9.5h2.5zm-1.5 9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
          ></path>
        </symbol>

        <symbol id="ic_local_shipping_alt" viewBox="0 0 24 24">
          <path
            d="M4 8h3V4h14a2 2 0 0 1 2 2v11h-2a3 3 0 1 1-6 0H9a3 3 0 1 1-6 0H1v-5l3-4Zm14 10.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm-13.5-9L2.54 12H7V9.5H4.5Zm1.5 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
          ></path>
        </symbol>

        <symbol id="ic_local_shipping_alt" viewBox="0 0 24 24">
          <path
            d="M4 8h3V4h14a2 2 0 0 1 2 2v11h-2a3 3 0 1 1-6 0H9a3 3 0 1 1-6 0H1v-5l3-4Zm14 10.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm-13.5-9L2.54 12H7V9.5H4.5Zm1.5 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
          ></path>
        </symbol>

        <symbol id="ic_star" viewBox="0 -960 960 960">
          <path
            d="m233-120 65-281L80-590l288-25 112-265 112 265 288 25-218 189 65 281-247-149-247 149Z"
          ></path>
        </symbol>

        <symbol id="ic_location_pin" viewBox="0 0 24 24">
          <path
            d="M12 2a7 7 0 0 0-7 7c0 1.74.5 3.37 1.41 4.84.95 1.54 2.2 2.86 3.16 *********.81 1.45 1.17 **********.47 1.5 1.26 1.5s1-.95 1.25-1.5c.37-.81.7-1.51 1.17-2.26.96-1.53 2.21-2.85 3.16-4.4A9.02 9.02 0 0 0 19 9a7 7 0 0 0-7-7zm0 9.75a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5z"
          ></path>
        </symbol>

        <symbol id="ic_lock" viewBox="0 0 24 24">
          <path
            d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"
          ></path>
        </symbol>

        <symbol id="ic_map" viewBox="0 0 24 24">
          <path
            d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"
          ></path>
        </symbol>

        <symbol id="ic_marine" viewBox="0 0 24 24">
          <path
            d="M20 21c-1.39 0-2.78-.47-4-1.32-2.44 1.71-5.56 1.71-8 0C6.78 20.53 5.39 21 4 21H2v2h2c1.38 0 2.74-.35 4-.99a8.752 8.752 0 0 0 8 0c1.26.65 2.62.99 4 .99h2v-2h-2zM3.95 19H4c1.6 0 3.02-.88 4-2 .98 1.12 2.4 2 4 2s3.02-.88 4-2c.98 1.12 2.4 2 4 2h.05l1.89-6.68c.08-.26.06-.54-.06-.78s-.34-.42-.6-.5L20 10.62V6c0-1.1-.9-2-2-2h-3V1H9v3H6c-1.1 0-2 .9-2 2v4.62l-1.29.42a1.007 1.007 0 0 0-.66 1.28L3.95 19zM6 6h12v3.97L12 8 6 9.97V6z"
          ></path>
        </symbol>

        <symbol id="ic_merge" viewBox="0 0 24 24">
          <path
            d="M 17 20.455 L 18.41 19.045 L 15 15.635 L 13.59 17.045 L 17 20.455 Z M 7.5 8.045 L 11 8.045 L 11 13.635 L 5.59 19.045 L 7 20.455 L 13 14.455 L 13 8.045 L 16.5 8.045 L 12 3.545 L 7.5 8.045 Z"
          ></path>
        </symbol>

        <symbol id="ic_messages" viewBox="0 0 24 24">
          <path
            d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
          ></path>
        </symbol>

        <symbol id="ic_messages_blujay" viewBox="0 0 20 20">
          <path
            d="M15.9 16.8H4.1c-1.3 0-2.3-1-2.3-2.3V5.4c0-1.3 1-2.3 2.3-2.3H16c1.3 0 2.3 1 2.3 2.3v9.1c-.1 1.3-1.1 2.3-2.4 2.3zM4.1 4.1c-.8 0-1.4.6-1.4 1.4v9.1c0 .8.6 1.4 1.4 1.4H16c.8 0 1.4-.6 1.4-1.4V5.4c0-.8-.6-1.4-1.4-1.4H4.1z"
          ></path>
          <path d="M10 10.5L2 5.8l.5-.8L10 9.5 17.5 5l.5.8z"></path>
        </symbol>

        <symbol id="ic_monitor_heart" viewBox="0 0 24 24">
          <path
            d="M20 4H4a2 2 0 0 0-2 2v3h2V6h16v3h2V6a2 2 0 0 0-2-2zM20 18H4v-3H2v3c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2v-3h-2v3z"
          ></path>
          <path
            d="M14.89 7.55c-.34-.68-1.45-.68-1.79 0L10 13.76l-1.11-2.21A.99.99 0 0 0 8 11H2v2h5.38l1.72 3.45c.18.34.52.55.9.55s.72-.21.89-.55L14 10.24l1.11 2.21c.17.34.51.55.89.55h6v-2h-5.38l-1.73-3.45z"
          ></path>
        </symbol>

        <symbol id="ic_more_horizontal" viewBox="0 0 24 24">
          <path
            d="M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
          ></path>
        </symbol>

        <symbol id="ic_more_vertical" viewBox="0 0 24 24">
          <path
            d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
          ></path>
        </symbol>

        <symbol id="ic_my_account" viewBox="0 0 24 24">
          <path
            d="M3 5v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2H5a2 2 0 0 0-2 2zm12 4c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zm-9 8c0-2 4-3.1 6-3.1s6 1.1 6 3.1v1H6v-1z"
          ></path>
        </symbol>

        <symbol id="ic_my_account_blujay" viewBox="0 0 20 20">
          <ellipse cx="10" cy="9.5" fill="none" rx="3.2" ry="3.7"></ellipse>
          <path
            fill="none"
            d="M13.8 14.2c-.4-.4-.8-.7-1.2-.9-.7.6-1.6.9-2.5.9-.9 0-1.8-.4-2.5-.9-1.3.7-2.3 2-2.6 3.4 1.4 1.1 3.2 1.8 5.2 1.8 1.9 0 3.7-.7 5.2-1.8-.5-.9-.9-1.8-1.6-2.5z"
          ></path>
          <path
            fill="none"
            d="M10 1.5c-4.7 0-8.5 3.8-8.5 8.5 0 2.3.9 4.4 2.5 6 .5-1.4 1.5-2.7 2.8-3.5-.6-.8-1-1.9-1-3 0-2.6 1.9-4.8 4.2-4.8s4.2 2.1 4.2 4.8c0 1.2-.4 2.2-1 3 .******* 1.2 1 .7.7 1.2 1.6 1.5 2.5 1.5-1.5 2.4-3.6 2.4-6 .2-4.7-3.6-8.5-8.3-8.5z"
          ></path>
          <path
            d="M10 .4C4.7.4.4 4.7.4 10s4.3 9.6 9.6 9.6 9.6-4.3 9.6-9.6S15.3.4 10 .4zM4.8 16.7c.3-1.5 1.3-2.7 2.6-******* 1.6.9 ******* 0 1.8-.4 2.5-.*******.5 *******.7 1.2 1.6 1.4 2.5-1.4 1.1-3.2 1.8-5.2 1.8-1.7 0-3.5-.7-5-1.8zm5.2-3.5c-1.8 0-3.2-1.7-3.2-3.7S8.2 5.8 10 5.8c1.8 0 3.2 1.7 3.2 3.7s-1.4 3.7-3.2 3.7zm6 2.8c-.3-.9-.8-1.8-1.5-2.5-.4-.4-.8-.7-1.2-1 .6-.8 1-1.9 1-3 0-2.6-1.9-4.8-4.2-4.8S5.8 6.8 5.8 9.5c0 1.2.4 2.2 1 3-1.3.8-2.3 2-2.8 3.5-1.5-1.5-2.5-3.6-2.5-6 0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5c0 2.3-.9 4.4-2.5 6z"
          ></path>
        </symbol>

        <symbol id="ic_my_tasks" viewBox="0 0 24 24">
          <path
            d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm0 4c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm6 12H6v-1.4c0-2 4-3.1 6-3.1s6 1.1 6 3.1V19z"
          ></path>
        </symbol>

        <symbol id="ic_neighbor" viewBox="0 0 24 24">
          <path
            d="M19 2H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h4l3 3 3-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 3.3c1.49 0 2.7 1.21 2.7 2.7 0 1.49-1.21 2.7-2.7 2.7-1.49 0-2.7-1.21-2.7-2.7 0-1.49 1.21-2.7 2.7-2.7zM18 16H6v-.9c0-2 4-3.1 6-3.1s6 1.1 6 3.1v.9z"
          ></path>
        </symbol>

        <symbol id="ic_optimization_search" viewBox="0 0 24 24">
          <path
            d="M9.7 10c.7 1.7 3 3 4.2 3v7.6c0 .7-.6 1.3-1.3 1.3H3.3c-.7 0-1.3-.6-1.3-1.3v-9.3c0-.7.6-1.3 1.3-1.3h6.4zm-5.6 4.3v6h1.8v-6H4.1zm3-2.6v8.5H9v-8.5H7.2zm3 4.9v3.6h1.7v-3.6h-1.7zm8.6-5.6l3 4.3-1.2 1-3-4.4v-.6l-.1-.2c-1 .5-2.2.8-3.4.6a4.9 4.9 0 1 1 4-1l.1.2h.6zm-3.8-.7a3.4 3.4 0 1 0 0-6.8 3.4 3.4 0 0 0 0 6.8z"
            id="a"
          ></path>
        </symbol>

        <symbol id="ic_out_of_time" viewBox="0 0 24 24">
          <path
            d="M12 6c3.87 0 7 3.13 7 7 0 .84-.16 1.65-.43 2.4l1.52 1.52c.58-1.19.91-2.51.91-3.92 0-4.97-4.03-9-9-9-1.41 0-2.73.33-3.92.91L9.6 6.43C10.35 6.16 11.16 6 12 6zm10-.28l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM2.92 2.29L1.65 3.57 2.98 4.9l-1.11.93 1.42 1.42 1.11-.94.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.02 9 9 9 2.25 0 4.31-.83 5.89-2.2l2.2 2.2 1.27-1.27L3.89 3.27l-.97-.98zm13.55 16.1C15.26 19.39 13.7 20 12 20c-3.87 0-7-3.13-7-7 0-1.7.61-3.26 1.61-4.47l9.86 9.86zM8.02 3.28L6.6 1.86l-.86.71 1.42 1.42.86-.71z"
          ></path>
        </symbol>

        <symbol id="ic_over_the_road" viewBox="0 0 24 24">
          <path
            d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"
          ></path>
        </symbol>

        <symbol id="ic_parcel" viewBox="0 0 24 24">
          <path
            d="M9.7 5h8a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5zm3 1v4h2V6h-2z"
          ></path>
          <path
            d="M3 8a3 3 0 0 0 3-3V3.542C6 2.476 4.143 2 3 2c-.952 0-3 .607-3 1.477V5a3 3 0 0 0 3 3zm9 8.985v-2.667c-2.053.027-4.12-1-5.427-2.44l-1.72-1.906a2.579 2.579 0 0 0-.813-.6.102.102 0 0 1-.04-.014c-.467-.266-1-.4-1.587-.346-1.4.12-2.413 1.36-2.413 2.76V19h5.333v-4.615c1.72 1.427 4.334 2.587 6.667 2.6z"
          ></path>
          <path
            d="M1 .8h4a1 1 0 0 1 1 1v2H0v-2a1 1 0 0 1 1-1zm5 1.7h2V4H6z"
          ></path>
        </symbol>

        <symbol id="ic_pause_circle" viewBox="0 0 24 24">
          <path
            d="M9 16h2V8H9v8zm3-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm1-4h2V8h-2v8z"
          ></path>
        </symbol>

        <symbol id="ic_person_pin_circle" viewBox="0 0 24 24">
          <path
            d="M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7zm0 2a2 2 0 1 1 0 4 2 2 0 0 1 0-4zm0 10a4.78 4.78 0 0 1-4-2.15c.02-1.32 2.67-2.05 4-2.05s3.98.73 4 2.05A4.78 4.78 0 0 1 12 14z"
          ></path>
        </symbol>

        <symbol id="ic_phone" viewBox="0 0 24 24">
          <path
            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"
          ></path>
        </symbol>

        <symbol id="ic_place" viewBox="0 0 24 24">
          <path
            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
          ></path>
        </symbol>

        <symbol id="ic_playlist_add" viewBox="0 0 24 24">
          <path
            d="M2 16h8v-2H2m16 0v-4h-2v4h-4v2h4v4h2v-4h4v-2m-8-8H2v2h12m0 2H2v2h12v-2Z"
          ></path>
        </symbol>

        <symbol id="ic_print" viewBox="0 0 24 24">
          <path
            d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"
          ></path>
        </symbol>

        <symbol id="ic_public" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
          ></path>
        </symbol>

        <symbol id="ic_question_mark" viewBox="0 0 24 24">
          <path
            d="M11.07 12.85c.77-1.39 2.25-2.21 3.11-3.44.91-1.29.4-3.7-2.18-3.7-1.69 0-2.52 1.28-2.87 2.34L6.54 6.96C7.25 4.83 9.18 3 11.99 3c2.35 0 3.96 1.07 4.78 2.41.7 1.15 1.11 3.3.03 4.9-1.2 1.77-2.35 2.31-2.97 3.45-.25.46-.35.76-.35 2.24h-2.89c-.01-.78-.13-2.05.48-3.15zM14 20c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2z"
          ></path>
        </symbol>

        <symbol id="ic_rail" viewBox="0 0 24 24">
          <path
            d="M12 2c-4 0-8 .5-8 4v9.5C4 17.43 5.57 19 7.5 19L6 20.5v.5h2.23l2-2H14l2 2h2v-.5L16.5 19c1.93 0 3.5-1.57 3.5-3.5V6c0-3.5-3.58-4-8-4zM7.5 17c-.83 0-1.5-.67-1.5-1.5S6.67 14 7.5 14s1.5.67 1.5 1.5S8.33 17 7.5 17zm3.5-7H6V6h5v4zm2 0V6h5v4h-5zm3.5 7c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"
          ></path>
        </symbol>

        <symbol id="ic_refresh" viewBox="0 0 24 24">
          <path
            d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
          ></path>
        </symbol>

        <symbol id="ic_remove_circle_outline" viewBox="0 0 24 24">
          <path
            d="M7 11v2h10v-2H7zm5-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_road" viewBox="0 0 24 24">
          <path
            d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"
          ></path>
        </symbol>

        <symbol id="ic_route" viewBox="0 0 24 24">
          <path
            id="Shape"
            d="M21.1633333,8.66666667 C21.1633333,9.58333333 20.4133333,10.3333333 19.4966666,10.3333333 C19.3466666,10.3333333 19.2049999,10.3166667 19.0716666,10.275 L16.1049999,13.2333333 C16.1466666,13.3666667 16.1633333,13.5166667 16.1633333,13.6666667 C16.1633333,14.5833333 15.4133333,15.3333333 14.4966666,15.3333333 C13.5799999,15.3333333 12.8299999,14.5833333 12.8299999,13.6666667 C12.8299999,13.5166667 12.8466666,13.3666667 12.8883333,13.2333333 L10.7633333,11.1083333 C10.6299999,11.15 10.4799999,11.1666667 10.3299999,11.1666667 C10.1799999,11.1666667 10.0299999,11.15 9.89666659,11.1083333 L6.10499992,14.9083333 C6.14666659,15.0416667 6.16333326,15.1833333 6.16333326,15.3333333 C6.16333326,16.25 5.41333326,17 4.49666659,17 C3.57999992,17 2.82999992,16.25 2.82999992,15.3333333 C2.82999992,14.4166667 3.57999992,13.6666667 4.49666659,13.6666667 C4.64666659,13.6666667 4.78833326,13.6833333 4.92166659,13.725 L8.72166659,9.93333333 C8.67999992,9.8 8.66333326,9.65 8.66333326,9.5 C8.66333326,8.58333333 9.41333326,7.83333333 10.3299999,7.83333333 C11.2466666,7.83333333 11.9966666,8.58333333 11.9966666,9.5 C11.9966666,9.65 11.9799999,9.8 11.9383333,9.93333333 L14.0633333,12.0583333 C14.1966666,12.0166667 14.3466666,12 14.4966666,12 C14.6466666,12 14.7966666,12.0166667 14.9299999,12.0583333 L17.8883333,9.09166667 C17.8466666,8.95833333 17.8299999,8.81666667 17.8299999,8.66666667 C17.8299999,7.75 18.5799999,7 19.4966666,7 C20.4133333,7 21.1633333,7.75 21.1633333,8.66666667 Z"
          ></path>
        </symbol>

        <symbol id="ic_run" viewBox="0 0 24 24">
          <path
            d="M10 16.5l6-4.5-6-4.5v9zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_save" viewBox="0 0 24 24">
          <path
            d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"
          ></path>
        </symbol>

        <symbol id="ic_search" viewBox="0 0 24 24">
          <path
            d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
          ></path>
        </symbol>

        <symbol id="ic_search_append" viewBox="0 0 24 24">
          <path
            d="M15.22 13.93l4.28 4.3-1.28 1.27-4.29-4.28v-.68l-.23-.24a5.57 5.57 0 11.6-.6l.24.23h.68z"
          ></path>
          <path
            d="M6.33 11a3.85 3.85 0 113.96 2.93A4.25 4.25 0 006.33 11z"
            fill="#FFF"
          ></path>
          <path d="M6.25 19.5a4.25 4.25 0 110-8.5 4.25 4.25 0 010 8.5z"></path>
          <path
            fill="#FFF"
            d="M9 15.8v-1.1H6.8v-2.2H5.7v2.2H3.5v1.1h2.2V18h1.1v-2.2z"
          ></path>
        </symbol>

        <symbol id="ic_search_rerun" viewBox="0 0 24 24">
          <path
            d="M11 6c1.38 0 2.63.56 3.54 1.46L12 10h6V4l-2.05 2.05A6.976 6.976 0 0 0 11 4c-3.53 0-6.43 2.61-6.92 6H6.1A5 5 0 0 1 11 6zm5.64 9.14A6.89 6.89 0 0 0 17.92 12H15.9a5 5 0 0 1-4.9 4c-1.38 0-2.63-.56-3.54-1.46L10 12H4v6l2.05-2.05A6.976 6.976 0 0 0 11 18c1.55 0 2.98-.51 4.14-1.36L20 21.49 21.49 20l-4.85-4.86z"
          ></path>
        </symbol>

        <symbol id="ic_search_rerun_append" viewBox="0 0 24 24">
          <path
            d="M14.3 13.71l.22.22h.68l4.28 4.3-1.28 1.27-4.29-4.28v-.68l-.22-.23a5.56 5.56 0 11.33-8.17l1.64-1.64v4.87h-4.88l2.25-2.24a4.12 4.12 0 00-2.94-1.24 4.18 4.18 0 103.93 5.57h1.45a5.58 5.58 0 01-1.18 2.25z"
          ></path>
          <path d="M6.25 19.5a4.25 4.25 0 110-8.5 4.25 4.25 0 010 8.5z"></path>
          <path
            fill="#FFF"
            d="M9 15.8v-1.1H6.8v-2.2H5.7v2.2H3.5v1.1h2.2V18h1.1v-2.2z"
          ></path>
        </symbol>

        <symbol id="ic_select_all" viewBox="0 0 24 24">
          <path
            d="M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2zM7 17h10V7H7v10zm2-8h6v6H9V9z"
          ></path>
        </symbol>

        <symbol id="ic_send" viewBox="0 0 24 24">
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
        </symbol>

        <symbol id="ic_settings" viewBox="0 0 24 24">
          <path
            d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"
          ></path>
        </symbol>

        <symbol id="ic_shop" viewBox="0 0 24 24">
          <path
            d="M8.4,17.4 C7.41,17.4 6.609,18.21 6.609,19.2 C6.609,20.19 7.41,21 8.4,21 C9.39,21 10.2,20.19 10.2,19.2 C10.2,18.21 9.39,17.4 8.4,17.4 Z M3,3 L3,4.8 L4.8,4.8 L8.04,11.631 L6.825,13.836 C6.681,14.088 6.6,14.385 6.6,14.7 C6.6,15.69 7.41,16.5 8.4,16.5 L19.2,16.5 L19.2,14.7 L8.778,14.7 C8.652,14.7 8.553,14.601 8.553,14.475 L8.58,14.367 L9.39,12.9 L16.095,12.9 C16.77,12.9 17.364,12.531 17.67,11.973 L20.892,6.132 C20.964,6.006 21,5.853 21,5.7 C21,5.205 20.595,4.8 20.1,4.8 L6.789,4.8 L5.943,3 L3,3 Z M17.4,17.4 C16.41,17.4 15.609,18.21 15.609,19.2 C15.609,20.19 16.41,21 17.4,21 C18.39,21 19.2,20.19 19.2,19.2 C19.2,18.21 18.39,17.4 17.4,17.4 Z"
          ></path>
        </symbol>

        <symbol id="ic_show_less" viewBox="0 0 24 24">
          <path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"></path>
        </symbol>

        <symbol id="ic_show_more" viewBox="0 0 24 24">
          <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
        </symbol>

        <symbol id="ic_signature" viewBox="0 0 24 24">
          <path d="M0 15h18v3H0z"></path>
          <path
            d="M13.38 5.5L10.5 2.62 3 10.12V13h2.88l7.5-7.5zm2.47-2.48c.2-.2.2-.51 0-.71L13.68.14c-.2-.2-.51-.2-.71 0L11.62 1.5l2.88 2.88 1.35-1.36z"
          ></path>
        </symbol>

        <symbol id="ic_signature_waived" viewBox="0 0 24 24">
          <path
            d="M8.03333333,10.2916667 C7.81666667,9.89166667 7.70833333,9.43333333 7.70833333,8.9 C7.70833333,8.39166667 7.81666667,7.93333333 8.04166667,7.50833333 C8.25833333,7.09166667 8.56666667,6.73333333 8.96666667,6.43333333 C9.36666667,6.14166667 9.84166667,5.90833333 10.3833333,5.74166667 C10.9333333,5.58333333 11.5416667,5.5 12.2,5.5 C12.875,5.5 13.4833333,5.59166667 14.0416667,5.78333333 C14.5916667,5.96666667 15.0666667,6.23333333 15.45,6.56666667 C15.8416667,6.9 16.1416667,7.3 16.35,7.75833333 C16.5583333,8.21666667 16.6666667,8.71666667 16.6666667,9.26666667 L14.1583333,9.26666667 C14.1583333,9.00833333 14.1166667,8.775 14.0333333,8.55833333 C13.9583333,8.33333333 13.8333333,8.15 13.6666667,7.99166667 C13.5,7.83333333 13.2916667,7.71666667 13.0416667,7.625 C12.7916667,7.54166667 12.4916667,7.49166667 12.1583333,7.49166667 C11.8333333,7.49166667 11.5416667,7.525 11.3,7.6 C11.0583333,7.675 10.8583333,7.775 10.7,7.9 C10.5416667,8.03333333 10.4166667,8.18333333 10.3333333,8.35833333 C10.25,8.53333333 10.2083333,8.71666667 10.2083333,8.90833333 C10.2083333,9.30833333 10.4166667,9.64166667 10.825,9.91666667 C11.1416667,10.125 11.4666667,10.3166667 12,10.5 L8.15833333,10.5 C8.11666667,10.4333333 8.06666667,10.3583333 8.03333333,10.2916667 Z M19.5,13 L19.5,11.3333333 L4.5,11.3333333 L4.5,13 L12.5166667,13 C12.6666667,13.0583333 12.85,13.1166667 12.975,13.1666667 C13.2833333,13.3083333 13.525,13.45 13.7,13.5916667 C13.875,13.7333333 13.9916667,13.8916667 14.0583333,14.0666667 C14.1166667,14.2333333 14.15,14.425 14.15,14.6416667 C14.15,14.8333333 14.1083333,15.0166667 14.0333333,15.1916667 C13.9583333,15.3583333 13.8416667,15.5083333 13.6833333,15.6333333 C13.525,15.7583333 13.3333333,15.85 13.0916667,15.925 C12.85,15.9916667 12.5666667,16.0333333 12.25,16.0333333 C11.8916667,16.0333333 11.5583333,16 11.2666667,15.925 C10.975,15.85 10.7166667,15.7333333 10.5083333,15.575 C10.3,15.4166667 10.1333333,15.2083333 10.0166667,14.95 C9.9,14.6916667 9.80833333,14.3166667 9.80833333,13.9416667 L7.33333333,13.9416667 C7.33333333,14.4 7.4,14.8833333 7.53333333,15.2583333 C7.66666667,15.6333333 7.84166667,15.9666667 8.075,16.2666667 C8.30833333,16.5583333 8.575,16.8166667 8.89166667,17.0333333 C9.2,17.25 9.54166667,17.4333333 9.90833333,17.575 C10.275,17.7166667 10.6583333,17.825 11.0583333,17.9 C11.4583333,17.9666667 11.8583333,18.0083333 12.2583333,18.0083333 C12.925,18.0083333 13.5333333,17.9333333 14.075,17.775 C14.6166667,17.6166667 15.0833333,17.4 15.4666667,17.1166667 C15.85,16.8333333 16.15,16.475 16.3583333,16.0583333 C16.5666667,15.6416667 16.675,15.1666667 16.675,14.6333333 C16.675,14.1333333 16.5916667,13.6833333 16.4166667,13.2916667 C16.375,13.2 16.325,13.1 16.275,13.0166667 L19.5,13.0166667 L19.5,13 Z"
          ></path>
        </symbol>

        <symbol id="ic_single_select" viewBox="0 0 24 24">
          <path d="M7 10l5 5 5-5z"></path>
        </symbol>

        <symbol id="ic_skip_backward" viewBox="0 0 24 24">
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path>
        </symbol>

        <symbol id="ic_skip_forward" viewBox="0 0 24 24">
          <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path>
        </symbol>

        <symbol id="ic_skip_to_beginning" viewBox="0 0 24 24">
          <path
            d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"
          ></path>
        </symbol>

        <symbol id="ic_skip_to_end" viewBox="0 0 24 24">
          <path
            d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"
          ></path>
        </symbol>

        <symbol id="ic_sort_ascend" viewBox="0 0 24 24">
          <path
            d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_sort_descend" viewBox="0 0 24 24">
          <path
            d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"
          ></path>
        </symbol>

        <symbol id="ic_split" viewBox="0 0 24 24">
          <path
            d="M14,4 L16.29,6.29 L13.41,9.17 L14.83,10.59 L17.71,7.71 L20,10 L20,4 L14,4 Z M10,4 L4,4 L4,10 L6.29,7.71 L11,12.41 L11,20 L13,20 L13,11.59 L7.71,6.29 L10,4 Z"
          ></path>
        </symbol>

        <symbol id="ic_success" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
          ></path>
        </symbol>

        <symbol id="ic_success_reversed" viewBox="0 0 24 24">
          <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"></path>
        </symbol>

        <symbol id="ic_support" viewBox="0 0 24 24">
          <path
            d="M19.04 8.84A6.94 6.94 0 0012 2a6.94 6.94 0 00-7.04 6.84C3.96 8.96 3 9.6 3 11.08v3.58c0 1.67 1.2 2.28 2.35 2.28h1.13c.22 0 .4-.17.4-.38V9.2a.39.39 0 00-.4-.38h-.74c0-3.36 2.8-6.08 6.26-6.08a6.17 6.17 0 016.26 6.08h-.74c-.22 0-.4.17-.4.38v7.35c0 .2.18.38.4.38h.74c-.1 2.6-1.4 3.22-3.98 3.33V20a.77.77 0 00-.78-.76h-2.46a.77.77 0 00-.79.76v1.24c0 .42.36.76.79.76h2.47c.43 0 .78-.34.78-.76v-.22c2.48-.11 4.62-.6 4.75-4.12 1-.12 1.96-.76 1.96-2.24V11.1c0-1.5-.95-2.13-1.96-2.25zM6.1 9.55v6.62h-.76c-.58 0-1.57-.2-1.57-1.52v-3.58c0-1.3.98-1.52 1.57-1.52h.76zm7.42 11.12v.57h-2.47v-1.23h2.47v.66zm6.7-6.01c0 1.3-.97 1.52-1.56 1.52h-.74v-6.6h.76c.58 0 1.57.2 1.57 1.52l-.02 3.56z"
          ></path>
        </symbol>

        <symbol id="ic_support_agent" viewBox="0 0 24 24">
          <path
            d="M21 12.22a9 9 0 1 0-18 .06c-.6.34-1 .98-1 1.72v2c0 1.1.9 2 2 2h1v-6.1a7 7 0 1 1 14 0V19h-8v2h8a2 2 0 0 0 2-2v-1.22c.59-.31 1-.92 1-1.64v-2.3c0-.7-.41-1.31-1-1.62z"
          ></path>
          <circle cx="9" cy="13" r="1"></circle>
          <circle cx="15" cy="13" r="1"></circle>
          <path
            d="M18 11.03a6.04 6.04 0 0 0-11.98 1.42 8.07 8.07 0 0 0 4.86-5.89A8.04 8.04 0 0 0 18 11.03z"
          ></path>
        </symbol>

        <symbol id="ic_survey_dissatisfied_very" viewBox="0 0 24 24">
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 3c-2.33 0-4.31 1.46-5.11 3.5h10.22c-.8-2.04-2.78-3.5-5.11-3.5z"
          ></path>
        </symbol>

        <symbol id="ic_survey_dissatisfied" viewBox="0 0 24 24">
          <circle cx="15.5" cy="9.5" r="1.5"></circle>
          <circle cx="8.5" cy="9.5" r="1.5"></circle>
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-6c-2.33 0-4.32 1.45-5.12 3.5h1.67c.69-1.19 1.97-2 3.45-2s2.75.81 3.45 2h1.67c-.8-2.05-2.79-3.5-5.12-3.5z"
          ></path>
        </symbol>

        <symbol id="ic_survey_neutral" viewBox="0 0 24 24">
          <path d="M9 14h6v1.5H9z"></path>
          <circle cx="15.5" cy="9.5" r="1.5"></circle>
          <circle cx="8.5" cy="9.5" r="1.5"></circle>
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
          ></path>
        </symbol>

        <symbol id="ic_survey_satisfied" viewBox="0 0 24 24">
          <circle cx="15.5" cy="9.5" r="1.5"></circle>
          <circle cx="8.5" cy="9.5" r="1.5"></circle>
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-4c-1.48 0-2.75-.81-3.45-2H6.88c.8 2.05 2.79 3.5 5.12 3.5s4.32-1.45 5.12-3.5h-1.67c-.7 1.19-1.97 2-3.45 2z"
          ></path>
        </symbol>

        <symbol id="ic_survey_satisfied_very" viewBox="0 0 24 24">
          <path
            d="M11.99 2C6.47 2 2 6.47 2 12s4.47 10 9.99 10S22 17.53 22 12 17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm1-10.06L14.06 11l1.06-1.06L16.18 11l1.06-1.06-2.12-2.12zm-4.12 0L9.94 11 11 9.94 8.88 7.82 6.76 9.94 7.82 11zM12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"
          ></path>
        </symbol>

        <symbol id="ic_system_health" viewBox="0 0 24 24">
          <path
            d="M21.66 12.19h-1.7c.7-1.64.63-2.96.37-3.86a4.55 4.55 0 00-6.12-2.98c-1.09.41-1.81.86-2.13 1.1a9.05 9.05 0 00-2.13-1.1 4.55 4.55 0 00-6.12 2.98c-.27.96-.37 2.4.53 4.2H2.3a.3.3 0 00-.3.32c0 .18.13.31.3.31h2.42c.1.18.2.35.34.53 1.98 2.92 6.27 6.1 7.05 6.3V20l.04-.02h.06v-.02c.66-.33 4.79-3.06 6.96-6.3.2-.28.38-.58.53-.87h2a.3.3 0 00.3-.3c0-.18-.17-.3-.34-.3zm-3.04 1.15c-2.02 3.02-5.73 5.45-6.52 5.98-.82-.53-4.51-2.98-6.54-5.98l-.12-.18h2.8c.11 0 .22-.06.28-.17l1.27-2.57 2 5.24c.04.11.17.2.29.2h.02c.13 0 .24-.1.28-.24l1.75-6.14.93 3.1c.04.11.13.2.25.21.11.02.23-.04.3-.13l.74-1.11.63 1.09c.06.1.15.15.27.15h1.7c-.1.2-.22.37-.33.55zm.68-1.15h-1.86l-.78-1.37a.3.3 0 00-.27-.15c-.11 0-.2.04-.26.14l-.65.97-1.05-3.49c-.03-.13-.17-.21-.3-.23-.13 0-.27.1-.3.23l-1.77 6.26-1.92-5.03a.29.29 0 00-.27-.2.3.3 0 00-.28.18l-1.5 3.02H5.05a5.37 5.37 0 01-.63-4.01 3.9 3.9 0 015.33-2.56c1.38.53 2.13 1.13 2.14 1.13.12.1.27.1.39 0 0 0 .74-.6 2.14-1.13a3.92 3.92 0 015.32 2.55 5.23 5.23 0 01-.45 3.69z"
          ></path>
        </symbol>

        <symbol id="ic_table_chart" viewBox="0 0 24 24">
          <path
            d="M10 10.02h5V21h-5zM17 21h3c1.1 0 2-.9 2-2v-9h-5v11zm3-18H5c-1.1 0-2 .9-2 2v3h19V5c0-1.1-.9-2-2-2zM3 19c0 1.1.9 2 2 2h3V10H3v9z"
          ></path>
        </symbol>

        <symbol id="ic_task" viewBox="0 0 24 24">
          <path
            d="M14 2H6a2 2 0 0 0-1.99 2L4 20a2 2 0 0 0 1.99 2H18a2 2 0 0 0 2-2V8l-6-6zm-3.06 16L7.4 14.46l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41L10.94 18zM13 9V3.5L18.5 9H13z"
          ></path>
        </symbol>

        <symbol id="ic_time" viewBox="0 0 24 24">
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
          ></path>
          <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"></path>
        </symbol>

        <symbol id="ic_time_filled" viewBox="0 0 24 24">
          <path
            d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm4.2 14.2L11 13V7h1.5v5.2l4.5 2.7-.8 1.3z"
          ></path>
        </symbol>

        <symbol id="ic_time_line" viewBox="0 0 24 24">
          <path
            d="M23 8c0 1.1-.9 2-2 2-.18 0-.35-.02-.51-.07l-3.56 3.55c.05.16.07.34.07.52 0 1.1-.9 2-2 2s-2-.9-2-2c0-.18.02-.36.07-.52l-2.55-2.55c-.16.05-.34.07-.52.07s-.36-.02-.52-.07l-4.55 4.56c.05.16.07.33.07.51 0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2c.18 0 .35.02.51.07l4.56-4.55C8.02 9.36 8 9.18 8 9c0-1.1.9-2 2-2s2 .9 2 2c0 .18-.02.36-.07.52l2.55 2.55c.16-.05.34-.07.52-.07s.36.02.52.07l3.55-3.56C19.02 8.35 19 8.18 19 8c0-1.1.9-2 2-2s2 .9 2 2z"
          ></path>
        </symbol>

        <symbol id="ic_transfer" viewBox="0 0 24 24">
          <path
            d="M10.59 9.17L5.41 4 4 5.41l5.17 5.17 1.42-1.41zM14.5 4l2.04 2.04L4 18.59 5.41 20 17.96 7.46 20 9.5V4h-5.5zm.33 9.41l-1.41 1.41 3.13 3.13L14.5 20H20v-5.5l-2.04 2.04-3.13-3.13z"
          ></path>
        </symbol>

        <symbol id="ic_transaction_log" viewBox="0 0 24 24">
          <path
            d="M18 17H6v-2h12v2zm0-4H6v-2h12v2zm0-4H6V7h12v2zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2v20z"
          ></path>
        </symbol>

        <symbol id="ic_undo" viewBox="0 0 24 24">
          <path
            d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"
          ></path>
        </symbol>

        <symbol id="ic_upload" viewBox="0 0 24 24">
          <path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"></path>
        </symbol>

        <symbol id="ic_visible" viewBox="0 0 24 24">
          <path
            d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"
          ></path>
        </symbol>

        <symbol id="ic_visibility_off" viewBox="0 0 24 24">
          <path
            d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"
          ></path>
        </symbol>

        <symbol id="ic_warning" viewBox="0 0 24 24">
          <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"></path>
        </symbol>

        <symbol id="ic_weigh_with_scale" viewBox="0 0 24 24">
          <path
            d="M8 23h8v-2H8v2zm8-21.99L8 1c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM16 15H8V5h8v10z"
          ></path>
        </symbol>

        <symbol id="ic_where_to_vote" viewBox="0 0 24 24">
          <path
            d="M12 2a7 7 0 0 1 7 7c0 5.25-7 13-7 13S5 14.25 5 9a7 7 0 0 1 7-7zm-1.53 12L17 7.41 15.6 6l-5.13 5.18L8.4 9.09 7 10.5l3.47 3.5z"
          ></path>
        </symbol>

        <symbol id="ic_zoom_in" viewBox="0 0 24 24">
          <path
            d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
          ></path>
          <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"></path>
        </symbol>

        <symbol id="ic_zoom_out" viewBox="0 0 24 24">
          <path
            d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"
          ></path>
        </symbol>
      </svg>
    </div>

    <div class="page-message" id="page-message-root">
      <div class="page-message__container">
        <div class="page-message__icon-container"></div>
        <div class="page-message__message-container">
          <ul class="page-message__messages">
            <li
              class="page-message__message page-message__message--primary"
            ></li>
          </ul>
          <div
            class="page-message__button-container"
            style="display: none"
          ></div>
        </div>
        <button type="button" class="icon-button page-message__close-button">
          <span role="tooltip" class="icon-span"
            ><svg class="icon ic_close" focusable="false">
              <use
                xlink:href="#ic_close"
                xlink="http://www.w3.org/1999/xlink"
              ></use></svg
          ></span>
        </button>
      </div>
    </div>
    <script type="text/javascript" src="login_files/PageMessage.js"></script>
    <script>
      (function () {
        var errors = [];
        var success = [];
        onDocumentReady(function () {
          var oldPageMessages = document.querySelectorAll(".old-page-message");
          for (var i = 0; i < oldPageMessages.length; i++) {
            var oldMessage = oldPageMessages[i];
            oldMessage.parentElement.removeChild(oldMessage);
          }
          if (errors.length) {
            PageMessage.error(errors, null, null, true);
          } else if (success.length) {
            PageMessage.success(success, null, null, true);
          }
        });
      })();
    </script>

    <div class="grid-container fluid">
      <form
        name="loginForm"
        id="languageForm"
        method="post"
        action="/security/login.do"
      >
        <input type="hidden" name="loginPageAction" value="login" />
        <input type="hidden" name="lastAction" value="" />
        <input type="hidden" name="SAML2failsafe" value="false" />
        <input type="hidden" name="SAML2initURL" value="" />

        <div class="topper grid-x grid-margin-x grid-margin-y">
          <div
            class="cell small-10 field-label field-label-right language-label"
          >
            Language
          </div>
          <div
            id="tmsComponent-defaultLanguage-3e79bafe-657e-43bc-8abd-cd313384eebc"
            class="js-tms-component-root cell small-2"
          >
            <div
              class="input-label has-icon js-tms-component-rendered has-value"
            >
              <div class="error-container input">
                <input
                  aria-controls="defaultLanguage_drop-down"
                  aria-expanded="false"
                  type="text"
                  name="defaultLanguage_input"
                  autocomplete="off"
                  id="defaultLanguage"
                  role="combobox"
                  tabindex=""
                  class=""
                  aria-describedby="defaultLanguage-field-tooltip"
                  value="English"
                />
                <div class="fixed-popover-container"></div>
                <div
                  class="icon action"
                  aria-describedby="defaultLanguage-field-tooltip"
                >
                  <span class="icon-span" title="Select one" role="tooltip"
                    ><svg class="icon ic_single_select" focusable="false">
                      <use
                        xlink:href="#ic_single_select"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                      ></use></svg
                  ></span>
                </div>
                <select
                  id="defaultLanguage_select"
                  name="defaultLanguage"
                  style="display: none"
                  aria-describedby="defaultLanguage-field-tooltip"
                >
                  <option value="ENGLISH_US" selected="selected">
                    English
                  </option>
                  <option value="FRENCH_CANADA">français (CA)</option>
                  <option value="FRENCH_FRANCE">français (FR)</option>
                  <option value="GERMAN_GERMANY">Deutsch</option>
                  <option value="PORTUGUESE_BRAZIL">Português (BR)</option>
                  <option value="SPANISH_SPAIN">Español (ES)</option>
                  <option value="ITALIAN_ITALY">Italiano</option>
                  <option value="PORTUGUESE_PORTUGAL">Português (PT)</option>
                  <option value="POLISH_POLAND">Polski</option>
                  <option value="TURKISH_TURKEY">Türkçe</option>
                  <option value="DUTCH_NETHERLANDS">Nederlands</option>
                  <option value="SPANISH_MEXICO">Español (MX)</option>
                  <option value="RUSSIAN_RUSSIA">Русский</option>
                  <option value="INDONESIAN_INDONESIA">Bahasa Indonesia</option>
                  <option value="CHINESE_SIMPLIFIED">中文（简体）</option>
                  <option value="CHINESE_TRADITIONAL">中文（繁體）</option>
                  <option value="KOREAN_KOREA">한국어</option>
                </select>
                <div class="fixed-popover-container"></div>
              </div>
            </div>
          </div>
          <script type="text/javascript">
            (function () {
              var props = {
                onChange: function (event) {
                  changeLanguage(this);
                },
                name: "defaultLanguage",
                multiple: false,
                preventClear: true,
                options: [
                  { label: "English", value: "ENGLISH_US" },
                  { label: "français (CA)", value: "FRENCH_CANADA" },
                  { label: "français (FR)", value: "FRENCH_FRANCE" },
                  { label: "Deutsch", value: "GERMAN_GERMANY" },
                  { label: "Português (BR)", value: "PORTUGUESE_BRAZIL" },
                  { label: "Español (ES)", value: "SPANISH_SPAIN" },
                  { label: "Italiano", value: "ITALIAN_ITALY" },
                  { label: "Português (PT)", value: "PORTUGUESE_PORTUGAL" },
                  { label: "Polski", value: "POLISH_POLAND" },
                  { label: "Türkçe", value: "TURKISH_TURKEY" },
                  { label: "Nederlands", value: "DUTCH_NETHERLANDS" },
                  { label: "Español (MX)", value: "SPANISH_MEXICO" },
                  { label: "Русский", value: "RUSSIAN_RUSSIA" },
                  { label: "Bahasa Indonesia", value: "INDONESIAN_INDONESIA" },
                  { label: "中文（简体）", value: "CHINESE_SIMPLIFIED" },
                  { label: "中文（繁體）", value: "CHINESE_TRADITIONAL" },
                  { label: "한국어", value: "KOREAN_KOREA" },
                ],
                readOnly: false,
                id: "defaultLanguage",
                value: ["ENGLISH_US"],
                required: false,
              };
              var children = null;
              TMSComponents.init(
                document.getElementById(
                  "tmsComponent-defaultLanguage-3e79bafe-657e-43bc-8abd-cd313384eebc",
                ),
                TMSComponents["Select"],
                props,
                children,
              );
            })();
          </script>
        </div>
      </form>
      <div class="login-stripe">
        <div id="logos">
          <div class="blujay-logo">
            <a href="https://www.e2open.com/">
              <img
                alt="tm4sprd07-web02-chg:master:2025-04-02_10-50-32"
                src="login_files/e2open_logo.svg"
              />
            </a>
          </div>
          <div></div>
        </div>
        <div id="loginPanel">
          <div class="grid-x" id="panelGrid">
            <div class="tabs cell">
              <div class="tab selected" data-tab-id="userLogin">User Login</div>
              <div class="tab" data-tab-id="companyLogin">Company Login</div>
            </div>
            <div class="tab-content-container cell">
              <form
                name="loginForm"
                id="userLoginForm"
                method="post"
                action="/security/login.do"
                novalidate="novalidate"
              >
                <input type="hidden" name="loginPageAction" value="login" />
                <input type="hidden" name="lastAction" value="" />
                <input type="hidden" name="SAML2failsafe" value="false" />
                <input type="hidden" name="SAML2initURL" value="" />

                <div id="userLogin" class="tab-content grid-x visible">
                  <div
                    id="tmsComponent-userID-3c45ef1b-4fc0-4d15-a25b-14eb5f328ccc"
                    class="js-tms-component-root cell small-12"
                  >
                    <div
                      class="input-label js-tms-component-rendered top-align"
                    >
                      <label for="userID" class="required-asterisk"
                        >Username</label
                      >
                      <div class="error-container input">
                        <input
                          id="userID"
                          type="text"
                          name="userID"
                          tabindex=""
                          autocomplete="on"
                          maxlength="70"
                          class="ll_rules_0 error"
                          aria-describedby="userID-field-tooltip"
                          aria-invalid="true"
                        />
                        <div class="fixed-popover-container"></div>
                      </div>
                    </div>
                  </div>
                  <script type="text/javascript">
                    (function () {
                      var props = {
                        autoComplete: "on",
                        name: "userID",
                        readOnly: false,
                        label: "Username",
                        id: "userID",
                        value: "",
                        required: true,
                        maxLength: 70,
                      };
                      var children = null;
                      TMSComponents.init(
                        document.getElementById(
                          "tmsComponent-userID-3c45ef1b-4fc0-4d15-a25b-14eb5f328ccc",
                        ),
                        TMSComponents["TextField"],
                        props,
                        children,
                      );
                    })();
                  </script>

                  <div
                    id="tmsComponent-password-d42aba19-d328-47c1-a5f0-e7bbc8f27b12"
                    class="js-tms-component-root cell small-12"
                  >
                    <div
                      class="input-label js-tms-component-rendered top-align"
                    >
                      <label for="password" class="required-asterisk"
                        >Password</label
                      >
                      <div class="error-container input">
                        <input
                          id="password"
                          type="password"
                          name="password"
                          tabindex=""
                          autocomplete="on"
                          maxlength="200"
                          class="ll_rules_1"
                          aria-describedby="password-field-tooltip"
                          value=""
                        />
                        <div class="fixed-popover-container"></div>
                      </div>
                    </div>
                  </div>
                  <script type="text/javascript">
                    (function () {
                      var props = {
                        autoComplete: "on",
                        onKeyPress: function (event) {
                          checkCapsLock(event);
                        },
                        name: "password",
                        readOnly: false,
                        label: "Password",
                        id: "password",
                        type: "password",
                        value: "",
                        required: true,
                        maxLength: 200,
                      };
                      var children = null;
                      TMSComponents.init(
                        document.getElementById(
                          "tmsComponent-password-d42aba19-d328-47c1-a5f0-e7bbc8f27b12",
                        ),
                        TMSComponents["TextField"],
                        props,
                        children,
                      );
                    })();
                  </script>

                  <div class="rowstatuswarn" id="capslockdiv">
                    Caps Lock is enabled
                  </div>

                  <div class="grid-x grid-margin-x grid-margin-y">
                    <div
                      id="tmsComponent-rememberMeon-3068ee1c-1a18-4a02-93b2-62a7f44aab4f"
                      class="js-tms-component-root cell small-6"
                    >
                      <div
                        class="switch-outer-container js-tms-component-rendered"
                      >
                        <label class="field-label field-label--small"
                          >Keep me logged in</label
                        >
                        <div class="switch-container">
                          <label class="checkbox-switch"
                            ><input
                              name="rememberMe"
                              id="rememberMe"
                              class=""
                              type="checkbox"
                              value="on"
                            /><span class="switch-circle"></span
                            ><span class="switch__pill focus-border"></span
                            ><span class="switch__pill switch-body"></span
                            ><span class="switch__pill switch-color"></span
                            ><span class="switch-label"
                              ><span class="switch-label-on">Yes</span
                              ><span class="switch-label-off">No</span></span
                            ></label
                          >
                        </div>
                      </div>
                    </div>
                    <script type="text/javascript">
                      (function () {
                        var props = {
                          enabledText: "Yes",
                          name: "rememberMe",
                          label: "Keep me logged in",
                          id: "rememberMe",
                          value: "on",
                          disabledText: "No",
                        };
                        var children = null;
                        TMSComponents.init(
                          document.getElementById(
                            "tmsComponent-rememberMeon-3068ee1c-1a18-4a02-93b2-62a7f44aab4f",
                          ),
                          TMSComponents["Switch"],
                          props,
                          children,
                        );
                      })();
                    </script>

                    <div class="cell small-6 submitbut">
                      <button
                        class="prominent"
                        type="submit"
                        onclick="useUserLogin()"
                        id="userSubmit"
                        title="This button will be enabled when all required fields and errors are fixed"
                        disabled="disabled"
                      >
                        Login
                      </button>
                    </div>
                  </div>

                  <div class="cell"><hr /></div>
                  <div class="cell">
                    <a
                      href="javascript:noop()"
                      onclick="popup('/security/forgotusername.do', 'LoginHelp', 600, 300)"
                    >
                      Recover Username
                    </a>
                  </div>
                  <div class="cell">
                    <a
                      href="javascript:noop()"
                      onclick="popup('/security/forgotpassword.do', 'LoginHelp', 600, 300);"
                    >
                      Reset Password
                    </a>
                  </div>
                  <div class="cell">
                    <a
                      href="javascript:noop()"
                      onclick="popup('https://blujaysolutions.force.com', 'e2openSupport', 600, 300);"
                    >
                      Global Support Portal
                    </a>
                  </div>
                  <div class="cell">
                    <a
                      href="javascript:void(0)"
                      onclick="popup('/thirdparty/tpexternaluserregistration.do?language=ENGLISH_US', 'ThirdPartyRegistration', 550, 750);"
                    >
                      Third Party Registration
                    </a>
                  </div>
                </div>
              </form>
              <form
                name="loginForm"
                id="companyLoginForm"
                method="post"
                action="/security/login.do"
                novalidate="novalidate"
              >
                <input type="hidden" name="loginPageAction" value="login" />
                <input type="hidden" name="lastAction" value="" />
                <input type="hidden" name="SAML2failsafe" value="false" />
                <input type="hidden" name="SAML2initURL" value="" />

                <div id="companyLogin" class="tab-content grid-x">
                  <div
                    id="tmsComponent-companyDomain-ba65b3c5-e335-4f01-9b06-4f469fc4cdc9"
                    class="js-tms-component-root cell small-12"
                  >
                    <div
                      class="input-label js-tms-component-rendered top-align"
                    >
                      <label for="companyDomain" class="required-asterisk"
                        >Domain name</label
                      >
                      <div class="error-container input">
                        <input
                          id="companyDomain"
                          type="text"
                          name="companyDomain"
                          tabindex=""
                          autocomplete="on"
                          maxlength="40"
                          class="ll_rules_2"
                          aria-describedby="companyDomain-field-tooltip"
                        />
                        <div class="fixed-popover-container"></div>
                      </div>
                    </div>
                  </div>
                  <script type="text/javascript">
                    (function () {
                      var props = {
                        autoComplete: "on",
                        name: "companyDomain",
                        readOnly: false,
                        label: "Domain name",
                        id: "companyDomain",
                        value: "",
                        required: true,
                        maxLength: 40,
                      };
                      var children = null;
                      TMSComponents.init(
                        document.getElementById(
                          "tmsComponent-companyDomain-ba65b3c5-e335-4f01-9b06-4f469fc4cdc9",
                        ),
                        TMSComponents["TextField"],
                        props,
                        children,
                      );
                    })();
                  </script>

                  <div class="cell example-domain">(e.g. e2open.com)</div>
                  <div class="submitbut cell">
                    <button
                      class="prominent"
                      type="submit"
                      onclick="useCompanyLogin()"
                      id="companySubmit"
                    >
                      Login
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="popover-container"></div>
    <div id="affix-container"></div>
    <div id="dwrImageDiv" style="display: none">
      <img
        id="dwrLoadingImg"
        src="login_files/eto-loader.svg"
        alt="Activity Indicator"
      />
    </div>
    <span class="cleanslate TridactylStatusIndicator TridactylModenormal"
      >normal</span
    >
  </body>
</html>
