<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="AcceptLoads" class="search-page">
    <head>
        <title>Accept Loads Search</title>
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
        <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
        <script type="text/javascript">
            var faviconICO = document.querySelector('#faviconICO');
            var faviconPNG = document.querySelector('#faviconPNG');
            var darkModeListener = function(event) {
            if (event.matches) {
            faviconICO.setAttribute("href","/favicon_dark.ico");
            faviconPNG.setAttribute("href","/favicon_dark.png");
            } else {
            faviconICO.setAttribute("href","/favicon.ico");
            faviconPNG.setAttribute("href","/favicon.png");
            }
            };
            var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
            if(darkModePreference.addEventListener){
            darkModePreference.addEventListener('change', function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            } else {
            darkModePreference.addListener(function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            }
            darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <script type="text/javascript">
            function popupMaestro() {




            }
            onDocumentReady(function() {
            var iconContainer = document.createElement('div');
            jQuery(document.body).prepend(iconContainer);
            jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
            popupMaestro();
            });
        </script>
        <script type="text/javascript">
            var useComponents = true;
            var newStylesComponents = false;
            var useETOComponents = true;
            var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
        </script>
        <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            ll.util.initFormats(1, 1,
            'en_US');
            var shortLang = 'en';
            var browserCacheKey = 'ba05ba21\-71c6\-4258\-bf8c\-5c8ff05a2a4a';
            ll.lang = ll.lang || {};
            ll.lang.locale = 'en_US';
        </script>
        <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            jQuery.noConflict();
            var LeanActionFormName = '';
            var LeanActionFullPath = '';
            var $LeanActionForm;
            onDocumentReady(function() {



            $LeanActionForm = jQuery('form');


            if (get_browser() === "msie") {
            jQuery('html').addClass("ie");
            }
            if ( 'noValidate' in document.createElement('form') ){
            jQuery('html').addClass("novalidate");
            }
            var $dwrImageDiv = jQuery('#dwrImageDiv');
            if (!$dwrImageDiv.length) {
            $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
            $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
            jQuery('body').append($dwrImageDiv);
            }


            hasExpiredSessionWarningBeenDisplayed = false;
            });
        </script>
        <script type="text/javascript">
            var defaultOriginState = '';
            var defaultDestState = '';
            function doSubmit(theForm) {
            if (validate(theForm)) {
            document.querySelector(".continue-button").disabled = true;
            theForm.submit();
            }
            }
            function saveSearchFromPopup(searchName, searchdefault) {
            var theForm = document.forms["SearchForm"];
            theForm.elements["mode"].value = 'savesearch';
            theForm.elements["savesearchname"].value = searchName;
            theForm.elements["defaultsavesearch"].value = searchdefault;
            if (searchValidate(theForm)) {
            theForm.submit();
            }
            }
            function manageSearches(){
            actionPopup("/system/savedsearchadmin.do?pageID=28", 420, 300);
            }
            function loadSavedSearch(form){
            form.elements["mode"].value='recallsearch';
            form.submit();
            }
            function checkDuplicateSearchName(name){
            var savedSearchDropDown = document.forms["SearchForm"].elements["savedsearch"];
            for (var i=0; i < savedSearchDropDown.options.length; i++) {
            if (savedSearchDropDown.options[i].text == name) {
            return true;
            }
            }
            return false;
            }
            function searchValidate(theForm) {
            if (!validateMultipleFields(theForm, "loadID", "integer")) {
            return false;
            }
            if (!validateMultipleFields(theForm, "refNbr", "quotableRefNum")) {
            return false;
            }
            var badDateMessage = "Please enter both a start date and an end date to filter on a date.";
            if (!ll.util.validateDateRangePair(theForm.elements["orgStartDate"], theForm.elements["orgEndDate"], badDateMessage)) {
            return false;
            }
            if (!ll.util.validateDateRangePair(theForm.elements["destStartDate"], theForm.elements["destEndDate"], badDateMessage)) {
            return false;
            }
            if (!ll.util.validateDateRangePair(theForm.elements["tenderStartDate"], theForm.elements["tenderEndDate"], badDateMessage)) {
            return false;
            }
            if (!theForm.elements["chkMyLoads"].checked && isEmpty(getSelectedValue(theForm.elements["groups"]))) {
            alert("When My Loads is not checked, a Group must be selected.");
            return false;
            }
            return true;
            }
            function setQuickDate(select, which) {
            var opt = getSelectedValue(select);
            var theForm = document.forms["SearchForm"];
            var startDate = theForm.elements[which + "StartDate"].value;
            var endDate = theForm.elements[which + "EndDate"].value;
            switch(opt){
            case "1": startDate="04/17/2025";endDate="04/17/2025";break;
            case "2": startDate="04/16/2025";endDate="04/16/2025";break;
            case "3": startDate="04/18/2025";endDate="04/18/2025";break;
            case "4": startDate="04/13/2025";endDate="04/19/2025";break;
            case "5": startDate="04/06/2025";endDate="04/12/2025";break;
            case "6": startDate="04/20/2025";endDate="04/26/2025";break;
            case "7": startDate="04/01/2025";endDate="04/30/2025";break;
            case "8": startDate="03/01/2025";endDate="03/31/2025";break;
            case "9": startDate="05/01/2025";endDate="05/31/2025";break;
            case "16": startDate="04/01/2025";endDate="06/30/2025";break;
            case "17": startDate="01/01/2025";endDate="03/31/2025";break;
            case "18": startDate="07/01/2025";endDate="09/30/2025";break;
            case "10": startDate="01/01/2025";endDate="04/17/2025";break;
            case "12": startDate="06/01/2025";endDate="06/30/2025";break;
            case "19": startDate="01/17/2025";endDate="04/17/2025";break;
            case "20": startDate="10/17/2024";endDate="04/17/2025";break;
            case "13": startDate="04/17/2024";endDate="04/17/2025";break;
            case "14": startDate="10/17/2023";endDate="04/17/2025";break;
            case "15": startDate="04/06/2025";endDate="04/26/2025";break;
            default: startDate=""; endDate=""; break;
            }
            theForm.elements[which + "StartDate"].value = startDate;
            theForm.elements[which + "EndDate"].value = endDate;
            }
            function resetQuickDate(which) {
            var theForm = document.forms["SearchForm"];
            theForm.elements[which + "QuickDate"].selectedIndex = 0;
            }
            function clearForm() {
            var theForm = document.forms["SearchForm"];
            theForm.elements["loadID"].value = "";
            theForm.elements["refNbr"].value = "";
            theForm.elements["shipper"].value = "";
            theForm.elements["orgStartDate"].value = "";
            theForm.elements["orgEndDate"].value = "";
            theForm.elements["query.equipmentTypes"].selectedIndex = -1;
            theForm.elements["orgQuickDate"].selectedIndex = 0;
            theForm.elements["tenderStartDate"].value = "";
            theForm.elements["tenderEndDate"].value = "";
            theForm.elements["destStartDate"].value = "";
            theForm.elements["destEndDate"].value = "";
            theForm.elements["groups"].selectedIndex = -1;
            theForm.elements["chkMyLoads"].checked = true;
            theForm.elements["tenderQuickDate"].selectedIndex = 0;
            theForm.elements["destQuickDate"].selectedIndex = 0;
            theForm.elements["chkNextTender"].checked = false;
            theForm.elements["chkExchange"].checked = false;
            theForm.elements["includeOldLoads"].checked = false;
            theForm.elements["orgName"].value = "";
            theForm.elements["orgCity"].value = "";
            theForm.elements["orgZip"].value = "";
            theForm.elements["orgCountry"].selectedIndex = 0;
            theForm.elements["orgCountry"].onchange.call(theForm.elements["orgCountry"]);
            theForm.elements["destName"].value = "";
            theForm.elements["destCity"].value = "";
            theForm.elements["destZip"].value = "";
            theForm.elements["destCountry"].selectedIndex = 0;
            theForm.elements["destCountry"].onchange.call(theForm.elements["destCountry"]);
            defaultOriginState = "";
            defaultDestState = "";
            }
            function actionPopup(url, width, height) {
            popup(url, 'statusAction', width, height);
            }
            function validate(theForm) {
            var atLeastOneChecked = false;

            for (var i=0; i < theForm.elements["numloads"].value*1; i++) {
            var loadID = theForm.elements["loadid" + i].value;
            var temp = theForm.elements["chkRejectLoad"+ loadID];
            var test = theForm.elements["chkAcceptLoad"+ loadID];

            if (temp != null && temp.checked == true && temp.type == "checkbox") {
            atLeastOneChecked = true;
            }
            if (test != null && test.checked == true && test.type == "checkbox") {
            atLeastOneChecked = true;
            }
            if (temp != null && temp.value == "ON" && temp.type == "hidden") {
            atLeastOneChecked = true;
            }
            if (test != null && test.value == "ON" && test.type == "hidden") {
            atLeastOneChecked = true;
            }

            }
            if (!atLeastOneChecked) {
            alert("Please choose to Accept or Reject at least one load before continuing.");
            return false;
            }
            if (!confirm("Click OK to process loads on this page or click CANCEL to stay on this page.")) {
            return false;
            }
            return true;
            }
            function hasDataChanged(theForm) {
            var atLeastOneChecked = false;

            for (var i=0; i < theForm.elements["numloads"].value*1; i++) {
            loadID = theForm.elements["loadid" + i].value;
            if (theForm.elements["chkRejectLoad" + loadID].checked) {
            atLeastOneChecked = true;
            break;
            }
            if (theForm.elements["chkAcceptLoad" + loadID].checked) {
            atLeastOneChecked = true;
            break;
            }
            }
            if (atLeastOneChecked) {
            if (!confirm("You have checked some loads to be accepted and\/or rejected.\nIf you continue these loads will no longer be checked. \n\n\tAre you sure you want to continue?"))
            return false;
            }
            return true;
            }

            function switchAll(switchFieldName, fieldNameBase, acceptTender, trueOrFalse) {
            var form = document.forms["AcceptForm"];
            form.elements[switchFieldName].checked = trueOrFalse;
            for (var i=0; i < form.elements["numloads"].value*1; i++) {
            var loadID = form.elements["loadid" + i].value;
            var switchField = form.elements[fieldNameBase + loadID];
            if (switchField != null) {
            switchField.checked = trueOrFalse;
            }
            var tourTenderID = form.elements["tourTenderID" + i];
            if (tourTenderID != null) {
            if (acceptTender) {
            var tourSwitchField = form.elements["tourTender" + tourTenderID.value + "Accept"];
            if (tourSwitchField != null) {
            tourSwitchField.checked = trueOrFalse;
            acceptTour(tourTenderID.value);
            }
            } else {
            var tourSwitchField = form.elements["tourTender" + tourTenderID.value + "Reject"];
            if (tourSwitchField != null) {
            tourSwitchField.checked = trueOrFalse;
            rejectTour(tourTenderID.value);
            }
            }
            }
            }
            }
            function showChargeDetails(loadID, chargeHeaderID) {
            var theURL = "/rating/payablechargedetail.do?loadID=" + loadID + "&chargeHeaderID=" + chargeHeaderID;
            popup(theURL, 'RateDetails', 800, 450);
            }

            function submitForm() {
            var form = document.forms["SearchForm"];
            var searchButton = document.getElementById('searchButton');
            if (!searchButton.readOnly && searchValidate(form)) {
            form.submit();
            searchButton.readOnly = true;
            searchButton.className = "search_disabled";
            searchButton.title = "To Cancel the Search, click Stop on the Browser";
            }
            }
            if (document.layers) {
            document.captureEvents(Event.KEYPRESS);
            }
            document.onkeypress = doSearchFormEnterKeypress;

            function doSearchFormEnterKeypress(e) {
            var evt = (e) ? e : ((window.event) ? window.event : null);
            var key = (evt.keyCode) ? evt.keyCode : evt.charCode;
            if (key == 13) {

            var eventSrcName = (evt.srcElement) ? evt.srcElement.name : evt.target.name;
            var searchForm = document.forms["SearchForm"];
            var elem = searchForm.elements[eventSrcName];
            if (elem) {
            submitForm();
            }
            }
            }
            function appointLoads(loadIDsToAppoint) {
            location.href = "/apptschedule/carrierappointmentsearch.do?pageaction=search&searchQO.myLoads=ON&leanIDString=" + loadIDsToAppoint;
            }
            function acceptTour(tourTenderID) {
            var form = document.forms["AcceptForm"];
            var loadString = form.elements["loadsForTourTenderID"+tourTenderID].value;
            var loadIDs = loadString.split(",");
            var checked = form.elements["tourTender"+tourTenderID+"Accept"].checked;
            var loadID = loadIDs[0];
            if(checked) {
            form.elements["chkAcceptLoad"+loadID].value = "ON";
            form.elements["chkRejectLoad"+loadID].value = "";
            form.elements["tourTender"+tourTenderID+"Reject"].checked = false;
            }else{
            form.elements["chkAcceptLoad"+loadID].value = "";
            }
            for(var i = 1; i < loadIDs.length; i++ ) {
            loadID = loadIDs[i];
            if(checked) {
            form.elements["tourAcceptLoad"+loadID].value = "ON";
            form.elements["tourRejectLoad"+loadID].value = "";
            }else{
            form.elements["tourAcceptLoad"+loadID].value = "";
            }
            }
            }
            function rejectTour(tourTenderID) {
            var form = document.forms["AcceptForm"];
            var loadString = form.elements["loadsForTourTenderID"+tourTenderID].value;
            var loadIDs = loadString.split(",");
            var checked = form.elements["tourTender"+tourTenderID+"Reject"].checked;
            var loadID = loadIDs[0];
            if(checked) {
            form.elements["chkRejectLoad"+loadID].value = "ON";
            form.elements["chkAcceptLoad"+loadID].value = "";
            form.elements["tourTender"+tourTenderID+"Accept"].checked = false;
            }else{
            form.elements["chkRejectLoad"+loadID].value = "";
            }
            for(var i = 1; i < loadIDs.length; i++ ) {
            loadID = loadIDs[i];
            if(checked) {
            form.elements["tourRejectLoad"+loadID].value = "ON";
            form.elements["tourAcceptLoad"+loadID].value = "";
            }else{
            form.elements["tourRejectLoad"+loadID].value = "";
            }
            }
            }
            onDocumentReady(function() {
            loadCountries();
            });
            var countryMap = {};
            function setCountryMap(countries) {
            countryMap = countries;
            }
            function loadCountries() {
            var form = document.forms['SearchForm'];
            if (form && form.elements['orgCountry']) {
            var countrySelects = [
            form.elements['orgCountry'],
            form.elements['destCountry']
            ];
            var defaultCountries = [
            '',
            ''
            ];
            ll.util.loadUserCountries(countrySelects, defaultCountries, ' ', false, setCountryMap);
            }
            }
            function loadStates(stopType) {
            var form = document.forms['SearchForm'];
            var countrySel = form.elements[stopType + 'Country'];
            var stateSel = form.elements[stopType + 'State'];
            var defaultState;
            if (stopType == 'org') {
            defaultState = defaultOriginState;
            defaultOriginState = null;
            } else {
            defaultState = defaultDestState;
            defaultDestState = null;
            }
            ll.util.loadStates(countrySel, stateSel, defaultState, ' ');
            var icon = document.getElementById(stopType + "Cntry");
            var country = getSelectedValue(countrySel);
            if (countryMap[country]) {
            icon.component.tooltip = countryMap[country];
            } else {
            icon.component.tooltip = "";
            }
            }
            function toggleAdvancedSearch(checkBox) {
            var advancedDisplay, adVal;
            if (checkBox.checked) {
            advancedDisplay = "block";
            loadCountries();
            adVal = 1;
            } else if (!validateAdvSearch(checkBox.form)) {
            advancedDisplay.checked = true;
            loadCountries();
            advancedDisplay = "block";
            adVal = 1;
            } else {
            advancedDisplay = "none";
            adVal = 0;
            }
            document.getElementById("advancedSearchOptions").style.display = advancedDisplay;
            checkBox.form.elements["advanced"].value = adVal;
            }
            function validateAdvSearch(form) {
            if (!ll.util.checkDate(form.elements["destStartDate"], true)) {
            return false;
            }
            if (!ll.util.checkDate(form.elements["destEndDate"], true)) {
            return false;
            }
            if (!ll.util.checkDate(form.elements["tenderStartDate"], true)) {
            return false;
            }
            if (!ll.util.checkDate(form.elements["tenderEndDate"], true)) {
            return false;
            }
            return true;
            }
            function fleetChanged(checkbox) {
            $box = jQuery(checkbox);
            if (checkbox.checked) {
            $td = jQuery(checkbox).closest('td');
            jQuery('input[type="checkbox"]', $td).prop('checked', true);
            jQuery('input[type="checkbox"]', $td.next()).prop("checked", false);
            }
            }
            function acceptChanged(checkbox) {
            $td = jQuery(checkbox).closest('td');
            if (checkbox.checked) {
            jQuery('input[type="checkbox"]', $td.next()).prop("checked", false);
            } else {
            jQuery('input[type="checkbox"]', $td).prop("checked", false);
            }
            }
            function rejectChanged(checkbox) {
            if (checkbox.checked) {
            $td = jQuery(checkbox).closest('td');
            jQuery('input[type="checkbox"]', $td.prev()).prop("checked", false);
            }
            }
            function reorderTable(event) {
            if(hasDataChanged(document.forms['AcceptForm'])) {
            switch(event.target.dataset.column){
            case '0':
            window.location = '/AcceptLoads.jsp?order=0\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '2':
            window.location = '/AcceptLoads.jsp?order=3\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '6':
            window.location = '/AcceptLoads.jsp?order=6\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '8':
            window.location = '/AcceptLoads.jsp?order=8\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '10':
            window.location = '/AcceptLoads.jsp?order=10\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '4':
            window.location = '/AcceptLoads.jsp?order=4\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '12':
            window.location = '/AcceptLoads.jsp?order=12\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '14':
            window.location = '/AcceptLoads.jsp?order=14\x26chkMyLoads=ON\x26groups=184269';
            break;
            case '16':
            window.location = '/AcceptLoads.jsp?order=16\x26chkMyLoads=ON\x26groups=184269';
            break;
            }
            }
            }

            onDocumentReady(function(){
            var elements = document.querySelectorAll('.sortable');
            for(var i = 0; i < elements.length; i++){
            elements[i].addEventListener('click', reorderTable);
            }
            });

        </script>
        <style>
            .search-container > .button-container {
            margin: 10px 0 5px 0;
            }
            #advancedSearchOptions {
            margin-top: 20px;
            }
            .geography-table td:not(.geography-name) {
            padding: 0 10px;
            }
            .geography-table td:first-child {
            padding-left: 0;
            }
            .geography-name {
            padding-bottom: 10px;
            }
            select[name=orgState], select[name=destState] {
            width: 100%;
            }
            .load-instructions > .label {
            font-weight: 700;
            float: left;
            }
            .load-instructions > .instructions {
            float: left;
            }
            .instructions > .instruction-entry:not(:first-child) {
            margin-top: 10px;
            }
            .load-instructions p {
            margin: 0;
            }
            table.list .reason-codes > td {
            padding-bottom: 10px;
            }
            .search-criteria-checkbox input {
            cursor: pointer;
            }
            .search-criteria-input-label {
            padding-right: 10px;
            }
            .search-criteria-checkbox span {
            display: inline-block;
            vertical-align: top;
            }
            .list--skinny > tbody > tr.load-info-row > td {
            padding: 0 0 0 5px;
            height: auto;
            }
        </style>
    </head>
    <body>
        <script type="text/javascript" src="/c/js/menu_js_functions.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/menu/menu.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/menu/menu-search.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/map/mapControls.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            function popupCarrierConsole() {
            popup("/console/carrierconsole.do", "Console382705", 1100, 620);
            }
            function ll_popupSystemNotification(percent) {
            var w = 900, h = 600;
            if (window.screen) {
            w = window.screen.availWidth * percent / 100;
            h = window.screen.availHeight * percent / 100;
            }
            popup("/agent/systemnotification.do?", "notes", w, h);
            }

            onDocumentReady(function() {



            popupMaestro();
            });
        </script>
        <style type="text/css">
            .admin-info {
            padding-right: 10px;
            vertical-align: top;
            }
            .system-logo {
            text-align: center;
            display: inline-block;
            padding-left: 15px;
            padding-right: 15px;
            font-size: 15px;
            color: #4A4E50;
            }
            img.logo {
            height: 48px;
            }
            img.logo.logo__default {
            padding: 12px 0;
            }
            .display-none-item {
            display: none;
            }
            #cookie-policy-privacy-subtitle, #cookie-selection {
            margin-top: 25px;
            }
            #cookie-policy-privacy-subtitle, #strictlyCookies, #functionalCookies, #performanceAndAnalyticsCookies {
            font-size: 14px;
            line-height: 180%;
            }
            h3{
            font-weight: 600;
            font-size: 18px;
            }
            .eto-switch {
            display: -ms-flexbox;
            display: flex;
            position: relative;
            }
            .eto-switch__field {
            position: absolute;
            opacity: 0;
            z-index: 0;
            }
            .eto-switch__box {
            background-color: #FFF;
            background-image: linear-gradient(to top, rgba(70, 129, 147, 0), rgba(70, 129, 147, .24)), linear-gradient(rgba(70, 129, 147, .24), rgba(70, 129, 147, .24));
            border-radius: 2rem;
            display: block;
            height: 2.5rem;
            position: relative;
            transition-duration: .2s;
            transition-property: background-color,background-image;
            width: 5.5rem;
            }
            .eto-switch__box::before {
            background-color: #FFF;
            border-radius: 100%;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
            content: '';
            display: block;
            height: 2.2rem;
            position: absolute;
            top: calc(.20rem - 1px);
            transition-duration: .1s;
            transition-property: left;
            width: 2.2rem;
            z-index: 1;
            }
            .eto-switch__label--on {
            display: none;
            }
            .eto-switch__label,.eto-switch__label--off,.eto-switch__label--on {
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            line-height: 2.3rem;
            }
            .eto-switch__field:checked~.eto-switch__label--off {
            display: none;
            }
            .eto-switch--integrated .eto-switch__label--off,.eto-switch--integrated .eto-switch__label--on {
            left: 0;
            margin: 2px;
            position: absolute;
            }
            .eto-switch--integrated .eto-switch__label--off {
            padding-left: 3rem;
            }
            .eto-switch__field:checked~.eto-switch__label--on {
            display: block;
            }
            .eto-switch--integrated .eto-switch__label--on {
            color: #FFF;
            padding-left: 0.7rem;
            }
            .eto-switch__field:checked~.eto-switch__box {
            background-color: #277AB5;
            background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .24));
            }
            .eto-switch__field:checked~.eto-switch__box::before {
            left: calc(100% - 2.525rem);
            box-shadow: 0 0 0 2px transparent,0 3px 3px rgba(0, 0, 0, .3);
            }
            .eto-switch__field[disabled]~.eto-switch__box {
            background-color: #F6F6F6;
            background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .16));
            cursor: default;
            }
            .eto-switch__field[disabled]~.eto-switch__box::before {
            background-color: #E5E8EB;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
            }
        </style>
        <div class="primary-header">
            <div>
                <a href="http://www.blujaysolutions.com/" target="_top">
                <img
                    class="logo logo__default"
                    alt="Transportation Management v. tm4sprd07-web02-chg:master:2025-04-02_10-50-32"
                    src="/images/logos/e2open_logo.svg"
                    />
                </a>
                <div class="system-title">
                    Transportation Management
                </div>
            </div>
            <div class="items">
                <a href="javascript:_webhelp();" title="Help" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_help_header" focusable="false">
                            <use xlink:href="#ic_help_header" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="/tmshealthcheck/tmshealthcheck.do" title="TMS Health Status" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_monitor_heart" focusable="false">
                            <use xlink:href="#ic_monitor_heart" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="javascript:_customerSupport();" title="Customer Support" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_support_agent" focusable="false">
                            <use xlink:href="#ic_support_agent" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="/agent/webmessages.do?query.current=true" title="Messages" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_bell" focusable="false">
                            <use xlink:href="#ic_bell" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <div class="item user" id="headerUserItem">
                    <div class="item-image">
                        <div title="My Account" class="user-name">
                            Brooke.Carroll
                            <span role="tooltip" class="icon-span">
                                <svg class="icon  ic_account_circle" focusable="false">
                                    <use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <ul class="popover use-show-class" id="headerUserPopover">
                        <li>
                            <div class="defaults-label">Your defaults</div>
                            <table class="user-defaults">
                                <tbody>
                                    <tr>
                                        <td>Company:</td>
                                        <td>FETCH FREIGHT LLC</td>
                                    </tr>
                                    <tr>
                                        <td>Load Group:</td>
                                        <td>THE J. M. SMUCKER COMPANY</td>
                                    </tr>
                                </tbody>
                            </table>
                        </li>
                        <li class="link">
                            <a href="/security/managepassword.do">Account Information</a>
                        </li>
                        <li class="link">
                            <a href="javascript:populate()">PRIVACY SETTINGS</a>
                        </li>
                        <li class="link"><a href="/security/LogOut.jsp?dt=*************">
                            Log Out
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div id="cookieDialog" class="display-none-item"></div>
        <script>
            function headerInit() {
            var headerUserItem = document.getElementById('headerUserItem');
            var menuUserPopover = new Popover(document.getElementById('headerUserPopover'), headerUserItem);
            headerUserItem.addEventListener('click', function() {
            menuUserPopover.show();
            });
            }
            onDocumentReady(headerInit);
            function populate(){
            jQuery('#headerUserPopover').hide();
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            if (response != null) {
            var cookies = [];
            var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
            if(response.cookieGroups.length > 0){
            cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
            response.cookieGroups.forEach((cookie) =>{
            cookies.push(cookie.id);
            cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
            var checked = cookie.value ? "checked" : "";
            var disabled = !cookie.enabled ? "disabled" : "";
            cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
            cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
            cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
            cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
            });
            cookieHtml += "\n</table>\n</section>";
            }
            jQuery("#cookieDialog").html(cookieHtml);
            openCookieDialog(response.title,cookies);
            }
            }, null, null, 'GET', null, false);
            }
            function openCookieDialog(title,cookies){
            var dialogID = '#cookieDialog';
            jQuery(dialogID).dialog({
            autoOpen: true,
            width: 1030,
            height : 500,
            modal: true,
            title : title,
            resizable: false,
            draggable: false,
            buttons: {
            "Close": function() {
            jQuery(dialogID).dialog("close");
            },
            "Save": function() {
            var resCookies = cookies.map((cookie) => {
            return {
            id: cookie,
            enabled : jQuery("#"+cookie).is(":checked")
            };
            })
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
            jQuery(dialogID).dialog("close");
            },
            function(error) {
            PageMessage.error('An error occurred while performing the update.');
            },
            null,
            'POST',
            {
            preferences : resCookies
            },
            false,
            'application/json'
            );
            }
            }
            });
            }
        </script>
        <div class="menu">
            <nav id="menu-root"></nav>
            04/17/2025 08:17 CDT
        </div>
        <script type="text/javascript">
            jQuery.ajax('/tmsrest/menu/json?cacheKey=ba05ba21-71c6-4258-bf8c-5c8ff05a2a4a').done(function(json) {
            new Menu(document.getElementById('menu-root'), json);
            });

            document.body.classList.add('with-footer');

        </script>
        <form name="SearchForm" action="AcceptLoads.jsp" method="post" onSubmit="return searchValidate(this);" class="criteria">
            <input type="hidden" name="mode" value="" />
            <input type="hidden" name="savesearchname" value="" />
            <input type="hidden" name="defaultsavesearch" value="" />
            <input type="hidden" name="exec" value="1" />
            <div class="page-header">
                <div class="page-header__container">
                    <div class="page-header__main">
                        <div class="page-header__title-container">
                            <div class="page-header__page-title">Accept Loads Search</div>
                            <div class="page-header__page-title page-header__page-title--to-top">
                                <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                                Return to the top of Accept Loads Search                 </button>
                            </div>
                        </div>
                        <div class="page-header__options">
                            <span class="saved-search-component">
                                Saved Search
                                <select name="savedsearch" size="1" class="formfieldinputs" onChange="loadSavedSearch(this.form)">
                                    <option value="-1">-- Choose --</option>
                                </select>
                                <button type="button" class="text" onclick="ll.util.saveSearchPopup()" title="Save the current search for later use.">
                                Save
                                </button>
                                <button type="button" class="text" onclick="manageSearches()" title="Delete searches and set default search for this page.">
                                Manage
                                </button>
                            </span>
                        </div>
                    </div>
                    <div class="page-message" id="page-message-root">
                        <div class="page-message__container">
                            <div class="page-message__icon-container"></div>
                            <div class="page-message__message-container">
                                <ul class="page-message__messages">
                                    <li class="page-message__message page-message__message--primary"></li>
                                </ul>
                                <div class="page-message__button-container" style="display: none;"></div>
                            </div>
                            <button type="button" class="icon-button page-message__close-button">
                                <span role="tooltip" class="icon-span">
                                    <svg class="icon  ic_close" focusable="false">
                                        <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                    <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
                    <script>
                        (function() {
                        var errors = [

                        ];
                        var success = [

                        ];
                        onDocumentReady(function() {
                        var oldPageMessages = document.querySelectorAll('.old-page-message');
                        for(var i = 0; i < oldPageMessages.length; i++) {
                        var oldMessage = oldPageMessages[i];
                        oldMessage.parentElement.removeChild(oldMessage);
                        }
                        if(errors.length) {
                        PageMessage.error(errors, null, null, false);
                        } else if(success.length) {
                        PageMessage.success(success, null, null, false);
                        }
                        });
                        })();
                    </script>
                </div>
            </div>
            <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
            <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
            <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
            <table class="lean_table searchcriteria" width="100%">
                <tr>
                    <td class="statusmessage" nowrap="nowrap"></td>
                    <td class="statusmessagelink" align="right" nowrap="nowrap"></td>
                </tr>
            </table>
            <div class="criteria skinny-criteria">
                <table class="lean_table searchcriteria" width="100%">
                    <tr>
                        <td nowrap="nowrap">
                            <table class="lean_table">
                                <tr class="searchcriteria">
                                    <td width="33%" align="left" class="search-criteria-input-label">
                                        TMS ID(s)
                                    </td>
                                    <td align="left">
                                        <input type="text" size="20" name="loadID" value="" class="formfieldinputs" />
                                    </td>
                                </tr>
                                <tr class="searchcriteria">
                                    <td width="33%" align="left" class="search-criteria-input-label">
                                        Ref #(s)
                                    </td>
                                    <td class="searchcriteria" align="left">
                                        <input type='text' size='20' name='refNbr' value='' class='formfieldinputs' />
                                    </td>
                                </tr>
                                <tr class="searchcriteria">
                                    <td width="33%" align="left" class="search-criteria-input-label">
                                        Shipper
                                    </td>
                                    <td class="searchcriteria" align="left">
                                        <input type="text" size="20" name="shipper" value="" class="formfieldinputs" />
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td nowrap="nowrap" valign="top">
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tr class="searchcriteria">
                                    <td>Pick Date</td>
                                </tr>
                                <tr class="searchcriteria">
                                    <td>
                                        <select size="1" name="orgQuickDate" onChange="setQuickDate(this, 'org')" class="formfieldinputs">
                                            <option value="0">-- Choose --</option>
                                            <option value="1">Today</option>
                                            <option value="3">Tomorrow</option>
                                            <option value="2">Yesterday</option>
                                            <option value="4">This Week</option>
                                            <option value="5">Last Week</option>
                                            <option value="6">Next Week</option>
                                        </select>
                                        <br />
                                        <table class="lean_table">
                                            <tr class="searchcriteria">
                                                <td class="search-criteria-input-label">Start</td>
                                                <td><input type="text" size="10" name="orgStartDate" value="" class="ll_datepicker formfieldinputs" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('org');"/></td>
                                            </tr>
                                            <tr class="searchcriteria">
                                                <td class="search-criteria-input-label">End</td>
                                                <td><input type="text" size="10" name="orgEndDate" value="" class="ll_datepicker formfieldinputs" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('org');"/></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td nowrap="nowrap" valign="top">
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tr class="searchcriteria">
                                    <td>Equipment</td>
                                </tr>
                                <tr>
                                    <td class="formfieldinputs">
                                        <select name="query.equipmentTypes" multiple="multiple">
                                            <option value="VAN">Van</option>
                                            <option value="REEFER">Reefer</option>
                                            <option value="BULK">Bulk</option>
                                            <option value="FLATBED">Flatbed</option>
                                            <option value="CONTAINER">Container</option>
                                            <option value="OTHER">Other</option>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td nowrap="nowrap" valign="top">
                            <table class="lean_table">
                                <tr class="searchcriteria">
                                    <td>Load Group</td>
                                </tr>
                                <tr class="searchcriteria">
                                    <td>
                                        <select size="4" name="groups" multiple="multiple" class="formfieldinputs">
                                            <option value="0" >-- All --</option>
                                            <option value="169586" >JM SMUCKER</option>
                                            <option value="173575" >MISC</option>
                                            <option value="184269" selected>THE J. M. SMUCKER COMPANY</option>
                                            <option value="173328" >UTZ SNACKS</option>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td nowrap="nowrap" class="searchcriteria">
                            <div class="search-container">
                                <label class="search-criteria-checkbox">
                                <input type="checkbox" name="chkMyLoads" value="ON" checked />
                                <span>
                                My Loads
                                </span>
                                </label>
                                <div class="button-container">
                                    <button type="button" class="text" onclick="clearForm()">Clear</button>
                                    <button id="searchButton" class="leanbutton search-button search" type="button" onclick="submitForm()">Search</button>
                                </div>
                                <label class="search-criteria-checkbox">
                                <input type="checkbox" name="advancedSearch" onclick="toggleAdvancedSearch(this);"  />
                                <span>
                                Advanced Search
                                </span>
                                </label>
                                <input type="hidden" name="advanced" value="0" />
                            </div>
                        </td>
                    </tr>
                </table>
                <div id="advancedSearchOptions" style="display: none;">
                    <table class="lean_table" width="100%">
                        <tr class="searchcriteria">
                            <td nowrap="nowrap" valign="top" width="50%">
                                <table>
                                    <tr class="searchcriteria">
                                        <td width="5%" colspan="5">Origin</td>
                                    </tr>
                                    <tr class="searchcriteria">
                                        <td>
                                            <table class="lean_table geography-table">
                                                <tr class="searchcriteria">
                                                    <td class="geography-name search-criteria-input-label" colspan="5" >
                                                        <span class="search-criteria-input-label">
                                                        Name
                                                        </span>
                                                        <input type="text" name="orgName" size="35" maxlength="50" value="" class="formfieldinputs" />
                                                    </td>
                                                </tr>
                                                <tr class="searchcriteria">
                                                    <td width="100%">
                                                        <table class="leanTable" style="width:100.0%">
                                                            <tr class="searchcriteria">
                                                                <td>Country</td>
                                                                <td>City</td>
                                                                <td>State/Province</td>
                                                                <td>Postal Code</td>
                                                            <tr class="searchcriteria">
                                                                <td nowrap="nowrap">
                                                                    <select name="orgCountry" class="formfieldinputs" onchange="loadStates(&#39;org&#39;)">
                                                                        <option value=""></option>
                                                                    </select>
                                                                    <div id="tmsComponent-IconWithTooltipTag--1-a9e10ef7-c368-4f52-a2fd-628e5ac06e92" class="js-tms-component-root  icon-with-tooltip--container"></div>
                                                                    <script type="text/javascript">
                                                                        (function() {
                                                                        var props = {"name":"information_reversed","id":"orgCntry","dangerHTML":false};
                                                                        var children = [];
                                                                        TMSComponents.init(document.getElementById('tmsComponent-IconWithTooltipTag--1-a9e10ef7-c368-4f52-a2fd-628e5ac06e92'), TMSComponents['IconWithTooltip'], props, children);
                                                                        })();
                                                                    </script>
                                                                </td>
                                                                <td><input type="text" name="orgCity"  value="" class="formfieldinputs" size="20" maxlength="50" /></td>
                                                                <td>
                                                                    <select name="orgState" class="formfieldinputs">
                                                                        <option value=""></option>
                                                                    </select>
                                                                </td>
                                                                <td nowrap="nowrap"><input type="text" name="orgZip"  value="" class="formfieldinputs" size="15" maxlength="15" /></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td nowrap="nowrap" valign="top" width="50%">
                                <table>
                                    <tr class="searchcriteria">
                                        <td width="5%" colspan="5">Destination</td>
                                    </tr>
                                    <tr class="searchcriteria">
                                        <td>
                                            <table class="lean_table geography-table">
                                                <tr class="searchcriteria">
                                                    <td class="geography-name" colspan="5">
                                                        <span class="search-criteria-input-label">
                                                        Name
                                                        </span>
                                                        <input type="text" name="destName" size="35" maxlength="50" value="" class="formfieldinputs" />
                                                    </td>
                                                </tr>
                                                <tr class="searchcriteria">
                                                    <td width="100%">
                                                        <table class="leanTable" style="width:100.0%">
                                                            <tr class="searchcriteria">
                                                                <td>Country</td>
                                                                <td>City</td>
                                                                <td>State/Province</td>
                                                                <td>Postal Code</td>
                                                            <tr class="searchcriteria">
                                                                <td nowrap="nowrap">
                                                                    <select name="destCountry" class="formfieldinputs" onchange="loadStates(&#39;dest&#39;)">
                                                                        <option value=""></option>
                                                                    </select>
                                                                    <div id="tmsComponent-IconWithTooltipTag--2-e5ac8bfb-156f-4e68-b32f-e5fb85502b33" class="js-tms-component-root  icon-with-tooltip--container"></div>
                                                                    <script type="text/javascript">
                                                                        (function() {
                                                                        var props = {"name":"information_reversed","id":"destCntry","dangerHTML":false};
                                                                        var children = [];
                                                                        TMSComponents.init(document.getElementById('tmsComponent-IconWithTooltipTag--2-e5ac8bfb-156f-4e68-b32f-e5fb85502b33'), TMSComponents['IconWithTooltip'], props, children);
                                                                        })();
                                                                    </script>
                                                                </td>
                                                                <td><input type="text" name="destCity"  value="" class="formfieldinputs" size="20" maxlength="50" /></td>
                                                                <td>
                                                                    <select name="destState" class="formfieldinputs">
                                                                        <option value=""></option>
                                                                    </select>
                                                                </td>
                                                                <td nowrap="nowrap"><input type="text" name="destZip"  value="" class="formfieldinputs" size="15" maxlength="15" /></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <table class="lean_table" width="100%" style="margin-top: 20px;">
                        <tr class="searchcriteria">
                            <td nowrap="nowrap" valign="top" width="12%">
                                <table cellpadding="0" cellspacing="0" border="0">
                                    <tr class="searchcriteria">
                                        <td>Drop Date</td>
                                    </tr>
                                    <tr class="searchcriteria">
                                        <td>
                                            <select size="1" name="destQuickDate" onChange="setQuickDate(this, 'dest')" class="formfieldinputs">
                                                <option value="0">-- Choose --</option>
                                                <option value="1">Today</option>
                                                <option value="3">Tomorrow</option>
                                                <option value="2">Yesterday</option>
                                                <option value="4">This Week</option>
                                                <option value="5">Last Week</option>
                                                <option value="6">Next Week</option>
                                            </select>
                                            <br />
                                            <table class="lean_table">
                                                <tr class="searchcriteria">
                                                    <td class="search-criteria-input-label">Start</td>
                                                    <td><input type="text" size="8" name="destStartDate" value="" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('dest');" class="ll_datepicker formfieldinputs" /></td>
                                                </tr>
                                                <tr class="searchcriteria">
                                                    <td class="search-criteria-input-label">End</td>
                                                    <td><input type="text" size="8" name="destEndDate" value="" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('dest');" class="ll_datepicker formfieldinputs" /></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td nowrap="nowrap" valign="top" width="12%">
                                <table cellpadding="0" cellspacing="0" border="0">
                                    <tr class="searchcriteria">
                                        <td>Tendered Date</td>
                                    </tr>
                                    <tr class="searchcriteria">
                                        <td>
                                            <select size="1" name="tenderQuickDate" onChange="setQuickDate(this, 'tender')" class="formfieldinputs">
                                                <option value="0">-- Choose --</option>
                                                <option value="1">Today</option>
                                                <option value="3">Tomorrow</option>
                                                <option value="2">Yesterday</option>
                                                <option value="4">This Week</option>
                                                <option value="5">Last Week</option>
                                                <option value="6">Next Week</option>
                                                <option value="8">Last Month</option>
                                            </select>
                                            <br />
                                            <table class="lean_table">
                                                <tr class="searchcriteria">
                                                    <td class="search-criteria-input-label">Start</td>
                                                    <td><input type="text" size="8" name="tenderStartDate" value="" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('tender');" class="ll_datepicker formfieldinputs" /></td>
                                                </tr>
                                                <tr class="searchcriteria">
                                                    <td class="search-criteria-input-label">End</td>
                                                    <td><input type="text" size="8" name="tenderEndDate" value="" onchange="ll.util.formatAndValidateDateInput(this); resetQuickDate('tender');" class="ll_datepicker formfieldinputs" /></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td nowrap="nowrap" valign="top" width="12%">
                                Tender Origin<br />
                                <label class="search-criteria-checkbox">
                                <input type="checkbox" name="chkNextTender" value="ON"  />
                                <span>
                                NexTender
                                </span>
                                </label>
                                <br />
                                <label class="search-criteria-checkbox">
                                <input type="checkbox" name="chkExchange" value="ON"  />
                                <span>
                                Spot Market
                                </span>
                                </label>
                            </td>
                            <td nowrap="nowrap" valign="top">
                                <label class="search-criteria-checkbox">
                                <input type="checkbox" name="includeOldLoads" value="ON"  />
                                <span>
                                Search for information older than 30 days
                                <br />
                                (Does not apply if tender dates exists)
                                </span>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </form>
        <div class="pagestatus"></div>
    </body>
</html>