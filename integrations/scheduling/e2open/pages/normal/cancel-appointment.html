<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="CancelAppt" class="transactional-page">
    <head>
        <title>
            Cancel Appointment
        </title>
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
        <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
        <script type="text/javascript">
            var faviconICO = document.querySelector('#faviconICO');
            var faviconPNG = document.querySelector('#faviconPNG');
            var darkModeListener = function(event) {
            if (event.matches) {
            faviconICO.setAttribute("href","/favicon_dark.ico");
            faviconPNG.setAttribute("href","/favicon_dark.png");
            } else {
            faviconICO.setAttribute("href","/favicon.ico");
            faviconPNG.setAttribute("href","/favicon.png");
            }
            };
            var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
            if(darkModePreference.addEventListener){
            darkModePreference.addEventListener('change', function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            } else {
            darkModePreference.addListener(function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            }
            darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/fallback.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <script type="text/javascript">
            function popupMaestro() {




            }
            onDocumentReady(function() {
            var iconContainer = document.createElement('div');
            jQuery(document.body).prepend(iconContainer);
            jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
            popupMaestro();
            });
        </script>
        <script type="text/javascript">
            var useComponents = false;
            var newStylesComponents = false;
            var useETOComponents = false;
            var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
        </script>
        <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            ll.util.initFormats(1, 1,
            'en_US');
            var shortLang = 'en';
            var browserCacheKey = 'ba05ba21\-71c6\-4258\-bf8c\-5c8ff05a2a4a';
            ll.lang = ll.lang || {};
            ll.lang.locale = 'en_US';
        </script>
        <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/Modal.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/Popover.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/PanelTransition.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            jQuery.noConflict();
            var LeanActionFormName = 'cancelApptForm';
            var LeanActionFullPath = '\/apptschedule\/cancelappt.do';
            var $LeanActionForm;
            onDocumentReady(function() {


            $LeanActionForm = jQuery('form[name=cancelApptForm]');



            if (get_browser() === "msie") {
            jQuery('html').addClass("ie");
            }
            if ( 'noValidate' in document.createElement('form') ){
            jQuery('html').addClass("novalidate");
            }
            var $dwrImageDiv = jQuery('#dwrImageDiv');
            if (!$dwrImageDiv.length) {
            $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
            $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
            jQuery('body').append($dwrImageDiv);
            }


            hasExpiredSessionWarningBeenDisplayed = false;
            });
        </script>
        <script>
            function validateForm() {
            var thisForm = document.forms["cancelApptForm"];
            var valid = true;
            if (thisForm.elements["comments"].value == "") {
            alert("Please enter a comment.");
            thisForm.elements["comments"].focus();
            valid = false;
            } else {
            if (thisForm.elements["consolidatedAppointment"].value == "true" && thisForm.elements["removeLoadFromConsolidatedAppt"].value != "true") {
            var proceed = confirm("This stop is on a consolidated appointment.  Continue to cancel appointment for all loads?");
            if(proceed == false) {
            valid = false;
            }
            }
            }
            if (false && jQuery("select[name=reasonCode]").val() == "-1") {
            alert("A reason code is required.  Please select a reason code.");
            valid = false;
            }
            if(valid) {
            if(thisForm.elements["removeLoadFromConsolidatedAppt"].value == "true") {
            thisForm.elements["pageaction"].value = "removeLoadFromConsolidatedAppt";
            }
            $LeanActionForm.find('button').prop('disabled', true);
            thisForm.submit();
            }
            }
            function apptCancelled(stopID) {
            try {
            parent.opener.apptCancelled(stopID);
            } catch (error) {}
            }
            function stopRemovedFromAppt(stopID) {
            try {
            parent.opener.stopRemovedFromAppt(stopID);
            } catch (error) {}
            }
            onDocumentReady(function() {
            if(false) {
            jQuery(jQuery("select[name=reasonCode]").children()[1]).prop("selected", "selected");
            }
            });
        </script>
        <style>
            .appoint-details td {
            padding-bottom: 10px;
            }
            .appoint-details {
            width: calc(100% - 20px);
            margin: 10px;
            }
        </style>
    </head>
    <body>
        <div class="page-header">
            <div class="page-header__container">
                <div class="page-header__main">
                    <div class="page-header__title-container">
                        <div class="page-header__page-title">Cancel Appointment</div>
                        <div class="page-header__page-title page-header__page-title--to-top">
                            <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                            Return to the top of Cancel Appointment                 </button>
                        </div>
                    </div>
                    <div class="page-header__options">
                        <button  class="text " onclick="closePopupWindow()" type="button">Close Window</button>
                    </div>
                </div>
                <div class="page-message" id="page-message-root">
                    <div class="page-message__container">
                        <div class="page-message__icon-container"></div>
                        <div class="page-message__message-container">
                            <ul class="page-message__messages">
                                <li class="page-message__message page-message__message--primary"></li>
                            </ul>
                            <div class="page-message__button-container" style="display: none;"></div>
                        </div>
                        <button type="button" class="icon-button page-message__close-button">
                            <span role="tooltip" class="icon-span">
                                <svg class="icon  ic_close" focusable="false">
                                    <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
                <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
                <script>
                    (function() {
                    var errors = [

                    ];
                    var success = [

                    ];
                    onDocumentReady(function() {
                    var oldPageMessages = document.querySelectorAll('.old-page-message');
                    for(var i = 0; i < oldPageMessages.length; i++) {
                    var oldMessage = oldPageMessages[i];
                    oldMessage.parentElement.removeChild(oldMessage);
                    }
                    if(errors.length) {
                    PageMessage.error(errors, null, null, false);
                    } else if(success.length) {
                    PageMessage.success(success, null, null, false);
                    }
                    });
                    })();
                </script>
            </div>
        </div>
        <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
        <form name="cancelApptForm" method="post" action="/apptschedule/cancelappt.do" class="leanform">
            <input type="hidden" name="pageaction" value="cancelAppt">
            <input type="hidden" name="stopID" value="412302370">
            <input type="hidden" name="consolidatedAppointment" value="false">
            <input type="hidden" name="removeLoadFromConsolidatedAppt" value="false">
            <input type="hidden" name="appointmentStopIDString" value="412302370">
            <div class="rulesheaders subheader">
                Appointment Information
            </div>
            <table class="appoint-details">
                <tr>
                    <td width="10%">
                        <b>Load ID</b>
                    </td>
                    <td>
                        188481886
                    </td>
                    <td>
                        <b>Plan Date</b>
                    </td>
                    <td>
                        03/27/2025
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Location</b>
                    </td>
                    <td>
                        240 KINDIG &nbsp;
                        240 KINDIG LANE &nbsp;
                    </td>
                    <td>
                        <b>Current Appointment&nbsp;</b>
                    </td>
                    <td>
                        04/04/2025 13:00 (CONFIRMED)
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        HANOVER,
                        PA
                        17331
                        US
                    </td>
                    <td>
                        <b>Estimated Loading/Unloading Time</b>
                    </td>
                    <td>
                        60
                        Minutes
                    </td>
                </tr>
                <tr>
                    <td colspan="4">
                        Please select one of the available pickup appointments
                    </td>
                </tr>
            </table>
            <div class="rulesheaders subheader">
                Are you sure you want to cancel this Appointment?
            </div>
            <table class="appoint-details">
                <tr>
                    <td>
                        <span class="required-asterisk"></span>
                        <b>Comments</b>
                    </td>
                    <td>
                        <textarea name="comments" cols="50" rows="4"></textarea>
                    </td>
                </tr>
            </table>
            <div class="action-bar">
                <button type="button" id ="test-actionBtn" class="leanbutton" onclick="validateForm();">
                Continue
                </button>
            </div>
        </form>
    </body>
</html>