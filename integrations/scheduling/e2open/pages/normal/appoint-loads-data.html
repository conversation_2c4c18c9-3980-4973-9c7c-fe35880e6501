<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="CarrierAppointmentSearch" class="search-page">
    <head>
        <title>Appoint Loads</title>
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
        <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
        <script type="text/javascript">
            var faviconICO = document.querySelector('#faviconICO');
            var faviconPNG = document.querySelector('#faviconPNG');
            var darkModeListener = function(event) {
            if (event.matches) {
            faviconICO.setAttribute("href","/favicon_dark.ico");
            faviconPNG.setAttribute("href","/favicon_dark.png");
            } else {
            faviconICO.setAttribute("href","/favicon.ico");
            faviconPNG.setAttribute("href","/favicon.png");
            }
            };
            var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
            if(darkModePreference.addEventListener){
            darkModePreference.addEventListener('change', function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            } else {
            darkModePreference.addListener(function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            }
            darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <script type="text/javascript">
            function popupMaestro() {




            }
            onDocumentReady(function() {
            var iconContainer = document.createElement('div');
            jQuery(document.body).prepend(iconContainer);
            jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
            popupMaestro();
            });
        </script>
        <script type="text/javascript">
            var useComponents = true;
            var newStylesComponents = false;
            var useETOComponents = true;
            var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
        </script>
        <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            ll.util.initFormats(1, 1,
            'en_US');
            var shortLang = 'en';
            var browserCacheKey = 'ba05ba21\-71c6\-4258\-bf8c\-5c8ff05a2a4a';
            ll.lang = ll.lang || {};
            ll.lang.locale = 'en_US';
        </script>
        <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            jQuery.noConflict();
            var LeanActionFormName = 'CarrierAppointmentSearchForm';
            var LeanActionFullPath = '\/apptschedule\/carrierappointmentsearch.do';
            var $LeanActionForm;
            onDocumentReady(function() {


            $LeanActionForm = jQuery('form[name=CarrierAppointmentSearchForm]');



            if (get_browser() === "msie") {
            jQuery('html').addClass("ie");
            }
            if ( 'noValidate' in document.createElement('form') ){
            jQuery('html').addClass("novalidate");
            }
            var $dwrImageDiv = jQuery('#dwrImageDiv');
            if (!$dwrImageDiv.length) {
            $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
            $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
            jQuery('body').append($dwrImageDiv);
            }


            hasExpiredSessionWarningBeenDisplayed = false;
            });
        </script>
        <script type="text/javascript" src="/execution/vessel.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            var reasonCodeProfileData = {"stopData":{},"profiles":{}};
            var apptValidationData= {"412073128":{"isEndDateTimeOpen":true,"endDate":"01/01/2100","isZeroRange":false,"startTime":"00:00","endTime":"00:00","isFullRange":true,"isStartDateTimeOpen":true,"startDate":"01/01/2000"}};
            var popupWin;
            var loadDates = {};
            function addDate(name, detailNum, definedType, checkTime, required) {
            var tempRow = new Array(name,definedType,checkTime,required);
            loadDates[detailNum][definedType] = tempRow;
            }
            var loadValidation = [[{"commentRequired":false,"appointmentChanged":false,"requiredAppointmentFCFS":false,"referenceNumberRequired":false},{"commentRequired":false,"appointmentChanged":false,"requiredAppointmentFCFS":true,"referenceNumberRequired":false}]];
            function setQuickDate(select) {
            var opt = getSelectedValue(select);
            var form = document.forms["CarrierAppointmentSearchForm"];
            var startDate = form.elements["pickDateStart"].value;
            var endDate = form.elements["pickDateEnd"].value;
            switch(opt){
            case "1": startDate="04/17/2025";endDate="04/17/2025";break;
            case "2": startDate="04/16/2025";endDate="04/16/2025";break;
            case "3": startDate="04/18/2025";endDate="04/18/2025";break;
            case "4": startDate="04/13/2025";endDate="04/19/2025";break;
            case "5": startDate="04/06/2025";endDate="04/12/2025";break;
            case "6": startDate="04/20/2025";endDate="04/26/2025";break;
            case "7": startDate="04/01/2025";endDate="04/30/2025";break;
            case "8": startDate="03/01/2025";endDate="03/31/2025";break;
            case "9": startDate="05/01/2025";endDate="05/31/2025";break;
            case "16": startDate="04/01/2025";endDate="06/30/2025";break;
            case "17": startDate="01/01/2025";endDate="03/31/2025";break;
            case "18": startDate="07/01/2025";endDate="09/30/2025";break;
            case "10": startDate="01/01/2025";endDate="04/17/2025";break;
            case "12": startDate="06/01/2025";endDate="06/30/2025";break;
            case "19": startDate="01/17/2025";endDate="04/17/2025";break;
            case "20": startDate="10/17/2024";endDate="04/17/2025";break;
            case "13": startDate="04/17/2024";endDate="04/17/2025";break;
            case "14": startDate="10/17/2023";endDate="04/17/2025";break;
            case "15": startDate="04/06/2025";endDate="04/26/2025";break;
            default: startDate=""; endDate=""; break;
            }
            form.elements["pickDateStart"].value = startDate;
            form.elements["pickDateEnd"].value = endDate;
            }
            function enableReasonInputs(idx, detailIdx) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var reasonCode = form.elements["results["+idx+"].details["+detailIdx+"].reasonCode"];
            var reasonCodeComment = form.elements["results["+idx+"].details["+detailIdx+"].reasonCodeComment"];
            if (reasonCode) {
            reasonCode.disabled = false;
            showCommentRequired(idx, detailIdx);
            }
            if (reasonCodeComment) {
            reasonCodeComment.disabled = false;
            }
            }
            function showCommentRequired(idx, detailIdx) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var reasonCode = form.elements["results["+idx+"].details["+detailIdx+"].reasonCode"];
            var option = reasonCode.options[reasonCode.selectedIndex];
            var commentRequiredDiv = document.getElementById("reasonCheckLabel["+idx+"]["+detailIdx+"]");
            if(option.id == "true") {
            commentRequiredDiv.style.display = "";
            loadValidation[idx][detailIdx].commentRequired = true;
            } else {
            commentRequiredDiv.style.display = "none";
            loadValidation[idx][detailIdx].commentRequired = false;
            }
            }
            function updateDropTrailer(checkbox, hiddenFieldName) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var dropTrailerType = form.elements[hiddenFieldName];
            if(checkbox.checked) {
            dropTrailerType.value = 2;
            } else {
            dropTrailerType.value = 1;
            }
            }
            function getDriverInfo(request, response) {
            ll.util.callAjaxAction('/tmsrest/autocomplete/carrier_drivers?q=' + encodeURIComponent(request.term), response);
            }
            function driverSelected(inputElement, selectedElement, driverObject) {
            jQuery(inputElement).val(driverObject.driverName);
            if(driverObject.defaultVehicleNumber != null) {
            jQuery(inputElement).closest('tr').find('input[name$="vehicleNumber"]').val(driverObject.defaultVehicleNumber);
            }
            }
            function driverNameSelector(driverObject) {
            return driverObject.driverName;
            }
            function getVehicleInfo(request, response) {
            ll.util.callAjaxAction('/tmsrest/autocomplete/carrier_vehicles?q=' + encodeURIComponent(request.term), response);
            }
            function vehicleSelected(inputElement, selectedElement, vehicleNumber) {
            jQuery(inputElement).val(vehicleNumber.value);
            }
            function vehicleNumberSelector(vehicleNumber) {
            return vehicleNumber;
            }
            onDocumentReady(function() {
            jQuery('div.criteria * input').on('keypress', function(e) {
            if (e.which == 13) {
            ll.util.triggerEvent(this, 'blur');
            ll.util.triggerEvent(this, 'change');
            ll.util.triggerEvent(ll.util.triggerEvent(jQuery('#searchButton'), 'focus'), 'click');
            }
            });
            jQuery('form[name="CarrierAppointmentSearchForm"] input')
            .filter('[name$="appointScheduledOnDate"], [name$="appointStartDateTime"], [name$="appointEndDateTime"]')
            .each(function() {
            var dDate = jQuery(this).closest('tr').find('input[name$="quickKeyDDate"]').val();
            jQuery(this).on('change', function(e) {
            var dateFld, timeFld, fieldType;
            if (jQuery(this).prop('name').indexOf('$TIME$') > -1) {
            dateFld = jQuery('input[name="' + this.name.substring(7) + '"]').get(0);
            timeFld = this;
            fieldType = "time";
            } else {
            dateFld = this;
            timeFld = jQuery('input[name="-$TIME$' + this.name + '"]').get(0);
            fieldType = "date";
            }
            ll.util.quickKey(dateFld, timeFld, fieldType, dDate);
            if (fieldType == "date") {
            ll.util.formatAndValidateDateInput(this);
            } else {
            checkMilitaryTime(this, true);
            }
            })
            });
            jQuery('input[name$="driverName"]').ll_autocomplete( { source: getDriverInfo, selectItem:driverSelected, displayItem:driverNameSelector, minLength:2, choices:15 });
            jQuery('input[name$="vehicleNumber"]').ll_autocomplete( { source: getVehicleInfo, selectItem:vehicleSelected, displayItem:vehicleNumberSelector, minLength:2, choices:15 });
            jQuery('.drayLoad').on('change keyup', ll.vessel.onContainerRefChange);
            var sortLinkLoadNum = document.getElementById('sortLinkLoadNum');
            var sortLinkDueDate = document.getElementById('sortLinkDueDate');
            var sortLinkApptDate = document.getElementById('sortLinkApptDate');
            if(sortLinkLoadNum){
            sortLinkLoadNum.addEventListener('click', function() { sortResults('SORT_FLD_LOADNUM')});
            }
            if(sortLinkDueDate){
            sortLinkDueDate.addEventListener('click', function() { sortResults('SORT_FLD_DUEDATE')});
            }
            if(sortLinkApptDate){
            sortLinkApptDate.addEventListener('click', function() { sortResults('SORT_FLD_APPTDATE')});
            }
            });
            function search() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var searchByRef = false;
            var searchByType = false;
            if (!validateMultipleFields(form,"leanIDString","integer")) {
            return false;
            }
            if (!validateMultipleFields(form,"proNumString","quotableRefNum")) {
            return false;
            }
            if (!validateMultipleFields(form,"refNumString","quotableRefNum")) {
            return false;
            }
            if (!ll.util.checkDate(form.elements["pickDateStart"], true)) {
            return false;
            }
            if (!ll.util.checkDate(form.elements["pickDateEnd"], true)) {
            return false;
            }
            if (form.elements["leanIDString"].value.length > 0
            || form.elements["proNumString"].value.length > 0
            || form.elements["refNumString"].value.length > 0)
            {
            searchByRef = true;
            }
            if (form.elements["pickDateStart"].value.length > 0 || form.elements["pickDateEnd"].value.length > 0 ||
            form.elements["searchQO.unappt"].checked || form.elements["searchQO.partAppt"].checked || form.elements["searchQO.fullAppt"].checked)
            {
            searchByType = true;
            }
            if ((form.elements["searchQO.groups"].selectedIndex == -1 || form.elements["searchQO.groups"].selectedIndex == null) && !form.elements["searchQO.myLoads"].checked) {
            alert("You must select at least one group or choose to search for \x27My Loads\x27.");
            return false;
            }
            if (!searchByRef && !searchByType) {
            alert("You must enter some search criteria before a search can be performed.");
            return false;
            }
            form.elements["pageaction"].value = "search";
            var searchBtn = document.getElementById('searchButton');
            searchBtn.readOnly = true;
            searchBtn.className = "search_disabled";
            searchBtn.title = "To Cancel the Search, click Stop on the Browser";
            form.submit();
            }
            function sortResults(sortByOption) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var sortByFld = form.elements["searchQO.sortBy"];
            var sortOrderFld = form.elements["searchQO.sortAscending"];
            if (sortByFld.value == sortByOption) {
            sortOrderFld.value = (sortOrderFld.value == "true" ? "false" : "true");
            } else {
            sortByFld.value = sortByOption;
            sortOrderFld.value = "true";
            }
            form.elements["pageaction"].value = "sort";
            form.submit();
            }
            function nextPage() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            form.elements["pageaction"].value = "nextPage";
            form.submit();
            }
            function previousPage() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            form.elements["pageaction"].value = "previousPage";
            form.submit();
            }
            function submitFormForSave() {
            if (popupWin != null && !popupWin.closed) {
            popupWin.close();
            }
            var form = document.forms["CarrierAppointmentSearchForm"];
            form.elements['submitBtn'].disabled = true;
            form.elements["pageaction"].value = "save";
            form.submit();
            }
            function save() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var today = new Date();

            var loadApptDates = new Object();
            var checkApptDates = false;
            for (var loadIdx=0; loadIdx < 1; loadIdx++) {
            var loadInfo = loadValidation[loadIdx];
            var proNum = form.elements["results["+loadIdx+"].proNum"];
            if (!validateQuotedRefNumField(proNum)) {
            return false;
            }
            var pastRestrictApptDays = form.elements["results["+loadIdx+"].pastRestrictApptDays;"];
            var futureRestrictApptDays = form.elements["results["+loadIdx+"].futureRestrictApptDays;"];
            today.setHours(23);
            today.setMinutes(59);
            today.setSeconds(59);
            var futureDaysAhead = new Date(today.getTime()+(futureRestrictApptDays*24*60*60*1000));
            today.setHours(0);
            today.setMinutes(0);
            today.setSeconds(0);
            var pastDaysAgo = new Date(today.getTime()-(pastRestrictApptDays*24*60*60*1000));
            var loadID = form.elements["results["+loadIdx+"].loadID"].value;
            var apptDates = new Array();
            for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
            var validationObj = loadValidation[loadIdx][detailIdx];
            validationObj.commentRequired;
            validationObj.appointmentChanged;
            var appointmentStartDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
            var appointmentStartTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
            var appointmentEndDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
            var appointmentEndTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
            var appointmentScheduledDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointScheduledOnDate"];
            var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
            var appointmentChanged = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointmentChanged"].value;

            if (appointmentScheduledDate != null && appointmentStartDate != null) {
            var appointmentFCFS = form.elements["results["+loadIdx+"].details["+detailIdx+"].FCFS"];
            var appointmentReference = form.elements["results["+loadIdx+"].details["+detailIdx+"].apptReference"];
            var appointmentDropTrailer = form.elements["results["+loadIdx+"].details["+detailIdx+"].dropTrailer"];
            var appointmentScheduledTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointScheduledOnDate"];
            var dateTimeFields = [];
            dateTimeFields.push({date: appointmentScheduledDate, time: appointmentScheduledTime});
            dateTimeFields.push({date: appointmentStartDate, time: appointmentStartTime});
            dateTimeFields.push({date: appointmentEndDate, time: appointmentEndTime});
            for (var i=0; i<dateTimeFields.length; i++) {
            var date = dateTimeFields[i].date;
            var time = dateTimeFields[i].time;

            if (!ll.util.checkDate(date, true)) {
            return false;
            }
            if (!checkMilitaryTime(time, true)) {
            return false;
            }

            if (!isEmpty(time.value) && isEmpty(date.value)) {
            alert('If entering an appointment time you must enter an appointment date');
            date.focus();
            return false;
            }

            if (!isEmpty(date.value)) {
            var jsDate = ll.util.jsDateTime(date.value, 23, 59);
            if (jsDate < pastDaysAgo) {
            alert(ll.util.formatMessage('One of the dates for Load# {0} cannot be more than {1} days ago. \n Please enter a valid date.', loadID, pastApptDays));
            date.focus();
            return false;
            }
            if (jsDate > futureDaysAhead) {
            alert(ll.util.formatMessage('One of the dates for Load# {0} cannot be more than {1} days in the future. \n Please enter a valid date.', loadID, futureApptDays));
            date.focus();
            return false;
            }
            }
            }

            if (appointmentFCFS.checked && validationObj.requiredAppointmentFCFS) {
            if (isEmpty(appointmentStartDate.value)) {
            alert(ll.util.formatMessage('An appointment date is required with FCFS for load ID# {0}.', loadID));
            appointmentStartDate.focus();
            return false;
            }
            }
            if (!appointmentFCFS.checked && !isEmpty(appointmentStartDate.value) && isEmpty(appointmentStartTime.value)) {
            alert('If FCFS has not been selected, you must enter a time when entering an appointment date.');
            appointmentStartTime.focus();
            return false;
            }
            if (isEmpty(appointmentStartDate.value) && !isEmpty(appointmentEndDate.value)) {
            alert('You may not have an end time without entering a start time.');
            appointmentStartDate.focus();
            return false;
            }


            if (!isEmpty(appointmentStartDate.value) && !isEmpty(appointmentEndDate.value)) {
            if (ll.util.getJSDateMilitaryTime(appointmentStartDate.value, appointmentStartTime.value) > ll.util.getJSDateMilitaryTime(appointmentEndDate.value, appointmentEndTime.value)) {
            alert('Appointment start time is set after the end time.  This is not allowed.');
            appointmentStartTime.focus();
            return false;
            }
            }
            var datePresent = (appointmentFCFS.checked || !isEmpty(appointmentStartDate.value));
            if (!datePresent && !isEmpty(appointmentReference.value)) {
            alert('Reference numbers cannot be saved unless an appointment date is also present or FCFS is checked.');
            appointmentReference.focus();
            return false;
            }
            var referenceNumberRequired = (datePresent && validationObj.referenceNumberRequired);
            if (!validateApptRef(appointmentReference, referenceNumberRequired)) {
            return false;
            }
            if (appointmentDropTrailer && appointmentDropTrailer.type == "checkbox" && appointmentDropTrailer.checked) {
            if (isEmpty(appointmentStartDate.value) && !appointmentFCFS.checked) {
            alert('Trailer Loading Type setting cannot be saved unless an appointment date is also present or FCFS is checked.');
            appointmentStartDate.focus();
            return false;
            }
            }
            var validTimeRange = apptValidationData[loaddetailid];
            if(validTimeRange != null && appointmentChanged == 'true'){
            var validStartDate = ll.util.getJSDateMilitaryTime(validTimeRange.startDate, validTimeRange.startTime );
            var validEndDate = ll.util.getJSDateMilitaryTime(validTimeRange.endDate, validTimeRange.endTime );
            var apptStartDate = ll.util.getJSDateMilitaryTime( appointmentStartDate.value, appointmentStartTime.value );
            if(!validTimeRange.isFullRange){
            if(validTimeRange.isZeroRange){
            var alertMsg = "No Valid Appointment dates based on rules.";
            alert(alertMsg);
            appointmentStartDate.focus();
            return false;
            } else {
            if(apptStartDate.getTime() < validStartDate.getTime() || apptStartDate.getTime() > validEndDate.getTime() ){
            var alertMsg = "Appointment date is not valid based on rules.";
            alertMsg = alertMsg + "\n\n" + "Valid appointment range:" + "\n";
            if(!validTimeRange.isStartDateTimeOpen){
            alertMsg = alertMsg + "\t\t " + ll.util.convertDateToString(validStartDate, true);
            } else{
            alertMsg = alertMsg + " Indefinite";
            }
            alertMsg = alertMsg + " - ";
            if(!validTimeRange.isEndDateTimeOpen){
            alertMsg = alertMsg + " " + ll.util.convertDateToString(validEndDate, true);
            } else{
            alertMsg = alertMsg + " Indefinite";
            }
            alert(alertMsg);
            appointmentStartDate.focus();
            return false;
            }
            }
            }
            }
            var rcStopData = reasonCodeProfileData.stopData[loaddetailid];
            var reasonCodeFld = form.elements["results["+loadIdx+"].details["+detailIdx+"].reasonCode"];
            if (datePresent && rcStopData && reasonCodeFld) {
            var rcProfile = reasonCodeProfileData.profiles[rcStopData.profileID];
            var selectedReason = getSelectedValue(reasonCodeFld);
            if (!reasonCodeFld.disabled && rcProfile.isReasonRequired && selectedReason == 0) {
            alert('A reason code is required.  Please select a reason code.');
            reasonCodeFld.focus();
            return false;
            }
            if (!reasonCodeFld.disabled && rcProfile.reasonCodes[selectedReason] && rcProfile.reasonCodes[selectedReason].isRequireComment) {
            var commentFld = form.elements["results["+loadIdx+"].details["+detailIdx+"].reasonCodeComment"];
            if (commentFld.value == '') {
            var alertMsg = ll.util.formatMessage('Please enter Comments for Reason Code: {0}', rcProfile.reasonCodes[selectedReason].label);
            alert(alertMsg);
            commentFld.focus();
            return false;
            }
            }
            }
            }

            var foundDates = false;
            var apptObject = new Object();
            apptObject.timezone = form.elements["timezone"+loaddetailid].value;
            if (appointmentStartDate != null && appointmentStartDate.value != "") {
            apptObject.startDate = appointmentStartDate.value;
            if(appointmentStartTime != null && appointmentStartTime.value != "") {
            apptObject.startTime = appointmentStartTime.value;
            }
            foundDates = true;
            if (appointmentStartDate.getAttribute("type") == "text") {

            checkApptDates = true;
            }
            }
            if (appointmentEndDate != null && appointmentEndDate.value != "") {
            apptObject.endDate = appointmentEndDate.value;
            if(appointmentEndTime != null && appointmentEndTime.value != "") {
            apptObject.endTime = appointmentEndTime.value;
            }
            foundDates = true;
            if (appointmentEndDate.getAttribute("type") == "text") {
            checkApptDates = true;
            }
            }
            if (foundDates) {

            apptDates.push(apptObject);
            }
            if (loadDates[loaddetailid]) {
            var loadDateArray = loadDates[loaddetailid];
            for (var d in loadDateArray) {
            var current = loadDateArray[d];
            var dateField = form.elements["stop" + loaddetailid +"type"+ current[1]];
            var timeField = form.elements["stop" + loaddetailid +"time"+ current[1]];
            var required = false;
            if (current[3] == 1 && !isEmpty(appointmentStartDate.value) && "--" !== appointmentStartDate.value) {
            required = true;
            }
            if (!ll.util.checkDate(dateField, !required)) {
            dateField.focus();
            dateField.select();
            return false;
            }
            if (current[2] == 0 && timeField) {
            if (!checkMilitaryTime(timeField, !required)) {
            timeField.focus();
            timeField.select();
            return false;
            }
            }
            }
            }
            }

            loadApptDates[loadID+""] = apptDates;
            var proNum = form.elements["results["+loadIdx+"].proNum"];
            var trailerNumber = form.elements["results["+loadIdx+"].trailerNumber"];
            var containerNumber = form.elements["results["+loadIdx+"].containerNumber"];
            var driverName = form.elements["results["+loadIdx+"].driverName"];
            var vehicleNumber = form.elements["results["+loadIdx+"].vehicleNumber"];
            var drayageContainerID = form.elements["results["+loadIdx+"].drayageContainerID"];
            if (!validateQuotedRefNumField(proNum)) {
            return false;
            }
            if (trailerNumber && !validateQuotedRefNumField(trailerNumber, true, 30, true)) {
            trailerNumber.focus();
            return false;
            }
            if (containerNumber && !validateQuotedRefNumField(containerNumber, true, 30, true)) {
            containerNumber.focus();
            return false;
            }
            if (containerNumber && drayageContainerID > 0 && !ll.vessel.validateContainerReference(jQuery(containerNumber))) {
            if (!confirm(ll.util.formatMessage('Container # \x27{0}\x27 is not in standard ISO 6346 format (e.g. \x27CSQU3054383\x27). Continue?', containerNumber.value))) {
            containerNumber.focus();
            return false;
            }
            }
            if (driverName && (!isString(driverName.value,true) || driverName.value.length > 60)) {
            alert('The Driver Name is invalid.');
            driverName.focus();
            return false;
            }
            if (vehicleNumber && !validateQuotedRefNumField(vehicleNumber, true, 30, true)) {
            vehicleNumber.focus();
            return false;
            }
            }

            if (checkApptDates) {

            ll.util.callAjaxAction("/tmsrest/datelookup/appt_sequence_warnings", onDateSequenceValidationLookup, null, null, 'POST',
            JSON.stringify(loadApptDates), false, 'text/plain; charset=UTF-8');
            } else {
            submitFormForSave();
            }
            }
            function onDateSequenceValidationLookup(warning) {
            if (isEmpty(warning) || confirm(warning+"\n\nContinue appointing the loads?")) {
            submitFormForSave();
            }
            }
            function checkApptTimes(chkBox, loadIdx, detailIdx) {
            if (!chkBox.checked) {
            var form = chkBox.form;
            var startDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
            var startTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointStartDateTime"];
            var endDate = form.elements["results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
            var endTime = form.elements["-$TIME$results["+loadIdx+"].details["+detailIdx+"].appointEndDateTime"];
            if ((startDate.value == endDate.value || isEmpty(endDate.value)) &&
            startTime.value == '00:00' && endTime.value == '23:59')
            {
            startTime.value = "";
            endTime.value = "";
            }
            }
            }
            function validateApptRef(refNum, refNumReq) {
            if (refNum != null && refNum.value != null) {
            if (refNumReq && isWhitespace(refNum.value)) {
            alert("Please enter an Appointment Confirmation #");
            refNum.focus();
            return false;
            }
            if (!isString(refNum.value, !refNumReq)) {
            alert("Reference numbers may only be letters, numbers and spaces.");
            refNum.focus();
            return false;
            }
            }
            return true;
            }
            function clearForm() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            form.elements["leanIDString"].value = "";
            form.elements["proNumString"].value = "";
            form.elements["refNumString"].value = "";
            form.elements["searchQO.shipper"].value = "";
            form.elements["pickDateStart"].value = "";
            form.elements["pickDateEnd"].value = "";
            form.elements["quickDate"].selectedIndex = 0;
            form.elements["searchQO.myLoads"].checked = true;
            var selectBox = form.elements["searchQO.groups"];
            for (var i=0; i < selectBox.options.length; i++) {
            selectBox.options[i].selected = false;
            }
            }
            function getEligibleDateRange(stopID, loadRowID, stopRowID, forConsolidation) {
            var form = document.forms['CarrierAppointmentSearchForm'];
            var rangeStartDate;
            var rangeStartTime;
            var rangeStartTimezone;
            var rangeEndDate;
            var rangeEndTime;
            var rangeEndTimezone;
            var numStops = loadValidation[loadRowID].length;
            var stopBefore = stopRowID - 1;
            var stopAfter = stopRowID + 1;
            if (stopBefore >= 0) {
            var detailNum = form.elements["results["+loadRowID+"].details["+stopBefore+"].detailNum"].value;
            var appointmentStartDate = form.elements["results["+loadRowID+"].details["+stopBefore+"].appointStartDateTime"].value;
            var appointmentStartTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopBefore+"].appointStartDateTime"].value;
            var appointmentEndDate = form.elements["results["+loadRowID+"].details["+stopBefore+"].appointEndDateTime"].value;
            var appointmentEndTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopBefore+"].appointEndDateTime"].value;
            var appointmentTimezone = form.elements["timezone"+detailNum].value;
            if (!isEmpty(appointmentEndDate)) {
            rangeStartDate = appointmentEndDate;
            if (!isEmpty(appointmentEndTime)) {
            rangeStartTime = appointmentEndTime;
            }
            rangeStartTimezone = appointmentTimezone;
            } else if(!isEmpty(appointmentStartDate)) {
            rangeStartDate = appointmentStartDate;
            if (!isEmpty(appointmentStartTime)) {
            rangeStartTime = appointmentStartTime;
            }
            rangeStartTimezone = appointmentTimezone;
            }
            }
            if (stopAfter < numStops) {
            var detailNum = form.elements["results["+loadRowID+"].details["+stopAfter+"].detailNum"].value;
            var appointmentStartDate = form.elements["results["+loadRowID+"].details["+stopAfter+"].appointStartDateTime"].value;
            var appointmentStartTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopAfter+"].appointStartDateTime"].value;
            var appointmentEndDate = form.elements["results["+loadRowID+"].details["+stopAfter+"].appointEndDateTime"].value;
            var appointmentEndTime = form.elements["-$TIME$results["+loadRowID+"].details["+stopAfter+"].appointEndDateTime"].value;
            var appointmentTimezone = form.elements["timezone"+detailNum].value;
            if(!isEmpty(appointmentStartDate)) {
            rangeEndDate = appointmentStartDate;
            if (!isEmpty(appointmentStartTime)) {
            rangeEndTime = appointmentStartTime;
            }
            rangeEndTimezone = appointmentTimezone;
            } else if (!isEmpty(appointmentEndDate)) {
            rangeEndDate = appointmentEndDate;
            if (!isEmpty(appointmentEndTime)) {
            rangeEndTime = appointmentEndTime;
            }
            rangeEndTimezone = appointmentTimezone;
            }
            }
            if (forConsolidation) {
            var range = new Object();
            range.startDate = rangeStartDate;
            range.startTime = rangeStartTime;
            range.startTimezone = rangeStartTimezone;
            range.endDate = rangeEndDate;
            range.endTime = rangeEndTime;
            range.endTimezone = rangeEndTimezone;
            return range;
            } else {
            var params= "";
            if (!isEmpty(rangeStartDate)) {
            params += "&rangeStartTimezone=" + rangeStartTimezone + "&rangeStartDate=" + rangeStartDate;
            if (!isEmpty(rangeStartTime)) {
            params += "&rangeStartTime=" + rangeStartTime;
            }
            }
            if (!isEmpty(rangeEndDate)) {
            params += "&rangeEndTimezone=" + rangeEndTimezone + "&rangeEndDate=" + rangeEndDate;
            if (!isEmpty(rangeEndTime)) {
            params += "&rangeEndTime=" + rangeEndTime;
            }
            }
            return params;
            }
            }
            function scheduleExternalProviderAppt(stopID) {
            var theURL = "/externalprovider/scheduleapptcontroller.do?stopID="+stopID;
            popupWin = popup(theURL, 'schappt', 1100, 650);
            }
            function scheduleAppt(stopID, loadRowID, stopRowID) {
            var params = getEligibleDateRange(stopID, loadRowID, stopRowID, false);
            var theURL = "/apptschedule/scheduleappt.do?stopIDs="+stopID+params;
            popupWin = popup(theURL, 'schappt', 1100, 650);
            }
            function scheduleDropTrailerAppt(stopID, loadRowID, stopRowID) {
            var params = getEligibleDateRange(stopID, loadRowID, stopRowID, false);
            var theURL = "/apptschedule/scheduledroptrailerappt.do?stopIDs="+stopID+params;
            popupWin = popup(theURL, 'schappt', 1100, 650);
            }
            function cancelAppt(stopID) {
            var theURL = "/apptschedule/cancelappt.do?stopID="+stopID;
            popupWin = popup(theURL, 'cancelappt', 700, 300);
            }
            function removeFromConsolidation(stopID) {
            var theURL = "/apptschedule/cancelappt.do?removeLoadFromConsolidatedAppt=true&stopID="+stopID;
            popupWin = popup(theURL, 'cancelappt', 700, 300);
            }
            function viewApptComments(stopID) {
            var theURL = "/apptschedule/carrierdockapptcomment.do?stopID=" + stopID;
            popupWin = popup(theURL, 'apptcomments', 800, 450);
            }
            function markAppointmentChanged(idx, detailIdx) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var appointmentChanged = form.elements["results["+idx+"].details["+detailIdx+"].appointmentChanged"];
            if(appointmentChanged) {
            appointmentChanged.value = true;
            }
            }
            function handleConsolidationAction() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var action = getSelectedValue(form.elements["consolidationOptions"])*1;
            switch(action) {
            case 1:
            createConsolidatedLiveAppointment();
            break;
            case 2:
            createConsolidatedDropAppointment();
            break;
            }
            }
            var stopIDsStr = "";
            function createConsolidatedLiveAppointment() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            stopIDsStr = "";
            var rangeObject = new Object();
            var rangeArray = new Array();
            for (var loadIdx=0; loadIdx < 1; loadIdx++) {
            var loadInfo = loadValidation[loadIdx];
            for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
            var checkbox = form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"];
            if(checkbox != null && checkbox.checked) {
            var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
            if(stopIDsStr.length > 0) {
            stopIDsStr += "&";
            }
            stopIDsStr += "stopIDs="+loaddetailid;
            rangeArray.push(getEligibleDateRange(loaddetailid, loadIdx, detailIdx, true));
            }
            }
            }
            if (!isEmpty(stopIDsStr)) {

            rangeObject.range = rangeArray;
            ll.util.callAjaxAction("/tmsrest/datelookup/eligible_date_ranges", openScheduleLiveAppt, null, null, 'POST',
            JSON.stringify(rangeObject), false, 'text/plain; charset=UTF-8');
            } else {
            alert("Please select one or more stops on which to perform the action.");
            }
            }
            function openScheduleLiveAppt(rangeObject) {
            var warning = rangeObject.warning;
            if (!warning || confirm(warning)) {
            var params = rangeObject.params;
            if (!params) {
            params = "";
            }
            var theURL = "/apptschedule/scheduleappt.do?"+stopIDsStr+params;
            popupWin = popup(theURL, 'schappt', 800, 450);
            }
            }
            function createConsolidatedDropAppointment() {
            var form = document.forms["CarrierAppointmentSearchForm"];
            stopIDsStr = "";
            var rangeObject = new Object();
            var rangeArray = new Array();
            for (var loadIdx=0; loadIdx < 1; loadIdx++) {
            var loadInfo = loadValidation[loadIdx];
            for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
            var checkbox = form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"];
            if(checkbox != null && checkbox.checked) {
            var loaddetailid = form.elements["results["+loadIdx+"].details["+detailIdx+"].detailNum"].value;
            if(stopIDsStr.length > 0) {
            stopIDsStr += "&";
            }
            stopIDsStr += "stopIDs="+loaddetailid;
            rangeArray.push(getEligibleDateRange(loaddetailid, loadIdx, detailIdx, true));
            }
            }
            }
            if (!isEmpty(stopIDsStr)) {

            rangeObject.range = rangeArray;
            ll.util.callAjaxAction("/tmsrest/datelookup/eligible_date_ranges", openScheduleDropAppt, null, null, 'POST',
            JSON.stringify(rangeObject), false, 'text/plain; charset=UTF-8');
            } else {
            alert("Please select one or more stops on which to perform the action.");
            }
            }
            function openScheduleDropAppt(rangeObject) {
            var warning = rangeObject.warning;
            if (!warning || confirm(warning)) {
            var params = rangeObject.params;
            if (!params) {
            params = "";
            }
            var theURL = "/apptschedule/scheduledroptrailerappt.do?"+stopIDsStr+params;
            popupWin = popup(theURL, 'schappt', 800, 450);
            }
            }
            function updateASMDisplay(stopID, apptDate, apptTime, apptStatusID, droptrailer) {
            var apptdivobj = document.getElementById("apptdiv"+stopID);
            var apptcommentdivobj = document.getElementById("apptcommentdiv"+stopID);
            if (apptdivobj != null) {
            var msgTxt = "";
            if (droptrailer) {
            msgTxt += "Drop Trailer<br />";
            }
            var apptStatusTxt = "";
            if (apptStatusID == '2') {
            apptStatusTxt = "CONFIRMED";
            } else if (apptStatusID == '1') {
            apptStatusTxt = "REQUESTED";
            }
            msgTxt += apptDate + ' ' + apptTime + ' <br /><span class="success-highling">' + apptStatusTxt + '<span>';
            jQuery('input[name^="results"]', apptdivobj).val(apptDate);
            jQuery('input[name^="-$TIME$"]', apptdivobj).val(apptTime);
            jQuery('span', apptdivobj).html(msgTxt);
            }
            if (apptcommentdivobj != null) {
            var msgTxt = "";
            if ((apptStatusID=="2")||(apptStatusID=="1")) {
            msgTxt = "<a href=\"javascript:viewApptComments(" +stopID+ ")\">Check Appt Comments</a>";
            }
            apptcommentdivobj.innerHTML = msgTxt;
            }
            }
            function apptCancelled(stopID) {
            jQuery("#apptdiv" + stopID).remove();
            jQuery("#apptcanceldiv" + stopID).remove();
            jQuery("#apptremovediv" + stopID).remove();
            }
            function stopRemovedFromAppt(stopID) {
            jQuery("#apptdiv" + stopID).remove();
            jQuery("#apptcanceldiv" + stopID).remove();
            jQuery("#apptremovediv" + stopID).remove();
            }
            function selectAllValues(stopType) {
            var form = document.forms["CarrierAppointmentSearchForm"];
            var consolidateAppointment = true;
            for (var loadIdx=0; loadIdx < 1; loadIdx++) {
            var loadInfo = loadValidation[loadIdx];
            for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
            if(stopType == form.elements["results["+loadIdx+"].details["+detailIdx+"].stopType"].value * 1) {
            if(form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"]) {
            consolidateAppointment = consolidateAppointment && form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"].checked;
            }
            }
            if(!consolidateAppointment) {
            break;
            }
            }
            if(!consolidateAppointment) {
            break;
            }
            }
            for (var loadIdx=0; loadIdx < 1; loadIdx++) {
            var loadInfo = loadValidation[loadIdx];
            for(var detailIdx=0; detailIdx < loadInfo.length; detailIdx++) {
            if(stopType == form.elements["results["+loadIdx+"].details["+detailIdx+"].stopType"].value * 1) {
            if(form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"]) {
            form.elements["results["+loadIdx+"].details["+detailIdx+"].consolidateAppointment"].checked = !consolidateAppointment;
            }
            }
            }
            }
            }
        </script>
        <style type="text/css">
            .field-input-label {
            white-space: nowrap;
            width: 1%;
            padding-right: 10px;
            }
            table.list td.action-section {
            text-align: right;
            }
            .save-button {
            margin-right: 10px;
            }
            .criteria {
            margin: 0 10px 20px 10px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ccc;
            font-weight: 700;
            }
            .criteria tr:first-of-type {
            vertical-align: top;
            }
            .criteria label {
            font-weight: 400;
            }
            .success-highling {
            color: #4cc14c;
            font-weight: 700;
            }
        </style>
    </head>
    <body>
        <script type="text/javascript" src="/c/js/menu_js_functions.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/menu/menu.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/menu/menu-search.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/map/mapControls.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            function popupCarrierConsole() {
            popup("/console/carrierconsole.do", "Console382705", 1100, 620);
            }
            function ll_popupSystemNotification(percent) {
            var w = 900, h = 600;
            if (window.screen) {
            w = window.screen.availWidth * percent / 100;
            h = window.screen.availHeight * percent / 100;
            }
            popup("/agent/systemnotification.do?", "notes", w, h);
            }

            onDocumentReady(function() {



            popupMaestro();
            });
        </script>
        <style type="text/css">
            .admin-info {
            padding-right: 10px;
            vertical-align: top;
            }
            .system-logo {
            text-align: center;
            display: inline-block;
            padding-left: 15px;
            padding-right: 15px;
            font-size: 15px;
            color: #4A4E50;
            }
            img.logo {
            height: 48px;
            }
            img.logo.logo__default {
            padding: 12px 0;
            }
            .display-none-item {
            display: none;
            }
            #cookie-policy-privacy-subtitle, #cookie-selection {
            margin-top: 25px;
            }
            #cookie-policy-privacy-subtitle, #strictlyCookies, #functionalCookies, #performanceAndAnalyticsCookies {
            font-size: 14px;
            line-height: 180%;
            }
            h3{
            font-weight: 600;
            font-size: 18px;
            }
            .eto-switch {
            display: -ms-flexbox;
            display: flex;
            position: relative;
            }
            .eto-switch__field {
            position: absolute;
            opacity: 0;
            z-index: 0;
            }
            .eto-switch__box {
            background-color: #FFF;
            background-image: linear-gradient(to top, rgba(70, 129, 147, 0), rgba(70, 129, 147, .24)), linear-gradient(rgba(70, 129, 147, .24), rgba(70, 129, 147, .24));
            border-radius: 2rem;
            display: block;
            height: 2.5rem;
            position: relative;
            transition-duration: .2s;
            transition-property: background-color,background-image;
            width: 5.5rem;
            }
            .eto-switch__box::before {
            background-color: #FFF;
            border-radius: 100%;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
            content: '';
            display: block;
            height: 2.2rem;
            position: absolute;
            top: calc(.20rem - 1px);
            transition-duration: .1s;
            transition-property: left;
            width: 2.2rem;
            z-index: 1;
            }
            .eto-switch__label--on {
            display: none;
            }
            .eto-switch__label,.eto-switch__label--off,.eto-switch__label--on {
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            line-height: 2.3rem;
            }
            .eto-switch__field:checked~.eto-switch__label--off {
            display: none;
            }
            .eto-switch--integrated .eto-switch__label--off,.eto-switch--integrated .eto-switch__label--on {
            left: 0;
            margin: 2px;
            position: absolute;
            }
            .eto-switch--integrated .eto-switch__label--off {
            padding-left: 3rem;
            }
            .eto-switch__field:checked~.eto-switch__label--on {
            display: block;
            }
            .eto-switch--integrated .eto-switch__label--on {
            color: #FFF;
            padding-left: 0.7rem;
            }
            .eto-switch__field:checked~.eto-switch__box {
            background-color: #277AB5;
            background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .24));
            }
            .eto-switch__field:checked~.eto-switch__box::before {
            left: calc(100% - 2.525rem);
            box-shadow: 0 0 0 2px transparent,0 3px 3px rgba(0, 0, 0, .3);
            }
            .eto-switch__field[disabled]~.eto-switch__box {
            background-color: #F6F6F6;
            background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, .16));
            cursor: default;
            }
            .eto-switch__field[disabled]~.eto-switch__box::before {
            background-color: #E5E8EB;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .24);
            }
        </style>
        <div class="primary-header">
            <div>
                <a href="http://www.blujaysolutions.com/" target="_top">
                <img
                    class="logo logo__default"
                    alt="Transportation Management v. tm4sprd07-web02-chg:master:2025-04-02_10-50-32"
                    src="/images/logos/e2open_logo.svg"
                    />
                </a>
                <div class="system-title">
                    Transportation Management
                </div>
            </div>
            <div class="items">
                <a href="javascript:_webhelp();" title="Help" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_help_header" focusable="false">
                            <use xlink:href="#ic_help_header" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="/tmshealthcheck/tmshealthcheck.do" title="TMS Health Status" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_monitor_heart" focusable="false">
                            <use xlink:href="#ic_monitor_heart" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="javascript:_customerSupport();" title="Customer Support" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_support_agent" focusable="false">
                            <use xlink:href="#ic_support_agent" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <a href="/agent/webmessages.do?query.current=true" title="Messages" class="item">
                    <span role="tooltip" class="icon-span item-image">
                        <svg class="icon  ic_bell" focusable="false">
                            <use xlink:href="#ic_bell" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </span>
                </a>
                <div class="item user" id="headerUserItem">
                    <div class="item-image">
                        <div title="My Account" class="user-name">
                            Brooke.Carroll
                            <span role="tooltip" class="icon-span">
                                <svg class="icon  ic_account_circle" focusable="false">
                                    <use xlink:href="#ic_account_circle" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <ul class="popover use-show-class" id="headerUserPopover">
                        <li>
                            <div class="defaults-label">Your defaults</div>
                            <table class="user-defaults">
                                <tbody>
                                    <tr>
                                        <td>Company:</td>
                                        <td>FETCH FREIGHT LLC</td>
                                    </tr>
                                    <tr>
                                        <td>Load Group:</td>
                                        <td>THE J. M. SMUCKER COMPANY</td>
                                    </tr>
                                </tbody>
                            </table>
                        </li>
                        <li class="link">
                            <a href="/security/managepassword.do">Account Information</a>
                        </li>
                        <li class="link">
                            <a href="javascript:populate()">PRIVACY SETTINGS</a>
                        </li>
                        <li class="link"><a href="/security/LogOut.jsp?dt=*************">
                            Log Out
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div id="cookieDialog" class="display-none-item"></div>
        <script>
            function headerInit() {
            var headerUserItem = document.getElementById('headerUserItem');
            var menuUserPopover = new Popover(document.getElementById('headerUserPopover'), headerUserItem);
            headerUserItem.addEventListener('click', function() {
            menuUserPopover.show();
            });
            }
            onDocumentReady(headerInit);
            function populate(){
            jQuery('#headerUserPopover').hide();
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            if (response != null) {
            var cookies = [];
            var cookieHtml = "<section class=\"eto-modal__body\">\n<div id=\"cookie-policy-privacy-subtitle\">" + response.description +"</div>";
            if(response.cookieGroups.length > 0){
            cookieHtml += "<table id=\"cookie-selection\"><colgroup><col span=\"1\" style=\"width: 15%;\"><col span=\"1\" style=\"width: 85%;\"></colgroup>";
            response.cookieGroups.forEach((cookie) =>{
            cookies.push(cookie.id);
            cookieHtml += "\n<tr>\n<td>\n<label class=\"eto-switch eto-switch--integrated\">";
            var checked = cookie.value ? "checked" : "";
            var disabled = !cookie.enabled ? "disabled" : "";
            cookieHtml += "\n<input class=\"eto-switch__field\"  type=\"checkbox\" id=" + cookie.id + " " + checked + " " + disabled + ">";
            cookieHtml += "\n<span class=\"eto-switch__box\"></span>\n<span class=\"eto-switch__label--on\">On</span>\n<span class=\"eto-switch__label--off\">Off</span>\n</label>\n</td>";
            cookieHtml += "\n<td><h3>" + cookie.name + "</h3></td></tr>";
            cookieHtml += "\n<tr><td></td><td id = \"strictlyCookies\">" + cookie.description + "</td></tr>"
            });
            cookieHtml += "\n</table>\n</section>";
            }
            jQuery("#cookieDialog").html(cookieHtml);
            openCookieDialog(response.title,cookies);
            }
            }, null, null, 'GET', null, false);
            }
            function openCookieDialog(title,cookies){
            var dialogID = '#cookieDialog';
            jQuery(dialogID).dialog({
            autoOpen: true,
            width: 1030,
            height : 500,
            modal: true,
            title : title,
            resizable: false,
            draggable: false,
            buttons: {
            "Close": function() {
            jQuery(dialogID).dialog("close");
            },
            "Save": function() {
            var resCookies = cookies.map((cookie) => {
            return {
            id: cookie,
            enabled : jQuery("#"+cookie).is(":checked")
            };
            })
            ll.util.callAjaxAction('/pages/cookieconsent',
            function(response) {
            PageMessage.success('Your privacy settings were saved and will be in effect the next page reload.');
            jQuery(dialogID).dialog("close");
            },
            function(error) {
            PageMessage.error('An error occurred while performing the update.');
            },
            null,
            'POST',
            {
            preferences : resCookies
            },
            false,
            'application/json'
            );
            }
            }
            });
            }
        </script>
        <div class="menu">
            <nav id="menu-root"></nav>
            04/17/2025 08:19 CDT
        </div>
        <script type="text/javascript">
            jQuery.ajax('/tmsrest/menu/json?cacheKey=ba05ba21-71c6-4258-bf8c-5c8ff05a2a4a').done(function(json) {
            new Menu(document.getElementById('menu-root'), json);
            });

            document.body.classList.add('with-footer');

        </script>
        <form name="CarrierAppointmentSearchForm" method="post" action="/apptschedule/carrierappointmentsearch.do">
            <input type="hidden" name="encodedPageData" value="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">
            <input type="hidden" name="pageaction" value="search">
            <input type="hidden" name="canCreateMiscAppts" value="false">
            <input type="hidden" name="searchQO.sortBy" value="SORT_FLD_DUEDATE">
            <input type="hidden" name="searchQO.sortAscending" value="true">
            <input type="hidden" name="$ENABLETIMEPARSING$" value="true" />
            <input type="hidden" name="$ENABLETZPARSING$" value="true" />
            <div class="page-header">
                <div class="page-header__container">
                    <div class="page-header__main">
                        <div class="page-header__title-container">
                            <div class="page-header__page-title">Appoint Loads</div>
                            <div class="page-header__page-title page-header__page-title--to-top">
                                <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                                Return to the top of Appoint Loads                 </button>
                            </div>
                        </div>
                    </div>
                    <div class="page-message" id="page-message-root">
                        <div class="page-message__container">
                            <div class="page-message__icon-container"></div>
                            <div class="page-message__message-container">
                                <ul class="page-message__messages">
                                    <li class="page-message__message page-message__message--primary"></li>
                                </ul>
                                <div class="page-message__button-container" style="display: none;"></div>
                            </div>
                            <button type="button" class="icon-button page-message__close-button">
                                <span role="tooltip" class="icon-span">
                                    <svg class="icon  ic_close" focusable="false">
                                        <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                    <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
                    <script>
                        (function() {
                        var errors = [

                        ];
                        var success = [

                        ];
                        onDocumentReady(function() {
                        var oldPageMessages = document.querySelectorAll('.old-page-message');
                        for(var i = 0; i < oldPageMessages.length; i++) {
                        var oldMessage = oldPageMessages[i];
                        oldMessage.parentElement.removeChild(oldMessage);
                        }
                        if(errors.length) {
                        PageMessage.error(errors, null, null, false);
                        } else if(success.length) {
                        PageMessage.success(success, null, null, false);
                        }
                        });
                        })();
                    </script>
                </div>
            </div>
            <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
            <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
            <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
            <div class="criteria">
                <table width="100%" class="lean_table">
                    <tr>
                        <td class="field-input-label">TMS ID(s)</td>
                        <td width="15%" ><input type="text" name="leanIDString" value="" class="formfieldinputs"></td>
                        <td width="15%" rowspan="4" nowrap>
                            <div>
                                Status
                            </div>
                            <div>
                                <label>
                                <input type="checkbox" name="searchQO.unappt" value="on">&nbsp;Unappointed
                                </label>
                            </div>
                            <div>
                                <label>
                                <input type="checkbox" name="searchQO.partAppt" value="on">&nbsp;Partially Appointed
                                </label>
                            </div>
                            <div>
                                <label>
                                <input type="checkbox" name="searchQO.fullAppt" value="on">&nbsp;Fully Appointed
                                </label>
                            </div>
                        </td>
                        <td width="24%" rowspan="4">
                            Pick Date <br />
                            <table class="lean_table searchcriteria">
                                <tr>
                                    <td colspan="2">
                                        <select name="quickDate" size="1" onchange="setQuickDate(this)" class="formfieldinputs">
                                            <option value="0">-- Choose --</option>
                                            <option value="1">Today</option>
                                            <option value="2">Yesterday</option>
                                            <option value="3">Tomorrow</option>
                                            <option value="4">This Week</option>
                                            <option value="5">Last Week</option>
                                            <option value="6">Next Week</option>
                                            <option value="7">This Month</option>
                                            <option value="8">Last Month</option>
                                            <option value="9">Next Month</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="field-input-label">Start</td>
                                    <td><input type="text" name="pickDateStart" maxlength="10" size="7" value="" onchange="ll.util.formatAndValidateDateInput(this)" class="ll_datepicker formfieldinputs"></td>
                                </tr>
                                <tr>
                                    <td class="field-input-label">End</td>
                                    <td><input type="text" name="pickDateEnd" maxlength="10" size="7" value="" onchange="ll.util.formatAndValidateDateInput(this)" class="ll_datepicker formfieldinputs"></td>
                                </tr>
                            </table>
                        </td>
                        <td width="25%" rowspan="4">
                            Load Group<br />
                            <select name="searchQO.groups" multiple="multiple" size="4" style="margin-right: 5px;" class="formfieldinputs">
                                <option value="0" class="formfieldinputs">-- All --</option>
                                <option value="169586" selected="selected">JM SMUCKER</option>
                                <option value="173575" selected="selected">MISC</option>
                                <option value="184269" selected="selected">THE J. M. SMUCKER COMPANY</option>
                                <option value="173328" selected="selected">UTZ SNACKS</option>
                            </select>
                        </td>
                        <td width="15%" rowspan="4">
                            <label>
                            <input type="checkbox" name="searchQO.myLoads" value="on" checked="checked">
                            My Loads
                            </label>
                            <div>
                                <button id="clearButton" class="leanbutton text" type="button" onclick="clearForm()">Clear</button>
                                <button id="searchButton" class="leanbutton search-button search" type="button" onclick="search()">Search</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Pro #(s)</td>
                        <td><input type="text" name="proNumString" value="0044016" class="formfieldinputs"></td>
                    </tr>
                    <tr>
                        <td>Ref #(s)</td>
                        <td><input type="text" name="refNumString" value="" class="formfieldinputs"></td>
                    </tr>
                    <tr>
                        <td>Shipper</td>
                        <td><input type="text" name="searchQO.shipper" value="" class="formfieldinputs"></td>
                    </tr>
                </table>
            </div>
            <table class="list no-stripes">
                <thead>
                    <tr>
                        <th id="sortLinkLoadNum" class="sortable
                            ">
                            TMS ID
                        </th>
                        <th>Notes</th>
                        <th>Stop<br />Type</th>
                        <th>Location</th>
                        <th id="sortLinkDueDate" class="sortable
                            sorted
                            ">
                            Plan<br />Date
                        </th>
                        <th>Date Appt Recorded<br/>Date&nbsp;&nbsp;-&nbsp;&nbsp;Time</th>
                        <th>FCFS</th>
                        <th id="sortLinkApptDate" class="sortable
                            ">
                            Appointment Start Time
                        </th>
                        <th nowrap>Appointment End Time</th>
                        <th>Appointment<br/>Confirmation #</th>
                        <th>Drop Trailer</th>
                        <th >Consolidate <br /> Appointment</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="resultrow1" style="vertical-align:text-top;">
                        <td>
                            <a href="javascript: noop()" id="test-load0" class="resultheaderlink" onClick="popup('/LoadReport2.jsp?inpopup=y&loadID=188481886&dt=1744895956506', 'LoadReport', 750, 500)">188481886</a>
                            <input type="hidden" name="results[0].pastRestrictApptDays" value="180">
                            <input type="hidden" name="results[0].futureRestrictApptDays" value="180">
                            <input type="hidden" name="results[0].loadID" value="188481886">
                        </td>
                        <td>
                            <a href="javascript:noop()" id="test-create0" onClick="popup('/agent/createloadnote.do?loadID=188481886', 'LoadNote', 750, 550);" class="resultheaderlink">
                            Create
                            </a>
                        </td>
                        <td>
                            <strong>Pick</strong>
                            <input type="hidden" name="results[0].details[0].stopType" value="1000">
                            <input type="hidden" name="results[0].details[0].detailNum" value="412302370">
                            <input type="hidden" name="timezone412302370" value="America/New_York" />
                            <input type="hidden" name="results[0].details[0].appointmentChanged" value="false" />
                        </td>
                        <td>
                            UTZ QUALITY FOODS - 240 KINDIG HANOVER, PA<br />HANOVER, PA&nbsp;17331&nbsp;US
                            <br /><EMAIL>
                            <br />**************
                        </td>
                        <td>
                            03/27/2025 00:00
                        </td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td colspan="2">
                            <div id="apptdiv412302370">
                                <input type="hidden" name="results[0].details[0].appointStartDateTime" value="04/04/2025"/>
                                <input type="hidden" name="-$TIME$results[0].details[0].appointStartDateTime" value="13:00"/>
                                <input type="hidden" name="results[0].details[0].appointEndDateTime" value="04/04/2025"/>
                                <input type="hidden" name="-$TIME$results[0].details[0].appointEndDateTime" value="13:00"/>
                                <span>
                                04/04/2025 13:00
                                <br />
                                <span class="success-highling">
                                CONFIRMED
                                </span>
                                </span>
                            </div>
                            <div id="apptcommentdiv412302370">
                                <a href="javascript:viewApptComments('412302370')">Check Appt Comments</a>
                            </div>
                            <div>
                                <a href="javascript:scheduleAppt('412302370', 0, 0)" id="test-liveappt0stop0">
                                Reschedule Live Appt
                                </a>
                            </div>
                            <div id="apptcanceldiv412302370">
                                <a href="javascript:cancelAppt('412302370')">
                                Cancel Appt
                                </a>
                            </div>
                        </td>
                        <td colspan="2">--</td>
                        <td align="center">
                            <input type="checkbox" name="results[0].details[0].consolidateAppointment"  />
                        </td>
                        <td></td>
                    </tr>
                    <tr class="resultrow1" style="vertical-align:text-top;">
                        <td colspan="2">&nbsp;</td>
                        <td>
                            <strong>Drop</strong>
                            <input type="hidden" name="results[0].details[1].stopType" value="1001">
                            <input type="hidden" name="results[0].details[1].detailNum" value="412073128">
                            <input type="hidden" name="timezone412073128" value="America/Chicago" />
                            <input type="hidden" name="results[0].details[1].appointmentChanged" value="false" />
                        </td>
                        <td>
                            WALMART DC 6068A-ASM<br />SANGER, TX&nbsp;76266&nbsp;US
                            <br />RETAILLINK.COM
                        </td>
                        <td>
                            04/07/2025 00:00
                        </td>
                        <td nowrap>
                            <input type="hidden" name="results[0].details[1].quickKeyDDate" value="04/07/2025" />
                            <NOBR><input type="text" name="results[0].details[1].appointScheduledOnDate" maxlength="10" size="8" value="" onchange="ll.util.formatAndValidateDateInput(this)" class="ll_datepicker formfieldinputs">&nbsp;
                                <input type="text" name="-$TIME$results[0].details[1].appointScheduledOnDate" maxlength="5" size="4" value="" class="ll_time formfieldinputs">
                            </NOBR>
                        </td>
                        <td>
                            <input type="checkbox" name="results[0].details[1].FCFS" value="on" onclick="checkApptTimes(this, '0', '1')" class="formfieldinputs">
                        </td>
                        <td nowrap>
                            <NOBR><input type="text" name="results[0].details[1].appointStartDateTime" maxlength="10" size="8" value="04/07/2025" onchange="ll.util.formatAndValidateDateInput(this);enableReasonInputs(0,1);markAppointmentChanged(0,1);" class="ll_datepicker formfieldinputs"><span title='Required' class='required-asterisk'></span>&nbsp;
                                <input type="text" name="-$TIME$results[0].details[1].appointStartDateTime" maxlength="5" size="4" value="10:00" onchange="enableReasonInputs(0,1);markAppointmentChanged(0,1);" class="ll_time formfieldinputs"><span title='Required' class='required-asterisk'></span>
                            </NOBR>
                        </td>
                        <td nowrap>
                            <NOBR><input type="text" name="results[0].details[1].appointEndDateTime" maxlength="10" size="8" value="04/07/2025" onchange="ll.util.formatAndValidateDateInput(this);enableReasonInputs(0,1);markAppointmentChanged(0,1);" class="ll_datepicker formfieldinputs">
                                <input type="text" name="-$TIME$results[0].details[1].appointEndDateTime" maxlength="5" size="4" value="10:00" onchange="enableReasonInputs(0,1);markAppointmentChanged(0,1);" class="ll_time formfieldinputs">
                            </NOBR>
                        </td>
                        <td>
                            <input type="text" name="results[0].details[1].apptReference" maxlength="50" size="5" value="32616428" onchange="validateApptRef(this);" class="formfieldinputs">
                        </td>
                        <td align="center">
                            <input type="hidden" name="results[0].details[1].trailerLoadingType" value="1">
                            --
                        </td>
                        <td>&nbsp;</td>
                        <td></td>
                    </tr>
                    <tr class="resultrow1">
                        <td colspan="2" style="vertical-align:bottom;">
                            <div><strong>Contact</strong>&nbsp;Utz Brands&nbsp;Fetch Freight</div>
                        </td>
                        <td colspan="2">
                            <strong>Pro #</strong><br>
                            <input type="text" name="results[0].proNum" maxlength="30" size="25" value="0044016" class="formfieldinputs">
                            <input type="hidden" name="initProNum[188481886]" value="0044016" />
                        </td>
                        <td colspan="2">
                            <strong>Vehicle #</strong><br>
                            <input type="text" name="results[0].vehicleNumber" maxlength="30" size="25" value="22554" class="formfieldinputs">
                            <input type="hidden" name="initVehicleNum[188481886]" value="22554" />
                        </td>
                        <td colspan="2">
                            <strong>
                            Trailer #
                            </strong>
                            <br />
                            <input type="text" name="results[0].trailerNumber" maxlength="30" size="25" value="STS1049" class="formfieldinputs">
                            <input type="hidden" name="initTrailerNum[188481886]" value="STS1049" />
                            <input type="hidden" name="results[0].drayageContainerID" value="0" />
                        </td>
                        <td colspan="5">
                            <strong>Driver</strong><br>
                            <input type="text" name="results[0].driverName" maxlength="30" size="25" value="JOAN" class="formfieldinputs">
                            <input type="hidden" name="initDriverName[188481886]" value="JOAN" />
                        </td>
                    </tr>
                    <tr class="resultrow1">
                        <td colspan="2"/>
                        <td colspan="2">
                            <strong>
                            Container #
                            </strong>
                            <br />
                            <input type="text" name="results[0].containerNumber" maxlength="30" size="25" value="" class="formfieldinputs">
                            <input type="hidden" name="initContainerNum[188481886]" value="" />
                        </td>
                        <td colspan="2">
                            <strong>Vehicle License Plate #</strong><br>
                            <input type="text" name="results[0].vehicleLicensePlate" maxlength="50" size="25" value="" class="formfieldinputs">
                            <input type="hidden" name="initVehiclePlate[188481886]" value="" />
                        </td>
                        <td colspan="2">
                            <strong>Trailer License Plate #</strong><br>
                            <input type="text" name="results[0].trailerLicensePlate" maxlength="50" size="25" value="" class="formfieldinputs">
                            <input type="hidden" name="initTrailerPlate[188481886]" value="" />
                        </td>
                        <td colspan="5">&nbsp;</td>
                    </tr>
                    <tr class="resultrow1">
                        <td colspan="13">
                            <NOBR>
                                <strong>PRO #</strong>&nbsp;
                                0044016
                                <br />
                            </NOBR>
                            <NOBR>
                                <strong>Appt #</strong>&nbsp;
                                32616428
                            </NOBR>
                        </td>
                    </tr>
                    <tr class="resultrow1">
                        <td colspan="13"><strong>Shipments</strong>&nbsp;S035327485,S035326357,S035324529,S035325332,S035326451</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5">
                            1 result found.
                            &nbsp;
                        </td>
                        <td colspan="7" class="action-section">
                            <button type="button" class="text" onclick="selectAllValues(1000)">Select All Pick Appointments</button>
                            <button type="button" class="text" onclick="selectAllValues(1001)">Select All Drop Appointments</button>
                            <button type="button" name="submitBtn" onclick="save()" class="leanbutton save-button subtle">
                            Save
                            </button>
                            <select name="consolidationOptions" class="formfieldinputs">
                                <option value="1">Consolidate Live Appointments</option>
                                <option value="2">Consolidate Drop Appointments</option>
                            </select>
                            <button type="button" onclick="handleConsolidationAction()" class="leanbutton go-button">
                            Go
                            </button>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="legend">
                <table>
                    <tr>
                        <td colspan="3" class="resultheaders" align="left" nowrap>Shortcuts for Dates and Times</td>
                    </tr>
                    <tr>
                        <td class="resultrow1" align="right">C</td>
                        <td class="resultrow1" align="center">&nbsp; - &nbsp;</td>
                        <td class="resultrow1" align="left">Current Date and Time</td>
                    </tr>
                    <tr>
                        <td class="resultrow1" align="right">T</td>
                        <td class="resultrow1" align="center">&nbsp; - &nbsp;</td>
                        <td class="resultrow1" align="left">Today's Date</td>
                    </tr>
                    <tr>
                        <td class="resultrow1" align="right">M</td>
                        <td class="resultrow1" align="center">&nbsp; - &nbsp;</td>
                        <td class="resultrow1" align="left">Tomorrow's Date</td>
                    </tr>
                    <tr>
                        <td class="resultrow1" align="right">Y</td>
                        <td class="resultrow1" align="center">&nbsp; - &nbsp;</td>
                        <td class="resultrow1" align="left">Yesterday's Date</td>
                    </tr>
                    <tr>
                        <td class="resultrow1" align="right">D</td>
                        <td class="resultrow1" align="center">&nbsp; - &nbsp;</td>
                        <td class="resultrow1" align="left">Appointment / Plan Date</td>
                    </tr>
                </table>
            </div>
        </form>
    </body>
</html>