<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="ScheduleAppt" class="transactional-page">
    <head>
        <title>Schedule Appointment</title>
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <script type="text/javascript" src="/c/js/utility.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <link rel="icon" type="image/x-icon" href="/favicon.ico" id="faviconICO">
        <link rel="icon" type="image/png" href="/favicon.png" id="faviconPNG">
        <script type="text/javascript">
            var faviconICO = document.querySelector('#faviconICO');
            var faviconPNG = document.querySelector('#faviconPNG');
            var darkModeListener = function(event) {
            if (event.matches) {
            faviconICO.setAttribute("href","/favicon_dark.ico");
            faviconPNG.setAttribute("href","/favicon_dark.png");
            } else {
            faviconICO.setAttribute("href","/favicon.ico");
            faviconPNG.setAttribute("href","/favicon.png");
            }
            };
            var darkModePreference = window.matchMedia('(prefers-color-scheme: dark)');
            if(darkModePreference.addEventListener){
            darkModePreference.addEventListener('change', function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            } else {
            darkModePreference.addListener(function(e) {
            if (e.matches) {
            activateDarkMode();
            }
            });
            }
            darkModeListener({matches: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches});
        </script>
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/unity.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/new_styles.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/components.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <link rel="stylesheet" type="text/css" href="/c/css/newstyles/intermediate_icons.css?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a">
        <script type="text/javascript">
            function popupMaestro() {




            }
            onDocumentReady(function() {
            var iconContainer = document.createElement('div');
            jQuery(document.body).prepend(iconContainer);
            jQuery(iconContainer).load('/images/icons/icon_library.svg?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a');
            popupMaestro();
            });
        </script>
        <script type="text/javascript">
            var useComponents = false;
            var newStylesComponents = false;
            var useETOComponents = false;
            var cacheKey = "6796fcdb785e9ed117e7de5f4f6cd1836aa2975a";
        </script>
        <script type="text/javascript" src="/c/js/bundles/i18next.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/lib.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/templates/all_en_US.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            ll.util.initFormats(1, 1,
            'en_US');
            var shortLang = 'en';
            var browserCacheKey = 'ba05ba21\-71c6\-4258\-bf8c\-5c8ff05a2a4a';
            ll.lang = ll.lang || {};
            ll.lang.locale = 'en_US';
        </script>
        <script type="text/javascript" src="/c/js/bundles/vendors/react.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/tms.components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/bundles/components.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/Modal.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/Popover.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/PanelTransition.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            jQuery.noConflict();
            var LeanActionFormName = 'scheduleApptForm';
            var LeanActionFullPath = '\/apptschedule\/scheduleappt.do';
            var $LeanActionForm;
            onDocumentReady(function() {


            $LeanActionForm = jQuery('form[name=scheduleApptForm]');



            if (get_browser() === "msie") {
            jQuery('html').addClass("ie");
            }
            if ( 'noValidate' in document.createElement('form') ){
            jQuery('html').addClass("novalidate");
            }
            var $dwrImageDiv = jQuery('#dwrImageDiv');
            if (!$dwrImageDiv.length) {
            $dwrImageDiv = jQuery('<div id="dwrImageDiv" style="display:none;"/>');
            $dwrImageDiv.append(jQuery('<img id="dwrLoadingImg" src="/images/eto-loader.svg" alt="' + validate_activityIndicator + '">'));
            jQuery('body').append($dwrImageDiv);
            }


            hasExpiredSessionWarningBeenDisplayed = false;
            });
        </script>
        <script type="text/javascript" src="/execution/vessel.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">
            var dockStartTimes = {};
            function updateStartTimes() {
            var selectedDock = jQuery("select[name=shipperOverRideDockID]").val();
            jQuery("select[name=shipperOverRideAffectedStartTime]").empty();
            if(dockStartTimes[selectedDock] && jQuery("input[name=shipperOverRideAffectDockUsage]:checked").val() == "true") {
            for(var i = 0; i < dockStartTimes[selectedDock].length; i++) {
            var time = dockStartTimes[selectedDock][i];
            var d = new Date(jQuery("input[name=shipperOverRideApptDate]").val());
            if(d.getTime() >= time.effectiveDate && d.getTime() <= time.expirationDate &&
            (d.getDay() == 0 && time.sunday || d.getDay() == 1 && time.monday || d.getDay() == 2 && time.tuesday ||
            d.getDay() == 3 && time.wednesday || d.getDay() == 4 && time.thursday || d.getDay() == 5 && time.friday ||
            d.getDay() == 6 && time.saturday)) {
            jQuery("select[name=shipperOverRideAffectedStartTime]").addOption(time.startTime, time.startTime);
            }
            }
            }
            if(jQuery("select[name=shipperOverRideAffectedStartTime]").children().length > 0) {
            jQuery("select[name=shipperOverRideAffectedStartTime]").show();
            } else {
            jQuery("select[name=shipperOverRideAffectedStartTime]").hide();
            }
            }
            function onDateSequenceValidationLookup(warning) {
            if (warning == null || warning.length == 0 || confirm(warning+"\nContinue scheduling the appointment?")) {
            save();
            }
            }
            function save() {
            var form = document.forms['scheduleApptForm'];
            if (form.elements["submitbut"] != null) {
            form.elements["submitbut"].disabled = true;
            }
            if (form.elements["nextdatbut"] != null) {
            form.elements["nextdatbut"].disabled = true;
            }
            if (form.elements["forcebut"] != null) {
            form.elements["forcebut"].disabled = true;
            }
            if (form.elements["cancelbut"] != null) {
            form.elements["cancelbut"].disabled = true;
            }
            form.submit();
            }
            function requiredDueToMismatchedPlanDate(form) {
            var usageType = form.elements['usageType'].value * 1;
            return true && form.elements['dueDate'].value != form.elements['apptDate'].value && true &&
            ((usageType === 2 && false) ||
            (usageType === 1 && false))
            }
            function validateAndSave() {
            var selected = false;
            var form = document.forms["scheduleApptForm"];
            var startSlotNum = form.elements["startSlotNum"];
            var startSlotNumValue;
            if (startSlotNum != null) {
            if (startSlotNum.length) {
            var count = startSlotNum.length;
            for (var i = 0; i < count; i++) {
            if (startSlotNum[i].checked) {
            selected = true;
            startSlotNumValue = startSlotNum[i].value;
            break;
            }
            }
            } else {
            if (startSlotNum.checked) {
            selected = true;
            startSlotNumValue = startSlotNum.value;
            }
            }
            }



            if (!validateQuotedRefNumField(form.elements["trailerNumber"], true, 30, true)) {
            form.elements["trailerNumber"].focus();
            return false;
            }
            if (!validateQuotedRefNumField(form.elements["containerNumber"], true, 30, true)) {
            form.elements["containerNumber"].focus();
            return false;
            }
            if (form.elements["containerNumber"] && form.elements["drayageContainerID"] && form.elements["drayageContainerID"].value > 0 && !ll.vessel.validateContainerReference(jQuery(form.elements["containerNumber"]))) {
            if (!confirm(ll.util.formatMessage('Container # \x27{0}\x27 is not in standard ISO 6346 format (e.g. \x27CSQU3054383\x27). Continue?', form.elements["containerNumber"].value))) {
            form.elements["containerNumber"].focus();
            return false;
            }
            }
            if (!validateQuotedRefNumField(form.elements["driverName"], true, 60, true)) {
            form.elements["driverName"].focus();
            return false;
            }
            if (!validateQuotedRefNumField(form.elements["vehicleNumber"], true, 30, true)) {
            form.elements["vehicleNumber"].focus();
            return false;
            }


            if (form.elements["shipperOverRideApptDate"] && !isEmpty(form.elements["shipperOverRideApptDate"].value)) {
            if (selected) {
            if (form.elements['isForceAppt'].value == 'true') {
            return forceAppt();
            } else if (confirm("Since you have entered a date under Option 2, the slot time selected under Option 1 will not be considered.\nDo you want to proceed?")) {
            selected = false;
            form.elements['isForceAppt'].value = "true";
            if (!forceAppt()) {
            return false;
            }
            } else {
            return false;
            }
            } else {
            if (!forceAppt()) {
            return false;
            }
            }
            }
            else {
            if (selected) {
            var comment = form.elements["comment"];
            var reasonCode = !!form.elements['reasonCodeSelection'] ? form.elements['reasonCodeSelection'].value : null;
            if (isRequiredComments(reasonCode)) {
            var requiredObj = document.getElementById("commentsRequired");
            if (requiredObj) {
            if (requiredObj.style.visibility=="visible" && isEmpty(comment.value)) {
            if (requiredDueToMismatchedPlanDate(form)) {
            alert("Load appointments outside of the plan date require a comment");
            } else {
            alert("The selected Reason Code requires a comment");
            }
            comment.focus();
            return false;
            }
            } else if (isEmpty(comment.value)) {

            if (requiredDueToMismatchedPlanDate(form)) {
            alert("Load appointments outside of the plan date require a comment");
            } else {
            alert("The selected Reason Code requires a comment");
            }
            comment.focus();
            return false;

            }
            }
            if (comment) {
            if (comment.value.length > 500) {
            alert("Comment must not exceed 500 characters.");
            comment.focus();
            return false;
            }
            }
            form.elements["pageaction"].value = 'makeAppt';
            } else if (form.elements["startSlotNum"] == null && form.elements["shipperOverRideApptDate"] && isEmpty(form.elements["shipperOverRideApptDate"].value)) {
            alert("Please enter a date under option 2 to proceed");
            return false;
            } else if (form.elements["startSlotNum"] != null && form.elements["shipperOverRideApptDate"] && isEmpty(form.elements["shipperOverRideApptDate"].value) && !selected) {
            alert("Please select a time slot to proceed.");
            return false;
            }
            }
            if (selected) {

            var rangeCheckObject = new Object();
            var appt = new Object();
            appt.date = form.elements["apptDate"].value;
            appt.time = form.elements["time" + startSlotNumValue].value;
            appt.timezone = form.elements["locationTimeZone"].value;
            rangeCheckObject["appt"] = appt;
            var range = new Object();
            if (form.elements["rangeStartDate"].value != "") {
            range.startDate = form.elements["rangeStartDate"].value;
            range.startTimezone = form.elements["rangeStartTimezone"].value;
            if (form.elements["rangeStartTime"].value != "") {
            range.startTime = form.elements["rangeStartTime"].value;
            }
            }
            if (form.elements["rangeEndDate"].value != "") {
            range.endDate = form.elements["rangeEndDate"].value;
            range.endTimezone = form.elements["rangeEndTimezone"].value;
            if (form.elements["rangeEndTime"].value != "") {
            range.endTime = form.elements["rangeEndTime"].value;
            }
            }
            if (form.elements["rangeStartTS"].value != "") {
            range.startTS = form.elements["rangeStartTS"].value;
            }
            if (form.elements["rangeEndTS"].value != "") {
            range.endTS = form.elements["rangeEndTS"].value;
            }
            rangeCheckObject["range"] = range;
            ll.util.callAjaxAction("/tmsrest/datelookup/appt_range_sequence_warnings", onDateSequenceValidationLookup, null, null, 'POST',
            JSON.stringify(rangeCheckObject), false, 'text/plain; charset=UTF-8');
            } else {
            save();
            }
            return true;
            }
            function forceAppt() {
            var form = document.forms["scheduleApptForm"];
            var shipperOverRideApptDate = form.elements["shipperOverRideApptDate"];
            var shipperOverRideApptTime = form.elements["-$TIME$shipperOverRideApptDate"];
            if (shipperOverRideApptDate != null) {
            if (!ll.util.checkDate(shipperOverRideApptDate, false)) {
            return false;
            }
            }
            if (!checkMilitaryTime(shipperOverRideApptTime, false)) {
            return false;
            }
            if (!ll.util.isPositiveInteger(form.elements["shipperOverRideDockTime"].value, false)) {
            alert("Load\/Unloading time must be a valid value greater than zero");
            return false;
            }
            if (ll.util.convertStringToNumber(form.elements["shipperOverRideDockTime"].value) > 1440) {
            alert("Load\/Unloading time exceeds 24 hrs. Please Check.");
            return false;
            }
            if ((form.shipperOverRideDockID.value * 1) <= 0) {
            alert("You must select a dock where appointment be made.");
            form.shipperOverRideDockID.focus();
            return false;
            }
            var comment = form.elements["comment"];
            var reasonCode = !!form.elements['reasonCodeSelection'] ? form.elements['reasonCodeSelection'].value : null;
            if (isRequiredComments(reasonCode)) {
            var requiredObj = document.getElementById("commentsRequired");
            if (requiredObj) {
            if (requiredObj.style.visibility=="visible" && isEmpty(comment.value)) {
            if (requiredDueToMismatchedPlanDate(form)) {
            alert("Load appointments outside of the plan date require a comment");
            } else {
            alert("The selected Reason Code requires a comment");
            }
            comment.focus();
            return false;
            }
            } else if (isEmpty(comment.value)) {
            if (requiredDueToMismatchedPlanDate(form)) {
            alert("Load appointments outside of the plan date require a comment");
            } else {
            alert("The selected Reason Code requires a comment");
            }
            comment.focus();
            return false;
            }
            }
            if (comment) {
            if (comment.value.length > 500) {
            alert("Comment must not exceed 500 characters.");
            comment.focus();
            return false;
            }
            }
            form.elements["pageaction"].value = 'forceAppt';
            save();
            return true;
            }
            function getNextDay() {
            if (!jQuery('a[name="nextDayLink"]').prop('disabled')) {
            jQuery('a[name="nextDayLink"]').prop('disabled', true);
            var form = document.forms["scheduleApptForm"];
            form.elements["pageaction"].value = 'nextDate';
            if (form.elements["submitbut"] != null) {
            form.elements["submitbut"].disabled = true;
            }
            form.submit();
            }
            }
            function getPrevDay() {
            if (!jQuery('a[name="prevDayLink"]').prop('disabled')) {
            jQuery('a[name="prevDayLink"]').prop('disabled', true);
            var form = document.scheduleApptForm;
            form.elements["pageaction"].value = 'prevDate';
            if (form.elements["submitbut"] != null) {
            form.elements["submitbut"].disabled = true;
            }
            form.submit();
            }
            }
            function GetNextDateAppts() {
            var form = document.forms["scheduleApptForm"];

            var nextApptDate = form.elements["nextApptDate"];
            if (nextApptDate != null) {
            if (!ll.util.checkDate(nextApptDate, true)) {
            return false;
            }
            }
            var jsDate1 = ll.util.jsDate(nextApptDate.value);
            var today = new Date();
            today = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            if (jsDate1.getTime() < today.getTime()) {
            alert("Appointment Date is in the past");
            return false;
            }
            form.elements["apptDate"].value = nextApptDate.value;
            if (form.elements["submitbut"] != null) {
            form.elements["submitbut"].disabled = true;
            }
            if (form.elements["nextdatbut"] != null) {
            form.elements["nextdatbut"].disabled = true;
            }
            if (form.elements["forcebut"] != null) {
            form.elements["forcebut"].disabled = true;
            }
            form.elements["pageaction"].value = 'nextApptDate';
            form.submit();
            }
            function updateParentPage(apptObjID, apptdt, appttime, apptStatusID, attributes) {
            try {
            parent.opener.updateASMDisplay(apptObjID, apptdt, appttime, apptStatusID, false, attributes);
            } catch (errorInfo) {}
            }

            function manageCommentLabel() {
            var form = document.forms["scheduleApptForm"];
            var requiredObj = document.getElementById("commentsRequired");
            if (requiredObj) {
            var reasonCode = !!form.elements['reasonCodeSelection'] ? form.elements['reasonCodeSelection'].value : null;
            if (isRequiredComments(reasonCode)) {
            requiredObj.style.visibility = "visible";
            } else {
            requiredObj.style.visibility = "hidden";
            }
            }
            }
            function isRequiredComments(reasonCodeValue) {
            var forceComment = false;
            if (!!reasonCodeValue) {

            }
            return requiredDueToMismatchedPlanDate(document.forms["scheduleApptForm"]);
            }
        </script>
        <style>
            .lean_table {
            width: 100%;
            }
            .option-header {
            font-weight: 700;
            margin: 20px 0 0 10px;
            }
            .force-appointment-option {
            padding-top:20px;
            }
            tr.loadreportsubhead td {
            padding-top: 10px;
            }
            .container {
            margin: 10px;
            width: calc(100% - 40px);
            }
            .leanform > table {
            margin: 10px;
            width: calc(100% - 20px);
            }
            .field-label,
            .field-label-table td:nth-child(odd) {
            font-weight: 700;
            width: 1%; /* This is here to make the cell as small as the words so that it looks like Field Labels specification */
            white-space: nowrap;
            padding-right: 20px;
            padding-left: 10px;
            }
            .standing-appts-day-cell {
            width: 1%; /* This is here to make the cell as small as the words to help center the text */
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
            }
            .next-day-cell,
            .previous-day-cell {
            width: 50%;
            }
            .previous-day-cell {
            text-align: right;
            }
        </style>
    </head>
    <body>
        <div class="page-header">
            <div class="page-header__container">
                <div class="page-header__main">
                    <div class="page-header__title-container">
                        <div class="page-header__page-title">Schedule Appointment</div>
                        <div class="page-header__page-title page-header__page-title--to-top">
                            <button type="button" tabIndex="-1" class="text page-header__to-top-button">
                            Return to the top of Schedule Appointment                 </button>
                        </div>
                    </div>
                </div>
                <div class="page-message" id="page-message-root">
                    <div class="page-message__container">
                        <div class="page-message__icon-container"></div>
                        <div class="page-message__message-container">
                            <ul class="page-message__messages">
                                <li class="page-message__message page-message__message--primary"></li>
                            </ul>
                            <div class="page-message__button-container" style="display: none;"></div>
                        </div>
                        <button type="button" class="icon-button page-message__close-button">
                            <span role="tooltip" class="icon-span">
                                <svg class="icon  ic_close" focusable="false">
                                    <use xlink:href="#ic_close" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
                <script type="text/javascript" src="/c/js/components/PageMessage.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
                <script>
                    (function() {
                    var errors = [

                    ];
                    var success = [

                    ];
                    onDocumentReady(function() {
                    var oldPageMessages = document.querySelectorAll('.old-page-message');
                    for(var i = 0; i < oldPageMessages.length; i++) {
                    var oldMessage = oldPageMessages[i];
                    oldMessage.parentElement.removeChild(oldMessage);
                    }
                    if(errors.length) {
                    PageMessage.error(errors, null, null, false);
                    } else if(success.length) {
                    PageMessage.success(success, null, null, false);
                    }
                    });
                    })();
                </script>
            </div>
        </div>
        <script type="text/javascript" src="/c/js/components/ScrollingAnimation.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript" src="/c/js/components/StickyHeader.js?cacheKey=6796fcdb785e9ed117e7de5f4f6cd1836aa2975a"></script>
        <script type="text/javascript">onDocumentReady(function() { new StickyHeader(); });</script>
        <form name="scheduleApptForm" method="POST" action="/apptschedule/scheduleappt.do" class="leanform">
            <input type="hidden" name="encodedPageData" value="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">
            <input type="hidden" name="pageaction" value="unspecified">
            <input type="hidden" name="dueDate" value="03/27/2025" />
            <input type="hidden" name="apptDate" value="04/17/2025"/>
            <input type="hidden" name="rangeStartDate" value="">
            <input type="hidden" name="rangeStartTime" value="">
            <input type="hidden" name="rangeStartTimezone" value="">
            <input type="hidden" name="rangeStartTS" value="0">
            <input type="hidden" name="rangeEndDate" value="04/07/2025">
            <input type="hidden" name="rangeEndTime" value="10:00">
            <input type="hidden" name="rangeEndTimezone" value="America/Chicago">
            <input type="hidden" name="rangeEndTS" value="0">
            <input type="hidden" name="locationTimeZone" value="America/New_York">
            <input type="hidden" name="usageType" value="2">
            <input type="hidden" name="doLookAheadSearch" value="false">
            <input type="hidden" name="$ENABLETIMEPARSING$" value="true" />
            <input type="hidden" name="isForceAppt" value="false"/>
            <input type="hidden" name="commodityLen" value="1"/>
            <input type="hidden" name="commodities[0].commodityID" value="7215">
            <input type="hidden" name="commodities[0].weight" value="30621.3">
            <input type="hidden" name="commodities[0].volume" value="2906">
            <input type="hidden" name="commodities[0].pallets" value="29.8">
            <input type="hidden" name="commodities[0].apptScheduleUnits" value="0">
            <input type="hidden" name="commodities[0].pieces" value="10347">
            <input type="hidden" name="allowSpecificDockSelection" value="false">
            <input type="hidden" name="forceCommentIfNotPlanDate" value="false">
            <input type="hidden" name="SCAC" value="FCFM">
            <input type="hidden" name="customerIDsLen" value="2"/>
            <input type="hidden" name="customerIDs[0]" value="15152714"/>
            <input type="hidden" name="customerIDs[1]" value="14986120"/>
            <input type="hidden" name="supplierIDsLen" value="0"/>
            <input type="hidden" name="orderGroupIDsLen" value="1"/>
            <input type="hidden" name="orderGroupIDs[0]" value="7905736"/>
            <input type="hidden" name="tempClassID" value="0">
            <input type="hidden" name="hazmat" value="false">
            <input type="hidden" name="standingApptProfileID" value="0">
            <input type="hidden" name="carrierID" value="37896">
            <input type="hidden" name="carrierAcd" value="-1">
            <input type="hidden" name="fleetCarrier" value="false">
            <div class="subheader loadreportheader">
                Appointment Information
            </div>
            <table cellpadding="3" class="resultrow1 field-label-table">
                <tr class="resultrow1">
                    <td>
                        TMS ID
                    </td>
                    <td class="loadreportbody">
                        188481886
                    </td>
                    <td>
                        Plan Date
                    </td>
                    <td class="loadreportbody">
                        03/27/2025
                    </td>
                </tr>
                <tr class="resultrow1">
                    <td valign="top">
                        Location &nbsp;
                    </td>
                    <td class="loadreportbody">
                        240 KINDIG &nbsp;
                        240 KINDIG LANE &nbsp;
                    </td>
                    <td>
                        Current Appointment&nbsp;
                    </td>
                    <td class="loadreportbody">
                        04/04/2025 13:00 (CONFIRMED)&nbsp;&nbsp;
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="loadreportbody">
                        HANOVER,
                        PA
                        17331
                        US
                    </td>
                    <td>
                        Estimated Loading/Unloading Time
                    </td>
                    <td class="loadreportbody">
                        60
                        Minutes
                    </td>
                </tr>
            </table>
            <div class="loadreportsubhead" align="center">
                Please select one of the available pickup appointments
            </div>
            <div class="subheader loadreportheader">
                Enter Live Information
            </div>
            <div class="loadreportheader option-header">
                Available Times - Please click on "Next Day" OR "Previous Day" or enter a date to retrieve available times and select an available time
            </div>
            <div class="container">
                <table class="lean_table" >
                    <tr class="loadreportsubhead">
                        <td align="center" colspan="3">
                            <span class="field-label">
                            Plan Date
                            </span>
                            03/27/2025
                        </td>
                    </tr>
                    <tr class="loadreportsubhead" >
                        <td align="center" colspan="3">
                            <span class="field-label">
                            Date
                            </span>
                            <input class="ll_datepicker" type="text" name="nextApptDate" size="8" maxlength="10" value="04/17/2025" onchange="ll.util.formatAndValidateDateInput(this)" />
                            <button type="button" class="leanbutton continue-button" name="nextdatbut" onClick="GetNextDateAppts();">
                            Show open Appointments
                            </button>
                        </td>
                    </tr>
                    <tr class="loadreportsubhead">
                        <td class="previous-day-cell">
                        </td>
                        <td class="standing-appts-day-cell">
                            Thursday
                            04/17/2025
                        </td>
                        <td class="next-day-cell">
                            <button type="button" class="text" name="nextDayLink" onClick="getNextDay();">
                            Next Day&gt;&gt;
                            </button>
                        </td>
                    </tr>
                    <tr class="loadreportsubhead">
                        <td colspan="3">
                            <table class="lean_table" >
                                <tr>
                                    <td class="rowstatuswarn">
                                        No appointments found for the day of 04/17/2025.
                                        <br />
                                        No appointments available for this day, please reach out to warehouse contact (<EMAIL>) if this date is needed to meet customer requested delivery date.
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="shipmentlabel subheader loadreportheader">
                Attributes
            </div>
            <table class="lean_table field-label-table">
                <tr class="loadreportsubhead freight-builder-fleet-attributes">
                    <td>
                        Container #
                    </td>
                    <td colspan="2">
                        <input type="text" name="containerNumber" maxlength="30" size="25" value="">
                    </td>
                    <td>
                        Trailer #
                    </td>
                    <td colspan="2">
                        <input type="text" name="trailerNumber" maxlength="30" size="25" value="STS1049">
                    </td>
                    <td>
                        Trailer License Plate #
                    </td>
                    <td colspan="2">
                        <input type="text" name="trailerLicense" maxlength="50" size="25" value="" id="trailerLicense">
                    </td>
                </tr>
                <tr class="loadreportsubhead freight-builder-fleet-attributes">
                    <td>
                        Driver
                    </td>
                    <td colspan="2">
                        <input type="text" name="driverName" maxlength="60" size="25" value="JOAN" id="driverName">
                        <div id="driverDiv" class="auto_complete"></div>
                    </td>
                    <td>
                        Vehicle #
                    </td>
                    <td colspan="2" id="vehicle_cell">
                        <input type="text" name="vehicleNumber" maxlength="30" size="25" value="22554" id="vehicleNumber">
                        <div id="vehicleDiv" class="auto_complete"></div>
                    </td>
                    <td>
                        Vehicle License Plate #
                    </td>
                    <td colspan="2" id="vehicle_license_cell">
                        <input type="text" name="vehicleLicense" maxlength="50" size="25" value="" id="vehicleLicense">
                    </td>
                </tr>
                <tr class="loadreportsubhead">
                    <td>
                        Comments
                        <span id="commentsRequired" class="required-asterisk" style="visibility:hidden;"></span>
                    </td>
                    <td colspan="5">
                        <textarea name="comment" cols="50" rows="3"></textarea>
                    </td>
                </tr>
            </table>
            <div class="action-bar">
                <button type="button" class="leanbutton close-button subtle" name="cancelbut" onClick="closePopupWindow();">
                Close
                </button>
                <button type="button" class="leanbutton save-button" name="submitbut" onClick="validateAndSave();">
                Submit Request
                </button>
            </div>
        </form>
        <script type="text/javascript">
            function getDriverInfo(request, response) {
            var url = '/tmsrest/autocomplete/carrier_drivers?q=' + encodeURIComponent(request.term) + '&carrierID=' + encodeURIComponent(document.forms["scheduleApptForm"].elements["carrierID"].value);
            ll.util.callAjaxAction(url, response);
            }
            function driverSelected(inputElement, selectedElement, driverObject) {
            var form = document.forms["scheduleApptForm"];
            form.elements["driverName"].value = driverObject.driverName;
            if(driverObject.defaultVehicleNumber != null) {
            form.elements["vehicleNumber"].value = driverObject.defaultVehicleNumber;
            }
            }
            function driverNameSelector(driverObject) {
            return driverObject.driverName;
            }
            function getVehicleInfo(request, response) {
            var url = '/tmsrest/autocomplete/carrier_vehicles?q=' + encodeURIComponent(request.term) + '&carrierID=' + encodeURIComponent(document.forms["scheduleApptForm"].elements["carrierID"].value);
            ll.util.callAjaxAction(url, response);
            }
            function vehicleSelected(inputElement, selectedElement, vehicleNumber) {
            var form = document.forms["scheduleApptForm"];
            form.elements["vehicleNumber"].value = vehicleNumber;
            }
            function vehicleNumberSelector(vehicleNumber) {
            return vehicleNumber;
            }

            onDocumentReady(function() {
            updateStartTimes();
            manageCommentLabel();

            if (document.getElementById('driverName') && true) {
            jQuery('#driverName').ll_autocomplete( { source: getDriverInfo, selectItem:driverSelected, displayItem:driverNameSelector, minLength:2, choices:15 });
            jQuery('#vehicleNumber').ll_autocomplete( { source: getVehicleInfo, selectItem:vehicleSelected, displayItem:vehicleNumberSelector, minLength:2, choices:15 });
            }


            });
        </script>
    </body>
</html>