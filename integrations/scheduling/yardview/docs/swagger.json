{"x-generator": "NSwag v13.19.0.0 (NJsonSchema v10.9.0.0 (Newtonsoft.Json v12.0.0.0))", "swagger": "2.0", "info": {"title": "YardView API", "version": "1.0.0"}, "host": "targettest.yardview.com", "basePath": "/", "schemes": ["https"], "produces": ["text/plain", "application/json", "text/json"], "paths": {"/api/appointments": {"get": {"tags": ["API"], "operationId": "AppointmentsAll", "parameters": [{"type": "integer", "name": "status", "in": "query", "x-schema": {"$ref": "#/definitions/AppointmentStatus"}, "x-nullable": true, "enum": [0, 1, 2]}, {"type": "string", "name": "startDate", "in": "query", "format": "date-time", "x-nullable": true}, {"type": "string", "name": "endDate", "in": "query", "format": "date-time", "x-nullable": true}, {"type": "string", "name": "trailerScac", "in": "query", "x-nullable": true}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/AppointmentPto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}, "post": {"tags": ["API"], "operationId": "CreateAppointment", "consumes": ["application/json", "text/json", "application/*+json"], "parameters": [{"name": "appointmentCreate", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AppointmentCreate"}, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/appointments/{appointmentProId}": {"get": {"tags": ["API"], "operationId": "AppointmentsGet", "parameters": [{"type": "string", "name": "appointmentProId", "in": "path", "required": true, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}, "put": {"tags": ["API"], "operationId": "UpdateAppointment", "consumes": ["application/json", "text/json", "application/*+json"], "parameters": [{"type": "string", "name": "appointmentProId", "in": "path", "required": true, "x-nullable": false}, {"name": "appointmentUpdate", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AppointmentUpdate"}, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/appointments/pid/{appointmentPid}": {"get": {"tags": ["API"], "operationId": "AppointmentsGetPid", "parameters": [{"type": "integer", "name": "appointmentPid", "in": "path", "required": true, "format": "int64", "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}, "put": {"tags": ["API"], "operationId": "UpdateAppointmentPID", "consumes": ["application/json", "text/json", "application/*+json"], "parameters": [{"type": "integer", "name": "appointmentPid", "in": "path", "required": true, "format": "int64", "x-nullable": false}, {"name": "appointmentUpdate", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AppointmentUpdate"}, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/appointments/{appointmentProId}/cancel": {"put": {"tags": ["API"], "operationId": "DeleteAppointment", "parameters": [{"type": "string", "name": "appointmentProId", "in": "path", "required": true, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/appointments/pid/{appointmentPid}/cancel": {"put": {"tags": ["API"], "operationId": "DeleteAppointmentPID", "parameters": [{"type": "integer", "name": "appointmentPid", "in": "path", "required": true, "format": "int64", "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/AppointmentPto"}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/locations": {"get": {"tags": ["API"], "operationId": "LocationsAll", "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/LocationPto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/schedule": {"get": {"tags": ["API"], "operationId": "SchedulesAll", "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/SchedulePto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/schedule/details": {"get": {"tags": ["API"], "operationId": "ScheduleDetailsAll", "parameters": [{"type": "string", "name": "weekDate", "in": "query", "format": "date", "x-nullable": false}, {"type": "integer", "name": "schedulePID", "in": "query", "format": "int32", "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/TimeslotPto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/scacs": {"get": {"tags": ["API"], "operationId": "ScacsAll", "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/ScacPto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/loadtypes": {"get": {"tags": ["API"], "operationId": "LoadTypesAll", "responses": {"200": {"x-nullable": false, "description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/LoadTypePto"}}}, "401": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/ProblemDetails"}}, "500": {"description": ""}}, "security": [{"Drumkit": []}]}}, "/api/Reports": {"post": {"tags": ["API"], "operationId": "Reports_GenerateReport", "consumes": ["application/json", "text/json", "application/*+json"], "produces": ["text/plain", "application/json", "text/json"], "parameters": [{"type": "string", "name": "reportName", "in": "query", "x-nullable": false}, {"name": "searchParams", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}, "x-nullable": false}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/SimpleReportRunPto"}}, "403": {"x-nullable": false, "description": "", "schema": {"type": "string"}}, "404": {"x-nullable": false, "description": "", "schema": {"type": "string"}}}, "security": [{"Drumkit": []}]}, "get": {"tags": ["API"], "operationId": "Reports_GetReportParameters", "produces": ["text/plain", "application/json", "text/json"], "parameters": [{"type": "string", "name": "reportName", "in": "query", "x-nullable": true}], "responses": {"200": {"x-nullable": false, "description": "", "schema": {"$ref": "#/definitions/SimpleReportDefinitionPto"}}, "403": {"x-nullable": false, "description": "", "schema": {"type": "string"}}, "404": {"x-nullable": false, "description": "", "schema": {"type": "string"}}}, "security": [{"Drumkit": []}]}}}, "definitions": {"AppointmentPto": {"type": "object", "required": ["appointmentPID", "startTime", "endTime", "status", "appointmentType", "updatedAt"], "properties": {"appointmentPID": {"type": "integer", "format": "int64"}, "appointmentID": {"type": "integer", "format": "int64"}, "appointmentKey": {"type": "string"}, "proId": {"type": "string"}, "carrierSCAC": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/definitions/AppointmentStatus"}, "appointmentType": {"$ref": "#/definitions/AppointmentType"}, "notes": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "trailerID": {"type": "string"}, "weight": {"type": "integer", "format": "int64"}, "loadTypePID": {"type": "integer", "format": "int32"}}}, "AppointmentStatus": {"type": "integer", "description": "", "x-enumNames": ["Pending", "Cancelled", "Completed"], "enum": [0, 1, 2]}, "AppointmentType": {"type": "string", "description": "", "x-enumNames": ["Pickup", "Delivery"], "enum": ["Pickup", "Delivery"]}, "ProblemDetails": {"type": "object", "additionalProperties": {}, "properties": {"type": {"type": "string"}, "title": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "detail": {"type": "string"}, "instance": {"type": "string"}}}, "AppointmentCreate": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "proId", "schedulePID", "startTime"], "properties": {"appointmentKey": {"type": "string", "minLength": 1}, "proId": {"type": "string", "minLength": 1}, "schedulePID": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time", "minLength": 1}, "notes": {"type": "string"}, "carrierSCAC": {"type": "string"}, "trailerID": {"type": "string"}, "locationPID": {"type": "integer", "format": "int64"}, "weight": {"type": "integer", "format": "int64"}, "loadTypePID": {"type": "integer", "format": "int32"}}}, "AppointmentUpdate": {"type": "object", "required": ["startTime", "schedulePID"], "properties": {"startTime": {"type": "string", "format": "date-time", "minLength": 1}, "schedulePID": {"type": "integer", "format": "int32"}, "comments": {"type": "string"}, "weight": {"type": "integer", "format": "int64"}, "loadTypePID": {"type": "integer", "format": "int32"}}}, "LocationPto": {"type": "object", "required": ["pid"], "properties": {"name": {"type": "string"}, "pid": {"type": "integer", "format": "int64"}, "capacity": {"type": "integer", "format": "int32"}, "area": {"type": "string"}, "facility": {"type": "string"}}}, "SchedulePto": {"type": "object", "required": ["pid", "type"], "properties": {"pid": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "type": {"$ref": "#/definitions/AppointmentType2"}}}, "AppointmentType2": {"type": "integer", "description": "", "x-enumNames": ["Pickup", "Delivery"], "enum": [0, 1]}, "TimeslotPto": {"type": "object", "required": ["startTime", "startDate", "duration", "capacity"], "properties": {"startTime": {"type": "string", "format": "time"}, "startDate": {"type": "string", "format": "date"}, "duration": {"type": "string", "format": "duration"}, "capacity": {"type": "integer", "format": "int32"}}}, "ScacPto": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}}}, "LoadTypePto": {"type": "object", "required": ["pid"], "properties": {"pid": {"type": "integer", "format": "int32"}, "name": {"type": "string"}}}, "SimpleReportRunPto": {"type": "object", "properties": {"title": {"type": "string"}, "data": {}}}, "SimpleReportDefinitionPto": {"type": "object", "properties": {"title": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/SimpleReportParameterPto"}}, "columns": {"type": "array", "items": {"$ref": "#/definitions/SimpleReportColumnDefinitionPto"}}}}, "SimpleReportParameterPto": {"type": "object", "required": ["required"], "properties": {"name": {"type": "string"}, "dataType": {"type": "string"}, "required": {"type": "boolean"}, "defaultValue": {}, "description": {"type": "string"}}}, "SimpleReportColumnDefinitionPto": {"type": "object", "properties": {"name": {"type": "string"}}}}, "securityDefinitions": {"Drumkit": {"type": "basic", "description": "Auth Policy Drumkit: Basic Http", "name": "Drumkit", "in": "header"}}}