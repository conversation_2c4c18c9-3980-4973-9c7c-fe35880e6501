"""YardView scheduling integration handlers."""

from . import api, scraper


SELENIUM_HANDLERS = {
    "GetWarehouse": scraper.get_warehouse,
    "GetOpenSlots": scraper.get_open_slots,
    "GetLoadTypes": scraper.get_load_types,
    "GetAppointment": scraper.get_appointment,
    "MakeAppointment": scraper.make_appointment,
    "UpdateAppointment": scraper.update_appointment,
    "CancelAppointment": scraper.cancel_appointment,
}

API_HANDLERS = {
    "GetCarrierScacs": api.get_carrier_scacs,
    "GetWarehouse": api.get_warehouse,
    "GetOpenSlots": api.get_open_slots,
    "GetLoadTypes": api.get_load_types,
    "GetAppointment": api.get_appointment,
    "MakeAppointment": api.make_appointment,
    "UpdateAppointment": api.update_appointment,
    "CancelAppointment": api.cancel_appointment,
}
