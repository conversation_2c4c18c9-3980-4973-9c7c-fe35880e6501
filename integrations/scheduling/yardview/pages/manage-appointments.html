<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title>Manage Appointments</title>
    <link type="text/css" rel="Stylesheet" href="/css/MenuPro.css" />
    <link
      href="/Scripts/jquery/bootstrap-4.3.1-dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="/CSS/yardview.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="/CSS/YardView/sidebar.min.css" />

    <title>Manage Appointments</title>
    <link
      rel="stylesheet"
      href="/Scripts/jquery/jquery-ui-1.12.1/jquery-ui.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.0.13/css/all.css"
      integrity="sha384-DNOHZ68U8hZfKXOrtjWvjxusGo9WQnrNx2sqG0tfsghAvtVlRW3tvkXWZh58N9jp"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="/CSS/YardView/manage-appointments.min.css" />
    <style>
      #appointment_form #Unconfirmed .color-box {
        border: 2px solid burlywood;
        background-color: transparent;
      }
      #appointment_form
        #appointment_table
        .appointment-slot
        .appointment-slot-entry.Unconfirmed {
        border: 4px solid burlywood;
      }

      .drag-highlight {
        background-color: #c1c9d0 !important;
      }
    </style>

    <style type="text/css">
      @font-face {
        font-family: "Atlassian Sans";
        font-style: normal;
        font-weight: 400 653;
        font-display: swap;
        src:
          local("AtlassianSans"),
          local("Atlassian Sans Text"),
          url("chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin.woff2")
            format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    </style>
  </head>
  <body style="">
    <div class="yv-container">
      <div id="divMain" align="center">
        <div class="header">
          <div id="headerRow" class="headerrow w-row">
            <div id="headernav" class="headernavsection w-col w-col-12">
              <div
                id="TABLE1Pro"
                class="headerright w-row w-col w-col-12"
                style="display: flex; height: 65px; align-items: center"
              >
                <div id="MenuHomeIcon" onclick="positionZero()">
                  <a href="/menu/Splash.htm" class="partner-logo">
                    <img
                      src="/CustomerFiles/Images/Menulogo.png"
                      alt="Home"
                      title="Home"
                    />
                  </a>
                </div>
                <div
                  id="LfButtonDiv"
                  class="scrollBtn"
                  style="visibility: hidden; display: inline"
                >
                  <button id="left-button" type="button">
                    <img
                      src="../CSS/Pro/Chevron-L-Dbl-Blue.png"
                      style="width: 37px"
                    />
                  </button>
                </div>
                <div
                  id="MainMenuPro"
                  class="w-row w-col scrollmenu"
                  style="display: inline-flex; padding: 0px 1px"
                >
                  <div class="" style="display: inline-block">
                    <a
                      href="/Help/Help.htm"
                      class=""
                      style="padding: 0px 20px 0px 0px"
                    >
                      <img src="/CSS/Pro/help2.png" alt="Help" title="Help" />
                    </a>
                  </div>

                  <div class="" style="display: inline-block">
                    <a href="/Menu/ReportsXmlLanding.htm" class="">
                      <img
                        src="/CSS/Pro/reports1.png"
                        alt="Reports"
                        title="Reports"
                      />
                    </a>
                  </div>

                  <div class="" style="display: inline-block">
                    <a href="/Menu/LandingPageAppt1.htm" class="">
                      <img
                        src="/css/Pro/Appt_Module.png"
                        alt="Manage Appointments"
                        title="Manage Appointments"
                      />
                    </a>
                  </div>

                  <div class="" style="display: inline-block">
                    <a
                      href="/Menu/Search.htm"
                      class=""
                      style="padding: 0px 0px 0px 20px"
                    >
                      <img
                        src="/CSS/Pro/search.png"
                        alt="Search"
                        title="Search"
                      />
                    </a>
                  </div>
                </div>
                <div id="RtButtonDiv" class="scrollBtn" style="display: none">
                  <button id="right-button" type="button">
                    <img
                      src="../CSS/Pro/Chevron-R-Dbl-Blue.png"
                      style="width: 37px"
                    />
                  </button>
                </div>
                <div
                  id="headerlogoutDiv"
                  class="headerlogoutcol"
                  style="margin: 8px 20px 0px; display: block"
                >
                  <a href="/maintenancepages/changepassword.aspx" target="_new">
                    <div id="mainMenuYVLogo" style="display: block">
                      <img
                        src="../CSS/Pro/blue-logo.png"
                        width="161"
                        style="margin-top: -5px"
                        class="noSelect"
                        alt="Change Password"
                      />
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div id="subHeader">
            <div
              id="ctl00_ctl00_divSubHeaderTitle"
              class="subheadertitle subHeader"
            >
              <div style="float: left">
                <h4 id="ctl00_ctl00_pageTitle" class="pageheadertxt">
                  Manage Appointments
                </h4>
              </div>
              <div
                id="ctl00_ctl00_divHeaderDev"
                style="
                  display: inline-block;
                  width: 33%;
                  text-align: center;
                  color: red;
                  font-weight: 600;
                  font-size: larger;
                "
              ></div>
              <div class="logoutcontainer" style="width: initial">
                <div class="logoutdivlinks" onclick="positionZero()">
                  <div style="float: left">
                    <a
                      href="../Logon/loginscreen.aspx?Action=logout"
                      id="ctl00_ctl00_LogoutControl_Logout"
                      target="_top"
                      class="logoutdivlinks"
                      >Logout</a
                    >
                  </div>
                  <div
                    id="ctl00_ctl00_LogoutControl_FacilityDiv"
                    style="float: right"
                  >
                    From: Breingsville
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="header spacer"></div>
      </div>

      <div class="yv-main" style="overflow: auto !important">
        <div id="sidebar">
          <table id="MainMenu">
            <tbody>
              <tr style="display: none"></tr>

              <tr style="display: none"></tr>

              <tr>
                <td>
                  <div class="reportSectionHeader2">RECEIVING APPOINTMENTS</div>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ManageAppointments.aspx?xmlfile=appointments/CalendarConfigInbound.xml&amp;ContentFile="
                  >
                    <img src="../css/menu/ApptCalIn.png" alt="" />
                    Receiving Calendar
                  </a>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ManageAppointments.aspx?LandingUrl='%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentReports%2fApptInboundStatus.xml'&amp;ContentFile="
                  >
                    <img src="../css/menu/PendingIn.png" alt="" />
                    Pending Receiving
                  </a>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ManageAppointments.aspx?LandingUrl='%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentreports%2fApptInboundCompleted.xml'&amp;ContentFile="
                  >
                    <img src="../css/menu/CompletedIB.png" alt="" />
                    Completed Receiving
                  </a>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ManageAppointments.aspx?LandingUrl='%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentreports%2fApptInboundLate.xml'&amp;ContentFile="
                  >
                    <img src="../css/menu/ApptLateIB.png" alt="" />
                    Late Receiving
                  </a>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ManageAppointments.aspx?LandingUrl='%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentReports%2fApptInboundCreate.xml'&amp;ContentFile="
                  >
                    <img
                      src="../css/menu/ApptCalIn.png"
                      alt="Create Receiving Appointment"
                    />
                    Schedule Receiving
                  </a>
                </td>
              </tr>

              <tr style="display: none"></tr>

              <tr style="display: none"></tr>

              <tr>
                <td>
                  <div class="reportSectionHeader2">CUSTOM REPORTS</div>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/Menu/MenuXML.aspx?menufile=MenuAppointmentsSub.xml&amp;ContentFile="
                  >
                    <img src="../css/menu/CustomReports.png" alt="" />
                    Custom Reports
                  </a>
                </td>
              </tr>

              <tr>
                <td>
                  <a
                    href="/AppointmentPages/ScheduleWeekView.aspx?xmlfile=appointments/CalendarConfigInbound.xml&amp;ContentFile="
                  >
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div id="appointment_form" style="overflow-y: auto; overflow-x: hidden">
          <form
            method="post"
            action="./ManageAppointments.aspx?xmlfile=appointments%2fCalendarConfigInbound.xml&amp;ContentFile="
            id="aspnetForm"
            class="flex-container"
          >
            <div class="aspNetHidden">
              <input
                type="hidden"
                name="__EVENTTARGET"
                id="__EVENTTARGET"
                value=""
              />
              <input
                type="hidden"
                name="__EVENTARGUMENT"
                id="__EVENTARGUMENT"
                value=""
              />
              <input
                type="hidden"
                name="__LASTFOCUS"
                id="__LASTFOCUS"
                value=""
              />
              <input
                type="hidden"
                name="__VIEWSTATE"
                id="__VIEWSTATE"
                value="/wEPDwULLTE4NzMwNzA4MDMPZBYCZg9kFgJmD2QWBAIBD2QWAgIFD2QWBAIBDxYCHglpbm5lcmh0bWwFBkxvZ291dGQCAw8WAh8ABRJGcm9tOiBCcmVpbmdzdmlsbGVkAgIPZBYCZg9kFgJmD2QWBGYPDxYCHgdWaXNpYmxlaBYCHgVzdHlsZQUNZGlzcGxheTpub25lO2QCAQ9kFgZmD2QWAgIBDxBkEBUDDUFsbCBTY2hlZHVsZXMHSW5ib3VuZBlUQVJHRVQgV0FURVIgU1RPUkFHRSBPTkxZFQMAATECMjMUKwMDZ2dnZGQCAQ8WAh8BaGQCAg9kFgICAQ9kFgJmD2QWAmYPDxYCHgRUZXh0BQkzLzI4LzIwMjVkZGTeT6AUK75ukPC5zjlZ0FJqT+Mn0kOxI7FVl2QMWtGuxQ=="
              />
            </div>

            <script type="text/javascript">
              //<![CDATA[
              var theForm = document.forms["aspnetForm"];
              if (!theForm) {
                theForm = document.aspnetForm;
              }
              function __doPostBack(eventTarget, eventArgument) {
                if (!theForm.onsubmit || theForm.onsubmit() != false) {
                  theForm.__EVENTTARGET.value = eventTarget;
                  theForm.__EVENTARGUMENT.value = eventArgument;
                  theForm.submit();
                }
              }
              //]]>
            </script>

            <script
              src="/WebResource.axd?d=p_Z9iTmXJpD37U8cMcNElOj-fPi-AZV6K27mYBIzSq2vJCRtV-8buQF_LeqFpgbQ9WnD9a_ZODDUMVb3rEpVFU8IF0fLjHrt3kWXR7F8NgQ1&amp;t=638628044640000000"
              type="text/javascript"
            ></script>

            <div class="aspNetHidden">
              <input
                type="hidden"
                name="__VIEWSTATEGENERATOR"
                id="__VIEWSTATEGENERATOR"
                value="5BEDB95F"
              />
              <input
                type="hidden"
                name="__EVENTVALIDATION"
                id="__EVENTVALIDATION"
                value="/wEdAAWIKS3YZeOkSwdZ1wStOAz1S0RWDAycviASS0oGAcGI0wKB8CDhlASNtDl0K4tfbNXbEZoFWgIROE8s9w0UiR7SKClCU5OusNZCiXzqobVibq+Oe90DMizXoFK48h+CH6FWo9f9Djew8/qq3EI4K4VG"
              />
            </div>
            <div id="backdrop"></div>
            <div id="AppointmentsSubPages" class="noShow">
              <div class="row" id="date-legend-row">
                <div class="col-8" id="day-col-8">
                  <div class="h1">
                    <button id="today">Today</button>
                    <span id="current_date"
                      ><div id="date-div">
                        <span id="dayofweek">Friday </span
                        ><img
                          id="day-back"
                          date="3/28/2025"
                          src="../Images/feather/chevron-left.svg"
                        />
                        March 28, 2025
                        <img
                          id="day-forward"
                          date="3/28/2025"
                          src="../Images/feather/chevron-right.svg"
                        />
                      </div>
                    </span>
                    <div class="p-2" id="LegendDiv">
                      <div class="legendRow" id="Pending">
                        <div class="color-box"></div>
                        <a
                          class="legendText"
                          href="/pages/reporting?xmlfile=appointmentreports/ApptInboundStatus.xml&amp;SelectedDate=3/28/2025&amp;SelectedSchedule="
                          target="_blank"
                        >
                          Pending (16)</a
                        >
                      </div>
                      <div class="legendRow" id="Completed">
                        <div class="color-box"></div>
                        <a
                          class="legendText"
                          href="/pages/reporting?xmlfile=appointmentreports/ApptInboundCompleted.xml&amp;SelectedDate=3/28/2025&amp;SelectedSchedule="
                          target="_blank"
                        >
                          Completed (0)</a
                        >
                      </div>
                      <div class="legendRow" id="Late">
                        <div class="color-box"></div>
                        <a
                          class="legendText"
                          href="/pages/reporting?xmlfile=appointmentreports/ApptInboundLate.xml&amp;SelectedDate=3/28/2025&amp;SelectedSchedule="
                          target="_blank"
                        >
                          Late (8)</a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <div
                  id="ctl00_ctl00_Body_AppointmentBody_mySidebar"
                  class="sidebar noTransition expanded"
                >
                  <a href="javascript:;" class="closebtn" id="closebtn">×</a>
                  <div class="col" id="date-column">
                    <div
                      id="ctl00_ctl00_Body_AppointmentBody_ScheduleNamesDiv"
                      class="col-1"
                    >
                      <div class="p-2" id="TypeSelectionDiv">
                        <div id="SelectTypeDiv" class="ui-widget-header">
                          <p id="SelectType">View Schedule For :</p>
                        </div>
                        <select
                          name="ctl00$ctl00$Body$AppointmentBody$ScheduleNames"
                          onchange="javascript:setTimeout('__doPostBack(\'ctl00$ctl00$Body$AppointmentBody$ScheduleNames\',\'\')', 0)"
                          id="ctl00_ctl00_Body_AppointmentBody_ScheduleNames"
                        >
                          <option selected="selected" value="">
                            All Schedules
                          </option>
                          <option value="1">Inbound</option>
                          <option value="23">TARGET WATER STORAGE ONLY</option>
                        </select>
                      </div>
                    </div>

                    <div
                      id="ctl00_ctl00_Body_AppointmentBody_CalendarDatePickerDiv"
                      class="col-1"
                    >
                      <div class="p-2" id="DateSelDiv">
                        <div id="date-sel-div" class="ui-widget-header">
                          <p id="date-sel">Date Selected :</p>
                        </div>
                        <div
                          id="ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_Body"
                        >
                          <link
                            rel="stylesheet"
                            href="/Scripts/jquery/jquery-ui-themes/themes/base/jquery-ui.min.css"
                          />
                          <link
                            rel="stylesheet"
                            href="/Scripts/jquery/jquery-ui-themes/themes/base/theme.css"
                          />

                          <script src="/Scripts/jquery-safe.js"></script>
                          <script
                            src="/Scripts/jquery/jquery-1.12.4.min.js"
                            type="text/javascript"
                          ></script>
                          <script src="/Scripts/jquery-ui-safe.js"></script>
                          <script
                            src="/Scripts/jquery/jquery-ui-1.12.1/jquery-ui.min.js"
                            type="text/javascript"
                          ></script>
                          <script src="/Scripts/jquery/jquery_maskedinput_min.js"></script>

                          <input
                            name="ctl00$ctl00$Body$AppointmentBody$CurrentDatePicker$DP1"
                            type="text"
                            value="3/28/2025"
                            onchange="javascript:setTimeout('__doPostBack(\'ctl00$ctl00$Body$AppointmentBody$CurrentDatePicker$DP1\',\'\')', 0)"
                            onkeypress="if (WebForm_TextBoxKeyHandler(event) == false) return false;"
                            id="ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_DP1"
                            class="datepicker hasDatepicker"
                          />
                          <div
                            id="ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_cal"
                            class="datepicker hasDatepicker"
                            autopostback="true"
                          >
                            <div
                              class="ui-datepicker-inline ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all"
                              style="display: block"
                            >
                              <div
                                class="ui-datepicker-header ui-widget-header ui-helper-clearfix ui-corner-all"
                              >
                                <a
                                  class="ui-datepicker-prev ui-corner-all"
                                  data-handler="prev"
                                  data-event="click"
                                  title="Prev"
                                  ><span
                                    class="ui-icon ui-icon-circle-triangle-w"
                                    >Prev</span
                                  ></a
                                ><a
                                  class="ui-datepicker-next ui-corner-all"
                                  data-handler="next"
                                  data-event="click"
                                  title="Next"
                                  ><span
                                    class="ui-icon ui-icon-circle-triangle-e"
                                    >Next</span
                                  ></a
                                >
                                <div class="ui-datepicker-title">
                                  <span class="ui-datepicker-month">March</span
                                  >&nbsp;<span class="ui-datepicker-year"
                                    >2025</span
                                  >
                                </div>
                              </div>
                              <table class="ui-datepicker-calendar">
                                <thead>
                                  <tr>
                                    <th
                                      scope="col"
                                      class="ui-datepicker-week-end"
                                    >
                                      <span title="Sunday">Su</span>
                                    </th>
                                    <th scope="col">
                                      <span title="Monday">Mo</span>
                                    </th>
                                    <th scope="col">
                                      <span title="Tuesday">Tu</span>
                                    </th>
                                    <th scope="col">
                                      <span title="Wednesday">We</span>
                                    </th>
                                    <th scope="col">
                                      <span title="Thursday">Th</span>
                                    </th>
                                    <th scope="col">
                                      <span title="Friday">Fr</span>
                                    </th>
                                    <th
                                      scope="col"
                                      class="ui-datepicker-week-end"
                                    >
                                      <span title="Saturday">Sa</span>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="1"
                                        >1</a
                                      >
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="2"
                                        >2</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="3"
                                        >3</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="4"
                                        >4</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="5"
                                        >5</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="6"
                                        >6</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="7"
                                        >7</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="8"
                                        >8</a
                                      >
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="9"
                                        >9</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="10"
                                        >10</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="11"
                                        >11</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="12"
                                        >12</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="13"
                                        >13</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="14"
                                        >14</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="15"
                                        >15</a
                                      >
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="16"
                                        >16</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="17"
                                        >17</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="18"
                                        >18</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="19"
                                        >19</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="20"
                                        >20</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="21"
                                        >21</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="22"
                                        >22</a
                                      >
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="23"
                                        >23</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="24"
                                        >24</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="25"
                                        >25</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="26"
                                        >26</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="27"
                                        >27</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-days-cell-over ui-datepicker-current-day ui-datepicker-today"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default ui-state-active ui-state-hover custom-ui-state-highlight"
                                        href="#"
                                        aria-current="true"
                                        data-date="28"
                                        >28</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="29"
                                        >29</a
                                      >
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      class="ui-datepicker-week-end"
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="30"
                                        >30</a
                                      >
                                    </td>
                                    <td
                                      class=" "
                                      data-handler="selectDay"
                                      data-event="click"
                                      data-month="2"
                                      data-year="2025"
                                    >
                                      <a
                                        class="ui-state-default"
                                        href="#"
                                        aria-current="false"
                                        data-date="31"
                                        >31</a
                                      >
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                    <td
                                      class="ui-datepicker-week-end ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
                                    >
                                      &nbsp;
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>

                          <script type="text/javascript">
                            $(function () {
                              var yvCalendarDatePicker = $(
                                "#ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_cal",
                              );
                              var datePicker = $(
                                "#ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_DP1",
                              );
                              var dateValue = new Date("2025-03-28T15:34:03");
                              var localFormat = "m/d/yy";
                              $(".datepicker").datepicker();

                              datePicker.datepicker({
                                dateFormat: localFormat,
                              });
                              yvCalendarDatePicker.datepicker({
                                inline: true,
                                altFormat: localFormat,
                                altField:
                                  "#ctl00_ctl00_Body_AppointmentBody_CurrentDatePicker_DP1",
                                dateFormat: localFormat,
                                minDate: "01/01/2010",
                                maxDate: "12/12/2026",
                                goToCurrent: true,
                                defaultDate: dateValue,
                              });

                              // Setting these above doesn't seem to work
                              datePicker.datepicker(
                                "option",
                                "dateFormat",
                                localFormat,
                              );
                              yvCalendarDatePicker.datepicker(
                                "option",
                                "dateFormat",
                                localFormat,
                              );

                              datePicker.datepicker("setDate", dateValue);
                              yvCalendarDatePicker.datepicker(
                                "setDate",
                                dateValue,
                              );

                              yvCalendarDatePicker.change(function () {
                                var selectedDate = $(this).val();
                                datePicker.datepicker("setDate", selectedDate);
                                yvCalendarDatePicker.datepicker(
                                  "setDate",
                                  selectedDate,
                                );
                                datePicker.change();
                              });

                              $(document)
                                .find("a.ui-state-highlight")
                                .removeClass("ui-state-highlight")
                                .addClass("custom-ui-state-highlight");
                            });
                          </script>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                id="ctl00_ctl00_Body_AppointmentBody_appointment_main"
                class="main noTransition"
              >
                <div class="col" id="appt-col">
                  <div class="table-flex">
                    <a
                      href="javascript:;"
                      class="openbtn"
                      id="openbtn"
                      style="display: none; height: 610px"
                      ><i
                        id="AppointmentFilter"
                        class="fas fa-chevron-right"
                      ></i
                    ></a>
                    <div
                      id="AptTable"
                      class="table-scroll"
                      style="height: 607px; width: 365px; overflow-x: auto"
                    >
                      <table
                        class="table table-bordered table-striped"
                        id="appointment_table"
                      >
                        <thead>
                          <tr>
                            <th class="blank-time-column"></th>

                            <th class="schedule-name" style="min-width: 200px">
                              Inbound
                            </th>

                            <th class="schedule-name" style="min-width: 200px">
                              TARGET WATER STORAGE ONLY
                            </th>
                          </tr>
                        </thead>
                        <tbody class="appointment_tbody" horizontalscroll="on">
                          <tr>
                            <td class="display-time">06:30</td>

                            <td
                              class="appointment-slot"
                              id="1-0630"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 6:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Late"
                                  id="188374"
                                  apptdatetime="3/28/2025 6:30:00 AM"
                                  ogapptslotid="1-0630"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes-late">-09:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188374);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188374);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188374);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Late"
                                  id="188430"
                                  apptdatetime="3/28/2025 6:30:00 AM"
                                  ogapptslotid="1-0630"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503277896
                                  </span>

                                  <span class="minutes-late">-09:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188430);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188430);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188430);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Late"
                                  id="188432"
                                  apptdatetime="3/28/2025 6:30:00 AM"
                                  ogapptslotid="1-0630"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503277906
                                  </span>

                                  <span class="minutes-late">-09:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188432);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188432);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188432);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="416-override"
                                    schedule="Inbound"
                                    time="06:30"
                                    pid="416"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="416"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-0630"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 6:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">07:30</td>

                            <td
                              class="appointment-slot"
                              id="1-0730"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 7:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="417-override"
                                    schedule="Inbound"
                                    time="07:30"
                                    pid="417"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="417"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-0730"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 7:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">08:30</td>

                            <td
                              class="appointment-slot"
                              id="1-0830"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 8:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="418-override"
                                    schedule="Inbound"
                                    time="08:30"
                                    pid="418"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="418"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-0830"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 8:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">09:30</td>

                            <td
                              class="appointment-slot"
                              id="1-0930"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 9:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Late"
                                  id="188423"
                                  apptdatetime="3/28/2025 9:30:00 AM"
                                  ogapptslotid="1-0930"
                                  appts="1"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53467486 | | **********
                                  </span>

                                  <span class="minutes-late">-06:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188423);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188423);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188423);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="419-override"
                                    schedule="Inbound"
                                    time="09:30"
                                    pid="419"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="419"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-0930"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 9:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">10:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1030"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 10:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Late"
                                  id="188428"
                                  apptdatetime="3/28/2025 10:30:00 AM"
                                  ogapptslotid="1-1030"
                                  appts="1"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes-late">-05:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188428);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188428);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188428);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="420-override"
                                    schedule="Inbound"
                                    time="10:30"
                                    pid="420"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="420"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1030"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 10:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">11:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1130"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 11:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="421-override"
                                    schedule="Inbound"
                                    time="11:30"
                                    pid="421"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="421"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1130"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 11:30:00 AM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">12:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1230"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 12:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Late"
                                  id="188383"
                                  apptdatetime="3/28/2025 12:30:00 PM"
                                  ogapptslotid="1-1230"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes-late">-03:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188383);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188383);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188383);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Late"
                                  id="188385"
                                  apptdatetime="3/28/2025 12:30:00 PM"
                                  ogapptslotid="1-1230"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503247706
                                  </span>

                                  <span class="minutes-late">-03:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188385);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188385);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188385);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Late"
                                  id="188409"
                                  apptdatetime="3/28/2025 12:30:00 PM"
                                  ogapptslotid="1-1230"
                                  appts="3"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53467553 | | 2503257808
                                  </span>

                                  <span class="minutes-late">-03:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188409);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188409);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188409);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="422-override"
                                    schedule="Inbound"
                                    time="12:30"
                                    pid="422"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="422"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1230"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 12:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">13:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1330"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 1:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="423-override"
                                    schedule="Inbound"
                                    time="13:30"
                                    pid="423"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="423"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1330"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 1:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr class="table-primary">
                            <td class="display-time">14:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1430"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 2:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="424-override"
                                    schedule="Inbound"
                                    time="14:30"
                                    pid="424"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="424"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1430"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 2:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">15:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1530"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 3:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188434"
                                  apptdatetime="3/28/2025 3:30:00 PM"
                                  ogapptslotid="1-1530"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes-late">-00:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188434);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188434);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188434);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188439"
                                  apptdatetime="3/28/2025 3:30:00 PM"
                                  ogapptslotid="1-1530"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503287934
                                  </span>

                                  <span class="minutes-late">-00:04</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188439);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188439);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188439);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="425-override"
                                    schedule="Inbound"
                                    time="15:30"
                                    pid="425"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="425"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1530"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 3:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">16:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1630"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 4:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188380"
                                  apptdatetime="3/28/2025 4:30:00 PM"
                                  ogapptslotid="1-1630"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+00:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188380);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188380);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188380);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188421"
                                  apptdatetime="3/28/2025 4:30:00 PM"
                                  ogapptslotid="1-1630"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53510526 | | 2503267856
                                  </span>

                                  <span class="minutes">+00:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188421);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188421);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188421);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="426-override"
                                    schedule="Inbound"
                                    time="16:30"
                                    pid="426"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="426"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1630"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 4:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">17:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1730"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 5:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188381"
                                  apptdatetime="3/28/2025 5:30:00 PM"
                                  ogapptslotid="1-1730"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+01:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188381);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188381);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188381);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188382"
                                  apptdatetime="3/28/2025 5:30:00 PM"
                                  ogapptslotid="1-1730"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53471401 | | 2503247692
                                  </span>

                                  <span class="minutes">+01:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188382);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188382);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188382);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="427-override"
                                    schedule="Inbound"
                                    time="17:30"
                                    pid="427"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="427"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1730"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 5:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">18:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1830"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 6:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188379"
                                  apptdatetime="3/28/2025 6:30:00 PM"
                                  ogapptslotid="1-1830"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+02:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188379);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188379);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188379);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188415"
                                  apptdatetime="3/28/2025 6:30:00 PM"
                                  ogapptslotid="1-1830"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53468252 | | 2503267832
                                  </span>

                                  <span class="minutes">+02:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188415);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188415);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188415);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="428-override"
                                    schedule="Inbound"
                                    time="18:30"
                                    pid="428"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="428"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1830"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 6:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">19:30</td>

                            <td
                              class="appointment-slot"
                              id="1-1930"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 7:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188424"
                                  apptdatetime="3/28/2025 7:30:00 PM"
                                  ogapptslotid="1-1930"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+03:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188424);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188424);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188424);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188435"
                                  apptdatetime="3/28/2025 7:30:00 PM"
                                  ogapptslotid="1-1930"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503277918
                                  </span>

                                  <span class="minutes">+03:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188435);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188435);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188435);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="429-override"
                                    schedule="Inbound"
                                    time="19:30"
                                    pid="429"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="429"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-1930"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 7:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">20:30</td>

                            <td
                              class="appointment-slot"
                              id="1-2030"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 8:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188436"
                                  apptdatetime="3/28/2025 8:30:00 PM"
                                  ogapptslotid="1-2030"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+04:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188436);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188436);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188436);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188443"
                                  apptdatetime="3/28/2025 8:30:00 PM"
                                  ogapptslotid="1-2030"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="3"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503287950
                                  </span>

                                  <span class="minutes">+04:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188443);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188443);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188443);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="430-override"
                                    schedule="Inbound"
                                    time="20:30"
                                    pid="430"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="430"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-2030"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 8:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">21:30</td>

                            <td
                              class="appointment-slot"
                              id="1-2130"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 9:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188419"
                                  apptdatetime="3/28/2025 9:30:00 PM"
                                  ogapptslotid="1-2130"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | **********
                                  </span>

                                  <span class="minutes">+05:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188419);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188419);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188419);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188444"
                                  apptdatetime="3/28/2025 9:30:00 PM"
                                  ogapptslotid="1-2130"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display">
                                    | | 2503287954
                                  </span>

                                  <span class="minutes">+05:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188444);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188444);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188444);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="431-override"
                                    schedule="Inbound"
                                    time="21:30"
                                    pid="431"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="431"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-2130"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 9:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>

                          <tr>
                            <td class="display-time">22:30</td>

                            <td
                              class="appointment-slot"
                              id="1-2230"
                              schedulepid="1"
                              locationpid="307"
                              slotdatetime="3/28/2025 10:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188412"
                                  apptdatetime="3/28/2025 10:30:00 PM"
                                  ogapptslotid="1-2230"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53461913 | | **********
                                  </span>

                                  <span class="minutes">+06:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188412);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188412);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188412);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <div
                                  class="appointment-slot-entry Pending"
                                  id="188445"
                                  apptdatetime="3/28/2025 10:30:00 PM"
                                  ogapptslotid="1-2230"
                                  appts="2"
                                  confirmation="0"
                                  slotactions="3"
                                  cap="2"
                                  slotorder="0"
                                  itemcapacity="0"
                                >
                                  <span class="appointment-slot-entry-display"
                                    >53467532 | | 2503287958
                                  </span>

                                  <span class="minutes">+06:55</span>

                                  <span class="appointment-slot-entry-links">
                                    <span
                                      class="links"
                                      onclick="cancel(188445);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/slash.svg"
                                        alt="Cancel Appointment"
                                        title="Cancel Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="edit(188445);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/feather/edit-2.svg"
                                        alt="Edit Appointment"
                                        title="Edit Appointment"
                                      />
                                    </span>

                                    <span
                                      class="links"
                                      onclick="inbound(188445);"
                                      height="680"
                                      width="930"
                                    >
                                      <img
                                        src="/Images/truckwcheck.png"
                                        alt="Inbound"
                                        title="Inbound"
                                      />
                                    </span>
                                  </span>
                                </div>

                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                  <a
                                    class="override-cap-link"
                                    id="432-override"
                                    schedule="Inbound"
                                    time="22:30"
                                    pid="432"
                                    date="3/28/2025"
                                  >
                                    <span
                                      ><svg
                                        id="tooltip-svg"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="feather feather-zap"
                                      >
                                        <polygon
                                          points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"
                                        ></polygon>
                                      </svg>
                                      <p
                                        class="tooltiptextavailabilityoc"
                                        oc="true"
                                        pid="432"
                                        date="3/28/2025"
                                      ></p
                                    ></span>
                                  </a>
                                </span>
                              </div>
                            </td>

                            <td
                              class="appointment-slot"
                              id="23-2230"
                              schedulepid="23"
                              locationpid="52"
                              slotdatetime="3/28/2025 10:30:00 PM"
                              style="min-width: 200px"
                            >
                              <div>
                                <span
                                  id="appt-action-span"
                                  class="appt-action-spans"
                                >
                                </span>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div id="appointment_actions"></div>
        <label
          id="save-message-update"
          class="alert alert-success"
          style="display: none"
          >Appointment has been updated</label
        >
        <label
          id="error-message-update"
          class="alert alert-danger"
          style="display: none"
          >Appointment unable to be updated</label
        >
        <div id="confirmation_modal" class="modal" tabindex="-1" role="dialog">
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Appointment Confirmation</h5>
                <button
                  type="button"
                  class="close"
                  data-dismiss="modal"
                  aria-label="Close"
                >
                  <span aria-hidden="true">×</span>
                </button>
              </div>
              <div class="modal-body" id="appointment-confirmation-body"></div>
            </div>
          </div>
        </div>
        <div
          id="quick-reserve-confirmation"
          class="modal"
          tabindex="-1"
          role="dialog"
        >
          <div class="modal-dialog" role="document" style="max-width: 400px">
            <div class="modal-content">
              <div class="modal-header">
                <h4 id="time-slot-info"></h4>
                <button
                  type="button"
                  class="close"
                  data-dismiss="modal"
                  aria-label="Close"
                  onclick="closeModal();"
                >
                  <span aria-hidden="true">×</span>
                </button>
              </div>
              <div class="modal-body">
                <label id="save-message" class="alert alert-success"
                  >Override capacity saved</label
                >
                <label id="save-error-message" class="alert alert-danger"
                  >Override capacity unable to be saved</label
                >
                <p id="reserve-confirm"></p>
              </div>
              <div class="modal-footer">
                <button class="modalBtn modalBtn_blue" id="Save">Save</button>
                <button
                  class="modalBtn modalBtn_grey"
                  id="Cancel"
                  onclick="closeModal();"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="/Scripts/jquery-safe.js"></script>
    <script src="/Scripts/jquery/popper.js-1.16.0/popper.min.js"></script>
    <script src="/Scripts/jquery/bootstrap-4.3.1-dist/js/bootstrap.min.js"></script>
    <script src="/Scripts/jquery/jquery.blockUI.js"></script>
    <script src="/Scripts/yardview/helper.js"></script>

    <script
      src="/Scripts/jquery/jquery-1.12.4.min.js"
      type="text/javascript"
    ></script>
    <script
      src="/Scripts/jquery/jquery-ui/jquery-ui.min.js"
      type="text/javascript"
    ></script>
    <script
      src="../Scripts/jquery/jquery.blockUI.js"
      type="text/javascript"
    ></script>
    <script
      src="../Scripts/default-nopostback.js"
      type="text/javascript"
    ></script>
    <script type="text/javascript" language="javascript">
      $(document).ready(function () {
        $.blockUI();
        setTimeout($.unblockUI, 500);
      });
    </script>
    <script
      type="text/javascript"
      src="/Scripts/jquery/touchpunch/touch-punch.js"
    ></script>
    <script
      type="text/javascript"
      src="/Scripts/yardview/appointments/appointment-manager.js"
    ></script>
    <script type="text/javascript">
      function doRefresh() {
        __doPostBack(
          "ctl00$ctl00$Body$AppointmentBody$CurrentDatePicker",
          "refresh",
        );
      }

      var refreshAppointmentView =
        "/AppointmentPages/ManageAppointments.aspx?xmlfile=appointments/CalendarConfigInbound.xml&SelectedDate=3/28/2025&SelectedSchedule=";
      var dragOption = "off";

      window.addEventListener("resize", setTableAppointmentsDayView);
      var currentFacilityTime = "3/28/2025 3:34:04 PM";

      setupPage(
        false,
        2,
        680,
        930,
        refreshAppointmentView,
        15,
        currentFacilityTime,
        dragOption,
        true,
      );
    </script>

    <script type="text/javascript">
      $(".yv-main").attr("style", "overflow:auto !important;");
    </script>

    <script type="text/javascript">
      var mobile = false;
      var iconsWidth = null;
      var firstIconPadding = null;
      var mainMenuProWidthNeeded = null;
      var RtButtonDiv = document.getElementById("RtButtonDiv");
      var LfButtonDiv = document.getElementById("LfButtonDiv");
      var divMainMenuPro = document.getElementById("MainMenuPro");
      var scrollRate = 100;
      var scrollInterval = null;
      var buttonLf = document.getElementById("left-button");
      var buttonRt = document.getElementById("right-button");
      var TABLE1ProDiv = document.getElementById("TABLE1Pro");
      var divSubHeaderTitle = document.getElementById("divSubHeaderTitle");
      var divMain = document.getElementById("divMain");

      var mainMenuYVLogoDiv = document.getElementById("mainMenuYVLogo");
      var headernavDiv = document.getElementById("headernav");
      var headerRowDiv = document.getElementById("headerRow");
      var headerlogoutDiv = document.getElementById("headerlogoutDiv");
      var displayWidth = divMain.scrollWidth;

      $(document).ready(function () {
        window.addEventListener("resize", windowResized);

        buttonLf.addEventListener("touchstart", startScrollLeftTouch);
        buttonRt.addEventListener("touchstart", startScrollRightTouch);

        buttonLf.addEventListener("mousedown", startScrollLeft);
        buttonRt.addEventListener("mousedown", startScrollRight);

        buttonLf.addEventListener("mouseup", stopScroll);
        buttonRt.addEventListener("mouseup", stopScroll);
      });
      $(window).load(function () {
        setMenu();
      });
      function removePadding() {
        var firstIcon = divMainMenuPro.firstElementChild.firstElementChild;
        firstIcon.style.padding = "0px 20px 0px 0px";
        var lastIcon = divMainMenuPro.lastElementChild.firstElementChild;
        lastIcon.style.padding = "0px 0px 0px 20px";
      }
      function setIconLengths() {
        var childNodes = divMainMenuPro.children;
        iconsWidth = childNodes[1].offsetWidth;
        var firstIconChild = childNodes[0].children;
        firstIconPadding = parseInt(firstIconChild[0].style.paddingRight);
        mainMenuProWidthNeeded =
          divMainMenuPro.childElementCount * iconsWidth - firstIconPadding * 2; //remove 40 for padding on 1st and last icons
      }
      function windowResized() {
        if (displayWidth != divMain.scrollWidth) {
          displayWidth = divMain.scrollWidth;
          setMenu();
        }
      }
      function setMenu() {
        setWebsiteMenu();
        setMenuScrollable();
      }
      function setWebsiteMenu() {
        LfButtonDiv.style.display = "inline";
        mainMenuYVLogoDiv.style.display = "block";
        headerlogoutDiv.style.display = "block";
        removePadding();
        setIconLengths();
      }
      function setMenuScrollable() {
        var RtBtnWidth = RtButtonDiv.offsetWidth;
        var iconMenuVisibleWidth = divMainMenuPro.clientWidth;
        if (RtButtonDiv.style.display == "block") {
          var iconMenuVisibleWidth = divMainMenuPro.clientWidth + RtBtnWidth;
        }
        if (iconMenuVisibleWidth < mainMenuProWidthNeeded) {
          RtButtonDiv.style.display = "block";
          setMenuPosition();
        } else {
          RtButtonDiv.style.display = "none";
          LfButtonDiv.style.visibility = "hidden";
        }
      }
      function setMenuPosition() {
        var position = sessionStorage.getItem("scrollPos");
        var menuPosition = Number(position);
        setPosition(menuPosition);
        if (position == "null") {
          //no session data for position
          RtButtonDiv.style.visibility = "visible";
          return;
        }
        checkBtnVis();
        if (mobile) {
          //Added after position is set to avoid scroll behavior
          divMainMenuPro.classList.add("mobile");
        }
      }
      function setPosition(menuPosition) {
        document.getElementById("MainMenuPro").scrollLeft = menuPosition;
      }
      function scrollLeft() {
        divMainMenuPro.scrollLeft -= 4;
      }
      function scrollRight() {
        divMainMenuPro.scrollLeft += 4;
      }
      function startScrollLeft() {
        scrollInterval = setInterval(scrollLeft, 1000 / scrollRate);
      }
      function startScrollRight() {
        scrollInterval = setInterval(scrollRight, 1000 / scrollRate);
      }
      function startScrollLeftTouch(event) {
        divMainMenuPro.scrollLeft -= 120;
        setTimeout(stopScroll, 500);
      }
      function startScrollRightTouch(event) {
        divMainMenuPro.scrollLeft += 120;
        setTimeout(stopScroll, 500);
      }
      function stopScroll() {
        clearInterval(scrollInterval);
        var scrollPos = divMainMenuPro.scrollLeft;
        sessionStorage.setItem("scrollPos", scrollPos);
        checkBtnVis();
      }
      function checkBtnVis() {
        var scrollPos = divMainMenuPro.scrollLeft;
        var scrollWidth =
          divMainMenuPro.scrollWidth - divMainMenuPro.clientWidth;
        if (scrollPos == 0) {
          LfButtonDiv.style.visibility = "hidden";
          RtButtonDiv.style.visibility = "visible";
        } else if (scrollPos >= scrollWidth - 1) {
          LfButtonDiv.style.visibility = "visible";
          RtButtonDiv.style.visibility = "hidden";
        } else {
          LfButtonDiv.style.visibility = "visible";
          RtButtonDiv.style.visibility = "visible";
        }
      }
      function positionZero() {
        sessionStorage.setItem("scrollPos", 0);
      }
    </script>

    <div
      id="ui-datepicker-div"
      class="ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all"
    >
      <div
        class="ui-datepicker-header ui-widget-header ui-helper-clearfix ui-corner-all"
      >
        <a
          class="ui-datepicker-prev ui-corner-all"
          data-handler="prev"
          data-event="click"
          title="Prev"
          ><span class="ui-icon ui-icon-circle-triangle-w">Prev</span></a
        ><a
          class="ui-datepicker-next ui-corner-all"
          data-handler="next"
          data-event="click"
          title="Next"
          ><span class="ui-icon ui-icon-circle-triangle-e">Next</span></a
        >
        <div class="ui-datepicker-title">
          <span class="ui-datepicker-month">March</span>&nbsp;<span
            class="ui-datepicker-year"
            >2025</span
          >
        </div>
      </div>
      <table class="ui-datepicker-calendar">
        <thead>
          <tr>
            <th scope="col" class="ui-datepicker-week-end">
              <span title="Sunday">Su</span>
            </th>
            <th scope="col"><span title="Monday">Mo</span></th>
            <th scope="col"><span title="Tuesday">Tu</span></th>
            <th scope="col"><span title="Wednesday">We</span></th>
            <th scope="col"><span title="Thursday">Th</span></th>
            <th scope="col"><span title="Friday">Fr</span></th>
            <th scope="col" class="ui-datepicker-week-end">
              <span title="Saturday">Sa</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td
              class="ui-datepicker-week-end ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="1"
                >1</a
              >
            </td>
          </tr>
          <tr>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="2"
                >2</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="3"
                >3</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="4"
                >4</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="5"
                >5</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="6"
                >6</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="7"
                >7</a
              >
            </td>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="8"
                >8</a
              >
            </td>
          </tr>
          <tr>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="9"
                >9</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="10"
                >10</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="11"
                >11</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="12"
                >12</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="13"
                >13</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="14"
                >14</a
              >
            </td>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="15"
                >15</a
              >
            </td>
          </tr>
          <tr>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="16"
                >16</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="17"
                >17</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="18"
                >18</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="19"
                >19</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="20"
                >20</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="21"
                >21</a
              >
            </td>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="22"
                >22</a
              >
            </td>
          </tr>
          <tr>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="23"
                >23</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="24"
                >24</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="25"
                >25</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="26"
                >26</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="27"
                >27</a
              >
            </td>
            <td
              class="ui-datepicker-days-cell-over ui-datepicker-current-day ui-datepicker-today"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default ui-state-active ui-state-hover custom-ui-state-highlight"
                href="#"
                aria-current="true"
                data-date="28"
                >28</a
              >
            </td>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="29"
                >29</a
              >
            </td>
          </tr>
          <tr>
            <td
              class="ui-datepicker-week-end"
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="30"
                >30</a
              >
            </td>
            <td
              class=" "
              data-handler="selectDay"
              data-event="click"
              data-month="2"
              data-year="2025"
            >
              <a
                class="ui-state-default"
                href="#"
                aria-current="false"
                data-date="31"
                >31</a
              >
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
            <td
              class="ui-datepicker-week-end ui-datepicker-other-month ui-datepicker-unselectable ui-state-disabled"
            >
              &nbsp;
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div id="loom-companion-mv3" ext-id="liecbddmkiiihnedobmlmillhodjkdmb">
      <div id="shadow-host-companion"></div>
    </div>
  </body>
</html>
