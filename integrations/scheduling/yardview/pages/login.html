<html
  xmlns="http://www.w3.org/1999/xhtml"
  class="w-mod-js wf-opensans-n3-active wf-opensans-i3-active wf-opensans-n4-active wf-opensans-i4-active wf-opensans-n6-active wf-opensans-i6-active wf-opensans-n7-active wf-opensans-i7-active wf-opensans-n8-active wf-opensans-i8-active wf-active"
>
  <head>
    <title>Login | Yardview</title>
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="Webflow" name="generator" />
    <script src="/Scripts/jquery/jquery-1.12.4.min.js"></script>
    <link href="/CSS/normalize.css" rel="stylesheet" />
    <link href="/CSS/normalize.css" rel="stylesheet" />
    <link href="/CSS/webflow.css" rel="stylesheet" />
    <link href="/CSS/YardView-Login.webflow.css" rel="stylesheet" />
    <script
      src="https://ajax.googleapis.com/ajax/libs/webfont/1.4.7/webfont.js"
      type="text/javascript"
    ></script>
    <script src="/Scripts/msal-browser.min.js" type="text/javascript"></script>
    <script src="/Scripts/okta-sign-in.min.js" type="text/javascript"></script>
    <script src="/Scripts/signon.js" type="text/javascript"></script>
    <link
      href="https://global.oktacdn.com/okta-signin-widget/5.9.1/css/okta-sign-in.min.css"
      type="text/css"
      rel="stylesheet"
    />

    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic,700,700italic,800,800italic"
    />
    <script type="text/javascript">
      $("#ieWarning").hide();
      if (parent.frames.length !== 0) {
        top.location = window.location;
      }

      $(document).ready(function () {
        document.getElementById("txtUsr").focus();
        IeVersion();
      });

      WebFont.load({
        google: {
          families: [
            "Open Sans:300,300italic,400,400italic,600,600italic,700,700italic,800,800italic",
          ],
        },
      });

      function IeVersion() {
        //Set defaults
        var value = {
          IsIE: false,
          TrueVersion: 0,
          ActingVersion: 0,
          CompatibilityMode: false,
        };

        //Try to find the Trident version number
        var trident = navigator.userAgent.match(/Trident\/(\d+)/);
        if (trident) {
          value.IsIE = true;
          //Convert from the Trident version number to the IE version number
          value.TrueVersion = parseInt(trident[1], 10) + 4;
        }

        //Try to find the MSIE number
        var msie = navigator.userAgent.match(/MSIE (\d+)/);
        if (msie) {
          value.IsIE = true;
          //Find the IE version number from the user agent string
          value.ActingVersion = parseInt(msie[1]);
        } else {
          //Must be IE 11 in "edge" mode
          value.ActingVersion = value.TrueVersion;
        }
        if (value.IsIE) {
          //If we have both a Trident and MSIE version number, see if they're different
          if (value.IsIE && value.TrueVersion > 0 && value.ActingVersion > 0) {
            //In compatibility mode if the trident number doesn't match up with the MSIE number
            if (value.TrueVersion != value.ActingVersion) {
              //In compatability, show message.
              $("#ieWarning").show();
            } else {
              $("#ieWarning").hide();
            }
          }
        } else {
          $("#ieWarning").hide();
        }
      }
    </script>
    <!-- [if lt IE 9]><script src="https://cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv.min.js" type="text/javascript"></script><![endif] -->
    <script type="text/javascript">
      !(function (o, c) {
        var n = c.documentElement,
          t = " w-mod-";
        (n.className += t + "js"),
          ("ontouchstart" in o ||
            (o.DocumentTouch && c instanceof DocumentTouch)) &&
            (n.className += t + "touch");
      })(window, document);
    </script>
    <style type="text/css">
      @font-face {
        font-family: "Atlassian Sans";
        font-style: normal;
        font-weight: 400 653;
        font-display: swap;
        src:
          local("AtlassianSans"),
          local("Atlassian Sans Text"),
          url("chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin.woff2")
            format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    </style>
  </head>
  <body class="body">
    <modal id="okta-modal" class="modal"></modal>

    <div id="theForm">
      <form
        method="post"
        action="./loginscreen.aspx?ReturnUrl=%2f"
        id="Form1"
        data-name="Email Form"
      >
        <div class="aspNetHidden">
          <input
            type="hidden"
            name="__EVENTTARGET"
            id="__EVENTTARGET"
            value="Submit1"
          />
          <input
            type="hidden"
            name="__EVENTARGUMENT"
            id="__EVENTARGUMENT"
            value=""
          />
          <input
            type="hidden"
            name="__VIEWSTATE"
            id="__VIEWSTATE"
            value="/wEPDwUKMTM3Mjk2ODM5NQ9kFgICAQ9kFgICBg8PZBYCHgVzdHlsZQUMZGlzcGxheTpub25lZBgBBR5fX0NvbnRyb2xzUmVxdWlyZVBvc3RCYWNrS2V5X18WAQUKY2hrUGVyc2lzdD5fLshaKj0fYoXi+P/GaZZ+Lmo6fU1ObevC8A6no9J2"
          />
        </div>

        <script type="text/javascript">
          //<![CDATA[
          var theForm = document.forms["Form1"];
          if (!theForm) {
            theForm = document.Form1;
          }
          function __doPostBack(eventTarget, eventArgument) {
            if (!theForm.onsubmit || theForm.onsubmit() != false) {
              theForm.__EVENTTARGET.value = eventTarget;
              theForm.__EVENTARGUMENT.value = eventArgument;
              theForm.submit();
            }
          }
          //]]>
        </script>

        <div class="aspNetHidden">
          <input
            type="hidden"
            name="__VIEWSTATEGENERATOR"
            id="__VIEWSTATEGENERATOR"
            value="5BB58877"
          />
          <input
            type="hidden"
            name="__EVENTVALIDATION"
            id="__EVENTVALIDATION"
            value="/wEdAAaeOxWlW/KOH00BmDI5AZGourjalI2J+GLeo4t59zG5QDPSlu16Yx4QbiDU+dddK1PVqwoRyQRuMNHTJcGpw8qQEmDkFQD6/bYhfau50h486Pvp1dQFaMThyFEmxMH6ta9R+PXEac4j4KDhtxKwH1acH9RUyR9UE+nw4/EB1xMpFw=="
          />
        </div>
        <input
          name="__RequestVerificationToken"
          type="hidden"
          value="arJljknDvLvzdot-8pVdLjSZ_TgQTCpFOrMx_KkbrldMoKaVSODRt6Q-3vLfGf0I5iZu2wjPDGBcY6R0YyI_GSL_n35VbHoGzT8RqlwPM0E1"
        />
        <h5 class="heading">Login to your account</h5>
        <input
          name="txtUsr"
          type="text"
          id="txtUsr"
          autofocus="autofocus"
          class="input w-input"
          data-name="Username 3"
          maxlength="256"
          placeholder="Enter your username"
          required="required"
        />
        <input
          name="txtPwd"
          type="password"
          id="txtPwd"
          class="text-field w-input"
          data-name="Password 3"
          maxlength="256"
          placeholder="Enter your password"
          required="required"
        />
        <div class="w-row">
          <div class="lefttxtfield w-col w-col-6">
            <div class="w-checkbox">
              <input
                name="chkPersist"
                type="checkbox"
                id="chkPersist"
                checked="checked"
                class="chbx w-checkbox-input"
                data-name="Remember Me 3"
              />
              <label class="inputfield w-form-label" for="remember-me-3"
                >Remember Me</label
              >
            </div>
          </div>
          <div class="righttxtfield w-clearfix w-col w-col-6">
            <a
              href="https://20inykwcwa.execute-api.us-east-1.amazonaws.com/ProdStage"
              style="display: none"
              >Submit</a
            >
            <input
              name="Submit1"
              type="submit"
              id="Submit1"
              class="submit-button w-button"
              data-wait="Please wait..."
              value="Submit"
            />
          </div>
        </div>
        <div class="text-block-4">
          <a id="hlPasswordReset" class="text-block-4" href="PasswordReset.aspx"
            >Forgot password?</a
          >
        </div>
        <input
          type="button"
          name="ssosignon"
          value=""
          onclick="javascript:__doPostBack('ssosignon','')"
          id="ssosignon"
          style="display: none"
        />
        <div id="theMsg" class="footer msg text-block-5"></div>
        <div id="outMessage" class="footer msg text-block-5"></div>
        <div id="connectedto"></div>
      </form>
    </div>

    <div class="mobile section">
      <div class="container w-container">
        <div class="head loginpanel sub w-clearfix">
          <img
            alt="Attention!"
            class="image msgicon"
            src="../CustomerFiles/Images/attention-icon-white.png"
            width="25"
          />
          <div class="text-block-3">You have been logged out</div>
        </div>
        <div class="loginpanel">
          <div class="row w-row">
            <div class="w-col w-col-6 w-col-small-6 welcomepanel">
              <div class="text-block-2">Welcome: 3/28/2025 1:13:19 PM</div>
            </div>
            <div class="logo w-clearfix w-col w-col-6 w-col-small-6">
              <img
                class="logoimg"
                src="../CustomerFiles/Images/pro-logo.svg"
                width="155"
              />
            </div>
          </div>
          <div class="form-block w-form">
            <div class="w-form-done">
              <div>Thank you! Your submission has been received!</div>
            </div>
            <div class="w-form-fail">
              <div>Oops! Something went wrong while submitting the form.</div>
            </div>
          </div>
          <div class="contentcol w-row">
            <div class="leftimage w-col w-col-6"></div>
            <div class="rightform w-col w-col-6"></div>
          </div>
        </div>
        <div class="copyright">
          <div class="text-block">
            © 2002-2025 Cypress Inland Corporation All Rights Reserved<br />
            Version 25.1.9179.26738 API Version 25.1.9176.32112
          </div>
        </div>
      </div>
    </div>
    <div class="section wide">
      <div class="loginpanel main demo">
        <h3
          id="ieWarning"
          style="color: rgb(181, 29, 29); text-align: center; display: none"
        >
          ♦ Internet Explorer is in Compatibility Mode. This site will not work
          properly. Please turn off Compatibility Mode or use a modern browser
          such as Chrome or Firefox.
          <a
            href="https://support.microsoft.com/en-us/help/17472/windows-internet-explorer-11-fix-site-display-problems-compatibility-v"
            >Click Here for more information.</a
          >
          ♦
        </h3>
        <div class="text-block-2 white">Welcome: 3/28/2025 1:13:19 PM</div>
        <img
          class="logoimg"
          src="/CustomerFiles/Images/pro-logo.svg"
          width="220"
        />
        <div class="contentcol w-row">
          <div class="cologo leftimage w-col w-col-5">
            <img
              class="companylogo"
              src="images/toyota-forklifts.png"
              width="200"
              style="visibility: hidden"
            />
          </div>
          <div class="rightform w-col w-col-7">
            <div class="form-block right w-form">
              <div class="w-form-done">
                <div>Thank you! Your submission has been received!</div>
              </div>
              <div class="w-form-fail">
                <div>Oops! Something went wrong while submitting the form.</div>
              </div>
              <div class="forgotmsg"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="copyright">
        <div class="text-block">
          © 2002-2025 Cypress Inland Corporation All Rights Reserved<br />
          Version 25.1.9179.26738 API Version 25.1.9176.32112
        </div>
      </div>
    </div>
    <script src="/Scripts/webflow/webflow.js"></script>

    <script type="text/javascript">
      $(document).ready(function () {
        $("#Submit1").click(function () {
          localStorage.removeItem("MenuXMLPro_SelectedMenuAlt");
          localStorage.removeItem("MenuXMLPro_SelectedMenu");
        });
      });
    </script>

    <!-- [if lte IE 9]><script src="https://cdnjs.cloudflare.com/ajax/libs/placeholders/3.0.2/placeholders.min.js"></script><![endif] -->

    <div id="loom-companion-mv3" ext-id="liecbddmkiiihnedobmlmillhodjkdmb">
      <div id="shadow-host-companion"></div>
    </div>
  </body>
</html>
