"""YardView Selenium scheduling implementation."""

import time
from datetime import datetime, timedelta
from typing import Optional, Type, Tuple
import functools
import re
from urllib.parse import urlparse

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from bs4 import BeautifulSoup

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

from models.base import BaseResponse
from integrations.scheduling.models import (
    Credentials,
    Appointment,
    LoginResponse,
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    CancelAppointmentResponse,
    GetAppointmentResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
    AppointmentData,
)
from integrations.scheduling.yardview.models import (
    YardViewLoginRequest,
    YardViewGetWarehouseRequest,
    YardViewGetOpenSlotsRequest,
    YardViewGetLoadTypesRequest,
    YardViewCancelAppointmentRequest,
    YardViewGetAppointmentRequest,
    YardViewMakeAppointmentRequest,
    YardViewUpdateAppointmentRequest,
)


# YardView element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "txtUsr"),
    "password_field": (By.ID, "txtPwd"),
    "login_button": (By.ID, "Submit1"),
    "login_error": (By.ID, "theMsg"),
    "remember_me": (By.ID, "chkPersist"),
    "logged_in_indicator": (By.ID, "main"),
}


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting YardView login process")

        wait_for_element(driver, LOCATORS["username_field"])
        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        remember_me = driver.find_element(*LOCATORS["remember_me"])
        if not remember_me.is_selected():
            safe_click(driver, LOCATORS["remember_me"])

        safe_click(driver, LOCATORS["login_button"])

        wait_for_page_load(driver)

        logger.debug(f"Current YardView URL: {driver.current_url}")
        logger.debug(f"YardView Page Source:\n{driver.page_source}\n")

        try:
            if is_element_present(driver, LOCATORS["login_error"], timeout=2):
                error_msg = get_element_text(driver, LOCATORS["login_error"])
                logger.error(f"YardView Login failed: {error_msg}")
                return False, error_msg
        except Exception:
            # Silently continue if no error message found
            pass

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"YardView Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("yardview", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            current_time = int(time.time())
            valid_cookies = [
                cookie
                for cookie in cookies
                if "expiry" in cookie and cookie["expiry"] > current_time
            ]

            if not valid_cookies or len(valid_cookies) == 0:
                logger.info("Cached cookies expired or invalid")
            else:
                driver.get(PLATFORM_URLS["yardview"])

                add_cookies(driver, cookies)

                driver.get(PLATFORM_URLS["yardview"])

                return True, None
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(PLATFORM_URLS["yardview"])

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("yardview", credentials.username, new_cookies)
            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: YardViewLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to YardView.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: YardViewGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from YardView.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


def get_date_from_header(driver):
    """Extract date from the page header.

    Args:
        driver: WebDriver instance

    Returns:
        tuple: (datetime object, formatted date string)
    """
    date_div = driver.find_element(By.ID, "date-div")
    date_text = date_div.text.strip()

    # Extract just the date part (e.g., "Tuesday `April 8, 2025`")
    if " " in date_text:
        date_text = " ".join(date_text.split()[1:])

    header_date = datetime.strptime(date_text, "%B %d, %Y")
    date_str = header_date.strftime("%m/%d/%Y")

    return header_date, date_str


def process_time_cell(time_cell, header_date, date_str):
    """Process a single time cell and extract appointment information.

    Args:
        time_cell: WebElement representing the time cell
        header_date: datetime object of the current date
        date_str: Formatted date string

    Returns:
        Appointment object or None if no valid appointment
    """
    try:
        time_text = time_cell.text.strip()
        hour, minute = map(int, time_text.split(":"))

        time_dt = datetime(
            header_date.year, header_date.month, header_date.day, hour, minute
        )

        slot_datetime = time_dt.strftime("%Y-%m-%d %H:%M:%S")

        row = time_cell.find_element(By.XPATH, "./..")
        slot_cells = row.find_elements(By.CLASS_NAME, "appointment-slot")
        if not slot_cells:
            return None

        slot_cell = slot_cells[0]
        add_links = slot_cell.find_elements(By.CSS_SELECTOR, "a.add")

        if not add_links:
            return None

        try:
            booked_appointments = slot_cell.find_elements(
                By.CLASS_NAME, "appointment-slot-entry"
            )
            capacity = f"{4 - len(booked_appointments)}/4"
        except:
            capacity = "4/4"

        return Appointment(
            appointmentId="",
            reference="",
            scheduledTime=slot_datetime,
            duration=60,
            location="",
            status="Available",
            notes=f"Capacity: {capacity}",
        )
    except Exception as e:
        logger.warning(f"Error processing time cell: {str(e)}")
        return None


def extract_appointments_from_html(html, header_date):
    appointments = []

    soup = BeautifulSoup(html, "html.parser")
    rows = soup.select("tr")  # or use a more specific selector if needed

    for row in rows:
        time_cell = row.select_one(".display-time")
        slot_cell = row.select_one(".appointment-slot")

        if not time_cell or not slot_cell:
            continue

        time_text = time_cell.get_text(strip=True)
        if not time_text:
            continue

        try:
            hour, minute = map(int, time_text.split(":"))
            slot_datetime = datetime(
                header_date.year,
                header_date.month,
                header_date.day,
                hour,
                minute,
            ).strftime("%Y-%m-%d %H:%M:%S")
        except Exception:
            continue

        if not slot_cell.select_one("a.add"):
            continue

        booked_count = len(slot_cell.select(".appointment-slot-entry"))
        capacity = f"{4 - booked_count}/4"

        appointments.append(
            AppointmentData(
                appointmentId="",
                scheduledTime=slot_datetime,
                duration=60,
                status="Available",
                notes=f"Capacity: {capacity}",
            )
        )

    return appointments


def process_date_page(driver, appointment_locators, date_url):
    """Process a single date page and extract all appointments.

    Args:
        driver: WebDriver instance
        appointment_locators: Dictionary of locators
        date_url: URL for the specific date

    Returns:
        list: List of Appointment objects
    """
    logger.info(f"Navigating to date URL: {date_url}")
    driver.get(date_url)
    wait_for_page_load(driver)

    date_appointments = []

    try:
        header_date, date_str = get_date_from_header(driver)
        logger.info(f"Processing appointments for date: {date_str}")

        html = driver.page_source
        date_appointments = extract_appointments_from_html(html, header_date)

        logger.info(
            f"Found {len(date_appointments)} appointments for {date_str}"
        )
        return date_appointments
    except Exception as e:
        logger.error(f"Failed to process date page: {str(e)}")
        return []


@with_driver
@requires_login(GetOpenSlotsResponse)
def get_open_slots(
    request: YardViewGetOpenSlotsRequest, driver: WebDriver
) -> GetOpenSlotsResponse:
    """Get open appointment slots from YardView.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        # Define locators for appointment page elements
        appointment_locators = {
            "calendar_icon_div": (By.ID, "MainMenuPro"),
            "calendar_icon": (By.TAG_NAME, "a"),
            "receiving_calendars": (By.LINK_TEXT, "Receiving Calendar"),
            "current_date": (By.ID, "current_date"),
            "day_forward": (By.ID, "day-forward"),
            "day_back": (By.ID, "day-back"),
            "time_cells": (By.CLASS_NAME, "display-time"),
            "slot_cells": (By.CLASS_NAME, "appointment-slot"),
        }

        start_date = getattr(request, "startDate", None)
        end_date = getattr(request, "endDate", None)

        # Validate required date parameters
        if not start_date or not end_date:
            missing = []
            if not start_date:
                missing.append("startDate")
            if not end_date:
                missing.append("endDate")
            error_msg = f"Missing required parameters: {', '.join(missing)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        all_appointments = []

        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        appointment_list_endpoint = "AppointmentPages/ManageAppointments.aspx?xmlfile=appointments/CalendarConfigInbound.xml"

        parsed_url = urlparse(driver.current_url)
        full_url = f"{parsed_url.scheme}://{parsed_url.netloc}/"

        base_url = full_url + appointment_list_endpoint

        if "SelectedDate=" in base_url:
            base_url = re.sub(r"&SelectedDate=[^&]*", "", base_url)

        current_dt = start_dt
        while current_dt <= end_dt:
            formatted_date = (
                f"{current_dt.month}/{current_dt.day}/{current_dt.year}"
            )
            date_url = f"{base_url}&SelectedDate={formatted_date}"

            date_appointments = process_date_page(
                driver, appointment_locators, date_url
            )
            all_appointments.extend(date_appointments)

            current_dt += timedelta(days=1)

        # Sort appointments by scheduledTime
        appointments = sorted(
            all_appointments,
            key=lambda x: datetime.strptime(
                x.scheduledTime, "%Y-%m-%d %H:%M:%S"
            ),
        )

        return GetOpenSlotsResponse(
            success=True,
            message="Successfully retrieved open slots",
            appointments=appointments,
        )
    except Exception as e:
        logger.error(f"Failed to fetch open slots: {str(e)}")
        return GetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: YardViewGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from YardView.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


def select_dropdown_option(driver, selector, option_text):
    """Select an option from a dropdown.

    Args:
        driver: WebDriver instance
        selector: Locator for the dropdown element
        option_text: Text of the option to select
    """

    try:
        dropdown = wait_for_element(driver, selector, timeout=1)

        select = Select(dropdown)
        select.select_by_value(option_text)

        logger.info(f"Selected value '{option_text}' for {option_text}")
    except Exception as dropdown_error:
        try:
            select.select_by_visible_text(option_text)

            logger.info(f"Selected text '{option_text}' for {option_text}")
        except Exception as text_error:
            logger.warning(
                f"Failed to select '{option_text}' for {option_text} by value or text: {str(text_error)}"
            )


def apply_appointment_filters(driver, filters):
    """Apply filters to the appointment list.

    Args:
        driver: WebDriver instance
        filters: Dictionary containing filter values
            - startDate: Start date in YYYY-MM-DD format
            - endDate: End date in YYYY-MM-DD format
            - schedule: Schedule filter value
            - scac: SCAC filter value
            - status: Appointment status filter value

    Returns:
        bool: True if filters were applied successfully

    Raises:
        ValueError: If only one date filter is provided without the other
    """
    logger.info("Applying appointment filters")

    start_date = filters.get("startDate")
    end_date = filters.get("endDate")

    if (start_date and not end_date) or (end_date and not start_date):
        error_msg = "Both startDate and endDate must be provided together"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if start_date and end_date:
        start_date_input = wait_for_element(
            driver, (By.ID, "filter-0-StartDate"), timeout=1
        )
        end_date_input = wait_for_element(
            driver, (By.ID, "filter-1-EndDate"), timeout=1
        )

        formatted_date = datetime.strptime(start_date, "%Y-%m-%d").strftime(
            "%Y-%m-%d"
        )
        driver.execute_script(
            "arguments[0].value = arguments[1]",
            start_date_input,
            formatted_date,
        )

        formatted_end_date = datetime.strptime(end_date, "%Y-%m-%d").strftime(
            "%Y-%m-%d"
        )
        driver.execute_script(
            "arguments[0].value = arguments[1]",
            end_date_input,
            formatted_end_date,
        )

        logger.info(f"Set date range: {start_date} to {end_date}")

    if filters.get("schedule"):
        select_dropdown_option(
            driver, (By.ID, "filter-2-Schedule"), filters["schedule"]
        )

    if filters.get("scac"):
        select_dropdown_option(
            driver, (By.ID, "filter-3-SCAC"), filters["scac"]
        )

    if filters.get("status"):
        select_dropdown_option(
            driver, (By.ID, "filter-4-Status"), filters["status"]
        )

    if any(filters.values()):
        accordion_icon = wait_for_element(
            driver, (By.ID, "filter-accordion-icon"), timeout=1
        )
        safe_click(driver, accordion_icon)

        filter_button = wait_for_element(
            driver, (By.ID, "filter-button"), timeout=1
        )
        safe_click(driver, filter_button)

        # Wait for filter to apply and table to load
        wait_for_page_load(driver, timeout=10)

        # Wait for filter to apply and table to load
        time.sleep(1)

        logger.info("Filters applied successfully")
        return True

    return False


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: YardViewCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in YardView.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        CANCELATION_STATUS = "Canceled"

        appointment_id = request.appointmentId
        reason = request.reason

        if not appointment_id:
            error_msg = "Missing required parameter: appointmentId"
            logger.error(error_msg)
            return CancelAppointmentResponse(
                success=False,
                message="Missing appointment ID",
                errors=[error_msg],
            )

        # Navigate to appointment management page
        appointment_list_url = (
            f"{PLATFORM_URLS['yardview']}/AppointmentPages/ManageAppointments.aspx"
            f"?LandingUrl=%27%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentReports%2fappointmentmanage.xml%27"
        )

        logger.info(
            f"Navigating to appointment list page: {appointment_list_url}"
        )
        driver.get(appointment_list_url)
        wait_for_page_load(driver, timeout=10)

        # Switch to the iframe containing the appointment data
        iframe = wait_for_element(
            driver,
            (By.XPATH, "//*[@id='appointment_form']/iframe"),
            timeout=10,
        )
        driver.switch_to.frame(iframe)
        logger.info("Switched to appointment iframe")

        # Prepare filters dictionary
        filters = {
            "startDate": getattr(request, "startDate", None),
            "endDate": getattr(request, "endDate", None),
            "schedule": getattr(request, "schedule", None),
            "scac": getattr(request, "scac", None),
            "status": getattr(request, "status", None),
        }

        # Apply filters
        apply_appointment_filters(driver, filters)

        # Find the appointment in the table
        logger.info(f"Searching for appointment ID: {appointment_id}")

        # Wait for the table to be visible
        wait_for_element(
            driver,
            (By.CSS_SELECTOR, "div.ag-center-cols-container"),
            timeout=1,
        )

        # Find all rows in the table - AG Grid uses div.ag-row elements
        rows = driver.find_elements(By.CSS_SELECTOR, "div.ag-row")

        appointment_found = False
        edit_link = None

        for row in rows:
            cells = row.find_elements(By.CSS_SELECTOR, "div.ag-cell")
            if len(cells) < 2:
                continue

            # The appointment ID is in the second column (index 1)
            appt_id_cell = cells[2]
            status_cell = cells[5]

            # Need to find the actual text within the nested span elements
            appt_id_text = appt_id_cell.find_element(
                By.CSS_SELECTOR, "span.ag-cell-value span"
            ).text.strip()
            status_text = status_cell.find_element(
                By.CSS_SELECTOR, "div span"
            ).text.strip()

            if appointment_id == appt_id_text:
                logger.info(f"Found appointment ID: {appointment_id}")
                appointment_found = True

                try:
                    edit_link = cells[0].find_element(
                        By.CSS_SELECTOR, "a.report-action-link"
                    )
                except Exception:
                    if status_text == "Canceled":
                        error_msg = f"Appointment ID {appointment_id} is already canceled"
                        logger.error(error_msg)
                        return CancelAppointmentResponse(
                            success=False,
                            message="Appointment already canceled",
                            errors=[error_msg],
                        )
                    else:
                        error_msg = f"Failed to find edit link for appointment ID {appointment_id}"
                        logger.error(error_msg)
                        return CancelAppointmentResponse(
                            success=False,
                            message="Failed to find edit link",
                            errors=[error_msg],
                        )

                break

        if not appointment_found or not edit_link:
            error_msg = (
                f"Appointment ID {appointment_id} not found in the list"
            )
            logger.error(error_msg)
            return CancelAppointmentResponse(
                success=False,
                message="Appointment not found",
                errors=[error_msg],
            )

        original_window = driver.current_window_handle
        edit_link.click()

        for window_handle in driver.window_handles:
            if window_handle != original_window:
                driver.switch_to.window(window_handle)
                break

        wait_for_page_load(driver)

        status_text = wait_for_element(driver, (By.ID, "TextStatus"))
        status_text.clear()
        status_text.send_keys(CANCELATION_STATUS)

        if reason:
            comment_text = wait_for_element(driver, (By.ID, "TextComments"))
            comment_text.clear()
            comment_text.send_keys(reason)

        save_button = wait_for_element(driver, (By.ID, "BtnSaveRecord"))
        safe_click(driver, save_button)

        # Wait for cancel appointment API to be successfully executed.
        time.sleep(1)

        logger.info(f"Successfully canceled appointment ID: {appointment_id}")

        return CancelAppointmentResponse(
            success=True,
            message=f"Successfully canceled appointment ID: {appointment_id}",
        )

    except Exception as e:
        logger.error(f"Failed to cancel appointment: {str(e)}")
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetAppointmentResponse)
def get_appointment(
    request: YardViewGetAppointmentRequest, driver: WebDriver
) -> GetAppointmentResponse:
    """Get an appointment from YardView.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        appointment_id = request.appointmentId

        if not appointment_id:
            error_msg = "Missing required parameter: appointmentId"
            logger.error(error_msg)
            return GetAppointmentResponse(
                success=False,
                message="Missing appointment ID",
                errors=[error_msg],
            )

        # Navigate to appointment management page
        appointment_list_url = (
            f"{PLATFORM_URLS['yardview']}/AppointmentPages/ManageAppointments.aspx"
            f"?LandingUrl=%27%2fReportPages%2fReportsXML.aspx%3fxmlfile%3dappointmentReports%2fappointmentmanage.xml%27"
        )

        logger.info(
            f"Navigating to appointment list page: {appointment_list_url}"
        )
        driver.get(appointment_list_url)
        wait_for_page_load(driver, timeout=10)

        # Switch to the iframe containing the appointment data
        iframe = wait_for_element(
            driver,
            (By.XPATH, "//*[@id='appointment_form']/iframe"),
            timeout=10,
        )
        driver.switch_to.frame(iframe)
        logger.info("Switched to appointment iframe")

        # Prepare filters dictionary
        filters = {
            "startDate": getattr(request, "startDate", None),
            "endDate": getattr(request, "endDate", None),
            # 'schedule': getattr(request, 'schedule', None),
            # 'scac': getattr(request.appointment, 'carrierId', None),
            # 'status': getattr(request.appointment, 'status', None)
        }

        # Apply filters
        apply_appointment_filters(driver, filters)

        rows = driver.find_elements(By.CSS_SELECTOR, "div.ag-row")
        logger.info(f"Found {len(rows)} rows in the appointment table")

        appointment_found = False
        appointment_details = {}

        # Actual table structure:
        # Edit, Appt ID#, Date, Time, Status, Schedule, Pro #, SCAC, Trailer #, LoadType, Comments
        for row in rows:
            cells = row.find_elements(By.CSS_SELECTOR, "div.ag-cell")
            if len(cells) < 5:  # Ensure we have enough cells
                continue

            appt_id_cell = cells[2]

            try:
                appt_id_text = appt_id_cell.find_element(
                    By.CSS_SELECTOR, "span.ag-cell-value span"
                ).text.strip()
            except:
                try:
                    appt_id_text = appt_id_cell.find_element(
                        By.CSS_SELECTOR, "span.ag-cell-value"
                    ).text.strip()
                except:
                    appt_id_text = appt_id_cell.text.strip()

            if appointment_id == appt_id_text:
                logger.info(f"Found appointment ID: {appointment_id}")
                appointment_found = True

                try:
                    field_mapping = {
                        3: "appointmentDate",  # Date (column 3)
                        4: "appointmentTime",  # Time (column 4)
                        5: "status",  # Status (column 5)
                        6: "schedule",  # Schedule (column 6)
                        7: "loadId",  # Pro # (column 7)
                        8: "carrierId",  # SCAC (column 8)
                        9: "dock",  # Trailer # (column 9)
                        10: "loadType",  # LoadType (column 10)
                        11: "notes",  # Comments (column 11)
                    }

                    # Extract each field
                    for idx, field in field_mapping.items():
                        if idx < len(cells):
                            try:
                                try:
                                    value = (
                                        cells[idx]
                                        .find_element(
                                            By.CSS_SELECTOR, "div span"
                                        )
                                        .text.strip()
                                    )
                                except:
                                    try:
                                        value = (
                                            cells[idx]
                                            .find_element(
                                                By.CSS_SELECTOR,
                                                "span.ag-cell-value",
                                            )
                                            .text.strip()
                                        )
                                    except:
                                        value = cells[idx].text.strip()

                                appointment_details[field] = value
                                logger.info(f"Found {field}: {value}")
                            except Exception as e:
                                logger.warning(
                                    f"Failed to extract {field}: {str(e)}"
                                )
                                appointment_details[field] = None

                    appointment_details["appointmentId"] = appointment_id

                    if appointment_details.get(
                        "appointmentDate"
                    ) and appointment_details.get("appointmentTime"):
                        try:
                            date_str = appointment_details["appointmentDate"]
                            time_str = appointment_details["appointmentTime"]

                            # Handle abbreviated year format (e.g., '04/09/25')
                            if len(date_str) == 8 and date_str.count("/") == 2:
                                month, day, short_year = date_str.split("/")
                                # Assume '25' means '2025'
                                full_year = (
                                    f"20{short_year}"
                                    if int(short_year) < 50
                                    else f"19{short_year}"
                                )
                                date_obj = datetime(
                                    int(full_year), int(month), int(day)
                                )
                            else:
                                # Try the original format
                                date_obj = datetime.strptime(
                                    date_str, "%m/%d/%Y"
                                )

                            time_parts = time_str.split(":")
                            hour = int(time_parts[0])
                            minute = int(
                                time_parts[1].split()[0]
                            )  # Handle "10:30 AM" format

                            scheduled_datetime = datetime(
                                date_obj.year,
                                date_obj.month,
                                date_obj.day,
                                hour,
                                minute,
                            )
                            appointment_details["scheduledTime"] = (
                                scheduled_datetime.strftime(
                                    "%Y-%m-%dT%H:%M:%S"
                                )
                            )
                        except Exception as e:
                            logger.warning(
                                f"Failed to parse date/time: {str(e)}"
                            )
                            appointment_details["scheduledTime"] = None

                    break

                except Exception as e:
                    logger.error(
                        f"Error extracting appointment details: {str(e)}"
                    )

        if not appointment_found:
            error_msg = (
                f"Appointment ID {appointment_id} not found in the list"
            )
            logger.error(error_msg)
            return GetAppointmentResponse(
                success=False,
                message="Appointment not found",
                errors=[error_msg],
            )

        # Create and return the appointment response
        appointment = Appointment(
            appointmentId=appointment_details.get("appointmentId"),
            scheduledTime=appointment_details.get("scheduledTime"),
            status=appointment_details.get("status"),
            duration=60,
            notes=appointment_details.get("notes"),
            reference=appointment_details.get("schedule", ""),
        )

        return GetAppointmentResponse(
            success=True,
            message=f"Successfully retrieved appointment ID: {appointment_id}",
            appointments=[appointment],
        )
    except Exception as e:
        return GetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(MakeAppointmentResponse)
def make_appointment(
    request: YardViewMakeAppointmentRequest, driver: WebDriver
) -> MakeAppointmentResponse:
    """Create an appointment in YardView.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        appointment_data = request.appointment

        appointment_datetime = datetime.strptime(
            appointment_data.appointmentTime, "%Y-%m-%dT%H:%M:%S"
        )

        current_datetime = datetime.now()
        if appointment_datetime < current_datetime:
            error_msg = f"Cannot create appointment in the past. Requested time: {appointment_datetime}, Current time: {current_datetime}"
            logger.error(error_msg)
            return MakeAppointmentResponse(
                success=False,
                message="Invalid appointment time",
                errors=[error_msg],
            )

        url_date = appointment_datetime.strftime("%-m/%-d/%Y")
        row_time = appointment_datetime.strftime(
            "%H:00"
        )  # Only using hour for it

        appointment_url = f"{PLATFORM_URLS['yardview']}/AppointmentPages/ManageAppointments.aspx?xmlfile=appointments/CalendarConfigInbound.xml&SelectedSchedule=&SelectedDate={url_date}"
        driver.get(appointment_url)
        wait_for_page_load(driver)

        slot_cell_locator = (
            By.XPATH,
            f"//td[@class='display-time' and contains(text(), '{row_time}')]/following-sibling::td[1]",
        )
        slot_cell = wait_for_element(driver, slot_cell_locator)

        location_pid = slot_cell.get_attribute("locationpid")
        logger.info(f"Found location PID: {location_pid}")

        add_button_locator = (
            By.XPATH,
            f"//td[@class='display-time' and contains(text(), '{row_time}')]/following-sibling::td[1]//a[contains(@class, 'add')]",
        )
        add_button = wait_for_element(driver, add_button_locator, timeout=4)

        if not add_button:
            error_msg = f"No open slots found for the specified time: {appointment_datetime}."
            logger.error(error_msg)
            return MakeAppointmentResponse(
                success=False,
                message=error_msg,
                errors=["Failed to find add button."],
            )

        schedule_detail_pid = add_button.get_attribute("scheduledetailpid")
        logger.info(f"Found schedule detail PID: {schedule_detail_pid}")

        create_appointment_url = (
            f"{PLATFORM_URLS['yardview']}/AppointmentPages/Appointments.aspx"
            f"?PageMode=Create&Date={url_date}"
            f"&LocationPID={location_pid}&ScheduleDetailPID={schedule_detail_pid}"
        )

        logger.info(
            f"Navigating to appointment creation page: {create_appointment_url}"
        )
        driver.get(create_appointment_url)
        wait_for_page_load(driver)

        logger.warning(f"Current URL after navigation: {driver.current_url}")

        # Fill in form fields based on their type
        script = """
        document.getElementById('TextfkLoadPID').value = arguments[0];
        document.getElementById('Text10').value = arguments[1];
        document.getElementById('Text2').value = arguments[2];
        document.getElementById('ListCarrier').value = arguments[3];
        document.getElementById('TextTrailerID').value = arguments[4];
        document.getElementById('ListLoadType').value = arguments[5];
        document.getElementById('TextStatus').value = arguments[6];
        document.getElementById('TextComments').value = arguments[7];
        """
        driver.execute_script(
            script,
            appointment_data.appointmentId,
            appointment_data.appointmentKey,
            appointment_data.loadId,
            appointment_data.carrierId,
            appointment_data.dock,
            appointment_data.loadType,
            appointment_data.status,
            appointment_data.notes,
        )

        save_button_locator = (By.ID, "BtnSaveRecord")
        save_button = wait_for_element(driver, save_button_locator)
        logger.info("Clicking Save Record button")
        safe_click(driver, save_button)

        wait_for_page_load(driver)

        # Wait for make appointment API to be successfully executed.
        time.sleep(1)

        logger.info("Appointment created successfully")

        return MakeAppointmentResponse(
            success=True,
            message="Appointment created successfully",
            appointment=Appointment(
                appointmentId=appointment_data.appointmentId,
                scheduledTime=str(appointment_datetime),
                duration=60,
                status=appointment_data.status,
                notes=appointment_data.notes,
            ),
        )
    except Exception as e:
        return MakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(UpdateAppointmentResponse)
def update_appointment(
    request: YardViewUpdateAppointmentRequest, driver: WebDriver
) -> UpdateAppointmentResponse:
    """Update an appointment in YardView.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        return UpdateAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment updating not implemented"],
        )
    except Exception as e:
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )
