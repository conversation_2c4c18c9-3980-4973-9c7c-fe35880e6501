import os
import re
from datetime import timedelta, datetime

from fastapi.testclient import TestClient
from app import app

client = TestClient(app)

YARDVIEW_CREDENTIALS = {
    "username": os.environ.get("YARDVIEW_USERNAME", ""),
    "password": os.environ.get("YARDVIEW_PASSWORD", ""),
}


def test_get_open_slots():
    payload = {
        "integration": "scheduling",
        "mode": "api",
        "platform": "YardView",
        "action": "GetOpenSlots",
        "userId": "789",
        "weekDate": f"{(datetime.now() + timedelta(days=1)).date()}",
        "filterType": "TARGET_AIR_FREIGHT_ONLY",  # value = 1
        "credentials": {
            "username": YARDVIEW_CREDENTIALS["username"],
            "password": YARDVIEW_CREDENTIALS["password"],
        },
    }

    response = client.post("/", json=payload)

    assert response.status_code == 200, "Should return 200 OK"

    data = response.json()

    assert data["success"] is True, "Expected success == True"
    assert isinstance(
        data.get("appointments"), list
    ), "Expected list of appointments"
    assert len(data["appointments"]) > 0, "Appointments should not be empty"

    # ISO datetime check
    iso8601_regex = r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}"
    for appt in data["appointments"]:
        assert re.match(
            iso8601_regex, appt["scheduledTime"]
        ), f"Invalid datetime format: {appt['scheduledTime']}"
        assert (
            appt["status"] == "AVAILABLE"
        ), "Expected appointment to be AVAILABLE"


def test_get_appointment():
    appointment_id = os.getenv("APPT_ID", None)

    payload = {
        "integration": "scheduling",
        "mode": "api",
        "platform": "YardView",
        "action": "GetAppointment",
        "userId": "789",
        "appointmentId": appointment_id,
        "credentials": {
            "username": YARDVIEW_CREDENTIALS["username"],
            "password": YARDVIEW_CREDENTIALS["password"],
        },
    }

    response = client.post("/", json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert isinstance(data.get("appointments"), list)
    assert len(data["appointments"]) == 1
