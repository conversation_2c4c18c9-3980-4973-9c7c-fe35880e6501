"""YardView API scheduling implementation."""

from datetime import datetime
from typing import List, Optional
import requests

from fastapi import status
from pydantic import BaseModel, ValidationError

from models.base import BaseResponse
from utils.logging import logger

from integrations.scheduling.models import (
    Appointment,
    Credentials,
    CancelAppointmentResponse,
    GetAppointmentResponse,
    UpdateAppointmentResponse,
    WarehouseDetails,
)

from integrations.scheduling.yardview.models import (
    AppointmentParams,
    AppointmentPto,
    Endpoints,
    LoadTypesPto,
    LocationPto,
    ScheduleDetailPto,
    ScheduleDetailsParams,
    ScacsPto,
    YardViewCancelAppointmentRequest,
    YardViewGetAppointmentRequest,
    YardViewGetCarrierScacsRequest,
    YardViewGetCarrierScacsResponse,
    YardViewGetLoadTypesRequest,
    YardViewGetLoadTypesResponse,
    YardViewGetOpenSlotsRequest,
    YardViewGetOpenSlotsResponse,
    YardViewGetWarehouseRequest,
    YardViewGetWarehouseResponse,
    YardViewMakeAppointmentRequest,
    YardViewMakeAppointmentResponse,
    YardViewUpdateAppointmentRequest,
    YardViewWarehouse,
    YardViewAppointment,
)


def _make_api_request(
    source: str,
    method: str,
    endpoint: str,
    credentials: Credentials,
    json: Optional[BaseModel] = None,
    params: Optional[BaseModel] = None,
) -> BaseResponse:
    """Make an API request with Basic Authentication.

    Args:
        method: HTTP method (e.g., "GET", "POST", "PUT").
        endpoint: API endpoint path (e.g., Endpoints.LOCATIONS).
        credentials: User credentials for Basic Auth.
        json: JSON payload for POST/PUT requests (default: None).
        params: Query parameters for GET requests (default: None).

    Returns:
        BaseResponse: Response with success status, message, and optional data or errors.
    """
    if not credentials.username or not credentials.password:
        error_msg = (
            "Username is empty"
            if not credentials.username
            else "Password is empty"
        )
        logger.error(error_msg)
        return BaseResponse(
            success=False,
            message=error_msg,
            errors=[error_msg],
        )

    auth = (credentials.username, credentials.password)
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/plain",
    }

    try:
        response = requests.request(
            method=method,
            url=f"{source.value}{endpoint}",
            auth=auth,
            headers=headers,
            json=json.dict(exclude_unset=True) if json else None,
            params=params.dict(exclude_unset=True) if params else None,
        )
        response.raise_for_status()
        content_type = response.headers.get("Content-Type", "").lower()

        if "text/plain" in content_type:
            data = [{"text": response.text}]
        else:
            json_data = response.json() if response.text else {}
            data = [json_data] if isinstance(json_data, dict) else json_data

        return BaseResponse(
            success=True,
            message="Request successful",
            platformData=data,
        )
    except requests.exceptions.HTTPError as e:
        default_error_msg = str(e)
        response_body = response.text if response.text else "No response body"

        if response.status_code == status.HTTP_400_BAD_REQUEST:
            error_msg = "Bad Request"
            logger.error(
                f"HTTP {status.HTTP_400_BAD_REQUEST}: {default_error_msg}, Response Body: {response_body}"
            )
            return BaseResponse(
                success=False,
                message=error_msg,
                errors=[response_body],
            )
        elif response.status_code == status.HTTP_401_UNAUTHORIZED:
            error_msg = "Authentication failed"
            logger.error(
                f"HTTP {status.HTTP_401_UNAUTHORIZED}: {default_error_msg}"
            )
            return BaseResponse(
                success=False,
                message=error_msg,
                errors=[error_msg],
            )
        elif response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
            error_msg = "Internal Server Error"
            logger.error(
                f"HTTP {status.HTTP_500_INTERNAL_SERVER_ERROR}: {default_error_msg}, Response Body: {response_body}"
            )
            return BaseResponse(
                success=False,
                message=error_msg,
                errors=[response_body],
            )
        return BaseResponse(
            success=False,
            message=f"Request failed: {default_error_msg}",
            errors=[response_body],
        )
    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        logger.error(f"Request exception: {error_msg}")
        return BaseResponse(
            success=False,
            message="Request failed",
            errors=[error_msg],
        )


def _convert_to_appointment(appt: AppointmentPto) -> Appointment:
    """Convert AppointmentPto to Appointment model.

    Args:
        appt: AppointmentPto object from API response.

    Returns:
        Appointment: Converted Appointment model.
    """
    try:
        start_time = datetime.fromisoformat(appt.startTime)
        end_time = datetime.fromisoformat(appt.endTime)
        duration_minutes = max(0, (end_time - start_time).seconds // 60)

    except ValueError as e:
        logger.error(f"Failed to parse appointment times: {str(e)}")
        duration_minutes = 0

    return Appointment(
        appointmentId=str(appt.appointmentID),
        scheduledTime=appt.startTime,
        duration=duration_minutes,
        status=appt.status.name,
        notes=appt.notes,
        reference=appt.appointmentKey,
        location=appt.trailerID,
        warehouse=WarehouseDetails(
            name="-",
            stopType=appt.appointmentType.name,
        ),
    )


def get_warehouse(
    request: YardViewGetWarehouseRequest,
) -> YardViewGetWarehouseResponse:
    """Get warehouse information from YardView API.

    Args:
        request: YardViewGetWarehouseRequest with credentials and optional filters.

    Returns:
        GetWarehouseResponse: Response with warehouse locations or errors.
    """
    try:
        response = _make_api_request(
            source=request.source,
            method="GET",
            endpoint=Endpoints.LOCATIONS,
            credentials=request.credentials,
        )
        if not response.success:
            return YardViewGetWarehouseResponse(
                success=False,
                message="Failed to fetch warehouse",
                errors=response.errors or ["Unknown error"],
            )

        locations = [LocationPto(**loc) for loc in response.platformData]
        return YardViewGetWarehouseResponse(
            success=True,
            message="Successfully retrieved warehouse information",
            warehouses=[
                YardViewWarehouse(
                    area=loc.area,
                    capacity=loc.capacity,
                    facility=loc.facility,
                    name=loc.name,
                    pid=loc.pid,
                )
                for loc in locations
            ],
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing warehouse data: {str(e)}")
        return YardViewGetWarehouseResponse(
            success=False,
            message="Failed to parse warehouse data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to fetch warehouse: {str(e)}")
        return YardViewGetWarehouseResponse(
            success=False,
            message="Failed to fetch warehouse",
            errors=[str(e)],
        )


def get_open_slots(
    request: YardViewGetOpenSlotsRequest,
) -> YardViewGetOpenSlotsResponse:
    """Get open appointment slots from YardView API.

    Args:
        request: YardViewGetOpenSlotsRequest with date range and filters.

    Returns:
        GetOpenSlotsResponse: Response with available appointment slots or errors.
    """
    try:
        schedule_pid = request.schedulePID if request.schedulePID else 1
        params = ScheduleDetailsParams(
            schedulePID=schedule_pid,
            weekDate=request.weekDate,
        )
        response = _make_api_request(
            source=request.source,
            method="GET",
            endpoint=Endpoints.SCHEDULE_DETAILS,
            credentials=request.credentials,
            params=params,
        )
        if not response.success:
            return YardViewGetOpenSlotsResponse(
                success=False,
                message="Failed to fetch open slots",
                errors=response.errors or ["Unknown error"],
            )
        schedule_details = [
            ScheduleDetailPto(**detail) for detail in response.platformData
        ]
        appointments = [
            YardViewAppointment(
                appointmentId=str(detail.pid) if detail.pid else "",
                scheduledTime=datetime.strptime(
                    f"{detail.startDate} {detail.startTime}",
                    "%Y-%m-%d %H:%M:%S",
                ).isoformat(),
                duration="1",
                status="AVAILABLE",
                notes=f"Capacity: {detail.capacity}",
            )
            for detail in schedule_details
        ]
        return YardViewGetOpenSlotsResponse(
            success=True,
            message="Successfully retrieved open slots",
            appointments=appointments,
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing open slots data: {str(e)}")
        return YardViewGetOpenSlotsResponse(
            success=False,
            message="Failed to parse open slots data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to fetch open slots: {str(e)}")
        return YardViewGetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


def get_load_types(
    request: YardViewGetLoadTypesRequest,
) -> YardViewGetLoadTypesResponse:
    """Get load types from YardView API.

    Args:
        request: YardViewGetLoadTypesRequest with optional filter.

    Returns:
        GetLoadTypesResponse: Response with load types or errors.
    """
    try:
        response = _make_api_request(
            source=request.source,
            method="GET",
            endpoint=Endpoints.LOAD_TYPES,
            credentials=request.credentials,
        )
        if not response.success:
            return YardViewGetLoadTypesResponse(
                success=False,
                message="Failed to fetch load types",
                errors=response.errors or ["Unknown error"],
            )

        return YardViewGetLoadTypesResponse(
            success=True,
            message="Successfully retrieved load types",
            loadTypes=[
                LoadTypesPto(**loadTypes)
                for loadTypes in response.platformData
            ],
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing load types data: {str(e)}")
        return YardViewGetLoadTypesResponse(
            success=False,
            message="Failed to parse load types data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to fetch load types: {str(e)}")
        return YardViewGetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


def get_carrier_scacs(
    request: YardViewGetCarrierScacsRequest,
) -> YardViewGetCarrierScacsResponse:
    """Get carrier scacs from YardView API.

    Args:
        request: YardViewGetCarrierScacsRequest

    Returns:
        YardViewGetCarrierScacsResponse: Response with carrier scacs or errors.
    """
    try:
        response = _make_api_request(
            source=request.source,
            method="GET",
            endpoint=Endpoints.SCACS,
            credentials=request.credentials,
        )
        if not response.success:
            return YardViewGetCarrierScacsResponse(
                success=False,
                message="Failed to fetch carrier scacs",
                errors=response.errors or ["Unknown error"],
            )

        return YardViewGetCarrierScacsResponse(
            success=True,
            message="Successfully retrieved carrier scacs",
            scacs=[ScacsPto(**scac) for scac in response.platformData],
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing carrier scacs data: {str(e)}")
        return YardViewGetCarrierScacsResponse(
            success=False,
            message="Failed to parse carrier scacs data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to fetch carrier scacs: {str(e)}")
        return YardViewGetCarrierScacsResponse(
            success=False,
            message="Failed to fetch carrier scacs",
            errors=[str(e)],
        )


def get_appointment(
    request: YardViewGetAppointmentRequest,
) -> GetAppointmentResponse:
    """Get an appointment or all appointments from YardView API.

    Args:
        request: YardViewGetAppointmentRequest with optional appointment ID.

    Returns:
        GetAppointmentResponse: Response with appointment details or errors.
    """
    try:
        appointment_word = (
            "appointment" if request.appointmentId else "appointments"
        )

        if request.appointmentId:
            endpoint = Endpoints.APPOINTMENT_BY_ID.format(
                appointmentId=request.appointmentId
            )
            response = _make_api_request(
                source=request.source,
                method="GET",
                endpoint=endpoint,
                credentials=request.credentials,
            )
        else:
            endpoint = Endpoints.APPOINTMENTS
            params = AppointmentParams(
                **{
                    key: value
                    for key, value in {
                        "status": request.status,
                        "startDate": request.startDate,
                        "endDate": request.endDate,
                        "customerId": request.customerId,
                    }.items()
                    if value is not None
                }
            )
            response = _make_api_request(
                source=request.source,
                method="GET",
                endpoint=endpoint,
                credentials=request.credentials,
                params=params,
            )

        if not response.success:
            return GetAppointmentResponse(
                success=False,
                message=f"Failed to fetch {appointment_word}",
                errors=response.errors or ["Unknown error"],
            )

        appointments: List[Appointment] = []

        for item in response.platformData:
            appt = AppointmentPto(**item)
            appointment = _convert_to_appointment(appt)
            appointments.append(appointment)

        success_message = (
            f"Successfully retrieved {appointment_word}"
            if not request.appointmentId
            else f"Successfully retrieved {appointment_word} ID: {request.appointmentId}"
        )

        return GetAppointmentResponse(
            success=True,
            message=success_message,
            appointments=appointments,
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing appointment data: {str(e)}")
        return GetAppointmentResponse(
            success=False,
            message="Failed to parse appointment data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(
            f"Failed to fetch {'appointment' if request.appointmentId else 'appointments'}: {str(e)}"
        )
        return GetAppointmentResponse(
            success=False,
            message=f"Failed to fetch {appointment_word}",
            errors=[str(e)],
        )


def make_appointment(
    request: YardViewMakeAppointmentRequest,
) -> YardViewMakeAppointmentResponse:
    """Create an appointment in YardView API.

    Args:
        request: YardViewMakeAppointmentRequest with appointment data.

    Returns:
        MakeAppointmentResponse: Response with created appointment or errors.
    """
    try:
        appointment_data = request.appointment
        response = _make_api_request(
            source=request.source,
            method="POST",
            endpoint=Endpoints.APPOINTMENTS,
            credentials=request.credentials,
            json=appointment_data,
        )
        if not response.success:
            return YardViewMakeAppointmentResponse(
                success=False,
                message="Failed to create appointment",
                errors=response.errors or ["Unknown error"],
            )

        if not response.platformData:
            return YardViewMakeAppointmentResponse(
                success=False,
                message="No appointment data returned",
                errors=["Empty platformData"],
            )

        appt = AppointmentPto(**response.platformData[0])
        appointment = _convert_to_appointment(appt)

        return YardViewMakeAppointmentResponse(
            success=True,
            message="Appointment created successfully",
            appointment=appointment,
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing appointment data: {str(e)}")
        return YardViewMakeAppointmentResponse(
            success=False,
            message="Failed to parse appointment data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to create appointment: {str(e)}")
        return YardViewMakeAppointmentResponse(
            success=False,
            message="Failed to create appointment",
            errors=[str(e)],
        )


def update_appointment(
    request: YardViewUpdateAppointmentRequest,
) -> UpdateAppointmentResponse:
    """Update an appointment in YardView API.

    Args:
        request: YardViewUpdateAppointmentRequest with appointment ID and update data.

    Returns:
        UpdateAppointmentResponse: Response with updated appointment or errors.
    """
    try:
        appointment_data = request.appointment.dict(exclude_unset=True)
        response = _make_api_request(
            source=request.source,
            method="PUT",
            endpoint=Endpoints.APPOINTMENT_BY_ID.format(
                appointmentId=request.appointmentId
            ),
            credentials=request.credentials,
            json=appointment_data,
        )
        if not response.success:
            return UpdateAppointmentResponse(
                success=False,
                message="Failed to update appointment",
                errors=response.errors or ["Unknown error"],
            )

        if not response.platformData:
            return UpdateAppointmentResponse(
                success=False,
                message="No appointment data returned",
                errors=["Empty platformData"],
            )

        appt = AppointmentPto(**response.platformData[0])
        appointment = _convert_to_appointment(appt)
        return UpdateAppointmentResponse(
            success=True,
            message=f"Successfully updated appointment ID: {request.appointmentId}",
            appointment=appointment,
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing appointment data: {str(e)}")
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to parse appointment data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to update appointment: {str(e)}")
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )


def cancel_appointment(
    request: YardViewCancelAppointmentRequest,
) -> CancelAppointmentResponse:
    """Cancel an appointment in YardView API.

    Args:
        request: YardViewCancelAppointmentRequest with appointment ID.

    Returns:
        CancelAppointmentResponse: Response with canceled appointment or errors.
    """
    try:
        response = _make_api_request(
            source=request.source,
            method="PUT",
            endpoint=Endpoints.APPOINTMENT_CANCEL.format(
                appointmentId=request.appointmentId
            ),
            credentials=request.credentials,
        )
        if not response.success:
            return CancelAppointmentResponse(
                success=False,
                message="Failed to cancel appointment",
                errors=response.errors or ["Unknown error"],
            )

        if not response.platformData:
            return CancelAppointmentResponse(
                success=False,
                message="No appointment data returned",
                errors=["Empty platformData"],
            )

        appt = AppointmentPto(**response.platformData[0])
        appointment = _convert_to_appointment(appt)
        return CancelAppointmentResponse(
            success=True,
            message=f"Successfully canceled appointment ID: {request.appointmentId}",
            appointment=appointment,
        )
    except ValidationError as e:
        logger.error(f"Validation error parsing appointment data: {str(e)}")
        return CancelAppointmentResponse(
            success=False,
            message="Failed to parse appointment data",
            errors=[str(e)],
        )
    except Exception as e:
        logger.error(f"Failed to cancel appointment: {str(e)}")
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )
