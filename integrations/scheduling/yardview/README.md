#### Testing

The YardView integration can be tested through YardView's custom API for Drumkit. The relevant warehouses are in Elwood and Allentown respectively.

Here are the swagger pages for each warehouse:
- Elwood: https://targettest.yardview.com/swagger/index.html
- Allentown: https://allentowntest.yardview.com/swagger/index.html

The login credentials for the API are the same for both, which can be found here:
- Elwood: https://share.1password.com/s#em08W3sMbacJYtcC09FMgox1E7Zi38gGI0jJnp0tJ7s
- Allentown: https://share.1password.com/s#jkGJcRoeP6b6-zGVnlx_Hv1eMGmG6lZbyXC9hYWJopw

We can verify the API operations by checking the test website for each:
- Elwood: https://share.1password.com/s#7M_mLWc49-xMFUPCQ23RZZCyjC95QvhZRpUZMYmVR4g
- Allentown: TBD

#### API Examples

All requests are sent as JSON to the endpoint http://localhost:8000/ using POST method. Here are examples for each supported operation:

##### Data Retrieval

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetOpenSlots",
  "userId": "user123",
  "startDate": "2023-09-01",
  "endDate": "2023-09-15",
  "locationId": "warehouse-1",
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

##### Appointment Management

###### Create Appointment

Creates a new appointment.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "MakeAppointment",
  "userId": "user123",
  "appointment": {
    "carrierId": "CARRIER_001",
    "loadId": "LOAD_123",
    "appointmentTime": "2023-09-15T14:30:00",
    "duration": 60,
    "dock": "DOCK_A1",
    "notes": "Special handling required",
    "status": "Scheduled"
  },
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

###### Cancel Appointment

Cancels an existing appointment.

#### Issues:
- We're canceling appointments using their IDs, but the ID displayed on the pending appointments page doesn't match the original input, leading to inconsistencies.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "CancelAppointment",
  "userId": "user123",
  "appointmentId": "APT12345",
  "reason": "Delivery rescheduled",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

##### Get Appointment

Get an existing appointment details.

#### Issues:
- We’re fetching appointments through the pending appointments page, but the details displayed don’t match the ones we originally input, which could lead to data inconsistency.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetAppointment",
  "userId": "user123",
  "startDate": "2025-04-15",
  "endDate": "2025-04-17",
  "appointmentId": "2504166886",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```
