"""Common models shared across all scheduling platforms."""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from models.base import BaseRequest, BaseResponse, RequestMode
from models.enums import CommonActionType


class SchedulingPlatform(str, Enum):
    """Scheduling platforms supported by the application."""

    C3RESERVATIONS = "C3Reservations"
    COSTCO = "Costco"
    E2OPEN = "E2open"
    MANHATTAN = "Manhattan"
    ONENETWORK = "OneNetwork"
    RETAILLINK = "RetailLink"
    YARDVIEW = "YardView"
    # NOTE: Add more scheduling platforms as needed
    RETALIX = "Retalix"


class SchedulingActionType(str, Enum):
    """Actions specific to scheduling integrations."""

    # Include common actions
    LOGIN = CommonActionType.LOGIN.value
    LOGOUT = CommonActionType.LOGOUT.value

    # Scheduling-specific actions that map to Drumkit's
    # scheduling interface
    GET_CARRIER_SCACS = "GetCarrierScacs"
    GET_LOAD_TYPES = "GetLoadTypes"
    GET_OPEN_SLOTS = "GetOpenSlots"
    GET_WAREHOUSE = "GetWarehouse"

    CANCEL_APPOINTMENT = "CancelAppointment"
    GET_APPOINTMENT = "GetAppointment"
    MAKE_APPOINTMENT = "MakeAppointment"
    UPDATE_APPOINTMENT = "UpdateAppointment"
    VALIDATE_APPOINTMENT = "ValidateAppointment"


class Credentials(BaseModel):
    """User credentials for authentication."""

    username: str
    password: str
    # Optional fields for other platforms if needed
    apiKey: Optional[str] = ""
    token: Optional[str] = ""
    extraAuth: Optional[Dict[str, Any]] = None


class SchedulingBaseRequest(BaseRequest):
    """Base request model for scheduling integrations."""

    integration: str = "scheduling"
    platform: SchedulingPlatform
    action: SchedulingActionType
    mode: Optional[RequestMode] = RequestMode.SELENIUM
    credentials: Optional[Credentials] = None


class AppointmentSlot(BaseModel):
    """Individual appointment time slot."""

    duration: int
    scheduledTime: str


class WarehouseDetails(BaseModel):
    """Warehouse details model."""

    city: Optional[str] = ""
    country: Optional[str] = ""
    name: Optional[str] = ""
    addressLine1: Optional[str] = ""
    id: Optional[str] = ""
    openSlots: Optional[List[AppointmentSlot]] = None
    state: Optional[str] = ""
    stopType: Optional[str] = ""
    website: Optional[str] = ""
    zipCode: Optional[str] = ""


class AppointmentData(BaseModel):
    """Generic appointment data model used across scheduling platforms."""

    appointmentId: Optional[str] = ""
    duration: Optional[int] = 0
    notes: Optional[str] = ""
    poNums: Optional[str] = ""
    scheduledTime: Optional[str] = ""
    status: Optional[str] = ""
    warehouse: Optional[WarehouseDetails] = None
    # Extended data specific to platforms
    extended: Optional[Dict[str, Any]] = None


class Appointment(BaseModel):
    """Standardized appointment model for responses."""

    appointmentId: str
    duration: Optional[int] = 0
    location: Optional[str] = ""
    notes: Optional[str] = ""
    reference: Optional[str] = ""
    scheduledTime: str
    status: str
    warehouse: Optional[WarehouseDetails] = None
    # Platform-specific data
    extended: Optional[Dict[str, Any]] = None


class LoginRequest(SchedulingBaseRequest):
    """Login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platformData: Optional[Dict[str, Any]] = None


class LoginResponse(BaseResponse):
    """Login response model."""

    userDetails: Optional[Dict[str, Any]] = None
    sessionToken: Optional[str] = ""


class GetLoadTypesRequest(SchedulingBaseRequest):
    """Request to get open appointment slots."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS

    endDate: Optional[str] = ""
    filters: Optional[Dict[str, Any]] = None
    locationId: Optional[str] = ""
    startDate: Optional[str] = ""


class GetLoadTypesResponse(BaseResponse):
    """Response containing open appointment slots."""

    appointments: Optional[List[Appointment]] = None
    loadTypes: Optional[List[Appointment]] = None
    pagination: Optional[Dict[str, Any]] = None


class GetOpenSlotsRequest(SchedulingBaseRequest):
    """Request to get open appointment slots."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS

    endDate: Optional[str] = ""
    filters: Optional[Dict[str, Any]] = None
    locationId: Optional[str] = ""
    startDate: Optional[str] = ""


class ValidateAppointmentResponse(BaseResponse):
    """Response containing open appointment slots."""

    appointments: Optional[List[Appointment]] = None
    pagination: Optional[Dict[str, Any]] = None


class GetOpenSlotsResponse(BaseResponse):
    """Response containing open appointment slots."""

    appointments: Optional[List[AppointmentData]] = None


class GetWarehouseRequest(SchedulingBaseRequest):
    """Request to get open appointment slots."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS

    endDate: Optional[str] = ""
    filters: Optional[Dict[str, Any]] = None
    locationId: Optional[str] = ""
    startDate: Optional[str] = ""


class GetWarehouseResponse(BaseResponse):
    """Response containing open appointment slots."""

    appointments: Optional[List[Appointment]] = None
    warehouses: Optional[List[WarehouseDetails]] = None
    pagination: Optional[Dict[str, Any]] = None


class CancelAppointmentRequest(SchedulingBaseRequest):
    """Request to cancel an appointment."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT

    appointmentId: str
    reason: Optional[str] = ""


class CancelAppointmentResponse(BaseResponse):
    """Response after canceling an appointment."""

    appointmentId: Optional[str] = ""

    alertMessage: Optional[str] = ""
    proId: Optional[str] = ""
    warehouse: Optional[WarehouseDetails] = None


class GetAppointmentRequest(SchedulingBaseRequest):
    """Request to get an appointment."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT

    appointmentId: str


class GetAppointmentResponse(BaseResponse):
    """Response after getting an appointment."""

    appointments: Optional[List[Appointment]] = None


class MakeAppointmentRequest(SchedulingBaseRequest):
    """Request to create a new appointment."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT

    appointment: AppointmentData


class MakeAppointmentResponse(BaseResponse):
    """Response after creating an appointment."""

    appointments: Optional[List[Appointment]] = None


class UpdateAppointmentRequest(SchedulingBaseRequest):
    """Request to update an existing appointment."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT

    appointment: AppointmentData
    appointmentId: str


class UpdateAppointmentResponse(BaseResponse):
    """Response after updating an appointment."""

    appointments: Optional[List[Appointment]] = None
