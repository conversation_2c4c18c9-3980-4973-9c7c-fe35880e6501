import asyncio
from fastapi import HTTPException
from utils.logging import logger

MAX_RETRIES = 2
RETRY_DELAYS = [1.0, 2.0]


async def retry_with_backoff(
    func, max_retries=MAX_RETRIES, retry_delays=RETRY_DELAYS
):
    """
    Retry function with fixed delays.

    Args:
        func: Async function to retry
        max_retries: Maximum number of retry attempts
        retry_delays: List of delays for each retry attempt
    """
    # Validate that we have enough delays for the max retries
    if len(retry_delays) < max_retries:
        raise ValueError(
            f"retry_delays must have at least {max_retries} elements, got {len(retry_delays)}"
        )

    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e

            # Don't retry on certain types of errors
            if isinstance(e, (HTTPException,)):
                raise e

            if attempt == max_retries:
                logger.error(
                    f"Max retries ({max_retries}) exceeded. Last error: {str(e)}"
                )
                raise e

            delay = retry_delays[attempt]
            logger.warning(
                f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {delay:.2f} seconds..."
            )
            await asyncio.sleep(delay)

    # This should never be reached, but just in case
    raise last_exception
