name: Deploy <PERSON><PERSON><PERSON> to EC2

on:
  push:
    branches:
      - "release"

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout current branch
        uses: actions/checkout@v4

      - name: Verify required secrets
        run: |
          missing_secrets=()

          [ -z "${{ secrets.ACCESS_KEY_ID }}" ] && missing_secrets+=("ACCESS_KEY_ID")
          [ -z "${{ secrets.SECRET_ACCESS_KEY }}" ] && missing_secrets+=("SECRET_ACCESS_KEY")
          [ -z "${{ secrets.EC2_INSTANCE_ID }}" ] && missing_secrets+=("EC2_INSTANCE_ID")
          [ -z "${{ secrets.S3_BUCKET }}" ] && missing_secrets+=("S3_BUCKET")
          [ -z "${{ secrets.OPENAI_API_KEY }}" ] && missing_secrets+=("OPENAI_API_KEY")

          if [ ${#missing_secrets[@]} -ne 0 ]; then
            echo "Error: The following required secrets are missing:"
            printf "  - %s\n" "${missing_secrets[@]}"
            exit 1
          fi

          echo "All required secrets are available."

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          aws-region: us-east-1
          aws-secret-access-key: ${{ secrets.SECRET_ACCESS_KEY }}

      - name: Prepare deployment files
        run: |
          # Create deployment zip file
          zip -r cyclops.zip . -x "*.git*"

      # TODO: Add step to make sure folders are created before
      # referenced and awscli is installed. (done manually now but we
      # need a check) aws-cli/zip/unzip/etc.

      # TODO: Replace OPENAI_API_KEY with Secrets Manager ARN so we
      # can have Secrets Manager read the OPENAI_API_KEY for us.

      - name: Create .env file on EC2
        env:
          EC2_INSTANCE_ID: ${{ secrets.EC2_INSTANCE_ID }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
        run: |
          # Create .env file on EC2 via SSM
          aws ssm send-command \
            --instance-ids ${EC2_INSTANCE_ID} \
            --document-name "AWS-RunShellScript" \
            --parameters '{"commands":["echo \"OPENAI_API_KEY='${OPENAI_API_KEY}'\nSENTRY_DSN='${SENTRY_DSN}'\nAPP_ENV=prod\" > /home/<USER>/cyclops/.env"]}'

      - name: Copy files to EC2 using S3 and SSM
        env:
          EC2_INSTANCE_ID: ${{ secrets.EC2_INSTANCE_ID }}
          S3_BUCKET: ${{ secrets.S3_BUCKET }}
        run: |
          # Upload deployment package to S3
          aws s3 cp cyclops.zip s3://${S3_BUCKET}/cyclops.zip

          # Use SSM to download from S3 and run deployment
          aws ssm send-command \
            --instance-ids ${EC2_INSTANCE_ID} \
            --document-name "AWS-RunShellScript" \
            --parameters commands="cd /home/<USER>
                                  aws s3 cp s3://${S3_BUCKET}/cyclops.zip . && \
                                  mkdir -p cyclops && \
                                  unzip -q -o cyclops.zip -d cyclops && \
                                  cd cyclops && \
                                  chmod +x ./scripts/deploy/install.sh && \
                                  ./scripts/deploy/install.sh"

      - name: Check deployment status
        env:
          EC2_INSTANCE_ID: ${{ secrets.EC2_INSTANCE_ID }}
        run: |
          # Wait a moment for deployment to initialize
          sleep 10

          # Check deployment status
          aws ssm send-command \
            --instance-ids ${EC2_INSTANCE_ID} \
            --document-name "AWS-RunShellScript" \
            --parameters '{"commands":["cd /home/<USER>/cyclops && ps aux | grep python3 | grep -v grep"]}'

      - uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "Cyclops EC2 Deployment {status}"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
