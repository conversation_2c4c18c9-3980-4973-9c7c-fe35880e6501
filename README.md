# Scraper

## Scheduling Scraper

A Selenium automation framework for scheduling platforms. This tool allows you to interact with various scheduling systems through a unified API, with support for:

- Getting open appointment slots
- Canceling appointments
- Updating appointments
- Making appointments
- User session management via cookie caching

## Features

- **Clean API**: Consistent JSON request/response format
- **Session Management**: Fast user switching via Redis cookie caching
- **Extensible Architecture**: Easy to add support for new scheduling platforms
- **Headless Operation**: Runs in headless mode by default for speed
- **Error Handling**: Robust error handling and reporting
- **Dual Operation Modes**:
  - Selenium mode for web scraping (default)
  - API mode for direct platform integration
  - Automatic fallback from API to Selenium

## Operation Modes

The system supports two operation modes:

### Selenium Mode (Default)

- Web automation using Selenium
- More universal compatibility
- Slower but more reliable
- Works with any web interface

### API Mode

- Direct API integration
- Faster performance
- Platform-dependent
- Limited to supported platforms

To specify the mode in your request (`api` or `selenium`):

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetWarehouse",
  "mode": "api"
}
```

If mode is not specified, the system defaults to "selenium".

## Supported Platforms

- Scheduling systems:
  - YardView
  - E2open
  - (More platforms can be easily added)
- (More integrations other than scheduling can be easily added)

## Getting Started

### Prerequisites

- Python 3.9+
- Chrome browser (chromedriver is needed)
- Redis (optional, will fall back to in-memory cache)

### Installation

1. Clone this repository
2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. If working with Zerox/document-processing, [you need poppler installed on your machine.](https://github.com/getomni-ai/zerox?tab=readme-ov-file#installation-1)

- On MacOS, run `brew install poppler` outside of the virtual environment.
  If you run into the following error:

```shell
Error: No developer tools installed.
Install the Command Line Tools:
  xcode-select --install
```

then run `xcode-select --install` as instructed, follow the prompts in the dialogue box, and then try installing poppler again.

### Configuration

Set the following environment variables:

- `APP_ENV`: App Environment (default: development)
- `LOG_LEVEL`: Logging level (default: INFO)
- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)
- `REDIS_PASSWORD`: Redis password (optional)
- `REDIS_PREFIX`: Prefix for Redis keys (default: scraper)
- `OPENAI_API_KEY`: OpenAI API key
- `SENTRY_DSN`: Sentry DSN

### Running Locally

_Steps 1-4 are additional steps for setting up virtual environment in VSCode/Cursor_

1. Create the venv in your project root:

```python
python3 -m venv .venv
```

2. Activate the venv:

```python
source .venv/bin/activate
```

3. Install the dependencies:

```python
pip install -r requirements.txt
```

4. Point VSCode to the new venv:

- Open the command palette (`Ctrl+Shift+P`)
- Type "Python: Select Interpreter"
- Select the venv you just created e.g. `.venv/bin/python`

5. Run the app

```python
python3 app.py --host 127.0.0.1 --port 8000 --reload
```

#### Health Check

The service provides a health check endpoint:

```bash
curl http://localhost:8000/health
```

#### API Examples

All requests are sent as JSON to the endpoint <http://localhost:8000/> using POST method. Here are examples for each supported operation:

Note: All examples can include an optional `mode` field:

- `"mode": "selenium"` (default)
- `"mode": "api"` (when available)

##### Data Retrieval

###### Get Warehouse Information

Fetches warehouse details.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetWarehouse",
  "userId": "user123",
  "startDate": "2023-09-01",
  "endDate": "2023-09-30",
  "locationId": "main-warehouse",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

###### Get Available Appointment Slots

Retrieves open time slots for scheduling.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetOpenSlots",
  "userId": "user123",
  "startDate": "2023-09-01",
  "endDate": "2023-09-15",
  "locationId": "warehouse-1",
  "filterType": "inbound",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

###### Get Load Types

Retrieves available load types for scheduling.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "GetLoadTypes",
  "userId": "user123",
  "startDate": "2023-09-01",
  "endDate": "2023-09-30",
  "locationId": "all",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

##### Appointment Management

###### Create Appointment

Creates a new appointment.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "MakeAppointment",
  "userId": "user123",
  "appointment": {
    "carrierId": "CARRIER_001",
    "loadId": "LOAD_123",
    "appointmentTime": "2023-09-15T14:30:00",
    "duration": 60,
    "dock": "DOCK_A1",
    "notes": "Special handling required",
    "status": "Scheduled"
  },
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

###### Update Appointment

Modifies an existing appointment.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "UpdateAppointment",
  "userId": "user123",
  "appointmentId": "APT12345",
  "appointment": {
    "carrierId": "CARRIER_001",
    "appointmentTime": "2023-09-16T10:00:00",
    "duration": 90,
    "notes": "Updated: Requires forklift",
    "status": "Confirmed"
  },
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

###### Cancel Appointment

Cancels an existing appointment.

```json
{
  "integration": "scheduling",
  "platform": "YardView",
  "action": "CancelAppointment",
  "userId": "user123",
  "appointmentId": "APT12345",
  "reason": "Delivery rescheduled",
  "credentials": {
    "username": "test_user",
    "password": "test_password"
  }
}
```

#### Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "errors": null,
  "platformData": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

On error:

```json
{
  "success": false,
  "message": "Error message",
  "errors": ["Detailed error 1", "Detailed error 2"],
  "platformData": null
}
```

## Adding New Platforms

1. Create a new module in the `integrations/scheduling/` directory
2. Implement the required functions (login, etc.)
3. Create a handlers map in `handlers.py` to expose your functions
4. Register the platform in `config.py` with its base URL

## Development

Run linting and formatting:

```python
black .
isort .
```

## Deployment

### AWS EC2 Deployment

The repository is configured to automatically deploy to AWS EC2 when code is pushed to the `release` branch using GitHub Actions.

#### Prerequisites for EC2 Deployment

1. An EC2 instance running Amazon Linux 2023
2. The following packages installed on the EC2 instance:
   - Python 3.9+
   - pip
   - git
   - Chrome/Chromium (for Selenium)
   - Redis (optional but recommended)
   - poppler-utils (for Zerox/document processing)

#### Setup GitHub Secrets

- `EC2_USERNAME`: SSH username (`ubuntu` for Ubuntu)
- `EC2_HOST`: Private IP of EC2 instance
- `EC2_SSH_KEY`: SSH private key to connect to the instance
- `OPENAI_API_KEY`: OpenAI API key
- `SENTRY_DSN`: Sentry DSN for Cyclops
- `LOG_LEVEL`: Logging level (default: INFO) - not added
- `REDIS_HOST`: Redis host (default: localhost) - not added
- `REDIS_PORT`: Redis port (default: 6379) - not added
- `REDIS_PASSWORD`: Redis password (if required) - not added
- `REDIS_PREFIX`: Prefix for Redis keys (default: scraper) - not added

#### Manual Deployment

If you need to deploy manually, follow these steps:

1. Install required packages on your EC2 instance
2. Clone the repository
3. Set up environment variables
4. Install Python dependencies
5. Start the service

## Architecture

### Handler Structure

Each integration platform implements two sets of handlers:

```python
# In platform's handlers.py
SELENIUM_HANDLERS = {
    "GetWarehouse": scraper.get_warehouse,
    "GetOpenSlots": scraper.get_open_slots,
    # ... other handlers
}

API_HANDLERS = {
    "GetWarehouse": api.get_warehouse,
    "GetOpenSlots": api.get_open_slots,
    # ... other handlers
}
```

The system automatically selects the appropriate handler based on:

1. Requested mode
2. Platform support
3. Handler availability

### Directory Structure

```bash
.
├── app.py                  # App entrypoint with FastAPI server
├── cache.py                # Redis/memory caching functions
├── config.py               # Configuration settings
├── handlers.py             # Dynamic request handler
├── integrations/           # Integration platforms
│   └── scheduling/         # Scheduling integrations
│       └── yardview/       # YardView integration
│           ├── __init__.py
│           ├── handlers.py # YardView handlers mapping
│           └── scraper.py  # YardView scraper implementation
│       └── ./              # More Scheduling integrations
├── router.py               # FastAPI router
├── models.py               # Data models
├── requirements.txt        # Dependencies
├── scripts/                # Deployment and maintenance scripts
│   └── deploy/             # Deployment automation
│       └── install.sh      # System installation and setup script
│   └── systemd/            # Service configuration
│       ├── cyclops.service # Systemd service definition
│       └── bin/            # Service control scripts
│           └── start.sh    # Service startup and environment setup
├── session.py              # Browser session management
└── utils/                  # Utility functions
    ├── __init__.py
    ├── json.py             # JSON utility functions
    ├── logging.py          # Logging utilities
    ├── selenium.py         # Selenium helper functions
    └── sentry.py           # Sentry configuration
```

### Request Flow

1. User submits JSON request to the API endpoint
2. Request is parsed and validated using Pydantic models
3. Mode is determined (defaults to Selenium if not specified)
4. Handler dynamically loads the appropriate integration module
5. Platform-specific handler (API or Selenium) performs the operation
6. Response is formatted and returned to the user

### Caching Mechanism

The system uses a multi-level caching approach:

1. **Primary**: Redis for distributed caching
2. **Fallback**: In-memory cache when Redis is unavailable
3. **Cache Keys**: Format `{prefix}:cookies:{platform}:{userId}`
4. **TTL**: Cookies expire after 24 hours by default

## Troubleshooting

### Health Check

- Run this CLI command to check service health:

```bash
curl https://api.drumkit.ai/cyclops/health
```

- Expected result is `OK%`

### SSH Access

- If health check fails, SSH into the EC2 instance:

  1. Visit [EC2 Console](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#ConnectToInstance:instanceId=i-03e0ee55765990e32)
  2. Click "Connect using EC2 Instance Connect Endpoint"
  3. Click bottom right "Connect" button

- Check if server is running:

  ```bash
  lsof -i :8000
  ```

  Expected output if running:

  ```
  COMMAND   PID    USER   FD   TYPE   DEVICE  SIZE/OFF  NODE  NAME
  python  12345  ubuntu   4u   IPv4   123456     0t0    TCP   *:8000 (LISTEN)
  ```

  What to look for:

  - Any output means server is running
  - No output means server is stopped
  - Multiple entries might indicate duplicate processes
  - Check USER column should be 'ubuntu'
  - Port should show as LISTEN

  Note: PID numbers will vary. The important part is seeing a Python process listening on port 8000.

- If the server is down, restart it:

  ```bash
  sudo systemctl daemon-reload
  sudo systemctl enable cyclops.service
  sudo systemctl restart cyclops.service
  ```

  Verify service status:

  ```bash
  sudo systemctl status cyclops.service
  ```

  Expected output:

  ```bash
  ● cyclops.service - Cyclops Scraper API Service
       Loaded: loaded (/etc/systemd/system/cyclops.service; enabled; preset: enabled)
       Active: active (running) since Thu 2024-01-01 12:00:00 UTC;
     Main PID: 12345 (start.s)
        Tasks: 11 (limit: 2272)
       Memory: 689.3M (peak: 1.3G)
          CPU: 3h 53min 21.926s
       CGroup: /system.slice/cyclops.service
               ├─12345 /bin/bash /home/<USER>/cyclops/scripts/systemd/start.sh
               └─12346 python3 app.py --host 0.0.0.0
  ```

  Common issues:

  - If status shows "failed", check logs: `journalctl -xe -u cyclops.service`
  - If "not found", verify service file exists
  - If permission errors, check ownership:
    ```bash
    cd /home/<USER>
    sudo chown -R ubuntu:users cyclops
    sudo chown -R ubuntu:users cyclops/venv
    ```
