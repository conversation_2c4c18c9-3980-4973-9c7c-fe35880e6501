# HTML snapshot and Upload to S3

This module mirrors an HTML page along with its associated assets (CSS, JS, images) to a structured local directory and uploads everything to an S3 bucket.

## 📁 Project Structure

```
utils/
├── s3/
│   └── s3_uploader.py           # Handles uploading files to S3
├── html_snapshot_service.py     # Core logic for mirroring and uploading HTML pages
```

---

## ⚙️ Configuration

Update your `.env` file or environment variables with the following:

```env
BASE_LOCAL_DIR=s3_temp
S3_BUCKET_NAME=drumkit-cyclops-archive
```

In `config.py`:
```python
from typing import Final
import os

BASE_LOCAL_DIR: Final[str] = os.environ.get("BASE_LOCAL_DIR", "s3_temp")
S3_BUCKET_NAME: Final[str] = os.environ.get("S3_BUCKET_NAME", "drumkit-cyclops-archive")
```

---

## 🚀 How It Works

### 1. Snapshot and Upload
Use the `snapshot_and_upload_webpage` function to:
- Parse and collect assets (images, JS, CSS)
- Download them into a structured local folder
- Rewrite paths in the HTML
- Upload all files to S3 while preserving relative paths
- Clean up the local temporary folder

### 2. Upload to S3
- `s3_uploader.py` provides `S3Uploader.upload_file` to handle S3 uploads with proper MIME type and path preservation.
- `upload_files_to_s3` manages the upload process with rate limiting to avoid API throttling (50 per minute).
- Removes the local temporary files after successful upload.

---

## 🔍 Example Usage

```python
from utils.aws_utils.s3.s3_uploader import S3Uploader
from utils.aws_utils.s3.html_snapshot_service import snapshot_and_upload_webpage

await snapshot_and_upload_webpage(
    request=request,
    current_url=driver.current_url,
    html_source=driver.page_source,
    function_name="get_open_slots",
    page_name="appoint_loads",
    status_code=200,
    s3_uploader=S3Uploader()
)
```

```python
# To call it asynchronously, call in the following way...
import asyncio
from utils.aws_utils.s3.html_snapshot_service import snapshot_and_upload_webpage

asyncio.create_task(
    snapshot_and_upload_webpage(
        request=request,
        current_url=driver.current_url,
        function_name="get_open_slots",
        page_name="appoint_loads",
        html_source=driver.page_source
    )
)
```

---

## ✨ Features

- Maintains folder structure for easy debugging
- Rewrites asset URLs to match S3 pathing
- Automatically cleans up local temp folder
- Rate-limited upload to avoid API throttling

---
