"""HTML snapshot service for S3."""

import asyncio
import os
import shutil
from datetime import datetime, timezone
import re
from urllib.parse import urlparse, urljoin

import aiohttp
from bs4 import BeautifulSoup

from config import BASE_LOCAL_DIR
from utils.aws_utils.s3.s3_uploader import S3Uploader
from utils.logging import logger

from integrations.scheduling.models import (
    SchedulingBaseRequest,
)


async def snapshot_and_upload_webpage(
    request: SchedulingBaseRequest,
    current_url: str,
    function_name: str,
    page_name: str,
    html_source: str,
    status_code: int = 200,
    s3_uploader: S3Uploader = None,
) -> None:
    """
    Mirrors a webpage using async HTTP requests with aiohttp and BeautifulSoup for HTML parsing, then uploads all assets to S3, maintaining folder structure.

    Args:
        request: The request object
        current_url: The URL of the page to be mirrored
        function_name: Name of the function calling this method
        page_name: Name of the page to be used in the S3 path
        html_source: HTML content of the page
        status_code: HTTP status code for the request
        s3_uploader: Optional S3Uploader instance
    """
    try:
        logger.info(f"Snapshotting and uploading webpage: {current_url}")
        user_id = str(request.userId)
        service_id = str(request.serviceId)
        integration_id = str(request.integrationId)
        platform = request.platform.value

        # Safe folder name for URL
        host = urlparse(current_url).netloc

        iso_timestamp = (
            datetime.now(timezone.utc)
            .isoformat(timespec="microseconds")
            .replace("+00:00", "Z")
        )
        relative_dir = os.path.join(
            "service",
            f"{service_id}",
            "user",
            f"{user_id}",
            f"{platform}",
            "integration",
            f"{integration_id}",
            host,
            "http",
            f"{status_code}",
            f"{function_name}-{iso_timestamp}",
        )

        main_temp_dir = os.path.join(BASE_LOCAL_DIR, relative_dir)
        local_temp_dir = os.path.join(main_temp_dir, page_name)
        public_dir = os.path.join(local_temp_dir, "public")
        os.makedirs(public_dir, exist_ok=True)

        updated_html = await update_and_prettify_html(
            html_source, current_url, public_dir
        )

        index_path = os.path.join(local_temp_dir, "index.html")
        with open(index_path, "w", encoding="utf-8") as f:
            f.write(updated_html)

        await upload_files_to_s3(local_temp_dir, s3_uploader or S3Uploader())

        await cleanup_folder(main_temp_dir)

        logger.info(f"Uploaded files to S3 for {current_url}")
    except Exception as e:
        logger.error(f"Failed to mirror and upload page: {str(e)}")


def resolve_asset_path(asset_url: str, current_url: str) -> str:
    """Resolve the asset path relative to the base URL.

    Args:
        asset_url: URL of the asset
        current_url: Current page URL

    Returns:
        Sanitized relative path of the asset
    """
    parsed = urlparse(asset_url)
    if parsed.scheme and parsed.netloc:
        rel_path = parsed.path.lstrip("/")
    else:
        abs_url = urljoin(current_url, asset_url)
        rel_path = urlparse(abs_url).path.lstrip("/")

    # Normalize path (removes things like a/../b → b)
    rel_path = os.path.normpath(rel_path)

    # Prevent directory traversal after normalization
    if rel_path.startswith("..") or os.path.isabs(rel_path):
        # Block malicious paths like ../../etc/passwd or /etc/passwd
        return ""

    return rel_path.lstrip("/.")


def rewrite_html_paths(html: str, asset_urls: set) -> str:
    """Rewrite HTML paths to point to the local public directory.

    Args:
        html: HTML content
        asset_urls: Set of asset URLs

    Returns:
        HTML content with updated paths
    """

    def replacement(match):
        attr, path = match.group(1), match.group(2)
        resolved = resolve_asset_path(path, "")
        return f'{attr}="./public/{resolved}"'

    html = re.sub(r'(href|src)=["\']([^"\']+)["\']', replacement, html)
    html = re.sub(
        r'url\(["\']?([^"\')]+)["\']?\)',
        lambda m: f"url('./public/{resolve_asset_path(m.group(1), '')}')",
        html,
    )
    return html


async def update_and_prettify_html(
    html_source: str, current_url: str, public_dir: str
) -> str:
    """
    Update and prettify the HTML content.

    Args:
        html_source: HTML content
        current_url: Current page URL
        public_dir: Local directory for public assets

    Returns:
        Updated HTML content
    """
    soup = BeautifulSoup(html_source, "html.parser")
    asset_urls = set()

    def collect_asset(tag, attr):
        for node in soup.find_all(tag):
            if node.has_attr(attr):
                asset_urls.add(node[attr])

    collect_asset("link", "href")
    collect_asset("script", "src")
    collect_asset("img", "src")

    css_urls = re.findall(r'url\(["\']?([^"\')]+)["\']?\)', html_source)

    for match in css_urls:
        asset_urls.add(match)

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(10)

        async def download_with_limit(url):
            async with semaphore:
                await download_asset_file(
                    url, public_dir, current_url, session
                )

        download_tasks = [download_with_limit(url) for url in asset_urls]
        await asyncio.gather(*download_tasks)

    updated_html = rewrite_html_paths(soup.prettify(), asset_urls)

    return updated_html


async def download_asset_file(
    asset_url: str,
    public_dir: str,
    base_url: str,
    session: aiohttp.ClientSession,
) -> None:
    """Download an asset file and save it in the public directory.

    Args:
        asset_url: URL of the asset
        public_dir: Local directory for public assets
        base_url: Base URL of the page
        session: aiohttp ClientSession
    """
    asset_rel_path = resolve_asset_path(asset_url, base_url)
    full_asset_path = os.path.join(public_dir, asset_rel_path)
    os.makedirs(os.path.dirname(full_asset_path), exist_ok=True)

    try:
        abs_url = urljoin(base_url, asset_url)
        async with session.get(abs_url) as response:
            if response.status == 200:
                with open(full_asset_path, "wb") as f:
                    f.write(await response.read())
    except Exception as e:
        print(f"Failed to download {asset_url}: {e}")


async def upload_files_to_s3(
    local_temp_dir: str, s3_uploader: S3Uploader, per_minute_limit: int = 50
) -> None:
    """Upload files to S3 with rate limiting.

    Args:
        local_temp_dir: Local directory containing the files to be uploaded
        s3_uploader: S3Uploader instance
        per_minute_limit: Maximum number of uploads per minute
    """
    logger.info(
        f"Uploading files from {local_temp_dir} to S3, limiting to {per_minute_limit} uploads per minute..."
    )

    uploaded_files_count = 0
    start_time = datetime.now()

    for root, _, files in os.walk(local_temp_dir):
        for file in files:
            full_path = os.path.join(root, file)
            await asyncio.to_thread(s3_uploader.upload_file, full_path)
            uploaded_files_count += 1

            if uploaded_files_count % per_minute_limit == 0:
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed < 60:
                    sleep_time = 60 - elapsed
                    logger.info(
                        f"Hit upload limit. Sleeping for {sleep_time:.2f} seconds."
                    )
                    await asyncio.sleep(sleep_time)
                start_time = datetime.now()


async def cleanup_folder(folder_path: str) -> None:
    """Cleanup the temporary folder.

    Args:
        folder_path: Path of the folder to be deleted
    """
    if os.path.exists(folder_path):
        shutil.rmtree(folder_path)
        logger.info(f"Deleted folder: {folder_path}")
    else:
        logger.warning(f"Folder not found, skipping deletion: {folder_path}")
