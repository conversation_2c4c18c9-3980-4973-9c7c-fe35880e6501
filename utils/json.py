"""JSON utility functions for parsing and validating requests."""

import json
import importlib
from typing import Any, Dict, Union, Type, Optional, Tuple

from pydantic import ValidationError, BaseModel

from models.base import BaseRequest
from utils.logging import logger


# Cache for model classes to avoid repeated imports
_MODEL_CACHE: Dict[Tuple[str, str], Type[BaseModel]] = {}


def _import_module_safe(module_path: str) -> Optional[Any]:
    """Safely import a module without raising exceptions."""
    try:
        return importlib.import_module(module_path)
    except ImportError:
        return None


def get_request_model(
    integration: str, platform: str, action: str
) -> Type[BaseModel]:
    """Get the appropriate request model based on all three parameters."""
    # Check cache first
    cache_key = (integration, platform, action)
    if cache_key in _MODEL_CACHE:
        return _MODEL_CACHE[cache_key]

    lower_platform = platform.lower()

    # Try platform-specific model first
    module_path = f"integrations.{integration}.{lower_platform}.models"
    module = _import_module_safe(module_path)
    class_name = f"{platform}{action}Request"

    if module and hasattr(module, class_name):
        model_class = getattr(module, class_name)
    else:
        # Try integration-level model (e.g., LoginRequest)
        module_path = f"integrations.{integration}.models"
        module = _import_module_safe(module_path)
        class_name = f"{action}Request"

        if module and hasattr(module, class_name):
            model_class = getattr(module, class_name)
        else:
            raise ValueError(f"No request model found for {class_name}")

    # Cache the result
    _MODEL_CACHE[cache_key] = model_class
    return model_class


def parse_request(data: Union[Dict[str, Any], str]) -> BaseRequest:
    """Parse a request from a dictionary or JSON string.

    Args:
        data: The request data

    Returns:
        The parsed request

    Raises:
        ValueError: If the request cannot be parsed
    """
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON: {str(e)}")
            raise ValueError(f"Invalid JSON: {str(e)}") from e

    integration = data.get("integration")
    platform = data.get("platform")
    action = data.get("action")

    if not platform:
        logger.error("Missing platform in request")
        raise ValueError("Missing platform in request")

    if not action:
        logger.error("Missing action in request")
        raise ValueError("Missing action in request")

    try:
        model_class = get_request_model(integration, platform, action)
        return model_class.model_validate(data)

    except ValidationError as e:
        errors = []
        for error in e.errors():
            loc = ".".join(str(l) for l in error["loc"])
            errors.append(f"{loc}: {error['msg']}")

        error_msg = "\n".join(errors)
        logger.error(f"Request validation failed:\n{error_msg}")
        raise ValueError(f"Invalid request: {error_msg}") from e

    except Exception as e:
        logger.error(f"Error parsing request: {str(e)}")
        raise ValueError(f"Failed to parse request: {str(e)}") from e
