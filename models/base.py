"""Base data models for requests and responses."""

from enum import Enum
from typing import Optional, List, Dict, Any

from pydantic import BaseModel


class RequestMode(str, Enum):
    """Request mode for handlers."""

    SELENIUM = "selenium"
    API = "api"


class BaseRequest(BaseModel):
    """Base request model for all integrations."""

    integration: str
    platform: str
    action: str
    mode: Optional[RequestMode] = RequestMode.SELENIUM
    userId: str
    serviceId: Optional[str] = None
    integrationId: Optional[str] = None


class BaseResponse(BaseModel):
    """Base response model."""

    success: bool
    message: Optional[str] = None
    errors: Optional[List[str]] = None
    platformData: Optional[List[Dict[str, Any]]] = None
