"""Common enums used across all integration types."""

from enum import Enum


class IntegrationType(str, Enum):
    """Types of integrations supported by the application."""

    SCHEDULING = "scheduling"
    # NOTE: Add more integration types as needed


class CommonActionType(str, Enum):
    """Common actions applicable to all integration types."""

    LOGIN = "login"
    LOGOUT = "logout"
    # NOTE: Add other common actions that ALL integrations would implement
